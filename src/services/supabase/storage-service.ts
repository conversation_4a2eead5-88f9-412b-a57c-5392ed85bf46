/**
 * Supabase Storage Service (Production Version)
 *
 * A production-ready service for handling file uploads to Supabase storage
 * This is an enhanced version of the storageService with better error handling
 * and more robust implementation for production use.
 */
import { supabase } from '@/lib/supabase';

/**
 * Upload a file to Supabase storage
 *
 * @param file The file to upload
 * @param bucket The storage bucket name
 * @param path The path within the bucket
 * @returns The public URL of the uploaded file
 */
export async function uploadFile(file: File, bucket: string, path: string): Promise<string> {
  console.log(`[StorageService] Uploading file to ${bucket}/${path}`, {
    fileType: file.type,
    fileSize: file.size,
    fileName: file.name
  });

  try {
    // Upload the file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error(`[StorageService] Upload error:`, error);
      throw new Error(`Upload failed: ${error.message}`);
    }

    console.log(`[StorageService] Upload successful, getting public URL for path: ${data.path}`);

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path);

    if (!urlData?.publicUrl) {
      console.error('[StorageService] Failed to get public URL for path:', data.path);
      throw new Error('Failed to get public URL');
    }

    console.log(`[StorageService] Upload complete with public URL: ${urlData.publicUrl}`);
    return urlData.publicUrl;
  } catch (error) {
    console.error(`[StorageService] Error in uploadFile:`, error);

    // Try a more direct approach as fallback
    try {
      console.log(`[StorageService] Attempting fallback upload method for ${bucket}/${path}`);

      // Try with a simpler path
      const simplePath = `${path.split('/').pop()}`;
      console.log(`[StorageService] Using simplified path: ${simplePath}`);

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(simplePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error(`[StorageService] Fallback upload also failed:`, error);
        throw error;
      }

      console.log(`[StorageService] Fallback upload successful, getting public URL for path: ${data.path}`);

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path);

      if (!urlData?.publicUrl) {
        console.error('[StorageService] Failed to get public URL for fallback path:', data.path);
        throw new Error('Failed to get public URL for fallback upload');
      }

      console.log(`[StorageService] Fallback upload complete with public URL: ${urlData.publicUrl}`);
      return urlData.publicUrl;
    } catch (fallbackError) {
      console.error(`[StorageService] Fallback upload also failed:`, fallbackError);
      throw error; // Throw the original error
    }
  }
}

/**
 * Upload a profile avatar
 *
 * @param file The image file to upload
 * @returns The public URL of the uploaded avatar
 */
export async function uploadProfileAvatar(file: File, overrideUserId?: string): Promise<string> {
  console.log(`[StorageService] Starting avatar upload process`, {
    fileType: file.type,
    fileSize: file.size,
    fileName: file.name,
    overrideUserId: overrideUserId || 'none'
  });

  let userId: string;

  // If an override user ID is provided, use it
  if (overrideUserId) {
    console.log(`[StorageService] Using override user ID: ${overrideUserId}`);
    userId = overrideUserId;
  } else {
    // Otherwise, get the current authenticated user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('[StorageService] Auth error:', userError);
      throw new Error(`Authentication error: ${userError.message}`);
    }

    if (!userData?.user?.id) {
      console.error('[StorageService] No authenticated user found');
      throw new Error('No authenticated user found');
    }

    userId = userData.user.id;
  }
  console.log(`[StorageService] Uploading avatar for user ${userId}`);

  // Create a unique filename
  const timestamp = Date.now();
  const fileExt = file.name.split('.').pop() || 'jpg';
  const fileName = `avatar-${timestamp}.${fileExt}`;

  // Create the path
  const filePath = `avatars/${userId}/${fileName}`;

  try {
    // Upload the file
    const url = await uploadFile(file, 'profiles', filePath);

    // Update the user profile
    await updateUserProfileImage(userId, 'avatar', url);

    return url;
  } catch (error) {
    console.error('[StorageService] Avatar upload failed:', error);

    // Try a simpler path as fallback
    const simplePath = `avatars/user-${userId}-${timestamp}.${fileExt}`;
    console.log(`[StorageService] Trying fallback path: ${simplePath}`);

    const url = await uploadFile(file, 'profiles', simplePath);

    // Update the user profile
    await updateUserProfileImage(userId, 'avatar', url);

    return url;
  }
}

/**
 * Upload a profile cover photo
 *
 * @param file The image file to upload
 * @returns The public URL of the uploaded cover photo
 */
export async function uploadProfileCover(file: File, overrideUserId?: string): Promise<string> {
  console.log(`[StorageService] Starting cover photo upload process`, {
    fileType: file.type,
    fileSize: file.size,
    fileName: file.name,
    overrideUserId: overrideUserId || 'none'
  });

  let userId: string;

  // If an override user ID is provided, use it
  if (overrideUserId) {
    console.log(`[StorageService] Using override user ID: ${overrideUserId}`);
    userId = overrideUserId;
  } else {
    // Otherwise, get the current authenticated user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('[StorageService] Auth error:', userError);
      throw new Error(`Authentication error: ${userError.message}`);
    }

    if (!userData?.user?.id) {
      console.error('[StorageService] No authenticated user found');
      throw new Error('No authenticated user found');
    }

    userId = userData.user.id;
  }
  console.log(`[StorageService] Uploading cover photo for user ${userId}`);

  // Create a unique filename
  const timestamp = Date.now();
  const fileExt = file.name.split('.').pop() || 'jpg';
  const fileName = `cover-${timestamp}.${fileExt}`;

  // Create the path
  const filePath = `covers/${userId}/${fileName}`;
  console.log(`[StorageService] Generated file path: ${filePath}`);

  try {
    // Upload the file
    console.log(`[StorageService] Attempting to upload cover photo to path: ${filePath}`);
    const url = await uploadFile(file, 'profiles', filePath);
    console.log(`[StorageService] Cover photo upload successful: ${url}`);

    // Update the user profile
    console.log(`[StorageService] Updating user profile with new cover photo URL`);
    await updateUserProfileImage(userId, 'cover', url);
    console.log(`[StorageService] User profile updated successfully`);

    return url;
  } catch (error) {
    console.error('[StorageService] Cover photo upload failed:', error);

    // Try a simpler path as fallback
    try {
      const simplePath = `covers/user-${userId}-${timestamp}.${fileExt}`;
      console.log(`[StorageService] Trying fallback path: ${simplePath}`);

      const url = await uploadFile(file, 'profiles', simplePath);
      console.log(`[StorageService] Fallback upload successful: ${url}`);

      // Update the user profile
      console.log(`[StorageService] Updating user profile with fallback cover photo URL`);
      await updateUserProfileImage(userId, 'cover', url);
      console.log(`[StorageService] User profile updated successfully with fallback URL`);

      return url;
    } catch (fallbackError) {
      console.error('[StorageService] Fallback cover photo upload also failed:', fallbackError);

      // Try one last approach with a very simple path
      try {
        const lastResortPath = `cover-${userId}-${timestamp}.${fileExt}`;
        console.log(`[StorageService] Trying last resort path: ${lastResortPath}`);

        const url = await uploadFile(file, 'profiles', lastResortPath);
        console.log(`[StorageService] Last resort upload successful: ${url}`);

        // Update the user profile
        await updateUserProfileImage(userId, 'cover', url);

        return url;
      } catch (lastError) {
        console.error('[StorageService] All cover photo upload attempts failed:', lastError);
        throw error; // Throw the original error
      }
    }
  }
}

/**
 * Update the user profile with the new image URL
 *
 * @param userId The user ID
 * @param type 'avatar' or 'cover'
 * @param url The image URL
 */
export async function updateUserProfileImage(userId: string, type: 'avatar' | 'cover', url: string): Promise<void> {
  console.log(`[StorageService] Updating user profile for ${type}:`, url);

  // Create the update data
  const updateData: Record<string, any> = {
    updated_at: new Date().toISOString()
  };

  if (type === 'avatar') {
    updateData.avatar = url;
  } else {
    updateData.cover_photo = url;
  }

  console.log(`[StorageService] Update data for user_profiles:`, {
    user_id: userId,
    ...updateData
  });

  try {
    // Update the user profile
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: userId,
        ...updateData
      }, { onConflict: 'user_id' });

    if (error) {
      console.error('[StorageService] Error updating profile:', error);
      throw new Error(`Failed to update profile: ${error.message}`);
    }

    console.log('[StorageService] Profile updated successfully', data);

    // Update localStorage cache
    try {
      const cacheKey = `user_profile_${userId}`;
      const cachedData = localStorage.getItem(cacheKey);

      if (cachedData) {
        console.log(`[StorageService] Found existing cache for user ${userId}`);
        const parsedData = JSON.parse(cachedData);

        if (type === 'avatar') {
          parsedData.avatar = url;
        } else {
          parsedData.coverPhoto = url;
        }

        localStorage.setItem(cacheKey, JSON.stringify(parsedData));
        console.log(`[StorageService] Updated localStorage cache for ${type}`);
      } else {
        console.log(`[StorageService] No existing cache found for user ${userId}, creating new cache entry`);

        // Create a new cache entry
        const newCacheData = {
          id: userId,
          [type === 'avatar' ? 'avatar' : 'coverPhoto']: url
        };

        localStorage.setItem(cacheKey, JSON.stringify(newCacheData));
      }

      // Also update generic cache keys
      if (type === 'avatar') {
        localStorage.setItem('lastUploadedProfileImage', url);
      } else {
        localStorage.setItem('lastUploadedCoverPhoto', url);
      }

      console.log(`[StorageService] Updated generic cache for ${type}`);
    } catch (cacheError) {
      console.error('[StorageService] Error updating localStorage:', cacheError);
      // Continue execution even if localStorage update fails
    }
  } catch (error) {
    console.error('[StorageService] Error in updateUserProfile:', error);

    // Try a direct update as a fallback
    try {
      console.log('[StorageService] Attempting fallback profile update');

      const { data, error: updateError } = await supabase
        .from('user_profiles')
        .update(updateData)
        .eq('user_id', userId);

      if (updateError) {
        console.error('[StorageService] Fallback profile update failed:', updateError);
        throw updateError;
      }

      console.log('[StorageService] Fallback profile update successful', data);
    } catch (fallbackError) {
      console.error('[StorageService] All profile update attempts failed:', fallbackError);
      throw error; // Throw the original error
    }
  }
}

// Export as a service object for consistency with other services
export const productionStorageService = {
  uploadFile,
  uploadProfileAvatar,
  uploadProfileCover,
  updateUserProfileImage
};
