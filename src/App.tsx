import { QueryClientProvider } from '@tanstack/react-query';
import { lazy, Suspense, useEffect } from 'react';
import { Routes, Route, BrowserRouter } from 'react-router-dom';
// Note: App.tsx will be replaced by Next.js app router structure

import { BackdropCleaner } from './components/common/BackdropCleaner';
// import { ChatPopupManager } from './components/chat/ChatPopupManager'; // Temporarily removed
import { ErrorBoundary } from './features/error/components/ErrorBoundary';
import { Footer } from './components/layout/Footer';
import { GlobalScrollToTop } from './components/common/ScrollToTop';
import { GlobalSettingsDialog } from './components/settings/GlobalSettingsDialog';
import { Header } from './components/layout/Header';
import { LoadingSpinner } from './components/ui/LoadingSpinner';
import { MessageNotification } from './components/chat/MessageNotification';
import { Toaster } from './components/ui/toaster';
import { AuthProvider } from './features/auth';
import { ToastContextProvider } from './features/toast';
import { queryClient } from './lib/queryClient';

// Temporarily remove all page imports to test if the build succeeds
// const HomePage = lazy(() => import('./pages/home'));
// const SessionsPage = lazy(() => import('./pages/my-sessions'));
// const SessionDetailPage = lazy(() => import('./pages/session-detail-redesign'));
// const TeachersPage = lazy(() => import('./pages/teacher-profile'));
// const CreateSessionPage = lazy(() => import('./pages/create-session'));
// const EditSessionPage = lazy(() => import('./pages/edit-session'));
// const BookSessionPage = lazy(() => import('./pages/book-session'));
// const AuthPage = lazy(() => import('./pages/auth-page'));
// const AdminPage = lazy(() => import('./pages/admin-page'));
// const SettingsPage = lazy(() => import('./pages/settings-page'));
// const MessagesPage = lazy(() => import('./pages/messages'));
// const FavoritesPage = lazy(() => import('./pages/favorites-page'));
// const SignInPage = lazy(() => import('./pages/sign-in'));
// const SignUpPage = lazy(() => import('./pages/sign-up'));
// const ResetPasswordPage = lazy(() => import('./pages/reset-password'));
// const VerifyEmailPage = lazy(() => import('./pages/VerifyEmail'));
// const RequestPasswordResetPage = lazy(() => import('./pages/RequestPasswordReset'));
// const RequestVerificationPage = lazy(() => import('./pages/RequestVerification'));
// const PaymentSuccessPage = lazy(() => import('./pages/payment-success'));
// const PaymentCancelPage = lazy(() => import('./pages/payment-cancel'));
// const NotFoundPage = lazy(() => import('./pages/not-found'));

// Simple placeholder component for testing
const PlaceholderPage = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">Session Hub</h1>
      <p className="text-lg text-gray-600">Build successful! Pages temporarily disabled for testing.</p>
    </div>
  </div>
);

function App() {
  useEffect(() => {
    // Clean up any existing backdrops on app start
    const backdrops = document.querySelectorAll('.backdrop-blur-sm, .backdrop-blur-md, .backdrop-blur-lg');
    backdrops.forEach(backdrop => {
      if (backdrop.parentNode) {
        backdrop.parentNode.removeChild(backdrop);
      }
    });
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ToastContextProvider>
            <BrowserRouter>
              <div className="min-h-screen bg-gray-50">
                <Header />
                <main className="flex-1">
                  <Suspense fallback={<LoadingSpinner />}>
                    <Routes>
                      {/* Temporarily use placeholder for all routes */}
                      <Route path="/" element={<PlaceholderPage />} />
                      <Route path="/sessions" element={<PlaceholderPage />} />
                      <Route path="/sessions/:id" element={<PlaceholderPage />} />
                      <Route path="/teachers" element={<PlaceholderPage />} />
                      <Route path="/teachers/:id" element={<PlaceholderPage />} />
                      <Route path="/create-session" element={<PlaceholderPage />} />
                      <Route path="/edit-session/:id" element={<PlaceholderPage />} />
                      <Route path="/book-session/:id" element={<PlaceholderPage />} />
                      <Route path="/auth" element={<PlaceholderPage />} />
                      <Route path="/admin" element={<PlaceholderPage />} />
                      <Route path="/settings" element={<PlaceholderPage />} />
                      <Route path="/messages" element={<PlaceholderPage />} />
                      <Route path="/favorites" element={<PlaceholderPage />} />
                      <Route path="/sign-in" element={<PlaceholderPage />} />
                      <Route path="/sign-up" element={<PlaceholderPage />} />
                      <Route path="/reset-password" element={<PlaceholderPage />} />
                      <Route path="/verify-email" element={<PlaceholderPage />} />
                      <Route path="/request-password-reset" element={<PlaceholderPage />} />
                      <Route path="/request-verification" element={<PlaceholderPage />} />
                      <Route path="/payment-success" element={<PlaceholderPage />} />
                      <Route path="/payment-cancel" element={<PlaceholderPage />} />
                      <Route path="*" element={<PlaceholderPage />} />
                    </Routes>
                  </Suspense>
                </main>
                <Footer />
                <Toaster />
                <MessageNotification />
                {/* <ChatPopupManager /> Temporarily removed */}
                <GlobalSettingsDialog />
                <GlobalScrollToTop />
                <BackdropCleaner />
              </div>
            </BrowserRouter>
          </ToastContextProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;