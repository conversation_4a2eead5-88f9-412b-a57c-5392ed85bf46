/**
 * useSynchronizedProfile Hook
 * 
 * A React hook that provides access to the synchronized user profile.
 * This hook ensures that the profile data is always consistent with the
 * authenticated user in Supabase.
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/features/auth';
import { supabase } from '@/lib/supabase-singleton';
import { 
  syncUserProfile, 
  UserProfile, 
  getAuthenticatedUserId 
} from '@/services/user-sync.service';
import { useToast } from './use-toast';

interface UseSynchronizedProfileResult {
  profile: UserProfile | null;
  isLoading: boolean;
  error: Error | null;
  refreshProfile: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<UserProfile>;
}

export function useSynchronizedProfile(): UseSynchronizedProfileResult {
  const { user: authUser, isCheckingAuth } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  // Fetch and synchronize the profile
  const fetchProfile = useCallback(async () => {
    if (!authUser) {
      setProfile(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Synchronize the profile with auth data
      const syncedProfile = await syncUserProfile(authUser);
      setProfile(syncedProfile);
    } catch (err) {
      console.error('[useSynchronizedProfile] Error fetching profile:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch profile'));
      
      toast({
        title: 'Profile Error',
        description: err instanceof Error ? err.message : 'Failed to fetch profile',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [authUser, toast]);

  // Refresh the profile
  const refreshProfile = useCallback(async () => {
    await fetchProfile();
  }, [fetchProfile]);

  // Update the profile
  const updateProfile = useCallback(async (updates: Partial<UserProfile>): Promise<UserProfile> => {
    if (!authUser) {
      throw new Error('Cannot update profile: Not authenticated');
    }

    try {
      setIsLoading(true);

      // Ensure we're updating the authenticated user's profile
      const authUserId = await getAuthenticatedUserId();
      
      // Create the update data
      const updateData: Partial<UserProfile> = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      // Update the profile
      const { data, error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: authUserId,
          ...updateData
        }, { onConflict: 'user_id' })
        .select('*')
        .single();

      if (error) {
        console.error('[useSynchronizedProfile] Error updating profile:', error);
        throw new Error(`Failed to update profile: ${error.message}`);
      }

      // Update the local state
      setProfile(data as UserProfile);
      
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully',
        variant: 'default',
      });

      return data as UserProfile;
    } catch (err) {
      console.error('[useSynchronizedProfile] Error updating profile:', err);
      
      toast({
        title: 'Update Failed',
        description: err instanceof Error ? err.message : 'Failed to update profile',
        variant: 'destructive',
      });
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [authUser, toast]);

  // Fetch the profile when the auth state changes
  useEffect(() => {
    if (!isCheckingAuth) {
      fetchProfile();
    }
  }, [isCheckingAuth, fetchProfile]);

  // Subscribe to profile changes
  useEffect(() => {
    if (!authUser) return;

    // Subscribe to changes in the user's profile
    const subscription = supabase
      .channel('profile-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_profiles',
          filter: `user_id=eq.${authUser.id}`,
        },
        (payload) => {
          console.log('[useSynchronizedProfile] Profile changed:', payload);
          fetchProfile();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [authUser, fetchProfile]);

  return {
    profile,
    isLoading,
    error,
    refreshProfile,
    updateProfile,
  };
}
