/**
 * Currency Management Hook
 * 
 * Provides functionality to manage user currency preferences,
 * including fetching, updating, and auto-detection.
 */

import { useState, useEffect } from 'react';
import { useUser } from '@/features/user/UserContext';
import { detectUserLocation } from '@/lib/location-detection';
import { DEFAULT_CURRENCY, getCurrencyInfo } from '@/lib/constants';
import { supabase } from '@/lib/supabase';

interface UseCurrencyReturn {
    currency: string;
    currencyInfo: ReturnType<typeof getCurrencyInfo>;
    isLoading: boolean;
    error: string | null;
    updateCurrency: (newCurrency: string) => Promise<void>;
    autoDetectCurrency: () => Promise<string | null>;
}

export function useCurrency(): UseCurrencyReturn {
    const { currentUser } = useUser();
    const [currency, setCurrency] = useState<string>(DEFAULT_CURRENCY);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Get current currency from user profile
    useEffect(() => {
        const fetchUserCurrency = async () => {
            if (!currentUser?.id) return;

            try {
                const { data: profile, error } = await supabase
                    .from('user_profiles')
                    .select('currency')
                    .eq('user_id', currentUser.id)
                    .single();

                if (error) {
                    console.error('Error fetching user currency:', error);
                    return;
                }

                if (profile?.currency) {
                    setCurrency(profile.currency);
                }
            } catch (err) {
                console.error('Error fetching user currency:', err);
            }
        };

        fetchUserCurrency();
    }, [currentUser?.id]);

    const currencyInfo = getCurrencyInfo(currency);

    /**
     * Update user's preferred currency
     */
    const updateCurrency = async (newCurrency: string): Promise<void> => {
        if (!currentUser?.id) {
            throw new Error('User not authenticated');
        }

        setIsLoading(true);
        setError(null);

        try {
            const { error: updateError } = await supabase
                .from('user_profiles')
                .update({ currency: newCurrency })
                .eq('user_id', currentUser.id);

            if (updateError) {
                throw updateError;
            }

            // Update local state
            setCurrency(newCurrency);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update currency';
            setError(errorMessage);
            throw new Error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Auto-detect currency based on user location
     */
    const autoDetectCurrency = async (): Promise<string | null> => {
        setIsLoading(true);
        setError(null);

        try {
            const locationInfo = await detectUserLocation();

            if (locationInfo.currency) {
                return locationInfo.currency;
            }

            return null;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to detect location';
            setError(errorMessage);
            return null;
        } finally {
            setIsLoading(false);
        }
    };

    return {
        currency,
        currencyInfo,
        isLoading,
        error,
        updateCurrency,
        autoDetectCurrency,
    };
}

/**
 * Hook for initializing user currency on first signup
 */
export function useInitializeCurrency() {
    const { currentUser } = useUser();
    const [isInitializing, setIsInitializing] = useState(false);

    useEffect(() => {
        const initializeCurrency = async () => {
            if (!currentUser?.id || isInitializing) {
                return;
            }

            // Check if user already has a currency set
            try {
                const { data: profile, error } = await supabase
                    .from('user_profiles')
                    .select('currency')
                    .eq('user_id', currentUser.id)
                    .single();

                if (error || profile?.currency) {
                    return; // User already has currency set or error occurred
                }

                setIsInitializing(true);

                // Auto-detect and set currency
                const locationInfo = await detectUserLocation();
                const detectedCurrency = locationInfo.currency || DEFAULT_CURRENCY;

                await supabase
                    .from('user_profiles')
                    .update({ currency: detectedCurrency })
                    .eq('user_id', currentUser.id);

            } catch (error) {
                console.error('Currency initialization error:', error);

                // Fallback to default currency
                try {
                    await supabase
                        .from('user_profiles')
                        .update({ currency: DEFAULT_CURRENCY })
                        .eq('user_id', currentUser.id);
                } catch (fallbackError) {
                    console.error('Failed to set default currency:', fallbackError);
                }
            } finally {
                setIsInitializing(false);
            }
        };

        initializeCurrency();
    }, [currentUser?.id, isInitializing]);

    return { isInitializing };
} 