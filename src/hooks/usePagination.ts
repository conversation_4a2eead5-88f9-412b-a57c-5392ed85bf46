import { useState, useCallback, useMemo } from 'react';
import { useSearchParams } from 'wouter/use-location';

interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  totalItems?: number;
  pageParam?: string;
  pageSizeParam?: string;
  syncWithUrl?: boolean;
}

/**
 * Custom hook for pagination
 * @param options - Pagination options
 * @returns Pagination state and handlers
 */
export function usePagination(options: UsePaginationOptions = {}) {
  const {
    initialPage = 1,
    initialPageSize = 10,
    totalItems = 0,
    pageParam = 'page',
    pageSizeParam = 'pageSize',
    syncWithUrl = false,
  } = options;

  // Get search params if syncing with URL
  const [searchParams, setSearchParams] = useSearchParams();

  // Initialize from URL if syncing with URL
  const urlPage = syncWithUrl ? Number(searchParams.get(pageParam)) || initialPage : initialPage;
  const urlPageSize = syncWithUrl
    ? Number(searchParams.get(pageSizeParam)) || initialPageSize
    : initialPageSize;

  const [page, setPageInternal] = useState(urlPage);
  const [pageSize, setPageSizeInternal] = useState(urlPageSize);

  /**
   * Set page with optional URL sync
   */
  const setPage = useCallback(
    (newPage: number) => {
      setPageInternal(newPage);

      if (syncWithUrl) {
        const params = new URLSearchParams(searchParams);
        params.set(pageParam, String(newPage));
        setSearchParams(params.toString());
      }
    },
    [syncWithUrl, searchParams, setSearchParams, pageParam]
  );

  /**
   * Set page size with optional URL sync
   */
  const setPageSize = useCallback(
    (newPageSize: number) => {
      setPageSizeInternal(newPageSize);
      setPageInternal(1); // Reset to first page when changing page size

      if (syncWithUrl) {
        const params = new URLSearchParams(searchParams);
        params.set(pageSizeParam, String(newPageSize));
        params.set(pageParam, '1');
        setSearchParams(params.toString());
      }
    },
    [syncWithUrl, searchParams, setSearchParams, pageParam, pageSizeParam]
  );

  /**
   * Go to next page
   */
  const nextPage = useCallback(() => {
    if (totalItems && page * pageSize >= totalItems) return;
    setPage(page + 1);
  }, [page, pageSize, totalItems, setPage]);

  /**
   * Go to previous page
   */
  const prevPage = useCallback(() => {
    if (page <= 1) return;
    setPage(page - 1);
  }, [page, setPage]);

  /**
   * Go to first page
   */
  const firstPage = useCallback(() => {
    if (page === 1) return;
    setPage(1);
  }, [page, setPage]);

  /**
   * Go to last page
   */
  const lastPage = useCallback(() => {
    if (!totalItems) return;
    const lastPageNumber = Math.max(1, Math.ceil(totalItems / pageSize));
    if (page === lastPageNumber) return;
    setPage(lastPageNumber);
  }, [page, pageSize, totalItems, setPage]);

  // Calculate pagination metadata
  const metadata = useMemo(() => {
    const totalPages = totalItems ? Math.ceil(totalItems / pageSize) : 0;
    const startItem = Math.min((page - 1) * pageSize + 1, totalItems || 0);
    const endItem = Math.min(page * pageSize, totalItems || 0);

    return {
      totalPages,
      startItem,
      endItem,
      hasNextPage: totalItems ? page * pageSize < totalItems : false,
      hasPrevPage: page > 1,
      isFirstPage: page === 1,
      isLastPage: totalItems ? page === Math.ceil(totalItems / pageSize) : false,
    };
  }, [page, pageSize, totalItems]);

  return {
    page,
    pageSize,
    setPage,
    setPageSize,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    ...metadata,
  };
}
