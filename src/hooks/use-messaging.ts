import { useConversation } from '../features/messaging/ConversationContext';
import { useMessages } from '../features/messaging/MessageContext';

/**
 * Custom hook for messaging functionality
 * This combines the conversation and message contexts for easier access
 */
export function useMessaging() {
  const conversation = useConversation();
  const messages = useMessages();

  return {
    // Conversation context
    conversations: conversation.conversations,
    activeConversation: conversation.activeConversation,
    isLoadingConversations: conversation.isLoading,
    conversationError: conversation.error,
    setActiveConversation: conversation.setActiveConversation,
    setActiveConversationId: conversation.setActiveConversationId,
    fetchConversations: conversation.fetchConversations,
    createConversation: conversation.createConversation,
    startNewConversation: conversation.startNewConversation,
    markConversationAsRead: conversation.markConversationAsRead,
    openConversationInPopup: conversation.openConversationInPopup,

    // Message context
    unreadCount: messages.unreadCount,
    unreadCounts: messages.unreadCounts,
    isLoadingMessages: messages.isLoading,
    messageError: messages.error,
    sendMessage: messages.sendMessage,
    setTypingStatus: messages.setTypingStatus,
    userTypingStatus: messages.userTypingStatus,
    isSocketConnected: messages.isSocketConnected,
    connectionFailed: messages.connectionFailed,
    reconnect: messages.reconnect,
    isAuthenticated: messages.isAuthenticated,
  };
}
