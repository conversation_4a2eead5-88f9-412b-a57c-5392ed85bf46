import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions<T> {
  fetchItems: (page: number) => Promise<T[]>;
  initialPage?: number;
  threshold?: number;
  dependencies?: any[];
}

/**
 * Custom hook for infinite scrolling
 * @param options - Infinite scroll options
 * @returns Infinite scroll state and handlers
 */
export function useInfiniteScroll<T>(options: UseInfiniteScrollOptions<T>) {
  const { fetchItems, initialPage = 1, threshold = 200, dependencies = [] } = options;

  const [items, setItems] = useState<T[]>([]);
  const [page, setPage] = useState(initialPage);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const observer = useRef<IntersectionObserver | null>(null);
  const lastItemRef = useRef<HTMLElement | null>(null);

  /**
   * Load more items
   */
  const loadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    setError(null);

    try {
      const newItems = await fetchItems(page);

      if (newItems.length === 0) {
        setHasMore(false);
      } else {
        setItems(prev => [...prev, ...newItems]);
        setPage(prev => prev + 1);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An error occurred while fetching items'));
      console.error('Error fetching items:', err);
    } finally {
      setIsLoading(false);
    }
  }, [fetchItems, page, isLoading, hasMore]);

  /**
   * Reset the infinite scroll
   */
  const reset = useCallback(() => {
    setItems([]);
    setPage(initialPage);
    setIsLoading(false);
    setHasMore(true);
    setError(null);
  }, [initialPage]);

  /**
   * Set up the intersection observer
   */
  const lastItemCallback = useCallback(
    (node: HTMLElement | null) => {
      if (isLoading) return;

      if (observer.current) {
        observer.current.disconnect();
      }

      observer.current = new IntersectionObserver(
        entries => {
          if (entries[0].isIntersecting && hasMore) {
            loadMore();
          }
        },
        {
          rootMargin: `0px 0px ${threshold}px 0px`,
        }
      );

      if (node) {
        observer.current.observe(node);
        lastItemRef.current = node;
      }
    },
    [isLoading, hasMore, loadMore, threshold]
  );

  // Initial load
  useEffect(() => {
    reset();
    loadMore();
  }, [...dependencies]);

  // Clean up observer on unmount
  useEffect(() => {
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  return {
    items,
    isLoading,
    hasMore,
    error,
    loadMore,
    reset,
    lastItemRef: lastItemCallback,
  };
}
