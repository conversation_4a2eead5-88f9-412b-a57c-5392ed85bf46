import { useState, useEffect } from 'react';

/**
 * Custom hook for media queries
 * @param query - Media query string
 * @returns Whether the media query matches
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // Prevent SSR issues
    if (typeof window === 'undefined') {
      return;
    }

    // Create media query list
    const mediaQuery = window.matchMedia(query);

    // Set initial value
    setMatches(mediaQuery.matches);

    // Create event listener
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add event listener
    mediaQuery.addEventListener('change', handler);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handler);
    };
  }, [query]);

  return matches;
}

/**
 * Predefined breakpoints
 */
export const breakpoints = {
  sm: '(min-width: 640px)',
  md: '(min-width: 768px)',
  lg: '(min-width: 1024px)',
  xl: '(min-width: 1280px)',
  '2xl': '(min-width: 1536px)',
  dark: '(prefers-color-scheme: dark)',
  light: '(prefers-color-scheme: light)',
  portrait: '(orientation: portrait)',
  landscape: '(orientation: landscape)',
  motion: '(prefers-reduced-motion: no-preference)',
  reducedMotion: '(prefers-reduced-motion: reduce)',
};

/**
 * Custom hook for predefined breakpoints
 * @returns Object with boolean values for each breakpoint
 */
export function useBreakpoints() {
  const sm = useMediaQuery(breakpoints.sm);
  const md = useMediaQuery(breakpoints.md);
  const lg = useMediaQuery(breakpoints.lg);
  const xl = useMediaQuery(breakpoints.xl);
  const xxl = useMediaQuery(breakpoints['2xl']);
  const dark = useMediaQuery(breakpoints.dark);
  const light = useMediaQuery(breakpoints.light);
  const portrait = useMediaQuery(breakpoints.portrait);
  const landscape = useMediaQuery(breakpoints.landscape);
  const motion = useMediaQuery(breakpoints.motion);
  const reducedMotion = useMediaQuery(breakpoints.reducedMotion);

  return {
    sm,
    md,
    lg,
    xl,
    xxl,
    dark,
    light,
    portrait,
    landscape,
    motion,
    reducedMotion,
  };
}
