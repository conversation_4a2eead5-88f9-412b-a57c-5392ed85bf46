import { useState, useCallback, useEffect } from 'react';
import { useQueryClient, useMutation, useQuery } from '@tanstack/react-query';
import { formatError, formatDate } from '@/lib/utils';
import { useToast } from './use-toast';
import { useAuth } from './use-auth';
import { apiRequest } from '@/lib/queryClient';
import { Session, User } from '@shared/schema';
import { Link, useLocation } from 'wouter';
import { parseISO } from 'date-fns';

export interface SessionInput {
  title: string;
  description: string;
  imageUrl?: string;
  date: Date | string;
  duration: number;
  price: number;
  maxParticipants?: number;
  format: string;
  location?: string;
  skillLevel?: string;
  type: string;
  isPublic: boolean;
  agenda?: string;
  meetingUrl?: string;
  prerequisites?: string;
  materials?: string;
  tags?: string[];
  categoryId?: number;
}

export interface Booking {
  id: number;
  userId: number;
  sessionId: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  // Include session details when needed
  session?: Session;
  user?: User;
}

export const useSession = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();

  // Fetch a specific session by ID
  const fetchSession = useCallback(
    async ({ sessionId, forceRefresh = false }: { sessionId: number; forceRefresh?: boolean }) => {
      try {
        const url = new URL(`/api/sessions/${sessionId}`, window.location.origin);

        // Add forceRefresh parameter when needed
        if (forceRefresh) {
          url.searchParams.append('refresh', 'true');
          url.searchParams.append('_t', Date.now().toString());
        }

        console.log(
          `[SessionHook] Fetching session ${sessionId} with forceRefresh=${forceRefresh}`
        );

        const { data } = await apiRequest('GET', url.toString(), undefined, {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
        });

        console.log(`[SessionHook] Received data for session ${sessionId}:`, {
          id: data?.id,
          title: data?.title,
          date: data?.date ? new Date(data.date).toISOString() : 'missing',
          updatedAt: data?.updatedAt,
        });

        return data;
      } catch (error) {
        console.error('Error fetching session:', error);
        throw error;
      }
    },
    []
  );

  // Use the fetchSession function in a query
  const useSessionQuery = (sessionId: number, options = {}) => {
    return useQuery({
      queryKey: ['session', sessionId],
      queryFn: () => fetchSession({ sessionId }),
      ...options,
    });
  };

  // Fetch sessions created by a teacher
  const fetchTeacherSessions = useCallback(
    async ({ teacherId }: { teacherId: number }) => {
      try {
        const { data } = await apiRequest('GET', `/api/users/${teacherId}/teaching-sessions`);
        return data;
      } catch (error) {
        console.error('Error fetching teacher sessions:', error);
        throw error;
      }
    },
    []
  );

  // Create a new session
  const createSessionMutation = useMutation({
    mutationFn: async (sessionData: SessionInput) => {
      try {
        // Ensure date is in ISO format
        const formattedData = {
          ...sessionData,
          date:
            sessionData.date instanceof Date ? sessionData.date.toISOString() : sessionData.date,
        };

        const { data } = await apiRequest('POST', '/api/sessions', formattedData);

        return data;
      } catch (error) {
        console.error('Error creating session:', error);
        throw error;
      }
    },
    onSuccess: data => {
      // Invalidate queries to refresh sessions list
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['teaching-sessions'] });

      // Show success message
      toast({
        title: 'Session Created',
        description: 'Your session has been successfully created.',
      });

      // Redirect to the session page
      navigate(`/sessions/${data.id}`);
    },
    onError: (error: any) => {
      console.error('Session creation error:', error);
      toast({
        title: 'Error Creating Session',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Update an existing session
  const updateSessionMutation = useMutation({
    mutationFn: async ({ sessionId, sessionData }) => {
      // Always use /api/simple-update-session
      const formattedData = { ...sessionData, id: sessionId };
      if (formattedData.date) {
        if (formattedData.date instanceof Date) {
          formattedData.date = formattedData.date.toISOString();
        } else if (typeof formattedData.date === 'string') {
          const parsedDate = new Date(formattedData.date);
          if (!isNaN(parsedDate.getTime())) {
            formattedData.date = parsedDate.toISOString();
          }
        }
      }
      const { data } = await apiRequest('POST', '/api/simple-update-session', formattedData, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          'X-Cache-Bust': Date.now().toString(),
        },
      });
      return data;
    },
    onSuccess: data => {
      queryClient.setQueryData(['session', data.id], data);
      queryClient.invalidateQueries({ queryKey: ['session', data.id] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: [`/api/sessions/${data.id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/sessions'] });
      queryClient.invalidateQueries({
        queryKey: [`/api/teachers/${data.teacherId}/sessions`],
      });
      setTimeout(() => {
        fetchSession({ sessionId: data.id, forceRefresh: true }).then(freshData => {
          queryClient.setQueryData(['session', data.id], freshData);
          queryClient.setQueryData([`/api/sessions/${data.id}`], freshData);
        });
      }, 500);

      // Only show toast for explicit form submissions, not automatic updates
      // This prevents notifications when media items are added
      const isExplicitUpdate = sessionStorage.getItem('explicit_session_update') === 'true';
      if (isExplicitUpdate) {
        sessionStorage.removeItem('explicit_session_update');
        toast({
          title: 'Session Updated',
          description: 'Your session has been successfully updated. Refresh the page if changes are not visible.',
          className: 'bg-[#C2A584]/10 border-[#C2A584]/30 text-[#8B4513]',
        });
      }

      localStorage.setItem('last_session_update', Date.now().toString());
    },
    onError: error => {
      toast({
        title: 'Error Updating Session',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Delete a session
  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: number) => {
      try {
        const { data } = await apiRequest('DELETE', `/api/sessions/${sessionId}`);
        return data;
      } catch (error) {
        console.error('Error deleting session:', error);
        throw error;
      }
    },
    onSuccess: (_, sessionId) => {
      // Remove from cache and invalidate queries
      queryClient.removeQueries({ queryKey: ['session', sessionId] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['teaching-sessions'] });

      // Show success message
      toast({
        title: 'Session Deleted',
        description: 'Your session has been successfully deleted.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error Deleting Session',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Toggle session visibility
  const toggleSessionVisibilityMutation = useMutation({
    mutationFn: async ({ sessionId, isPublic }: { sessionId: number; isPublic: boolean }) => {
      try {
        const { data } = await apiRequest('PATCH', `/api/sessions/${sessionId}/visibility`, {
          isPublic,
        });
        return data;
      } catch (error) {
        console.error('Error toggling session visibility:', error);
        throw error;
      }
    },
    onSuccess: (data, { sessionId }) => {
      // Update the cached session data
      queryClient.setQueryData(['session', sessionId], data);
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['teaching-sessions'] });

      toast({
        title: 'Visibility Updated',
        description: `Session is now ${data.isPublic ? 'public' : 'private'}.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error Updating Visibility',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Book a session
  const bookSessionMutation = useMutation({
    mutationFn: async (sessionId: number) => {
      try {
        const { data } = await apiRequest('POST', `/api/sessions/${sessionId}/book`);
        return data;
      } catch (error) {
        console.error('Error booking session:', error);
        throw error;
      }
    },
    onSuccess: (data, sessionId) => {
      // Invalidate session and bookings queries
      queryClient.invalidateQueries({ queryKey: ['session', sessionId] });
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });

      toast({
        title: 'Session Booked',
        description: 'You have successfully booked this session.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Booking Failed',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Cancel a booking
  const cancelBookingMutation = useMutation({
    mutationFn: async (bookingId: number) => {
      try {
        const { data } = await apiRequest('DELETE', `/api/bookings/${bookingId}`);
        return data;
      } catch (error) {
        console.error('Error canceling booking:', error);
        throw error;
      }
    },
    onSuccess: (data, bookingId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });

      toast({
        title: 'Booking Canceled',
        description: 'Your booking has been successfully canceled.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Cancellation Failed',
        description: formatError(error),
        variant: 'destructive',
      });
    },
  });

  // Fetch user's bookings
  const fetchUserBookings = useCallback(async () => {
    try {
      const { data } = await apiRequest('GET', '/api/user/bookings');
      return data;
    } catch (error) {
      console.error('Error fetching user bookings:', error);
      throw error;
    }
  }, []);

  // Utility function to format session date/time
  const formatSessionDateTime = useCallback((session: Session) => {
    try {
      const date = parseISO(session.date);
      return {
        date: formatDate(date),
        time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      };
    } catch (error) {
      console.error('Error formatting session date:', error);
      return {
        date: 'Invalid date',
        time: 'Invalid time',
      };
    }
  }, []);

  return {
    // Queries
    useSessionQuery,
    fetchSession,
    fetchTeacherSessions,
    fetchUserBookings,

    // Mutations
    createSession: createSessionMutation.mutate,
    createSessionAsync: createSessionMutation.mutateAsync,
    isCreatingSession: createSessionMutation.isPending,

    updateSession: updateSessionMutation.mutate,
    updateSessionAsync: updateSessionMutation.mutateAsync,
    isUpdatingSession: updateSessionMutation.isPending,

    deleteSession: deleteSessionMutation.mutate,
    deleteSessionAsync: deleteSessionMutation.mutateAsync,
    isDeletingSession: deleteSessionMutation.isPending,

    toggleSessionVisibility: toggleSessionVisibilityMutation.mutate,
    toggleSessionVisibilityAsync: toggleSessionVisibilityMutation.mutateAsync,
    isTogglingVisibility: toggleSessionVisibilityMutation.isPending,

    bookSession: bookSessionMutation.mutate,
    bookSessionAsync: bookSessionMutation.mutateAsync,
    isBookingSession: bookSessionMutation.isPending,

    cancelBooking: cancelBookingMutation.mutate,
    cancelBookingAsync: cancelBookingMutation.mutateAsync,
    isCancelingBooking: cancelBookingMutation.isPending,

    // Utilities
    formatSessionDateTime,
  };
};