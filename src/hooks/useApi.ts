import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/contexts/ToastContext';
import errorHandler from '@/lib/errorHandler';

interface UseApiOptions<T> {
  initialData?: T;
  dependencies?: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  showErrorToast?: boolean;
  showSuccessToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
  skipFetch?: boolean;
}

/**
 * Custom hook for API data fetching with loading and error handling
 * @param fetchFn - The fetch function to call
 * @param options - Options for the hook
 * @returns The data, loading state, error, and refetch function
 */
export function useApi<T>(fetchFn: () => Promise<T>, options: UseApiOptions<T> = {}) {
  const {
    initialData,
    dependencies = [],
    onSuccess,
    onError,
    showErrorToast = true,
    showSuccessToast = false,
    successMessage = 'Operation completed successfully',
    errorMessage,
    skipFetch = false,
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [isLoading, setIsLoading] = useState(!skipFetch);
  const [error, setError] = useState<Error | null>(null);
  const toast = useToast();

  const fetchData = useCallback(async () => {
    if (skipFetch) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchFn();
      setData(result);
      setIsLoading(false);

      if (onSuccess) {
        onSuccess(result);
      }

      if (showSuccessToast) {
        toast.success(successMessage);
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      setIsLoading(false);

      if (onError) {
        onError(error);
      }

      if (showErrorToast) {
        const message = errorMessage || errorHandler.handleApiError(error);
        toast.error(message);
      }

      return undefined;
    }
  }, [
    fetchFn,
    onSuccess,
    onError,
    showErrorToast,
    showSuccessToast,
    successMessage,
    errorMessage,
    skipFetch,
    toast,
  ]);

  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  return { data, isLoading, error, refetch: fetchData };
}

/**
 * Custom hook for API mutation operations with loading and error handling
 * @param mutationFn - The mutation function to call
 * @param options - Options for the hook
 * @returns The mutation function, loading state, and error
 */
export function useMutation<T, P>(
  mutationFn: (params: P) => Promise<T>,
  options: Omit<UseApiOptions<T>, 'initialData' | 'dependencies' | 'skipFetch'> = {}
) {
  const {
    onSuccess,
    onError,
    showErrorToast = true,
    showSuccessToast = true,
    successMessage = 'Operation completed successfully',
    errorMessage,
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const toast = useToast();

  const mutate = useCallback(
    async (params: P) => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await mutationFn(params);
        setIsLoading(false);

        if (onSuccess) {
          onSuccess(result);
        }

        if (showSuccessToast) {
          toast.success(successMessage);
        }

        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error('An unknown error occurred');
        setError(error);
        setIsLoading(false);

        if (onError) {
          onError(error);
        }

        if (showErrorToast) {
          const message = errorMessage || errorHandler.handleApiError(error);
          toast.error(message);
        }

        return undefined;
      }
    },
    [
      mutationFn,
      onSuccess,
      onError,
      showErrorToast,
      showSuccessToast,
      successMessage,
      errorMessage,
      toast,
    ]
  );

  return { mutate, isLoading, error };
}
