import { useState, useCallback, ChangeEvent, FormEvent } from 'react';

interface UseFormOptions<T> {
  initialValues: T;
  onSubmit?: (values: T) => void | Promise<void>;
  validate?: (values: T) => Partial<Record<keyof T, string>>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

/**
 * Custom hook for form handling
 * @param options - Form options
 * @returns Form state and handlers
 */
export function useForm<T extends Record<string, any>>(options: UseFormOptions<T>) {
  const {
    initialValues,
    onSubmit,
    validate,
    validateOnChange = false,
    validateOnBlur = true,
  } = options;

  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(true);

  /**
   * Validate form values
   */
  const validateForm = useCallback(() => {
    if (!validate) {
      setIsValid(true);
      return {};
    }

    const validationErrors = validate(values);
    const hasErrors = Object.keys(validationErrors).length > 0;

    setErrors(validationErrors);
    setIsValid(!hasErrors);

    return validationErrors;
  }, [values, validate]);

  /**
   * Handle input change
   */
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name, value, type } = e.target;

      // Handle different input types
      let newValue: any = value;

      if (type === 'checkbox') {
        newValue = (e.target as HTMLInputElement).checked;
      } else if (type === 'number') {
        newValue = value === '' ? '' : Number(value);
      }

      setValues(prev => ({
        ...prev,
        [name]: newValue,
      }));

      setTouched(prev => ({
        ...prev,
        [name]: true,
      }));

      if (validateOnChange) {
        validateForm();
      }
    },
    [validateOnChange, validateForm]
  );

  /**
   * Handle input blur
   */
  const handleBlur = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name } = e.target;

      setTouched(prev => ({
        ...prev,
        [name]: true,
      }));

      if (validateOnBlur) {
        validateForm();
      }
    },
    [validateOnBlur, validateForm]
  );

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault();

      // Mark all fields as touched
      const allTouched = Object.keys(values).reduce(
        (acc, key) => {
          acc[key as keyof T] = true;
          return acc;
        },
        {} as Partial<Record<keyof T, boolean>>
      );

      setTouched(allTouched);

      // Validate form
      const validationErrors = validateForm();

      if (Object.keys(validationErrors).length === 0 && onSubmit) {
        setIsSubmitting(true);

        try {
          await onSubmit(values);
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      }
    },
    [values, validateForm, onSubmit]
  );

  /**
   * Set a specific field value
   */
  const setFieldValue = useCallback(
    (name: keyof T, value: any) => {
      setValues(prev => ({
        ...prev,
        [name]: value,
      }));

      if (validateOnChange) {
        validateForm();
      }
    },
    [validateOnChange, validateForm]
  );

  /**
   * Set multiple field values
   */
  const setFieldValues = useCallback(
    (newValues: Partial<T>) => {
      setValues(prev => ({
        ...prev,
        ...newValues,
      }));

      if (validateOnChange) {
        validateForm();
      }
    },
    [validateOnChange, validateForm]
  );

  /**
   * Reset form to initial values
   */
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(true);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldValues,
    resetForm,
    validateForm,
  };
}
