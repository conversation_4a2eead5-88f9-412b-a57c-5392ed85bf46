import { createContext, useState, useContext, ReactNode, useEffect, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useLocation } from '../lib/next-router-utils';
import { User } from '@shared/schema';
import { getQueryFn, apiRequest, queryClient } from '../lib/queryClient';
import { useToast } from './use-toast';
import { z } from 'zod';

interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: Error | null;
  login: (username: string, password: string) => Promise<User | null>;
  register: (userData: RegisterData) => Promise<User | null>;
  logout: () => Promise<void>;
  requestPasswordReset: (email: string) => Promise<boolean>;
  resetPassword: (token: string, password: string) => Promise<boolean>;
  loginMutation: ReturnType<
    typeof useMutation<User, Error, { username: string; password: string }>
  >;
  registerMutation: ReturnType<typeof useMutation<User, Error, RegisterData>>;
  logoutMutation: ReturnType<typeof useMutation<any, Error, void>>;
  isAuthenticated: boolean;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  name: string;
}

const AuthContext = createContext<AuthContextType | null>(null);

function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [error, setError] = useState<Error | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Fetch the current user from the API
  const {
    data: user,
    isLoading,
    error: fetchError,
    refetch,
  } = useQuery({
    queryKey: ['/api/user'],
    queryFn: async () => {
      try {
        // Check if we've hit rate limits recently
        const lastRateLimitTime = localStorage.getItem('auth_rate_limit_time');
        if (lastRateLimitTime && Date.now() - parseInt(lastRateLimitTime, 10) < 10000) {
          // 10 seconds
          console.warn('Auth check rate limited, using cached user data');
          const cachedUser = localStorage.getItem('current_user');
          if (cachedUser) {
            try {
              return JSON.parse(cachedUser) as User;
            } catch (e) {
              console.error('Error parsing cached user:', e);
            }
          }
        }

        // Use a more cache-friendly approach
        const response = await fetch(`/api/user`, {
          credentials: 'include',
          headers: {
            'Cache-Control': 'max-age=60', // Allow caching for 60 seconds
            Pragma: 'cache',
          },
        });

        if (!response.ok) {
          if (response.status === 429) {
            console.warn('Rate limited when fetching user data. Will retry with backoff.');

            const retryAfter = response.headers.get('Retry-After') || '30';
            const secondsToWait = parseInt(retryAfter, 10);

            // Store the rate limit time for future reference
            localStorage.setItem('auth_rate_limit_time', Date.now().toString());

            // Try to use cached user data
            const cachedUser = localStorage.getItem('current_user');
            if (cachedUser) {
              try {
                return JSON.parse(cachedUser) as User;
              } catch (e) {
                console.error('Error parsing cached user:', e);
              }
            }

            throw new Error(`Rate limited. Retry after ${secondsToWait}s`);
          }

          if (response.status === 401 || response.status === 403) {
            console.log('User is not authenticated');
            return null;
          }

          console.error(`Failed to fetch user: ${response.status} ${response.statusText}`);
          throw new Error('Failed to fetch user');
        }

        const data = await response.json();
        return data as User;
      } catch (error) {
        const cachedUser = localStorage.getItem('current_user');
        if (cachedUser) {
          try {
            const parsedUser = JSON.parse(cachedUser);
            console.log('Using cached user from localStorage due to fetch error');
            return parsedUser as User;
          } catch (parseError) {
            console.error('Error parsing cached user:', parseError);
          }
        }

        console.error('Error fetching user:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attempt, error) => {
      const retryAfterMatch = error?.message?.match(/Retry after (\d+)s/);
      if (retryAfterMatch) {
        const seconds = parseInt(retryAfterMatch[1], 10);
        return seconds * 1000;
      }

      return Math.min(1000 * 2 ** attempt, 15000);
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: true,
  });

  useEffect(() => {
    if (user) {
      localStorage.setItem('current_user', JSON.stringify(user));
    }
  }, [user]);

  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async ({ username, password }: { username: string; password: string }) => {
      const { data } = await apiRequest('/api/login', {
        method: 'POST',
        body: JSON.stringify({ username, password }),
      });
      return data;
    },
    onSuccess: data => {
      // Update the user in the query cache
      if (data) {
        queryClient.setQueryData(['/api/user'], data);
        refetch();
        toast({
          title: 'Login successful',
          description: `Welcome back, ${data.name}!`,
        });
      }
    },
    onError: (error: Error) => {
      console.error('[Auth] Login error:', error);
      toast({
        title: 'Login failed',
        description: error.message || 'Invalid username or password.',
        variant: 'destructive',
      });
      setError(error);
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: async (userData: RegisterData) => {
      const { data } = await apiRequest('/api/register', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
      return data;
    },
    onSuccess: data => {
      // Update the user in the query cache
      if (data) {
        queryClient.setQueryData(['/api/user'], data);
        refetch();
        toast({
          title: 'Registration successful',
          description: `Welcome, ${data.name}!`,
        });
      }
    },
    onError: (error: Error) => {
      console.error('[Auth] Registration error:', error);
      toast({
        title: 'Registration failed',
        description: error.message || 'Could not create your account.',
        variant: 'destructive',
      });
      setError(error);
    },
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const { data } = await apiRequest('/api/logout', { method: 'POST' });
      return data;
    },
    onSuccess: () => {
      // Clear auth-related data from all locations
      queryClient.setQueryData(['/api/user'], null);
      queryClient.clear();
      // Navigate to home page after logout
      window.location.href = '/';
      toast({
        title: 'Logged out',
        description: "You've been successfully logged out",
      });
    },
    onError: (error: Error) => {
      console.error('[Auth] Logout error:', error);
      toast({
        title: 'Logout failed',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
    },
  });

  // Password reset request mutation
  const requestPasswordResetMutation = useMutation({
    mutationFn: async (email: string) => {
      const { data } = await apiRequest('/api/password-reset/request', {
        method: 'POST',
        body: JSON.stringify({ email }),
      });
      return data;
    },
    onSuccess: () => {
      toast({
        title: 'Password reset requested',
        description: 'If your email is in our system, you will receive a password reset link.',
      });
    },
    onError: (error: Error) => {
      console.error('[Auth] Password reset request error:', error);
      toast({
        title: 'Password reset request failed',
        description: error.message || 'Could not request password reset.',
        variant: 'destructive',
      });
    },
  });

  // Password reset mutation
  const resetPasswordMutation = useMutation({
    mutationFn: async ({ token, password }: { token: string; password: string }) => {
      const { data } = await apiRequest('/api/password-reset/reset', {
        method: 'POST',
        body: JSON.stringify({ token, password }),
      });
      return data;
    },
    onSuccess: () => {
      toast({
        title: 'Password reset successful',
        description: 'Your password has been reset. You can now log in with your new password.',
      });
      setLocation('/login');
    },
    onError: (error: Error) => {
      console.error('[Auth] Password reset error:', error);
      toast({
        title: 'Password reset failed',
        description: error.message || 'Could not reset your password.',
        variant: 'destructive',
      });
    },
  });

  // Login helper function
  const login = async (username: string, password: string): Promise<User | null> => {
    try {
      return await loginMutation.mutateAsync({ username, password });
    } catch (error) {
      return null;
    }
  };

  // Register helper function
  const register = async (userData: RegisterData): Promise<User | null> => {
    try {
      return await registerMutation.mutateAsync(userData);
    } catch (error) {
      return null;
    }
  };

  // Logout helper function
  const logout = useCallback(async (): Promise<void> => {
    try {
      // Clear any chat popups
      const chatContainer = document.getElementById('chat-container');
      if (chatContainer) {
        chatContainer.innerHTML = '';
      }
      localStorage.removeItem('session-hub-chat-states');
      // Make logout API call
      await apiRequest('/api/logout', { method: 'POST' });
      // Update state
      queryClient.setQueryData(['/api/user'], null);
      queryClient.clear();
      // Navigate to home page after logout
      window.location.href = '/';
      toast({
        title: 'Logged out',
        description: "You've been successfully logged out",
      });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }, [toast, queryClient]);

  // Request password reset helper function
  const requestPasswordReset = async (email: string): Promise<boolean> => {
    try {
      await requestPasswordResetMutation.mutateAsync(email);
      return true;
    } catch (error) {
      return false;
    }
  };

  // Reset password helper function
  const resetPassword = async (token: string, password: string): Promise<boolean> => {
    try {
      await resetPasswordMutation.mutateAsync({ token, password });
      return true;
    } catch (error) {
      return false;
    }
  };

  // Create the context value
  const contextValue: AuthContextType = {
    user: user ?? null,
    isLoggedIn: !!user,
    isLoading,
    error,
    login,
    register,
    logout,
    requestPasswordReset,
    resetPassword,
    loginMutation,
    registerMutation,
    logoutMutation,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export { AuthProvider, useAuth };
