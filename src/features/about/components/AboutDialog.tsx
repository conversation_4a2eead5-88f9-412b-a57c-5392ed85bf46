import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  <PERSON><PERSON><PERSON>lose,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { CalendarCheck } from 'lucide-react';

interface AboutDialogProps {
  trigger?: React.ReactNode;
}

export function AboutDialog({ trigger }: AboutDialogProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>{trigger || <Button variant="ghost">About</Button>}</DialogTrigger>
      <DialogContent className="sm:max-w-md" aria-describedby="about-description">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 p-2 bg-primary/10 rounded-xl inline-block">
            <CalendarCheck className="h-10 w-10 text-primary" />
          </div>
          <DialogTitle className="text-xl font-semibold">Session Hub</DialogTitle>
          <p className="text-gray-500 mt-1">Your path to peer-powered learning</p>
          <p className="text-sm text-muted-foreground mt-1">Version 1.0.0</p>
        </DialogHeader>

        <div id="about-description" className="space-y-4 py-4 text-sm">
          <p>
            Session Hub connects people who want to share skills with those who want to learn. It
            doesn't matter if you're a professional, an expert, or just someone with knowledge to
            share - Session Hub is open to all.
          </p>

          <p>
            <strong>As a teacher</strong>, you can offer your expertise through individual or group
            sessions, set your own rates, or even arrange skill swaps. Show yourself to the world,
            find clients, and share what you know.
          </p>

          <p>
            <strong>As a learner</strong>, you can find exactly the help you need - whether it's
            fitness coaching, DIY guidance, therapy, or any other skill. Session Hub creates a
            social environment where people connect directly with each other.
          </p>

          <h3 className="font-semibold mt-6 mb-2">How It Works</h3>
          <p>
            Browse and book sessions with qualified teachers, or become an teacher yourself to
            share your expertise with others. If you can't find what you're looking for using the
            filters, try using the search bar to find relevant sessions that might match your
            interests.
          </p>

          <p className="text-xs text-muted-foreground mt-6">
            © {new Date().getFullYear()} Session Hub. All rights reserved.
          </p>
        </div>

        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
}
