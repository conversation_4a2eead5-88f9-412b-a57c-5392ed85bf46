import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  Toast,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';
import { Toaster, toast } from 'sonner';

// Toast types
export type ToastType = 'default' | 'success' | 'error' | 'warning' | 'info';

// Toast interface
export interface ToastMessage {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
}

// Toast context interface
interface ToastContextProps {
  addToast: (type: ToastType, message: string, title?: string, duration?: number) => string;
  removeToast: (id: string) => void;
  success: (message: string, title?: string, duration?: number) => string;
  error: (message: string, title?: string, duration?: number) => string;
  warning: (message: string, title?: string, duration?: number) => string;
  info: (message: string, title?: string, duration?: number) => string;
}

// Create context
const ToastContext = createContext<ToastContextProps | undefined>(undefined);

// Toast provider props
interface ToastProviderProps {
  children: ReactNode;
}

/**
 * Toast provider component
 */
export const ToastContextProvider: React.FC<ToastProviderProps> = ({ children }) => {
  // Add toast
  const addToast = useCallback(
    (type: ToastType, message: string, title?: string, duration?: number): string => {
      const id = uuidv4();

      switch (type) {
        case 'success':
          toast.success(title || 'Success', {
            description: message,
            duration: duration || 5000,
            id,
          });
          break;
        case 'error':
          toast.error(title || 'Error', {
            description: message,
            duration: duration || 5000,
            id,
          });
          break;
        case 'warning':
          toast.warning(title || 'Warning', {
            description: message,
            duration: duration || 5000,
            id,
          });
          break;
        case 'info':
          toast.info(title || 'Info', {
            description: message,
            duration: duration || 5000,
            id,
          });
          break;
        default:
          toast(title || 'Notification', {
            description: message,
            duration: duration || 5000,
            id,
          });
      }

      return id;
    },
    []
  );

  // Remove toast
  const removeToast = useCallback((id: string) => {
    toast.dismiss(id);
  }, []);

  // Shorthand methods
  const success = useCallback(
    (message: string, title?: string, duration?: number) =>
      addToast('success', message, title, duration),
    [addToast]
  );

  const error = useCallback(
    (message: string, title?: string, duration?: number) =>
      addToast('error', message, title, duration),
    [addToast]
  );

  const warning = useCallback(
    (message: string, title?: string, duration?: number) =>
      addToast('warning', message, title, duration),
    [addToast]
  );

  const info = useCallback(
    (message: string, title?: string, duration?: number) =>
      addToast('info', message, title, duration),
    [addToast]
  );

  return (
    <ToastContext.Provider
      value={{
        addToast,
        removeToast,
        success,
        error,
        warning,
        info,
      }}
    >
      {children}
      <Toaster position="top-right" />
    </ToastContext.Provider>
  );
};

/**
 * Hook to use the toast context
 */
export const useToast = (): ToastContextProps => {
  const context = useContext(ToastContext);

  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  return context;
};
