import { useState, useRef } from 'react';
import { ImagePlus, Upload, Camera, XCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface AvatarPickerProps {
  onSelect: (url: string) => void;
}

function AvatarPicker({ onSelect }: AvatarPickerProps) {
  // Default avatars users can choose from
  const defaultAvatars = [
    { id: 1, url: 'https://api.dicebear.com/7.x/initials/svg?seed=JS', label: 'Initials' },
    { id: 2, url: 'https://api.dicebear.com/7.x/bottts/svg?seed=Felix', label: 'Robot' },
    { id: 3, url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Midnight', label: 'Person 1' },
    { id: 4, url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Felix', label: 'Person 2' },
  ];

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-500">
        Select one of these default avatars or upload your own image.
      </p>

      <div className="grid grid-cols-2 gap-3">
        {defaultAvatars.map(avatar => (
          <div
            key={avatar.id}
            className="border rounded-lg overflow-hidden cursor-pointer hover:border-primary transition-colors"
            onClick={() => onSelect(avatar.url)}
          >
            <div className="relative">
              <img
                src={avatar.url}
                alt={`${avatar.label} Avatar`}
                className="w-full h-32 object-cover"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 px-2 py-1">
                <span className="text-xs text-white">{avatar.label}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface CameraUploaderProps {
  onCapture: (imageDataUrl: string) => void;
}

function CameraUploader({ onCapture }: CameraUploaderProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreamActive, setIsStreamActive] = useState(false);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreamActive(true);
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      alert('Could not access camera. Please check your permissions.');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      const tracks = stream.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsStreamActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext('2d');
      if (!context) return;

      // Set canvas dimensions to match video
      const videoWidth = videoRef.current.videoWidth;
      const videoHeight = videoRef.current.videoHeight;

      // Calculate new dimensions (resize if too large)
      let width = videoWidth;
      let height = videoHeight;
      const maxWidth = 1200;

      if (width > maxWidth) {
        height = Math.round((height * maxWidth) / width);
        width = maxWidth;
      }

      canvasRef.current.width = width;
      canvasRef.current.height = height;

      // Draw the video frame to the canvas with resizing
      context.drawImage(videoRef.current, 0, 0, width, height);

      // Convert canvas to data URL with quality reduction
      const imageDataUrl = canvasRef.current.toDataURL('image/jpeg', 0.8);

      // Pass the image data to the parent component
      onCapture(imageDataUrl);

      // Stop the camera
      stopCamera();
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative aspect-video rounded-lg overflow-hidden bg-gray-100 border">
        <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />
        <canvas ref={canvasRef} className="hidden" />

        {!isStreamActive && (
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <Camera className="w-12 h-12 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">Camera feed will appear here</p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        {!isStreamActive ? (
          <Button type="button" onClick={startCamera} variant="outline">
            <Camera className="w-4 h-4 mr-2" />
            Start Camera
          </Button>
        ) : (
          <>
            <Button type="button" onClick={stopCamera} variant="outline">
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="button" onClick={captureImage}>
              <Camera className="w-4 h-4 mr-2" />
              Take Photo
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

interface ImageUploaderProps {
  initialImage?: string;
  onImageSelected: (imageUrl: string) => void;
  aspectRatio?: 'square' | 'cover';
  title?: string;
}

export function ImageUploader({
  initialImage,
  onImageSelected,
  aspectRatio = 'square',
  title = 'Upload Image',
}: ImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>(initialImage);
  const [urlInput, setUrlInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Resize the image before setting it
      resizeImage(file, 1200, 0.7)
        .then(resizedDataUrl => {
          setPreviewImage(resizedDataUrl);
          onImageSelected(resizedDataUrl);
          setIsOpen(false);
        })
        .catch(error => {
          console.error('Error resizing image:', error);
          // Fallback to original file if resizing fails
          const reader = new FileReader();
          reader.onload = event => {
            const result = event.target?.result as string;
            setPreviewImage(result);
            onImageSelected(result);
            setIsOpen(false);
          };
          reader.readAsDataURL(file);
        });
    }
  };

  // Function to resize image before upload
  const resizeImage = (file: File, maxWidth: number, quality: number): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Create a FileReader to read the file
      const reader = new FileReader();

      // Set up the FileReader onload event
      reader.onload = readerEvent => {
        // Create an image element
        const img = new Image();

        // Set up the image onload event
        img.onload = () => {
          // Get the image dimensions
          let width = img.width;
          let height = img.height;

          // Calculate new dimensions if the image is larger than maxWidth
          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }

          // Create a canvas element
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          // Draw the image on the canvas
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // Get the data URL from the canvas
          const dataUrl = canvas.toDataURL('image/jpeg', quality);

          resolve(dataUrl);
        };

        // Set the image source
        img.src = readerEvent.target?.result as string;
      };

      // Read the file as a data URL
      reader.readAsDataURL(file);
    });
  };

  const handleUrlSubmit = () => {
    if (urlInput && urlInput.trim()) {
      setPreviewImage(urlInput);
      onImageSelected(urlInput);
      setIsOpen(false);
      setUrlInput('');
    }
  };

  const handleAvatarSelect = (url: string) => {
    setPreviewImage(url);
    onImageSelected(url);
    setIsOpen(false);
  };

  const handleCameraCapture = (imageDataUrl: string) => {
    setPreviewImage(imageDataUrl);
    onImageSelected(imageDataUrl);
    setIsOpen(false);
  };

  const imagePlaceholderClasses =
    aspectRatio === 'square' ? 'w-full aspect-square rounded-lg' : 'w-full h-48 rounded-lg';

  return (
    <div className="relative">
      <div
        className={`border-2 border-dashed border-gray-300 rounded-lg overflow-hidden 
          hover:border-primary transition-colors cursor-pointer flex items-center justify-center
          bg-gray-50 ${aspectRatio === 'square' ? 'aspect-square' : 'aspect-video'}`}
        onClick={() => setIsOpen(true)}
      >
        {previewImage ? (
          <img src={previewImage} alt="Uploaded" className="w-full h-full object-cover" />
        ) : (
          <div className="text-center p-4">
            <ImagePlus className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">Click to upload</p>
          </div>
        )}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="file">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="file">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
            </TabsList>

            <TabsContent value="file" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="file">Upload from your device</Label>
                <Input
                  id="file"
                  type="file"
                  ref={fileInputRef}
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>

              <div className="mt-6">
                <h3 className="text-sm font-medium mb-2">Or select from avatars:</h3>
                <AvatarPicker onSelect={handleAvatarSelect} />
              </div>
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="url">Image URL</Label>
                <Input
                  id="url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
            </TabsContent>

            <TabsContent value="camera" className="space-y-4">
              <CameraUploader onCapture={handleCameraCapture} />
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>

            <Button
              type="button"
              disabled={!urlInput && !previewImage}
              onClick={handleUrlSubmit}
              className="ml-2"
            >
              <Upload className="w-4 h-4 mr-2" />
              Use URL
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
