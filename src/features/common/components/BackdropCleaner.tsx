import { useEffect } from 'react';
import { cleanupBackdrops } from '@/lib/utils';

/**
 * BackdropCleaner - A utility component that cleans up any backdrop/overlay elements
 * that might be stuck in the DOM due to component unmounting issues
 *
 * Place this component in your app layout or any component that handles filter dropdowns
 */
export function BackdropCleaner() {
  useEffect(() => {
    // Clean up on mount
    cleanupBackdrops();

    // Clean up on route changes or other app events
    const cleanupHandler = () => {
      cleanupBackdrops();
    };

    // Handle history changes
    window.addEventListener('popstate', cleanupHandler);

    // Handle page visibility (user switching tabs/coming back)
    document.addEventListener('visibilitychange', cleanupHandler);

    // Set up periodic cleanup every 3 seconds while visible
    // This provides a safety net if any events are missed
    const intervalId = setInterval(() => {
      if (document.visibilityState === 'visible') {
        cleanupBackdrops();
      }
    }, 3000);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('popstate', cleanupHandler);
      document.removeEventListener('visibilitychange', cleanupHandler);
      clearInterval(intervalId);

      // Final cleanup on component unmount
      cleanupBackdrops();
    };
  }, []);

  // This component doesn't render anything visible
  return null;
}
