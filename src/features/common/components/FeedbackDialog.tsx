import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { MessageSquare, ThumbsUp, ThumbsDown, Smile, Frown, Star } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface FeedbackType {
  id: string;
  label: string;
  icon: React.ReactNode;
}

const feedbackTypes: FeedbackType[] = [
  { id: 'feature', label: 'Feature Request', icon: <Star className="w-4 h-4" /> },
  { id: 'bug', label: 'Bug Report', icon: <Frown className="w-4 h-4" /> },
  { id: 'praise', label: 'Praise', icon: <ThumbsUp className="w-4 h-4" /> },
  { id: 'criticism', label: 'Criticism', icon: <ThumbsDown className="w-4 h-4" /> },
  { id: 'general', label: 'General Feedback', icon: <MessageSquare className="w-4 h-4" /> },
];

interface FeedbackDialogProps {
  trigger?: React.ReactNode;
}

export function FeedbackDialog({ trigger }: FeedbackDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [feedbackText, setFeedbackText] = useState('');
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!selectedType || !feedbackText.trim()) {
      toast({
        title: 'Incomplete feedback',
        description: 'Please select a feedback type and provide some details.',
        variant: 'destructive',
      });
      return;
    }

    // Here you would typically send the feedback to your backend
    console.log('Feedback submitted:', { type: selectedType, text: feedbackText });

    toast({
      title: 'Thank you for your feedback!',
      description: 'We appreciate your input and will review it shortly.',
    });

    // Reset and close
    setSelectedType(null);
    setFeedbackText('');
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            <span>Feedback</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>App Feedback & Feature Requests</DialogTitle>
          <DialogDescription>
            Help us improve the Session Hub platform with your feedback on the app, report bugs, or
            suggest new features you'd like to see.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>What type of feedback do you have?</Label>
            <div className="flex flex-wrap gap-2">
              {feedbackTypes.map(type => (
                <Button
                  key={type.id}
                  variant={selectedType === type.id ? 'default' : 'outline'}
                  size="sm"
                  className="flex items-center gap-1.5"
                  onClick={() => setSelectedType(type.id)}
                >
                  {type.icon}
                  <span>{type.label}</span>
                </Button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="feedback">Tell us more</Label>
            <Textarea
              id="feedback"
              value={feedbackText}
              onChange={e => setFeedbackText(e.target.value)}
              placeholder="Please describe your feedback about the app, suggest a new feature, or report an issue with the platform..."
              className="min-h-[120px]"
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="ghost" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} className="bg-[#E07A5F] hover:bg-[#E07A5F]/90">
            Submit Feedback
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function FloatingFeedbackButton() {
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <FeedbackDialog
        trigger={
          <Button
            size="sm"
            className="rounded-full h-12 w-12 shadow-lg bg-[#E07A5F] hover:bg-[#E07A5F]/90"
            title="App Feedback & Feature Requests"
            aria-label="Open app feedback form"
          >
            <MessageSquare className="h-5 w-5" />
          </Button>
        }
      />
    </div>
  );
}
