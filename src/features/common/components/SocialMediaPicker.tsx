import { useState } from 'react';
import { Facebook, Instagram, Linkedin, Globe, Plus, Youtube } from 'lucide-react';
import { XLogo } from '@/features/icons/components/XLogo';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type SocialPlatform =
  | 'instagram'
  | 'facebook'
  | 'twitter'
  | 'linkedin'
  | 'youtube'
  | 'tiktok'
  | 'website';

interface SocialAccount {
  platform: SocialPlatform;
  username: string;
  url: string;
}

interface SocialMediaPickerProps {
  accounts: SocialAccount[];
  onChange: (accounts: SocialAccount[]) => void;
}

const PLATFORM_CONFIG = {
  instagram: {
    name: 'Instagram',
    icon: <Instagram className="w-4 h-4" />,
    baseUrl: 'https://instagram.com/',
    color: 'text-pink-500',
    placeholder: 'yourusername',
  },
  facebook: {
    name: 'Facebook',
    icon: <Facebook className="w-4 h-4" />,
    baseUrl: 'https://facebook.com/',
    color: 'text-blue-600',
    placeholder: 'yourusername',
  },
  twitter: {
    name: 'X',
    icon: <XLogo className="w-4 h-4" />,
    baseUrl: 'https://x.com/',
    color: 'text-black',
    placeholder: 'yourusername',
  },
  linkedin: {
    name: 'LinkedIn',
    icon: <Linkedin className="w-4 h-4" />,
    baseUrl: 'https://linkedin.com/in/',
    color: 'text-blue-700',
    placeholder: 'yourusername',
  },
  youtube: {
    name: 'YouTube',
    icon: <Youtube className="w-4 h-4" />,
    baseUrl: 'https://youtube.com/@',
    color: 'text-red-600',
    placeholder: 'yourchannel',
  },
  tiktok: {
    name: 'TikTok',
    // Custom TikTok icon
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"></path>
        <path d="M15 8a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"></path>
        <path d="M15 2v20"></path>
        <path d="M9 16v6"></path>
      </svg>
    ),
    baseUrl: 'https://tiktok.com/@',
    color: 'text-black',
    placeholder: 'yourusername',
  },
  website: {
    name: 'Website',
    icon: <Globe className="w-4 h-4" />,
    baseUrl: '',
    color: 'text-gray-500',
    placeholder: 'https://yourwebsite.com',
  },
};

export function SocialMediaPicker({ accounts, onChange }: SocialMediaPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<SocialPlatform>('instagram');
  const [username, setUsername] = useState('');

  // Keep track of all platforms
  const allPlatforms = Object.keys(PLATFORM_CONFIG) as SocialPlatform[];

  // Get platforms that haven't been added yet - recalculate this every time
  // to ensure it's always up to date
  const availablePlatforms = allPlatforms.filter(
    platform => !accounts.some(a => a.platform === platform)
  );

  // If no platforms are available, use all platforms
  // This ensures the dropdown is never empty
  const displayPlatforms = availablePlatforms.length > 0 ? availablePlatforms : allPlatforms;

  // Debug log to help troubleshoot
  console.log('Available platforms:', availablePlatforms);
  console.log('Current accounts:', accounts);

  // Handle dialog open state
  const handleOpenChange = (open: boolean) => {
    // Only update if the state is actually changing
    if (open !== isOpen) {
      setIsOpen(open);

      // When dialog opens, recalculate available platforms and select the first one
      if (open) {
        // If there are available platforms, select the first one
        if (displayPlatforms.length > 0) {
          setSelectedPlatform(displayPlatforms[0]);
        }
      } else {
        // When dialog closes, reset the form
        setUsername('');
      }
    }
  };

  const handleAddAccount = () => {
    if (!username.trim()) return;

    try {
      // Format the URL properly based on platform
      let formattedUrl = username.trim();

      // For website, ensure it has http/https prefix
      if (selectedPlatform === 'website') {
        if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
          formattedUrl = `https://${formattedUrl}`;
        }
      } else {
        // For social media, build the URL with the base URL
        const baseUrl = PLATFORM_CONFIG[selectedPlatform].baseUrl;
        // Remove @ symbol if user included it
        const cleanUsername = formattedUrl.startsWith('@')
          ? formattedUrl.substring(1)
          : formattedUrl;
        formattedUrl = `${baseUrl}${cleanUsername}`;
      }

      // Create the new account object
      const newAccount: SocialAccount = {
        platform: selectedPlatform,
        username: username.trim(),
        url: formattedUrl,
      };

      // Create a new accounts array with the new account
      // This ensures we're not modifying the original array
      const updatedAccounts = [
        ...accounts.filter(a => a.platform !== selectedPlatform),
        newAccount,
      ];

      console.log(`Adding ${selectedPlatform} account:`, newAccount);

      // Update the parent component with the new accounts
      // This will trigger the onChange callback in the parent component
      onChange(updatedAccounts);

      // Reset state and close dialog
      setUsername('');
      setIsOpen(false);

      // Reset selected platform to first available
      if (displayPlatforms.length > 0) {
        setSelectedPlatform(displayPlatforms[0]);
      }

      console.log('Updated accounts list:', updatedAccounts);
    } catch (error) {
      console.error('Error adding social media account:', error);
    }
  };

  const handleRemoveAccount = (platform: SocialPlatform) => {
    try {
      // Remove the account from the list
      const updatedAccounts = accounts.filter(a => a.platform !== platform);

      // Log for debugging
      console.log(`Removing ${platform} account, updated accounts:`, updatedAccounts);

      // Update the parent component with the new accounts list
      onChange(updatedAccounts);

      // Set the selected platform to the one that was just removed
      // This makes it easy to add it back if the user wants
      setSelectedPlatform(platform);

      // Don't open the dialog automatically after removing an account
      // This prevents the dialog from opening when the user just wants to remove accounts
    } catch (error) {
      console.error(`Error removing ${platform} account:`, error);
    }
  };

  // We already defined availablePlatforms at the top of the component

  return (
    <div className="space-y-4">
      {/* Add Account button moved to the left under title */}
      {availablePlatforms.length > 0 && (
        <div className="flex justify-start mb-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="rounded-full inline-flex"
            onClick={e => {
              e.preventDefault(); // Prevent any form submission
              e.stopPropagation(); // Stop event bubbling
              setIsOpen(true);
            }}
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Account
          </Button>
        </div>
      )}

      <div className="flex flex-wrap gap-3">
        {accounts.map(account => {
          const config = PLATFORM_CONFIG[account.platform];
          return (
            <div
              key={account.platform}
              className="inline-flex items-center gap-2 bg-gray-100 hover:bg-gray-200 transition-colors rounded-full py-2 px-4"
            >
              <span className={config.color}>{config.icon}</span>
              <span className="text-sm font-medium">{account.username}</span>
              <button
                type="button"
                onClick={e => {
                  e.preventDefault(); // Prevent any form submission
                  e.stopPropagation(); // Stop event bubbling
                  handleRemoveAccount(account.platform);
                }}
                className="ml-1 text-gray-500 hover:text-red-500 flex-shrink-0"
              >
                ×
              </button>
            </div>
          );
        })}
      </div>

      <Dialog open={isOpen} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Social Media Account</DialogTitle>
            <DialogDescription>
              Add your social media accounts to display on your profile.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="platform">Platform</Label>
              <Select
                value={selectedPlatform}
                onValueChange={value => setSelectedPlatform(value as SocialPlatform)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent className="min-w-[250px]">
                  {displayPlatforms.map(platform => (
                    <SelectItem key={platform} value={platform} className="py-2">
                      <div className="flex items-center gap-4 w-full">
                        <span className={PLATFORM_CONFIG[platform].color}>
                          {PLATFORM_CONFIG[platform].icon}
                        </span>
                        <span className="ml-2 truncate font-medium">
                          {PLATFORM_CONFIG[platform].name}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">
                {selectedPlatform === 'website' ? 'URL' : 'Username'}
              </Label>
              <div className="flex gap-2 items-center">
                {selectedPlatform !== 'website' && (
                  <div className="bg-gray-100 px-2 py-2 rounded-l-md border-y border-l text-sm text-gray-500 truncate max-w-[180px]">
                    {PLATFORM_CONFIG[selectedPlatform].baseUrl}
                  </div>
                )}
                <Input
                  id="username"
                  value={username}
                  onChange={e => setUsername(e.target.value)}
                  placeholder={PLATFORM_CONFIG[selectedPlatform].placeholder}
                  className={selectedPlatform !== 'website' ? 'rounded-l-none' : ''}
                />
              </div>
            </div>

            {/* Preview */}
            {username && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <p className="text-xs text-gray-500 mb-1">Preview:</p>
                <div className="flex items-center gap-2 bg-white border border-gray-200 rounded-full py-1.5 px-3 text-sm inline-flex">
                  <span className={PLATFORM_CONFIG[selectedPlatform].color}>
                    {PLATFORM_CONFIG[selectedPlatform].icon}
                  </span>
                  <span>{username}</span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={e => {
                e.preventDefault(); // Prevent any form submission
                e.stopPropagation(); // Stop event bubbling
                handleAddAccount();
              }}
              disabled={!username.trim()}
            >
              Add Account
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
