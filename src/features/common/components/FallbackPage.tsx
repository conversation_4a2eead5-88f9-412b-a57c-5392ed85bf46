import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

interface FallbackPageProps {
  error?: Error | null;
  resetErrorBoundary?: () => void;
}

export function FallbackPage({ error, resetErrorBoundary }: FallbackPageProps) {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-amber-500" />
            <CardTitle>Something went wrong</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            The application encountered an error while loading this page.
          </p>
          {error && (
            <div className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40 mb-4">
              <p className="font-medium">Error details:</p>
              <p className="text-red-600">{error.message}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center gap-4">
          <Button onClick={() => window.location.reload()} variant="outline">
            Reload Page
          </Button>
          <Button onClick={() => (window.location.href = '/')}>Go Home</Button>
          {resetErrorBoundary && (
            <Button onClick={resetErrorBoundary} variant="outline">
              Try Again
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
