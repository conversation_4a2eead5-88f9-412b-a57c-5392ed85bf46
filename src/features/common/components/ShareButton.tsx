import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Share2,
  Facebook,
  Twitter,
  Mail,
  Link as LinkIcon,
  Instagram,
  Linkedin,
  Copy,
  MessageSquare,
  X,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

interface ShareButtonProps {
  url: string;
  title: string;
  description?: string;
  image?: string;
  variant?: 'icon' | 'button' | 'dropdown';
  className?: string;
  iconOnly?: boolean;
  showDialog?: boolean;
}

export function ShareButton({
  url,
  title,
  description = '',
  image = '',
  variant = 'dropdown',
  className = '',
  iconOnly = false,
  showDialog = false,
}: ShareButtonProps) {
  const { toast } = useToast();
  const [dialogOpen, setDialogOpen] = useState(false);

  const shareText = description || `Check out: ${title}`;

  // Ensure we're using the full URL
  const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;

  const handleWebShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Use the Web Share API if available (primarily mobile devices)
    if (navigator.share) {
      navigator
        .share({
          title: title,
          text: shareText,
          url: fullUrl,
        })
        .catch(err => {
          console.error('Error sharing:', err);
          // Fallback to dropdown if sharing fails
          setDialogOpen(true);
        });
    } else {
      // Otherwise show the custom share UI
      setDialogOpen(true);
    }
  };

  const handleFacebookShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}`,
      '_blank'
    );
  };

  const handleTwitterShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(
        shareText
      )}&url=${encodeURIComponent(fullUrl)}`,
      '_blank'
    );
  };

  const handleLinkedInShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}`,
      '_blank'
    );
  };

  const handleEmailShare = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(
      `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(
        `${shareText}: ${fullUrl}`
      )}`,
      '_blank'
    );
  };

  const handleCopyLink = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigator.clipboard.writeText(fullUrl).then(() => {
      toast({
        title: 'Link copied',
        description: 'Link copied to clipboard',
      });
    });
  };

  // Dropdown menu content - shared between variants
  const shareOptions = (
    <>
      <DropdownMenuItem onClick={handleFacebookShare}>
        <Facebook className="mr-2 h-4 w-4" />
        <span>Facebook</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleTwitterShare}>
        <Twitter className="mr-2 h-4 w-4" />
        <span>Twitter</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleLinkedInShare}>
        <Linkedin className="mr-2 h-4 w-4" />
        <span>LinkedIn</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleEmailShare}>
        <Mail className="mr-2 h-4 w-4" />
        <span>Email</span>
      </DropdownMenuItem>
      <DropdownMenuItem onClick={handleCopyLink}>
        <Copy className="mr-2 h-4 w-4" />
        <span>Copy link</span>
      </DropdownMenuItem>
    </>
  );

  // Icon-only button
  if (variant === 'icon') {
    return (
      <>
        <button
          className={`bg-white/80 rounded-full p-1.5 hover:bg-white ${className}`}
          onClick={handleWebShare}
          aria-label="Share"
        >
          <Share2 className="h-4 w-4 text-gray-600" />
        </button>

        {showDialog && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Share</DialogTitle>
                <DialogDescription>
                  Share this content with your friends and colleagues
                </DialogDescription>
              </DialogHeader>
              <div className="flex flex-col space-y-3 py-4">
                <div className="flex flex-wrap gap-2 justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleFacebookShare}
                  >
                    <Facebook className="h-4 w-4" />
                    <span>Facebook</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleTwitterShare}
                  >
                    <Twitter className="h-4 w-4" />
                    <span>Twitter</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleLinkedInShare}
                  >
                    <Linkedin className="h-4 w-4" />
                    <span>LinkedIn</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleEmailShare}
                  >
                    <Mail className="h-4 w-4" />
                    <span>Email</span>
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Input value={fullUrl} readOnly />
                  <Button size="sm" onClick={handleCopyLink}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  }

  // Regular button
  if (variant === 'button') {
    return (
      <>
        <Button variant="outline" size="sm" className={className} onClick={handleWebShare}>
          <Share2 className="mr-2 h-4 w-4" />
          {!iconOnly && <span>Share</span>}
        </Button>

        {showDialog && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Share</DialogTitle>
                <DialogDescription>
                  Share this content with your friends and colleagues
                </DialogDescription>
              </DialogHeader>
              <div className="flex flex-col space-y-3 py-4">
                <div className="flex flex-wrap gap-2 justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleFacebookShare}
                  >
                    <Facebook className="h-4 w-4" />
                    <span>Facebook</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleTwitterShare}
                  >
                    <Twitter className="h-4 w-4" />
                    <span>Twitter</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleLinkedInShare}
                  >
                    <Linkedin className="h-4 w-4" />
                    <span>LinkedIn</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={handleEmailShare}
                  >
                    <Mail className="h-4 w-4" />
                    <span>Email</span>
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Input value={fullUrl} readOnly />
                  <Button size="sm" onClick={handleCopyLink}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </>
    );
  }

  // Dropdown variant (default)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild onClick={e => e.stopPropagation()}>
        {variant === 'dropdown' ? (
          <Button variant="outline" size="sm" className={className}>
            <Share2 className={`h-4 w-4 ${iconOnly ? '' : 'mr-2'}`} />
            {!iconOnly && <span>Share</span>}
          </Button>
        ) : (
          <button
            className={`bg-white/80 rounded-full p-1.5 hover:bg-white ${className}`}
            aria-label="Share"
          >
            <Share2 className="h-4 w-4 text-gray-600" />
          </button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {shareOptions}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
