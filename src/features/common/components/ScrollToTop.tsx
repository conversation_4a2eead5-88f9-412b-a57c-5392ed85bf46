import { useEffect } from 'react';
import { useLocation } from '../../../lib/next-router-utils';

interface ScrollToTopProps {
  dependencyArray?: any[];
}

/**
 * A component that scrolls the window to the top when its dependency array changes.
 * Place this at the top of route components to automatically scroll to top on route change.
 */
export function ScrollToTop({ dependencyArray = [] }: ScrollToTopProps) {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, dependencyArray);

  return null;
}

/**
 * Global ScrollToTop component that scrolls to top on route changes.
 * To be used in the main App component.
 * Uses instant scrolling (not smooth) to avoid interfering with native scroll behavior.
 */
export function GlobalScrollToTop() {
  const [location] = useLocation();

  useEffect(() => {
    // Use immediate scrolling without smooth behavior to avoid scroll delays
    window.scrollTo(0, 0);
  }, [location]);

  return null;
}
