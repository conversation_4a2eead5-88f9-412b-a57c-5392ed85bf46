import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Link } from '../../../components/ui/NextLink';
import { useAuth } from '@/features/auth/AuthContext';

export function FloatingCreateSessionButton() {
  const { user } = useAuth();

  // Don't show the button if user is not logged in or not an teacher
  const isTeacher = (user as any)?.isTeacher;

  if (!user || !isTeacher) {
    return null;
  }

  return (
    <div className="fixed bottom-20 right-4 z-50">
      <Link href="/create-session">
        <Button
          size="lg"
          className="rounded-full h-14 w-14 shadow-lg bg-[#84a59d] hover:bg-[#84a59d]/90 transition-all duration-300 hover:scale-105"
          title="Create a new session"
          aria-label="Create a new session"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </Link>
    </div>
  );
}
