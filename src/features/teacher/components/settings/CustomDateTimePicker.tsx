import { Control, UseFormWatch } from 'react-hook-form';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface CustomDateTimePickerProps {
  control: Control<any>;
  watch: UseFormWatch<any>;
  timingFieldName: string;
  dateFieldName: string;
  timeFieldName: string;
  triggerValue: string;
}

export function CustomDateTimePicker({
  control,
  watch,
  timingFieldName,
  dateFieldName,
  timeFieldName,
  triggerValue,
}: CustomDateTimePickerProps) {
  const showPicker = watch(timingFieldName) === triggerValue;

  if (!showPicker) return null;

  return (
    <div className="absolute top-[-30px] right-0">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-xs text-gray-500 hover:bg-transparent hover:text-gray-700"
          >
            <CalendarIcon className="h-4 w-4 mr-1" />
            Set date/time
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="end">
          <div className="space-y-4">
            <div>
              <FormLabel className="text-sm text-gray-500 block mb-1">
                Date:
              </FormLabel>
              <FormField
                control={control}
                name={dateFieldName}
                render={({ field }) => (
                  <FormItem>
                    <Calendar
                      mode="single"
                      selected={field.value || undefined}
                      onSelect={field.onChange}
                      initialFocus
                      className="rounded-md border"
                    />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormLabel className="text-sm text-gray-500 block mb-1">
                Time:
              </FormLabel>
              <FormField
                control={control}
                name={timeFieldName}
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="time"
                        className="w-full h-8 text-xs rounded-md"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
