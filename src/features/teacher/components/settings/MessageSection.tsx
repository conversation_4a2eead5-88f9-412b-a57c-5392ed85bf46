import { useState } from 'react';
import { Control, UseFormWatch, UseFormSetValue } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { DeliveryMethodToggle } from './DeliveryMethodToggle';
import { TimingSelector } from './TimingSelector';
import { CustomDateTimePicker } from './CustomDateTimePicker';
import { EmailTemplateSelector } from './EmailTemplateSelector';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

interface EmailTemplate {
  id: string;
  name: string;
  content: string;
}

interface MessageSectionProps {
  control: Control<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  title: string;
  messageFieldName: string;
  deliveryFieldName: string;
  timingFieldName: string;
  dateFieldName: string;
  timeFieldName: string;
  templateFieldName: string;
  timingOptions: Array<{
    value: string;
    label: string;
  }>;
  placeholder: string;
  isWelcomeMessage?: boolean;
  emailTemplates: EmailTemplate[];
  messageType?: string;
}

export function MessageSection({
  control,
  watch,
  setValue,
  title,
  messageFieldName,
  deliveryFieldName,
  timingFieldName,
  dateFieldName,
  timeFieldName,
  templateFieldName,
  timingOptions,
  placeholder,
  isWelcomeMessage = false,
  emailTemplates,
}: MessageSectionProps) {
  const deliveryMethod = watch(deliveryFieldName);
  const isEmail = deliveryMethod === 'email';
  const selectedTemplateId = watch(templateFieldName);
  
  // State for preview dialog
  const [previewOpen, setPreviewOpen] = useState(false);
  
  // Function to show preview
  const handleShowPreview = () => {
    setPreviewOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center mb-3">
          <div>
            <FormLabel className="text-base font-medium mb-2 block">
              {title}
            </FormLabel>

            {/* Delivery Method Toggle */}
            <DeliveryMethodToggle control={control} name={deliveryFieldName} />
          </div>

          {/* Timing Options - Not shown for Welcome Message */}
          {!isWelcomeMessage && (
            <div className="flex items-center">
              <TimingSelector
                control={control}
                name={timingFieldName}
                options={timingOptions}
              />
            </div>
          )}
        </div>

        {/* Email Templates Selector - Only shown when email is selected */}
        {isEmail && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-700">Choose a template:</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowPreview}
              >
                Preview Email
              </Button>
            </div>
            
            {/* Basic template selector */}
            <EmailTemplateSelector
              control={control}
              name={messageFieldName}
              templateFieldName={templateFieldName}
              templates={emailTemplates}
              onTemplateSelect={(content) => setValue(messageFieldName, content)}
            />
          </div>
        )}

        {/* Custom Date/Time Picker - Not shown for Welcome Message */}
        {!isWelcomeMessage && (
          <div className="relative">
            <CustomDateTimePicker
              control={control}
              watch={watch}
              timingFieldName={timingFieldName}
              dateFieldName={dateFieldName}
              timeFieldName={timeFieldName}
              triggerValue="custom_time"
            />
          </div>
        )}

        {/* Message Content */}
        <FormField
          control={control}
          name={messageFieldName}
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormControl>
                <RichTextEditor
                  value={field.value || ''}
                  onChange={field.onChange}
                  placeholder={placeholder}
                  minHeight="150px"
                  className="rounded-md"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        {/* Preview Dialog */}
        <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Email Preview</DialogTitle>
            </DialogHeader>
            
            <div className="mt-4">
              <div className="p-4 bg-gray-100 rounded-md">
                <p className="text-center text-gray-500">Email preview will be available when connected to the email service.</p>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setPreviewOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
