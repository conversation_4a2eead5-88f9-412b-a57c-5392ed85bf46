import { Control } from 'react-hook-form';
import { Mail, MessageCircle } from 'lucide-react';
import {
  FormControl,
  FormField,
  FormItem,
} from '@/components/ui/form';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { cn } from '@/lib/utils';

interface DeliveryMethodToggleProps {
  control: Control<any>;
  name: string;
}

export function DeliveryMethodToggle({ control, name }: DeliveryMethodToggleProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-0">
          <FormControl>
            <div className="bg-gray-100 rounded-full p-1 w-fit">
              <ToggleGroup
                type="single"
                value={field.value}
                onValueChange={(value) => {
                  if (value) field.onChange(value);
                }}
                className="flex"
              >
                <ToggleGroupItem
                  value="email"
                  className={cn(
                    "flex-1 px-3 py-1 text-xs font-medium rounded-full transition-all",
                    field.value === "email" ? "bg-white shadow-sm text-gray-800" : "text-gray-600 hover:text-gray-800"
                  )}
                >
                  <Mail className="h-3.5 w-3.5 mr-1.5" />
                  Email
                </ToggleGroupItem>
                <ToggleGroupItem
                  value="message"
                  className={cn(
                    "flex-1 px-3 py-1 text-xs font-medium rounded-full transition-all",
                    field.value === "message" ? "bg-white shadow-sm text-gray-800" : "text-gray-600 hover:text-gray-800"
                  )}
                >
                  <MessageCircle className="h-3.5 w-3.5 mr-1.5" />
                  Message
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
