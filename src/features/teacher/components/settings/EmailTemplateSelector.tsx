import { Control } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface EmailTemplate {
  id: string;
  name: string;
  content: string;
}

interface EmailTemplateSelectorProps {
  control: Control<any>;
  name: string;
  templateFieldName: string;
  templates: EmailTemplate[];
  onTemplateSelect: (content: string) => void;
}

export function EmailTemplateSelector({
  control,
  name,
  templateFieldName,
  templates,
  onTemplateSelect,
}: EmailTemplateSelectorProps) {
  return (
    <div className="mt-4 mb-2">
      <FormField
        control={control}
        name={templateFieldName}
        render={({ field }) => (
          <FormItem className="space-y-1">
            <FormLabel className="text-sm text-gray-500">
              Choose a template:
            </FormLabel>
            <Select
              onValueChange={(value) => {
                field.onChange(value);
                const selectedTemplate = templates.find(t => t.id === value);
                if (selectedTemplate) {
                  onTemplateSelect(selectedTemplate.content);
                }
              }}
              value={field.value || ''}
            >
              <FormControl>
                <SelectTrigger className="w-full h-8 text-xs rounded-md">
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="">Custom message</SelectItem>
                {templates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        )}
      />
    </div>
  );
}
