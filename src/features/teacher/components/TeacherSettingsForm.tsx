import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { UserWithProfile } from '@shared/schema';
import { useCurrency } from '@/hooks/useCurrency';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CurrencySettings } from '@/features/settings/components/CurrencySettings';

// Import modular components
import { MessageSection, CancellationSection } from './settings';
import { ScheduledEmailsDashboard } from './ScheduledEmailsDashboard';

// Teacher settings schema
const teacherSettingsSchema = z.object({
  defaultZoomLink: z.string().optional().or(z.literal('')),

  // Welcome Message
  defaultWelcomeMessage: z.string().optional(),
  defaultWelcomeMessageTiming: z.enum(['on_booking', 'custom_time', 'manual']).optional(),
  defaultWelcomeMessageCustomTime: z.string().optional(),
  defaultWelcomeMessageCustomDate: z.date().optional().nullable(),
  defaultWelcomeMessageDelivery: z.enum(['email', 'message']).optional(),
  defaultWelcomeMessageTemplate: z.string().optional(),

  // Reminder Message
  defaultReminderMessage: z.string().optional(),
  defaultReminderMessageTiming: z
    .enum([
      '24_hours_before',
      '12_hours_before',
      '6_hours_before',
      '1_hour_before',
      'custom_time',
      'manual',
    ])
    .optional(),
  defaultReminderMessageCustomTime: z.string().optional(),
  defaultReminderMessageCustomDate: z.date().optional().nullable(),
  defaultReminderMessageDelivery: z.enum(['email', 'message']).optional(),
  defaultReminderMessageTemplate: z.string().optional(),

  // Follow-up Message
  defaultFollowUpMessage: z.string().optional(),
  defaultFollowUpMessageTiming: z
    .enum([
      'immediately_after',
      '1_hour_after',
      '6_hours_after',
      '24_hours_after',
      'custom_time',
      'manual',
    ])
    .optional(),
  defaultFollowUpMessageCustomTime: z.string().optional(),
  defaultFollowUpMessageCustomDate: z.date().optional().nullable(),
  defaultFollowUpMessageDelivery: z.enum(['email', 'message']).optional(),
  defaultFollowUpMessageTemplate: z.string().optional(),

  // Cancellation Policy
  defaultCancellationPolicy: z.string().optional(),
  defaultCancellationTimeframe: z
    .enum(['12_hours', '24_hours', '48_hours', '72_hours', '4_days', '1_week', '2_weeks'])
    .optional(),
  defaultCancellationFeePercentage: z.coerce.number().min(0).max(100).optional(),
  defaultNoShowFeePercentage: z.coerce.number().min(0).max(100).optional(),
});

type TeacherSettingsFormValues = z.infer<typeof teacherSettingsSchema>;

interface TeacherSettingsFormProps {
  userWithProfile: any;
  isOpen: boolean;
  onClose: () => void;
}

// Email templates
const welcomeEmailTemplates = [
  {
    id: 'welcome_template_1',
    name: 'Professional Welcome',
    content: `<p>Dear Student,</p>
<p>Thank you for booking a session with me. I'm looking forward to our time together on the scheduled date.</p>
<p>Please make sure to prepare any questions or topics you'd like to cover during our session. If you need to share any materials with me beforehand, feel free to message me directly.</p>
<p>Best regards,<br/>[Your Name]</p>`
  },
  {
    id: 'welcome_template_2',
    name: 'Friendly Welcome',
    content: `<p>Hi there!</p>
<p>I'm excited that you've booked a session with me! I'm looking forward to our time together and helping you achieve your goals.</p>
<p>Feel free to message me if you have any questions before we meet. I'm here to help make this a great experience for you.</p>
<p>See you soon!<br/>[Your Name]</p>`
  },
  {
    id: 'welcome_template_3',
    name: 'Detailed Welcome',
    content: `<p>Hello and welcome!</p>
<p>Thank you for booking a session with me. I'm thrilled to have the opportunity to work with you.</p>
<p>Here's what you can expect:</p>
<ul>
  <li>We'll meet via the Zoom link provided in your booking confirmation</li>
  <li>The session will last for the full scheduled time</li>
  <li>We'll focus on the topics you've indicated in your booking</li>
  <li>You'll have the opportunity to ask questions throughout</li>
</ul>
<p>Please come prepared with any specific questions or areas you'd like to focus on. If you need to share any materials with me beforehand, please do so at least 24 hours in advance.</p>
<p>I'm looking forward to our session!<br/>[Your Name]</p>`
  }
];

const reminderEmailTemplates = [
  {
    id: 'reminder_template_1',
    name: 'Simple Reminder',
    content: `<p>Hello,</p>
<p>This is a friendly reminder about our upcoming session. I'm looking forward to meeting with you!</p>
<p>Best regards,<br/>[Your Name]</p>`
  },
  {
    id: 'reminder_template_2',
    name: 'Detailed Reminder',
    content: `<p>Hello,</p>
<p>I wanted to send a quick reminder about our upcoming session. Here are the details:</p>
<ul>
  <li>Date: [Session Date]</li>
  <li>Time: [Session Time]</li>
  <li>Location: Zoom (link in your booking confirmation)</li>
</ul>
<p>Please make sure you have a stable internet connection and a quiet environment for our session. If you need to reschedule, please let me know at least 24 hours in advance.</p>
<p>Looking forward to our time together!</p>
<p>Best regards,<br/>[Your Name]</p>`
  },
  {
    id: 'reminder_template_3',
    name: 'Preparation Reminder',
    content: `<p>Hello,</p>
<p>Our session is coming up soon, and I wanted to remind you to prepare any questions or topics you'd like to cover. This will help us make the most of our time together.</p>
<p>If you have any materials you'd like me to review before our session, please send them as soon as possible.</p>
<p>I'm looking forward to our session!</p>
<p>Best regards,<br/>[Your Name]</p>`
  }
];

const followUpEmailTemplates = [
  {
    id: 'followup_template_1',
    name: 'Simple Thank You',
    content: `<p>Hello,</p>
<p>Thank you for attending our session today. I hope you found it valuable and informative.</p>
<p>If you have any questions or need further clarification on anything we discussed, please don't hesitate to reach out.</p>
<p>Best regards,<br/>[Your Name]</p>`
  },
  {
    id: 'followup_template_2',
    name: 'Feedback Request',
    content: `<p>Hello,</p>
<p>Thank you for attending our session today. I hope it met your expectations and provided value.</p>
<p>I would greatly appreciate your feedback on our session. What worked well? What could be improved? Your insights will help me enhance the experience for future sessions.</p>
<p>If you have any questions or need further assistance, please feel free to reach out.</p>
<p>Best regards,<br/>[Your Name]</p>`
  },
  {
    id: 'followup_template_3',
    name: 'Next Steps',
    content: `<p>Hello,</p>
<p>Thank you for our productive session today. I enjoyed our discussion and hope you found it valuable.</p>
<p>As we discussed, here are the next steps:</p>
<ul>
  <li>Review the materials we covered</li>
  <li>Practice the techniques we discussed</li>
  <li>Consider scheduling a follow-up session to build on what we've learned</li>
</ul>
<p>If you have any questions or need clarification on anything we discussed, please don't hesitate to reach out.</p>
<p>Best regards,<br/>[Your Name]</p>`
  }
];

export function TeacherSettingsForm({
  userWithProfile,
  isOpen,
  onClose,
}: TeacherSettingsFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const { currency, updateCurrency } = useCurrency();

  // Teacher settings form
  const form = useForm<TeacherSettingsFormValues>({
    resolver: zodResolver(teacherSettingsSchema),
    defaultValues: {
      defaultZoomLink: (userWithProfile.profile as any)?.defaultZoomLink || '',

      // Welcome Message
      defaultWelcomeMessage:
        (userWithProfile.profile as any)?.defaultWelcomeMessage ||
        "Thank you for booking my session! I'm looking forward to our time together. Feel free to message me if you have any questions before we meet.",
      defaultWelcomeMessageTiming:
        (userWithProfile.profile as any)?.defaultWelcomeMessageTiming || 'on_booking',
      defaultWelcomeMessageCustomTime:
        (userWithProfile.profile as any)?.defaultWelcomeMessageCustomTime || '',
      defaultWelcomeMessageCustomDate:
        (userWithProfile.profile as any)?.defaultWelcomeMessageCustomDate || null,
      defaultWelcomeMessageDelivery:
        (userWithProfile.profile as any)?.defaultWelcomeMessageDelivery || 'email',
      defaultWelcomeMessageTemplate:
        (userWithProfile.profile as any)?.defaultWelcomeMessageTemplate || '',

      // Reminder Message
      defaultReminderMessage:
        (userWithProfile.profile as any)?.defaultReminderMessage ||
        'Just a friendly reminder about our upcoming session. Looking forward to seeing you!',
      defaultReminderMessageTiming:
        (userWithProfile.profile as any)?.defaultReminderMessageTiming || '24_hours_before',
      defaultReminderMessageCustomTime:
        (userWithProfile.profile as any)?.defaultReminderMessageCustomTime || '',
      defaultReminderMessageCustomDate:
        (userWithProfile.profile as any)?.defaultReminderMessageCustomDate || null,
      defaultReminderMessageDelivery:
        (userWithProfile.profile as any)?.defaultReminderMessageDelivery || 'email',
      defaultReminderMessageTemplate:
        (userWithProfile.profile as any)?.defaultReminderMessageTemplate || '',

      // Follow-up Message
      defaultFollowUpMessage:
        (userWithProfile.profile as any)?.defaultFollowUpMessage ||
        'Thank you for attending the session today! I hope you found it valuable. Feel free to reach out if you have any questions.',
      defaultFollowUpMessageTiming:
        (userWithProfile.profile as any)?.defaultFollowUpMessageTiming || 'immediately_after',
      defaultFollowUpMessageCustomTime:
        (userWithProfile.profile as any)?.defaultFollowUpMessageCustomTime || '',
      defaultFollowUpMessageCustomDate:
        (userWithProfile.profile as any)?.defaultFollowUpMessageCustomDate || null,
      defaultFollowUpMessageDelivery:
        (userWithProfile.profile as any)?.defaultFollowUpMessageDelivery || 'email',
      defaultFollowUpMessageTemplate:
        (userWithProfile.profile as any)?.defaultFollowUpMessageTemplate || '',

      // Cancellation Policy
      defaultCancellationPolicy:
        (userWithProfile.profile as any)?.defaultCancellationPolicy ||
        'Cancellations must be made at least 24 hours in advance to receive a full refund.',
      defaultCancellationTimeframe:
        (userWithProfile.profile as any)?.defaultCancellationTimeframe || '24_hours',
      defaultCancellationFeePercentage:
        (userWithProfile.profile as any)?.defaultCancellationFeePercentage || 50,
      defaultNoShowFeePercentage: (userWithProfile.profile as any)?.defaultNoShowFeePercentage || 100,
    },
  });

  const onSubmit = async (data: TeacherSettingsFormValues) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/users/${userWithProfile.id}/teacher-settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update teacher settings');
      }

      // Invalidate user profile query to refresh data
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userWithProfile.id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userWithProfile.id}/profile`] });

      toast({
        title: 'Settings updated',
        description: 'Your teacher settings have been updated successfully.',
      });

      onClose();
    } catch (error) {
      console.error('Error updating teacher settings:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle className="text-2xl font-semibold">Teacher Settings</DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground mt-1">
                Configure your teaching preferences and session defaults
              </DialogDescription>
            </div>
            <ScheduledEmailsDashboard />
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 overflow-hidden">
          <TabsList className="grid w-full grid-cols-4 mb-8 bg-sage-50/60 rounded-2xl p-1.5 border border-sage-200/25">
            <TabsTrigger
              value="general"
              className="text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600 data-[state=active]:border-0"
            >
              General
            </TabsTrigger>
            <TabsTrigger
              value="currency"
              className="text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600 data-[state=active]:border-0"
            >
              Currency
            </TabsTrigger>
            <TabsTrigger
              value="messages"
              className="text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600 data-[state=active]:border-0"
            >
              Messages
            </TabsTrigger>
            <TabsTrigger
              value="platforms"
              className="text-sm font-medium rounded-xl transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600 data-[state=active]:border-0"
            >
              Platforms
            </TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto max-h-[60vh] pr-2">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

                {/* General Settings Tab */}
                <TabsContent value="general" className="mt-0 space-y-6">
                  <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-lg text-sage-700 font-medium">Basic Settings</CardTitle>
                      <CardDescription className="text-sage-500">
                        Configure your basic teaching preferences
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Zoom Link */}
                      <FormField
                        control={form.control}
                        name="defaultZoomLink"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-base font-medium text-sage-600">Default Zoom Link</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="https://zoom.us/j/123456789"
                                className="rounded-xl border-sage-200/40 bg-white/80 focus:border-accent/50 focus:ring-accent/20"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Cancellation Policy */}
                      <div className="pt-4">
                        <CancellationSection control={form.control} />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Currency Settings Tab */}
                <TabsContent value="currency" className="mt-0 space-y-6">
                  <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-lg text-sage-700 font-medium">Currency Preferences</CardTitle>
                      <CardDescription className="text-sage-500">
                        Set your preferred currency for session pricing. This will be used as the default for all your sessions.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <CurrencySettings
                        currentCurrency={currency}
                        onCurrencyUpdate={updateCurrency}
                        isLoading={false}
                      />
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Messages Tab */}
                <TabsContent value="messages" className="mt-0 space-y-6">
                  <div className="space-y-6">
                    {/* Welcome Message */}
                    <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-lg text-sage-700 font-medium">Welcome Message</CardTitle>
                        <CardDescription className="text-sage-500">
                          Automatically sent when students book your sessions
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <MessageSection
                          control={form.control}
                          watch={form.watch}
                          setValue={form.setValue}
                          title=""
                          messageFieldName="defaultWelcomeMessage"
                          deliveryFieldName="defaultWelcomeMessageDelivery"
                          timingFieldName="defaultWelcomeMessageTiming"
                          dateFieldName="defaultWelcomeMessageCustomDate"
                          timeFieldName="defaultWelcomeMessageCustomTime"
                          templateFieldName="defaultWelcomeMessageTemplate"
                          timingOptions={[
                            { value: "on_booking", label: "Send on booking" }
                          ]}
                          placeholder="Enter your welcome message"
                          isWelcomeMessage={true}
                          emailTemplates={welcomeEmailTemplates}
                        />
                      </CardContent>
                    </Card>

                    {/* Reminder Message */}
                    <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-lg text-sage-700 font-medium">Reminder Message</CardTitle>
                        <CardDescription className="text-sage-500">
                          Automatically sent before sessions start
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <MessageSection
                          control={form.control}
                          watch={form.watch}
                          setValue={form.setValue}
                          title=""
                          messageFieldName="defaultReminderMessage"
                          deliveryFieldName="defaultReminderMessageDelivery"
                          timingFieldName="defaultReminderMessageTiming"
                          dateFieldName="defaultReminderMessageCustomDate"
                          timeFieldName="defaultReminderMessageCustomTime"
                          templateFieldName="defaultReminderMessageTemplate"
                          timingOptions={[
                            { value: "24_hours_before", label: "24 hours before" },
                            { value: "12_hours_before", label: "12 hours before" },
                            { value: "6_hours_before", label: "6 hours before" },
                            { value: "1_hour_before", label: "1 hour before" },
                            { value: "custom_time", label: "Custom time" },
                            { value: "manual", label: "Send manually" }
                          ]}
                          placeholder="Enter your reminder message"
                          emailTemplates={reminderEmailTemplates}
                        />
                      </CardContent>
                    </Card>

                    {/* Follow-up Message */}
                    <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-lg text-sage-700 font-medium">Follow-up Message</CardTitle>
                        <CardDescription className="text-sage-500">
                          Automatically sent after sessions end
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <MessageSection
                          control={form.control}
                          watch={form.watch}
                          setValue={form.setValue}
                          title=""
                          messageFieldName="defaultFollowUpMessage"
                          deliveryFieldName="defaultFollowUpMessageDelivery"
                          timingFieldName="defaultFollowUpMessageTiming"
                          dateFieldName="defaultFollowUpMessageCustomDate"
                          timeFieldName="defaultFollowUpMessageCustomTime"
                          templateFieldName="defaultFollowUpMessageTemplate"
                          timingOptions={[
                            { value: "immediately_after", label: "Immediately after" },
                            { value: "1_hour_after", label: "1 hour after" },
                            { value: "6_hours_after", label: "6 hours after" },
                            { value: "24_hours_after", label: "24 hours after" },
                            { value: "custom_time", label: "Custom time" },
                            { value: "manual", label: "Send manually" }
                          ]}
                          placeholder="Enter your follow-up message"
                          emailTemplates={followUpEmailTemplates}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Video Platforms Tab */}
                <TabsContent value="platforms" className="mt-0 space-y-6">
                  <Card className="bg-sage-50/25 border-sage-200/20 shadow-sm shadow-sage-200/10 rounded-3xl">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-lg text-sage-700 font-medium">Video Platform Integration</CardTitle>
                      <CardDescription className="text-sage-500">
                        Connect your video hosting accounts for seamless session management
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-12 text-sage-400">
                        <div className="mb-4 text-2xl">🚀</div>
                        <h3 className="text-lg font-medium mb-2 text-sage-600">Coming Soon</h3>
                        <p className="text-sage-500">Video platform integration will be available in a future update.</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <DialogFooter className="pt-6 border-t border-sage-200/30">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className="rounded-xl border-sage-200/40 text-sage-600 hover:bg-sage-50/50 hover:text-sage-700"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="rounded-xl bg-accent/85 hover:bg-accent text-white shadow-sm shadow-accent/20 hover:shadow-md hover:shadow-accent/30 transition-all duration-300 border-0"
                  >
                    {isSubmitting ? 'Saving...' : 'Save Settings'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
