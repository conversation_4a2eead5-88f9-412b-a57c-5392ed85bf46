import { useState } from 'react';
import { useFilter } from '@/contexts/FilterContext';
import { INSTRUCTOR_SORT_OPTIONS } from '@/lib/constants';
import { SortOption } from '@/lib/types';
import { UserWithProfile } from '@shared/schema';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TeacherCard } from '@/features/teacher/components/TeacherCard';
import { FilterSelect } from '@/components/ui/mobile-filter-select'; //Updated import

interface TeacherListProps {
  title?: string;
  limit?: number;
  showControls?: boolean;
}

/**
 * TeacherList component - displays a list of teachers/teachers
 * Using the unified TeacherCard component
 */
export function TeacherList({ title, limit, showControls = false }: TeacherListProps) {
  const [displayCount, setDisplayCount] = useState<number>(7);
  const { sortedTeachers, sortOption, setSortOption } = useFilter();
  const isMobile = useIsMobile();

  // Apply limit if provided, otherwise use display count
  const displayedTeachers = limit
    ? sortedTeachers.slice(0, limit)
    : sortedTeachers.slice(0, displayCount);

  const loadMore = () => {
    setDisplayCount(prev => prev + 7);
  };

  const handleSortChange = (value: string) => {
    setSortOption(value as SortOption);
  };

  return (
    <div className="space-y-4">
      {title && (
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
          <h2 className="text-lg md:text-xl font-medium">{title}</h2>

          {showControls && (
            <div className="flex items-center gap-2">
              {/* Sort dropdown - use mobile version on small screens */}
              <FilterSelect //Using the updated component for all cases.  The original code's conditional rendering is removed.
                value={sortOption}
                onChange={handleSortChange}
                options={INSTRUCTOR_SORT_OPTIONS.map(option => ({ label: option, value: option }))}
                label="Sort by"
              />
            </div>
          )}
        </div>
      )}

      <div className="space-y-2">
        {displayedTeachers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No teachers found.</p>
          </div>
        ) : (
          displayedTeachers.map((teacher: UserWithProfile) => (
            <TeacherCard
              key={teacher.id}
              teacher={teacher}
              variant={isMobile ? 'list' : 'list'}
              className="px-0 py-3"
            />
          ))
        )}
      </div>

      {/* Load More button - only show when not using limit and there are more to load */}
      {!limit && sortedTeachers.length > displayCount && (
        <div className="mt-8 flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={loadMore}
            className="bg-[#84A59D]/5 hover:bg-[#84A59D]/10 text-[#84A59D] border-[#84A59D]/20"
          >
            Load More Teachers
          </Button>
        </div>
      )}
    </div>
  );
}
