import React from 'react';
import { Link } from '../../../components/ui/NextLink';
// import { UserWithProfile } from '@shared/schema';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Star, Users } from 'lucide-react';
import { getInitials } from '@/lib/utils';

interface TeacherCardProps {
  teacher: any;
  className?: string;
  variant?: 'grid' | 'list';
}

/**
 * TeacherCard component - displays teacher information in a card format
 */
export function TeacherCard({ teacher, className = '', variant = 'grid' }: TeacherCardProps) {
  const isListVariant = variant === 'list';

  return (
    <Link href={`/teacher/${teacher.id}`}>
      <Card className={`cursor-pointer hover:shadow-md transition-shadow ${className}`}>
        <CardContent className={`p-4 ${isListVariant ? 'flex items-center space-x-4' : 'space-y-3'}`}>
          {/* Avatar */}
          <Avatar className={isListVariant ? 'h-12 w-12' : 'h-16 w-16 mx-auto'}>
            <AvatarImage src={teacher.avatar || ''} alt={teacher.name} />
            <AvatarFallback>{getInitials(teacher.name)}</AvatarFallback>
          </Avatar>

          <div className={`${isListVariant ? 'flex-1' : 'text-center'}`}>
            {/* Name */}
            <h3 className={`font-semibold ${isListVariant ? 'text-base' : 'text-lg'}`}>
              {teacher.name}
            </h3>

            {/* Bio */}
            {teacher.bio && (
              <p className={`text-gray-600 text-sm ${isListVariant ? 'line-clamp-1' : 'line-clamp-2'} mt-1`}>
                {teacher.bio}
              </p>
            )}

            {/* Stats */}
            <div className={`flex items-center gap-4 mt-2 ${isListVariant ? '' : 'justify-center'}`}>
              {/* Rating */}
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">4.8</span>
              </div>

              {/* Sessions count */}
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">12 sessions</span>
              </div>
            </div>

            {/* Teacher badge */}
            {teacher.isTeacher && (
              <Badge variant="secondary" className="mt-2">
                Teacher
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

// Memoized version for performance
export const MemoizedTeacherCard = React.memo(TeacherCard);
