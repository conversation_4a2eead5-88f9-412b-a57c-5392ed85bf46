import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Mail, MessageCircle, AlertCircle, CheckCircle2 } from 'lucide-react';

// Mock data for scheduled emails
interface ScheduledEmail {
  id: string;
  sessionId: string;
  sessionTitle: string;
  studentName: string;
  type: 'welcome' | 'reminder' | 'follow_up';
  deliveryMethod: 'email' | 'message';
  scheduledFor: string;
  status: 'scheduled' | 'sent' | 'failed';
  subject?: string;
}

const mockScheduledEmails: ScheduledEmail[] = [
  {
    id: '1',
    sessionId: 'session-1',
    sessionTitle: 'Introduction to React',
    studentName: '<PERSON>',
    type: 'welcome',
    deliveryMethod: 'email',
    scheduledFor: '2023-06-15T10:00:00Z',
    status: 'sent',
    subject: 'Welcome to Introduction to React',
  },
  {
    id: '2',
    sessionId: 'session-1',
    sessionTitle: 'Introduction to React',
    studentName: 'John Doe',
    type: 'reminder',
    deliveryMethod: 'email',
    scheduledFor: '2023-06-14T10:00:00Z',
    status: 'scheduled',
    subject: 'Reminder: Your session is tomorrow',
  },
  {
    id: '3',
    sessionId: 'session-2',
    sessionTitle: 'Advanced JavaScript',
    studentName: 'Jane Smith',
    type: 'welcome',
    deliveryMethod: 'message',
    scheduledFor: '2023-06-20T14:00:00Z',
    status: 'scheduled',
  },
  {
    id: '4',
    sessionId: 'session-3',
    sessionTitle: 'CSS Fundamentals',
    studentName: 'Bob Johnson',
    type: 'follow_up',
    deliveryMethod: 'email',
    scheduledFor: '2023-06-10T16:30:00Z',
    status: 'failed',
    subject: 'Thank you for attending CSS Fundamentals',
  },
];

export function ScheduledEmailsDashboard() {
  const [emails, setEmails] = useState<ScheduledEmail[]>(mockScheduledEmails);
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState('all');
  const [search, setSearch] = useState('');

  // Filter emails based on selected filter and search term
  const filteredEmails = emails.filter((email) => {
    // Filter by type
    if (filter !== 'all' && email.type !== filter) {
      return false;
    }

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      return (
        email.sessionTitle.toLowerCase().includes(searchLower) ||
        email.studentName.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'welcome':
        return 'Welcome';
      case 'reminder':
        return 'Reminder';
      case 'follow_up':
        return 'Follow-up';
      default:
        return type;
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Scheduled</Badge>;
      case 'sent':
        return <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800"><CheckCircle2 className="h-3 w-3" /> Sent</Badge>;
      case 'failed':
        return <Badge variant="destructive" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Failed</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Get delivery method icon
  const getDeliveryIcon = (method: string) => {
    switch (method) {
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'message':
        return <MessageCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">View Scheduled Emails</Button>
      </DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Scheduled Emails</DialogTitle>
          <DialogDescription>
            View and manage all your scheduled automated emails.
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="welcome">Welcome</SelectItem>
                <SelectItem value="reminder">Reminder</SelectItem>
                <SelectItem value="follow_up">Follow-up</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Input
              placeholder="Search by session or student"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-[300px]"
            />
          </div>
        </div>

        <Table>
          <TableCaption>A list of your scheduled automated emails.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Session</TableHead>
              <TableHead>Student</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Scheduled For</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredEmails.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No scheduled emails found.
                </TableCell>
              </TableRow>
            ) : (
              filteredEmails.map((email) => (
                <TableRow key={email.id}>
                  <TableCell className="font-medium">{email.sessionTitle}</TableCell>
                  <TableCell>{email.studentName}</TableCell>
                  <TableCell>{getTypeLabel(email.type)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {getDeliveryIcon(email.deliveryMethod)}
                      <span className="capitalize">{email.deliveryMethod}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {formatDate(email.scheduledFor)}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(email.status)}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // In a real implementation, this would open a preview dialog
                        console.log('Preview email:', email);
                      }}
                    >
                      Preview
                    </Button>
                    {email.status === 'scheduled' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // In a real implementation, this would cancel the scheduled email
                          console.log('Cancel email:', email);
                        }}
                      >
                        Cancel
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
