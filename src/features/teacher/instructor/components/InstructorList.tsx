import { useState } from 'react';
import { useFilter } from '@/contexts/FilterContext';
import { INSTRUCTOR_SORT_OPTIONS } from '@/lib/constants';
import { SortOption } from '@/lib/types';
// import { UserWithProfile } from '@shared/schema';
import { Link } from '../../../../components/ui/NextLink';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { StarRating } from '@/components/ui/star-rating';
import { getInitials } from '@/lib/utils';
import { CheckCircle2, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface TeacherListProps {
  title?: string;
  limit?: number;
  showControls?: boolean;
}

export function TeacherList({ title, limit, showControls = false }: TeacherListProps) {
  const [displayCount, setDisplayCount] = useState<number>(7);
  const { sortedTeachers, sortOption, setSortOption } = useFilter();

  // Apply limit if provided, otherwise use display count
  const displayedTeachers = limit
    ? sortedTeachers.slice(0, limit)
    : sortedTeachers.slice(0, displayCount);

  const loadMore = () => {
    setDisplayCount(prev => prev + 7);
  };

  return (
    <div className="space-y-4">
      {title && (
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
          <h2 className="text-lg md:text-xl font-medium">{title}</h2>

          {showControls && (
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center text-xs md:text-sm gap-1 h-8 px-2 md:px-3"
                  >
                    <span className="truncate max-w-[100px] md:max-w-full">Sort: {sortOption}</span>
                    <ChevronDown className="h-3 w-3 md:h-4 md:w-4 ml-1 shrink-0" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-44 md:w-48">
                  <DropdownMenuRadioGroup
                    value={sortOption}
                    onValueChange={value => setSortOption(value as SortOption)}
                  >
                    {INSTRUCTOR_SORT_OPTIONS.map(option => (
                      <DropdownMenuRadioItem
                        key={option}
                        value={option}
                        className="text-xs md:text-sm"
                      >
                        {option}
                      </DropdownMenuRadioItem>
                    ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      )}

      <div className="space-y-4">
        {displayedTeachers.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No teachers found.</p>
          </div>
        ) : (
          displayedTeachers.map((teacher: any) => (
            <Link
              key={teacher.id}
              href={`/teachers/${teacher.id}`}
              className="block cursor-pointer"
            >
              <div className="group flex gap-2 md:gap-4 py-4 px-1 border-b border-gray-200 hover:bg-gray-50/50 transition-all">
                <div className="shrink-0">
                  <Avatar className="h-12 w-12 sm:h-14 sm:w-14 md:h-16 md:w-16 rounded-full shadow-sm">
                    <AvatarImage
                      src={teacher.avatar || ''}
                      alt={teacher.name}
                      className="object-cover"
                    />
                    <AvatarFallback className="text-xs md:text-sm">
                      {getInitials(teacher.name)}
                    </AvatarFallback>
                  </Avatar>
                </div>

                <div className="flex flex-col flex-1 min-w-0">
                  <div className="flex items-center gap-1 md:gap-2">
                    <span className="text-sm sm:text-base md:text-lg font-medium line-clamp-1">
                      {teacher.name}
                    </span>
                    {teacher.isTeacher && (
                      <CheckCircle2 className="h-3 w-3 md:h-4 md:w-4 text-primary/80" />
                    )}
                  </div>

                  <div className="flex items-center gap-1 md:gap-2 text-xs md:text-sm text-gray-600 mt-0.5 md:mt-1">
                    <StarRating rating={teacher.profile?.rating || 0} size="sm" />
                    <span>
                      {(teacher.profile?.rating || 0).toFixed(1)}
                      <span className="hidden sm:inline">
                        ({teacher.profile?.reviewCount || 0} reviews)
                      </span>
                    </span>
                  </div>

                  <p className="text-xs md:text-sm text-gray-600 mt-1 line-clamp-2 hidden sm:block">
                    {teacher.profile?.bio || 'No bio available'}
                  </p>

                  {/* Display specializations if they exist */}
                  <div className="flex gap-1 flex-wrap mt-1 md:mt-2">
                    {teacher.profile?.specializations?.slice(0, 2).map((spec: any, index: number) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2"
                      >
                        {spec}
                      </Badge>
                    ))}
                    {teacher.profile?.specializations &&
                      teacher.profile.specializations.length > 2 && (
                        <Badge
                          variant="outline"
                          className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2"
                        >
                          +{teacher.profile.specializations.length - 2}
                        </Badge>
                      )}
                    {teacher.profile?.location && (
                      <Badge
                        variant="outline"
                        className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2 hidden md:inline-flex"
                      >
                        {teacher.profile.location}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))
        )}
      </div>

      {/* Load More button - only show when not using limit and there are more to load */}
      {!limit && sortedTeachers.length > displayCount && (
        <div className="mt-8 flex justify-center">
          <Button variant="outline" size="sm" onClick={loadMore} className="hover:bg-[#E07A5F]/10">
            Load More Teachers
          </Button>
        </div>
      )}
    </div>
  );
}
