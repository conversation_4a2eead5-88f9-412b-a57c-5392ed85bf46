// import { UserWithProfile } from '@shared/schema';
import { Link } from '../../../../components/ui/NextLink';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { StarRating } from '@/components/ui/star-rating';
import { getInitials, getAvatarUrl } from '@/lib/utils';
import { CheckCircle2 } from 'lucide-react';

interface TeacherListItemProps {
  teacher: any;
}

export function TeacherListItem({ teacher }: TeacherListItemProps) {
  return (
    <Link href={`/teachers/${teacher.id}`} className="block cursor-pointer">
      <div className="group flex gap-2 md:gap-4 py-4 px-1 border-b border-gray-200 hover:bg-gray-50/50 transition-all">
        <div className="shrink-0">
          <Avatar className="h-12 w-12 sm:h-14 sm:w-14 md:h-16 md:w-16 rounded-full shadow-sm">
            <AvatarImage
              src={teacher.avatar ? getAvatarUrl(teacher.avatar) : ''}
              alt={teacher.name}
              className="object-cover"
              onError={(e) => {
                console.log(`Avatar error for teacher: ${teacher.name}`);
                const target = e.target as HTMLImageElement;
                target.onerror = null; // Prevent infinite loop
                target.src = 'https://placehold.co/100x100?text=Profile';
              }}
            />
            <AvatarFallback className="text-xs md:text-sm">
              {getInitials(teacher.name)}
            </AvatarFallback>
          </Avatar>
        </div>

        <div className="flex flex-col flex-1 min-w-0">
          <div className="flex items-center gap-1 md:gap-2">
            <span className="text-sm sm:text-base md:text-lg font-medium line-clamp-1">
              {teacher.name}
            </span>
            {teacher.isTeacher && (
              <CheckCircle2 className="h-3 w-3 md:h-4 md:w-4 text-primary/80" />
            )}
          </div>

          <div className="flex items-center gap-1 md:gap-2 text-xs md:text-sm text-gray-600 mt-0.5 md:mt-1">
            <StarRating rating={teacher.profile?.rating || 0} size="sm" />
            <span>
              {(teacher.profile?.rating || 0).toFixed(1)}
              <span className="hidden sm:inline">
                ({teacher.profile?.reviewCount || 0} reviews)
              </span>
            </span>
          </div>

          <p className="text-xs md:text-sm text-gray-600 mt-1 line-clamp-2 hidden sm:block">
            {teacher.profile?.bio || 'No bio available'}
          </p>

          {/* Display specializations if they exist */}
          <div className="flex gap-1 flex-wrap mt-1 md:mt-2">
            {teacher.profile?.specializations?.slice(0, 2).map((spec: any, index: number) => (
              <Badge
                key={index}
                variant="outline"
                className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2"
              >
                {spec}
              </Badge>
            ))}
            {teacher.profile?.specializations &&
              teacher.profile.specializations.length > 2 && (
                <Badge
                  variant="outline"
                  className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2"
                >
                  +{teacher.profile.specializations.length - 2}
                </Badge>
              )}
            {teacher.profile?.location && (
              <Badge
                variant="outline"
                className="text-[0.65rem] md:text-xs bg-gray-50 py-0 px-1.5 md:px-2 hidden md:inline-flex"
              >
                {teacher.profile.location}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
