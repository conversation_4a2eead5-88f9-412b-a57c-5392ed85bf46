import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, Plus, Trash, Save, Clock, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/features/auth';

// Type definitions
interface GlobalAvailability {
  id?: string;
  teacher_id: string;
  day_of_week: string;
  start_time: string;
  end_time: string;
  created_at?: string;
  updated_at?: string;
}

interface GlobalAvailabilityManagerProps {
  sessionId?: string; // Optional session ID to apply global availability to
  className?: string;
  onAvailabilityApplied?: () => void;
}

const timeOptions = (() => {
  // Generate time options in 30-minute intervals (12am to 11:30pm)
  const options = [];
  const date = new Date();
  date.setHours(0, 0, 0, 0);

  for (let i = 0; i < 48; i++) {
    options.push({
      value: format(date, 'HH:mm'),
      label: format(date, 'h:mm a'),
    });
    date.setMinutes(date.getMinutes() + 30);
  }

  return options;
})();

export function GlobalAvailabilityManager({
  sessionId,
  className,
  onAvailabilityApplied,
}: GlobalAvailabilityManagerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [globalAvailability, setGlobalAvailability] = useState<GlobalAvailability[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [applying, setApplying] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // New availability form state
  const [newAvailability, setNewAvailability] = useState<Omit<GlobalAvailability, 'teacher_id'>>(
    {
      day_of_week: 'monday',
      start_time: '09:00',
      end_time: '17:00',
    }
  );

  // Load global availability for the teacher
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        if (!user?.id) return;

        // Fetch teacher's global availability
        const response = await fetch(`/api/global-availability/${user.id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch global availability');
        }

        const data = await response.json();
        setGlobalAvailability(data || []);
      } catch (error) {
        console.error('Error fetching global availability data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load global availability data. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.id, toast]);

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setNewAvailability(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Create new global availability
  const handleCreateAvailability = async () => {
    try {
      setSaving(true);

      // Validate time range
      const startParts = newAvailability.start_time.split(':').map(Number);
      const endParts = newAvailability.end_time.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(startParts[0], startParts[1], 0);
      const endDate = new Date();
      endDate.setHours(endParts[0], endParts[1], 0);

      if (startDate >= endDate) {
        toast({
          title: 'Invalid Time Range',
          description: 'Start time must be before end time',
          variant: 'destructive',
        });
        return;
      }

      // Create the global availability
      const response = await fetch('/api/global-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dayOfWeek: newAvailability.day_of_week,
          startTime: newAvailability.start_time,
          endTime: newAvailability.end_time,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create global availability');
      }

      const newAvailabilityData = await response.json();

      // Update the availabilities list
      setGlobalAvailability(prev => [...prev, newAvailabilityData]);

      // Reset form and close dialog
      setNewAvailability({
        day_of_week: 'monday',
        start_time: '09:00',
        end_time: '17:00',
      });

      setIsDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Global availability created successfully',
      });
    } catch (error) {
      console.error('Error creating global availability:', error);
      toast({
        title: 'Error',
        description: 'Failed to create global availability. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete a global availability
  const handleDeleteAvailability = async (id: string) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/global-availability/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete global availability');
      }

      // Remove from the list
      setGlobalAvailability(prev => prev.filter(item => item.id !== id));

      toast({
        title: 'Success',
        description: 'Global availability deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting global availability:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete global availability. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Apply global availability to a session
  const handleApplyToSession = async () => {
    if (!sessionId) return;

    try {
      setApplying(true);

      const response = await fetch(`/api/global-availability/apply/${sessionId}`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to apply global availability to session');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: `Applied ${result.availabilities.length} availability slots to session.`,
      });

      // Call the callback if provided
      if (onAvailabilityApplied) {
        onAvailabilityApplied();
      }
    } catch (error) {
      console.error('Error applying global availability to session:', error);
      toast({
        title: 'Error',
        description: 'Failed to apply global availability to session. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setApplying(false);
    }
  };

  // Format day of week for display
  const formatDayOfWeek = (day: string) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  // Format time for display
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return format(date, 'h:mm a');
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex justify-end items-center">
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Global Time Slot
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Global Availability</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="day_of_week">Day of Week</Label>
                <Select
                  value={newAvailability.day_of_week}
                  onValueChange={value => handleInputChange('day_of_week', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monday">Monday</SelectItem>
                    <SelectItem value="tuesday">Tuesday</SelectItem>
                    <SelectItem value="wednesday">Wednesday</SelectItem>
                    <SelectItem value="thursday">Thursday</SelectItem>
                    <SelectItem value="friday">Friday</SelectItem>
                    <SelectItem value="saturday">Saturday</SelectItem>
                    <SelectItem value="sunday">Sunday</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="start_time">Start Time</Label>
                <Select
                  value={newAvailability.start_time}
                  onValueChange={value => handleInputChange('start_time', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Start time" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_time">End Time</Label>
                <Select
                  value={newAvailability.end_time}
                  onValueChange={value => handleInputChange('end_time', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="End time" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateAvailability} disabled={saving}>
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : globalAvailability.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Clock className="mx-auto h-12 w-12 opacity-20 mb-2" />
          <p className="text-base font-medium">No global availability set yet</p>
          <p className="text-sm mt-1">
            Add your regular working hours to quickly apply them to your sessions.
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {globalAvailability.map(item => (
            <Card key={item.id} className="overflow-hidden">
              <CardHeader className="bg-muted pb-2">
                <CardTitle className="text-base">{formatDayOfWeek(item.day_of_week)}</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatTime(item.start_time)} - {formatTime(item.end_time)}
                  </span>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end border-t bg-muted/50 p-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => item.id && handleDeleteAvailability(item.id)}
                  disabled={loading}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {sessionId && globalAvailability.length > 0 && (
        <div className="mt-6 flex justify-center">
          <Button variant="default" onClick={handleApplyToSession} disabled={applying}>
            {applying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Applying...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Apply to This Session
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
