import React, { useState, useEffect } from 'react';
import { useSession } from '@/hooks/use-auth-compat';
import axios from 'axios';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';

export function StripeConnectSetup() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accountStatus, setAccountStatus] = useState<{
    hasAccount: boolean;
    onboardingComplete: boolean;
    accountId: string | null;
    accountDetails?: any;
  } | null>(null);

  useEffect(() => {
    if (session) {
      fetchAccountStatus();
    }
  }, [session]);

  const fetchAccountStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/stripe/connect/accounts/status');
      setAccountStatus(response.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch account status');
      console.error('Error fetching account status:', err);
    } finally {
      setLoading(false);
    }
  };

  const createConnectAccount = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/stripe/connect/accounts');
      setAccountStatus({
        hasAccount: true,
        onboardingComplete: false,
        accountId: response.data.accountId,
      });

      // Redirect to onboarding immediately
      redirectToOnboarding();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create Connect account');
      console.error('Error creating Connect account:', err);
      setLoading(false);
    }
  };

  const redirectToOnboarding = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/stripe/connect/account-links', {
        refreshUrl: window.location.href,
        returnUrl: window.location.href,
      });

      // Redirect to Stripe's hosted onboarding
      window.location.href = response.data.url;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create onboarding link');
      console.error('Error creating onboarding link:', err);
      setLoading(false);
    }
  };

  const redirectToDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/stripe/connect/dashboard-links');

      // Open Stripe dashboard in a new tab
      window.open(response.data.url, '_blank');
      setLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create dashboard link');
      console.error('Error creating dashboard link:', err);
      setLoading(false);
    }
  };

  if (loading && !accountStatus) {
    return (
      <div className="flex justify-center items-center my-5">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <h5 className="text-xl font-semibold">Payment Setup</h5>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {accountStatus?.hasAccount ? (
          <>
            {accountStatus.onboardingComplete ? (
              <>
                <Alert className="mb-4 bg-green-50 border-green-200">
                  <AlertTitle className="text-green-800">
                    Your payment account is set up!
                  </AlertTitle>
                  <AlertDescription className="text-green-700">
                    Your Stripe Connect account is fully set up and ready to receive payments. You
                    can manage your account, view payouts, and update your banking information
                    through the Stripe dashboard.
                  </AlertDescription>
                </Alert>

                <div className="grid gap-2">
                  <Button
                    onClick={redirectToDashboard}
                    disabled={loading}
                    size="lg"
                    className="mb-2"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading Dashboard...
                      </>
                    ) : (
                      '💰 View Earnings & Payouts'
                    )}
                  </Button>
                  <Button variant="outline" onClick={redirectToDashboard} disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading Dashboard...
                      </>
                    ) : (
                      '🏦 Update Banking Information'
                    )}
                  </Button>
                </div>
              </>
            ) : (
              <>
                <Alert className="mb-4 bg-amber-50 border-amber-200">
                  <AlertTitle className="text-amber-800">Complete your payment setup</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    <p>
                      You've started the payment setup process, but you need to complete the
                      onboarding process to receive payments for your sessions.
                    </p>
                    {accountStatus.accountDetails?.requirements?.currently_due?.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">Required information:</p>
                        <ul className="list-disc pl-5 mt-1">
                          {accountStatus.accountDetails.requirements.currently_due.map(
                            (item: string) => (
                              <li key={item}>{item.replace(/_/g, ' ')}</li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>

                <Button onClick={redirectToOnboarding} disabled={loading} className="w-full">
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Complete Payment Setup'
                  )}
                </Button>
              </>
            )}
          </>
        ) : (
          <>
            <p>
              To receive payments for your sessions, you need to set up a payment account. We use
              Stripe Connect to securely handle payments and transfers.
            </p>
            <p>Setting up your payment account will allow you to:</p>
            <ul>
              <li>Receive payments directly to your bank account</li>
              <li>Track your earnings and payouts</li>
              <li>Manage your tax information</li>
            </ul>

            <Button onClick={createConnectAccount} disabled={loading} className="w-full">
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                'Set Up Payment Account'
              )}
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
}
