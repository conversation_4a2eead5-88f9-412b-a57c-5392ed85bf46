import { useFilter } from '@/contexts/FilterContext';
import { User } from '@shared/schema';
import { TeacherCard } from '../../components/TeacherCard';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEffect, useState, useCallback } from 'react';
import { ArrowRight } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface TeacherGridProps {
  title?: string;
  subtitle?: string;
  limit?: number;
  showLoadMore?: boolean;
  featured?: boolean;
  isLoading?: boolean;
}

/**
 * TeacherGrid - Displays a responsive grid of teachers with animations and loading states
 */
export function TeacherGrid({
  title = 'Featured Teachers',
  subtitle,
  limit,
  showLoadMore = false,
  featured = false,
  isLoading = false,
}: TeacherGridProps) {
  const { sortedTeachers } = useFilter();
  const [visibleTeachers, setVisibleTeachers] = useState<number[]>([]);
  const [displayCount, setDisplayCount] = useState<number>(7);
  const isMobile = useIsMobile();

  // Apply limit if provided, otherwise use displayCount
  const teachers = limit
    ? sortedTeachers.slice(0, limit)
    : sortedTeachers.slice(0, displayCount);

  // Handler for loading more teachers
  const loadMore = useCallback(() => {
    setDisplayCount(prev => prev + 7);
  }, []);

  // Reset and animate cards when teachers change
  useEffect(() => {
    setVisibleTeachers([]);

    if (teachers.length > 0 && !isLoading) {
      // Stagger the appearance of teacher cards
      const animationDelay = isMobile ? 25 : 50; // Faster on mobile

      teachers.forEach((_, index) => {
        setTimeout(() => {
          setVisibleTeachers(prevVisible => [...prevVisible, index]);
        }, animationDelay * index);
      });
    }
  }, [teachers, isLoading, isMobile]);

  // Generate column classes based on screen size
  const gridColumns = isMobile ? 'grid-cols-1' : 'grid-cols-2 lg:grid-cols-3 xl:grid-cols-4';

  return (
    <div className={`${featured ? 'py-6 md:py-8' : 'py-3 md:py-4'}`}>
      {/* Section title and subtitle */}
      {(title || subtitle) && (
        <div className="mb-4 md:mb-8">
          {title && (
            <h2
              className={`${featured ? 'text-2xl md:text-3xl' : 'text-xl md:text-2xl'} font-bold text-[#333333] mb-1 md:mb-2`}
            >
              {title}
            </h2>
          )}
          {subtitle && <p className="text-sm md:text-base text-[#666666] max-w-3xl">{subtitle}</p>}
        </div>
      )}

      {/* Loading skeletons */}
      {isLoading ? (
        <div className={`grid gap-3 sm:gap-5 ${gridColumns}`}>
          {Array.from({ length: limit || (isMobile ? 4 : 8) }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full animate-pulse-subtle"
            >
              {/* Image area */}
              <div className="aspect-[16/9] relative bg-gray-100">
                <Skeleton className="absolute top-2 right-2 h-6 w-6 rounded-full" />
                <div className="absolute -bottom-4 md:-bottom-6 left-3 md:left-4">
                  <Skeleton className="h-10 w-10 md:h-16 md:w-16 rounded-full" />
                </div>
              </div>
              {/* Content area */}
              <div className="p-3 pt-5 md:p-4 md:pt-7">
                <div className="pl-10 md:pl-16 mb-2 md:mb-3">
                  <Skeleton className="h-4 md:h-5 w-3/4 mb-1 md:mb-2 rounded-md" />
                  <Skeleton className="h-2.5 md:h-3 w-1/2 rounded-md" />
                </div>
                <div className="flex gap-1 md:gap-2 mt-2">
                  <Skeleton className="h-2.5 md:h-3 w-16 rounded-full" />
                  <Skeleton className="h-2.5 md:h-3 w-12 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {/* Teacher grid with animated cards */}
          <div className={`grid gap-3 sm:gap-4 md:gap-5 ${gridColumns}`}>
            {teachers.length > 0 ? (
              teachers.map((teacher, index) => (
                <div
                  key={teacher.id}
                  className={`staggered-fade-in ${visibleTeachers.includes(index) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
                  style={{
                    transitionDelay: `${index * (isMobile ? 25 : 50)}ms`,
                    transitionProperty: 'opacity, transform',
                    transitionDuration: '500ms',
                    transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                >
                  <TeacherCard teacher={teacher} variant="grid" />
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-12 md:py-16 bg-[#F0EEE9]/40 rounded-xl">
                <h3 className="text-lg md:text-xl font-semibold text-[#666666]">
                  No teachers found
                </h3>
                <p className="text-sm md:text-base text-[#808080] mt-2 max-w-md mx-auto">
                  Try adjusting your filters or search criteria to discover more teachers.
                </p>
              </div>
            )}
          </div>

          {/* Load more button */}
          {!limit && sortedTeachers.length > displayCount && (
            <div className="mt-6 md:mt-8 flex justify-center">
              <Button
                variant="outline"
                onClick={loadMore}
                size={isMobile ? 'sm' : 'default'}
                className="bg-[#84A59D]/5 hover:bg-[#84A59D]/10 text-[#84A59D] border-[#84A59D]/20 group"
              >
                <span>Load More Teachers</span>
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
