import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { format, parse, addMinutes } from 'date-fns';
import { CalendarIcon, Loader2, Plus, Trash, Save, Clock, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/features/auth';
import { GlobalAvailabilityManager } from './GlobalAvailabilityManager';

// Type definitions
interface Availability {
  id?: string;
  teacher_id: string;
  session_id?: string;
  day_of_week?: string;
  start_time: string;
  end_time: string;
  recurring: boolean;
  specific_date?: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

interface TimeSlot {
  id: string;
  availability_id: string;
  start_time: string;
  end_time: string;
  is_booked: boolean;
  booking_id?: string;
}

interface AvailabilityManagerProps {
  sessionId?: string; // Optional: if provided, will show availability for this specific session
  className?: string;
}

const timeOptions = (() => {
  // Generate time options in 30-minute intervals (12am to 11:30pm)
  const options = [];
  const date = new Date();
  date.setHours(0, 0, 0, 0);

  for (let i = 0; i < 48; i++) {
    options.push({
      value: format(date, 'HH:mm'),
      label: format(date, 'h:mm a'),
    });
    date.setMinutes(date.getMinutes() + 30);
  }

  return options;
})();

export function AvailabilityManager({ sessionId, className }: AvailabilityManagerProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [availabilities, setAvailabilities] = useState<Availability[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('recurring');
  const [activeManagerTab, setActiveManagerTab] = useState('custom');

  // New availability form state
  const [newAvailability, setNewAvailability] = useState<
    Omit<Availability, 'teacher_id' | 'status'>
  >({
    session_id: sessionId,
    day_of_week: 'monday',
    start_time: '09:00',
    end_time: '17:00',
    recurring: true,
    specific_date: format(new Date(), 'yyyy-MM-dd'),
  });

  // Load availabilities and time slots
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        if (!user?.id) return;

        // Fetch teacher's availabilities
        const response = await fetch(
          `/api/teacher/${user.id}/availability${sessionId ? `?sessionId=${sessionId}` : ''}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch availabilities');
        }

        const data = await response.json();
        setAvailabilities(data.availabilities || []);

        // Fetch time slots if availabilities exist
        if (data.availabilities?.length > 0) {
          // Get time slots for the first availability as an example
          const firstAvailabilityId = data.availabilities[0].id;
          const timeSlotsResponse = await fetch(
            `/api/time-slots?availabilityId=${firstAvailabilityId}`
          );

          if (timeSlotsResponse.ok) {
            const timeSlotsData = await timeSlotsResponse.json();
            setTimeSlots(timeSlotsData);
          }
        }
      } catch (error) {
        console.error('Error fetching availability data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load availability data. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.id, sessionId, toast]);

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setNewAvailability(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Toggle between recurring and specific date
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    handleInputChange('recurring', value === 'recurring');
  };

  // Create new availability
  const handleCreateAvailability = async () => {
    try {
      setSaving(true);

      // Validate time range
      const startTime = parse(newAvailability.start_time, 'HH:mm', new Date());
      const endTime = parse(newAvailability.end_time, 'HH:mm', new Date());

      if (startTime >= endTime) {
        toast({
          title: 'Invalid Time Range',
          description: 'Start time must be before end time',
          variant: 'destructive',
        });
        return;
      }

      // Create the availability
      const availabilityData: Availability = {
        ...newAvailability,
        teacher_id: user!.id as string,
        status: 'active', // Auto-approve for now
      };

      const response = await fetch('/api/teacher-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(availabilityData),
      });

      if (!response.ok) {
        throw new Error('Failed to create availability');
      }

      const newAvailabilityData = await response.json();

      // Update the availabilities list
      setAvailabilities(prev => [...prev, newAvailabilityData]);

      // Reset form and close dialog
      setNewAvailability({
        session_id: sessionId,
        day_of_week: 'monday',
        start_time: '09:00',
        end_time: '17:00',
        recurring: true,
        specific_date: format(new Date(), 'yyyy-MM-dd'),
      });

      setIsDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Availability created successfully',
      });
    } catch (error) {
      console.error('Error creating availability:', error);
      toast({
        title: 'Error',
        description: 'Failed to create availability. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete an availability
  const handleDeleteAvailability = async (id: string) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/teacher-availability/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete availability');
      }

      // Remove from the list
      setAvailabilities(prev => prev.filter(item => item.id !== id));

      toast({
        title: 'Success',
        description: 'Availability deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting availability:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete availability. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Display day of week in a more readable format
  const formatDayOfWeek = (day: string) => {
    return day.charAt(0).toUpperCase() + day.slice(1);
  };

  // Format time for display
  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(':');
      const date = new Date();
      date.setHours(parseInt(hours, 10), parseInt(minutes, 10));
      return format(date, 'h:mm a');
    } catch (e) {
      return time;
    }
  };

  // Format date for display
  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), 'MMMM d, yyyy');
    } catch (e) {
      return dateStr;
    }
  };

  // Refresh availability after applying global settings
  const handleGlobalAvailabilityApplied = async () => {
    try {
      setLoading(true);

      // Fetch teacher's availabilities
      const response = await fetch(
        `/api/teacher/${user?.id}/availability${sessionId ? `?sessionId=${sessionId}` : ''}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch availabilities');
      }

      const data = await response.json();
      setAvailabilities(data.availabilities || []);

      toast({
        title: 'Success',
        description: 'Global availability applied successfully',
      });
    } catch (error) {
      console.error('Error refreshing availability data:', error);
      toast({
        title: 'Error',
        description: 'Failed to refresh availability data after applying global settings.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Availability Settings</h3>
          <p className="text-sm text-muted-foreground">
            {sessionId
              ? 'Set when this session is available for booking'
              : 'Set your general availability schedule'}
          </p>
        </div>

        <Button onClick={() => setIsDialogOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Availability
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <Tabs value={activeManagerTab} onValueChange={setActiveManagerTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="custom">Custom Availability</TabsTrigger>
            <TabsTrigger value="global">Apply Global Patterns</TabsTrigger>
          </TabsList>

          <TabsContent value="custom" className="space-y-4">
            {availabilities.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/30">
                <Clock className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No Availability Set</h3>
                <p className="mt-2 text-sm text-muted-foreground max-w-md mx-auto">
                  You haven't set any availability times yet. Add your available times so students
                  can book sessions with you.
                </p>
                <Button className="mt-4" onClick={() => setIsDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Availability
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Recurring Availability
                  </h4>
                  <div className="space-y-2">
                    {availabilities
                      .filter(a => a.recurring)
                      .map(availability => (
                        <div
                          key={availability.id}
                          className="flex items-center justify-between p-3 border rounded-md hover:bg-accent/50 transition-colors"
                        >
                          <div>
                            <p className="font-medium">
                              {formatDayOfWeek(availability.day_of_week || '')}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {formatTime(availability.start_time)} -{' '}
                              {formatTime(availability.end_time)}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              availability.id && handleDeleteAvailability(availability.id)
                            }
                          >
                            <Trash className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-muted-foreground">Specific Dates</h4>
                  <div className="space-y-2">
                    {availabilities
                      .filter(a => !a.recurring)
                      .map(availability => (
                        <div
                          key={availability.id}
                          className="flex items-center justify-between p-3 border rounded-md hover:bg-accent/50 transition-colors"
                        >
                          <div>
                            <p className="font-medium">
                              {formatDate(availability.specific_date || '')}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {formatTime(availability.start_time)} -{' '}
                              {formatTime(availability.end_time)}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() =>
                              availability.id && handleDeleteAvailability(availability.id)
                            }
                          >
                            <Trash className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="global" className="space-y-4">
            {sessionId ? (
              <GlobalAvailabilityManager
                sessionId={sessionId}
                onAvailabilityApplied={handleGlobalAvailabilityApplied}
              />
            ) : (
              <div className="text-center py-8 border rounded-md bg-muted/30">
                <RefreshCw className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No Session Selected</h3>
                <p className="mt-2 text-sm text-muted-foreground max-w-md mx-auto">
                  Global availability patterns can only be applied to a specific session.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}

      {timeSlots.length > 0 && activeManagerTab === 'custom' && (
        <div className="mt-8 space-y-4">
          <h4 className="text-sm font-medium">Generated Time Slots</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
            {timeSlots.slice(0, 6).map(slot => (
              <div
                key={slot.id}
                className={cn(
                  'p-2 border rounded-md text-sm',
                  slot.is_booked ? 'bg-muted' : 'bg-white'
                )}
              >
                <p className="font-medium">{format(new Date(slot.start_time), 'MMM d, yyyy')}</p>
                <p className="text-muted-foreground">
                  {format(new Date(slot.start_time), 'h:mm a')} -
                  {format(new Date(slot.end_time), 'h:mm a')}
                </p>
                <p className={slot.is_booked ? 'text-destructive' : 'text-green-600'}>
                  {slot.is_booked ? 'Booked' : 'Available'}
                </p>
              </div>
            ))}
          </div>
          {timeSlots.length > 6 && (
            <p className="text-sm text-center text-muted-foreground">
              Showing 6 of {timeSlots.length} time slots
            </p>
          )}
        </div>
      )}

      {/* Add Availability Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Availability</DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid grid-cols-2 w-full">
              <TabsTrigger value="recurring">Recurring</TabsTrigger>
              <TabsTrigger value="specific">Specific Date</TabsTrigger>
            </TabsList>

            <TabsContent value="recurring" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="day-of-week">Day of Week</Label>
                <Select
                  value={newAvailability.day_of_week}
                  onValueChange={value => handleInputChange('day_of_week', value)}
                >
                  <SelectTrigger id="day-of-week">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monday">Monday</SelectItem>
                    <SelectItem value="tuesday">Tuesday</SelectItem>
                    <SelectItem value="wednesday">Wednesday</SelectItem>
                    <SelectItem value="thursday">Thursday</SelectItem>
                    <SelectItem value="friday">Friday</SelectItem>
                    <SelectItem value="saturday">Saturday</SelectItem>
                    <SelectItem value="sunday">Sunday</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="specific" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>Date</Label>
                <div className="border rounded-md p-1">
                  <Calendar
                    mode="single"
                    selected={new Date(newAvailability.specific_date || '')}
                    onSelect={date =>
                      handleInputChange('specific_date', format(date || new Date(), 'yyyy-MM-dd'))
                    }
                    disabled={date => date < new Date()}
                    className="mx-auto"
                  />
                </div>
              </div>
            </TabsContent>

            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-time">Start Time</Label>
                  <Select
                    value={newAvailability.start_time}
                    onValueChange={value => handleInputChange('start_time', value)}
                  >
                    <SelectTrigger id="start-time">
                      <SelectValue placeholder="Select time" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end-time">End Time</Label>
                  <Select
                    value={newAvailability.end_time}
                    onValueChange={value => handleInputChange('end_time', value)}
                  >
                    <SelectTrigger id="end-time">
                      <SelectValue placeholder="Select time" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </Tabs>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleCreateAvailability} disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
