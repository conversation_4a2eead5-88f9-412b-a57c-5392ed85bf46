import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { User } from '@supabase/supabase-js';
import { BookingWithDetails, Booking } from '../../../shared/schema';
import { apiRequest } from '@/lib/apiClient';
import { useToast } from '@/components/ui/use-toast';

interface BookingContextType {
  userBookings: BookingWithDetails[];
  isLoadingBookings: boolean;
  createBookingMutation: UseMutationResult<any, Error, Booking>;
  cancelBookingMutation: UseMutationResult<any, Error, number>;
  refreshBookings: () => Promise<void>;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export function BookingProvider({ children }: { children: ReactNode }) {
  const [userBookings, setUserBookings] = useState<BookingWithDetails[]>([]);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get user from query client or context
  const user = queryClient.getQueryData<User>(['user']);

  // Fetch user bookings when user changes
  useEffect(() => {
    if (!user) {
      setUserBookings([]);
      return;
    }

    const fetchUserBookings = async () => {
      try {
        setIsLoadingBookings(true);
        const { data } = await apiRequest(`/api/users/${user.id}/bookings`, {
          method: 'GET',
        });
        setUserBookings(data || []);
      } catch (error) {
        console.error('Error fetching user bookings:', error);
        setUserBookings([]);
      } finally {
        setIsLoadingBookings(false);
      }
    };

    fetchUserBookings();
  }, [user]);

  // Function to refresh bookings
  const refreshBookings = async () => {
    if (!user) {
      setUserBookings([]);
      return;
    }

    try {
      setIsLoadingBookings(true);
      const { data } = await apiRequest(`/api/users/${user.id}/bookings`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      });
      setUserBookings(data || []);
    } catch (error) {
      console.error('Error refreshing user bookings:', error);
    } finally {
      setIsLoadingBookings(false);
    }
  };

  // Create booking mutation
  const createBookingMutation = useMutation({
    mutationFn: async (booking: Booking) => {
      try {
        const { data } = await apiRequest('/api/bookings', {
          method: 'POST',
          body: JSON.stringify(booking),
        });
        return data;
      } catch (error) {
        console.error('Error creating booking:', error);
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred while creating your booking');
        }
      }
    },
    onSuccess: async (data: any) => {
      // Refresh bookings
      await refreshBookings();

      // Invalidate relevant queries
      if (user?.id) {
        queryClient.invalidateQueries({ queryKey: [`/api/users/${user.id}/bookings`] });
      }
      if (data && typeof data === 'object' && 'sessionId' in data) {
        queryClient.invalidateQueries({ queryKey: [`/api/sessions/${data.sessionId}`] });
      }

      toast({
        title: 'Booking successful',
        description: 'Your session has been booked successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Create booking error:', error);

      let customMessage = '';

      if (error.message.includes('Session is full')) {
        customMessage = 'This session is already full. Please try another session.';
      } else if (error.message.includes('already booked')) {
        customMessage = 'You have already booked this session.';
      } else if (error.message.includes('payment')) {
        customMessage = 'There was an issue processing your payment. Please try again.';
      } else {
        customMessage = 'Failed to create your booking. Please try again.';
      }

      toast({
        title: 'Booking failed',
        description: customMessage,
        variant: 'destructive',
      });
    },
  });

  // Cancel booking mutation
  const cancelBookingMutation = useMutation({
    mutationFn: async (bookingId: number) => {
      try {
        const { data } = await apiRequest(`/api/bookings/${bookingId}/cancel`, {
          method: 'POST',
        });
        return data;
      } catch (error) {
        console.error('Error cancelling booking:', error);
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred while cancelling your booking');
        }
      }
    },
    onSuccess: async () => {
      // Refresh bookings
      await refreshBookings();

      // Invalidate relevant queries
      if (user?.id) {
        queryClient.invalidateQueries({ queryKey: [`/api/users/${user.id}/bookings`] });
      }

      toast({
        title: 'Booking cancelled',
        description: 'Your booking has been cancelled successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Cancel booking error:', error);

      let customMessage = '';

      if (error.message.includes('cancellation period')) {
        customMessage = 'This booking is past the cancellation period and cannot be cancelled.';
      } else if (error.message.includes('permission') || error.message.includes('not authorized')) {
        customMessage = "You don't have permission to cancel this booking.";
      } else {
        customMessage = 'Failed to cancel your booking. Please try again.';
      }

      toast({
        title: 'Cancellation failed',
        description: customMessage,
        variant: 'destructive',
      });
    },
  });

  // Create the context value
  const contextValue: BookingContextType = {
    userBookings,
    isLoadingBookings,
    createBookingMutation,
    cancelBookingMutation,
    refreshBookings,
  };

  return <BookingContext.Provider value={contextValue}>{children}</BookingContext.Provider>;
}

// Custom hook to use the booking context
export function useBooking() {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
}