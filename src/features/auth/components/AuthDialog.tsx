import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/features/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, UserPlus, User, KeyRound, AtSign, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { Separator } from '@/components/ui/separator';
import { SocialLoginButtons } from './SocialLoginButtons';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  rememberMe: z.boolean().optional(),
});

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['Learner', 'Teacher']),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

interface AuthDialogProps {
  trigger?: React.ReactNode;
  defaultTab?: 'login' | 'register';
  onClose?: () => void;
}

export function AuthDialog({ trigger, defaultTab = 'login', onClose }: AuthDialogProps) {
  const { user, loginMutation, registerMutation } = useAuth();
  const [activeTab, setActiveTab] = useState<string>(defaultTab);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [formError, setFormError] = useState<string>('');
  const { toast } = useToast();
  const isMobile = useIsMobile();

  // Load saved credentials if any
  useEffect(() => {
    const savedEmail = localStorage.getItem('rememberedEmail');
    if (savedEmail) {
      loginForm.setValue('email', savedEmail);
      loginForm.setValue('rememberMe', true);
    }
  }, []);

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      name: '',
      role: 'Learner',
    },
  });

  const onLoginSubmit = (data: LoginFormValues) => {
    // Save email if remember me is checked
    if (data.rememberMe) {
      localStorage.setItem('rememberedEmail', data.email);
    } else {
      localStorage.removeItem('rememberedEmail');
    }

    loginMutation.mutate(
      { email: data.email, password: data.password },
      {
        onSuccess: () => {
          setIsOpen(false);
          if (onClose) onClose();
        },
        onError: error => {
          // Provide user-friendly error messages based on error type
          if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            setFormError("We're experiencing technical difficulties. Please try again later.");
          } else if (
            error.message.includes('Network Error') ||
            error.message.includes('Failed to fetch')
          ) {
            setFormError(
              'Unable to connect to the server. Please check your internet connection and try again.'
            );
          } else if (
            error.message.includes('401') ||
            error.message.includes('Invalid username or password')
          ) {
            setFormError('The username or password you entered is incorrect.');
          } else {
            setFormError(error.message || 'Sign-in failed.');
          }
        },
      }
    );
  };

  const onRegisterSubmit = (data: RegisterFormValues) => {
    registerMutation.mutate(data, {
      onSuccess: () => {
        setIsOpen(false);
        if (onClose) onClose();
      },
      onError: error => {
        // Provide user-friendly error messages based on error type
        if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          setFormError("We're experiencing technical difficulties. Please try again later.");
        } else if (
          error.message.includes('Network Error') ||
          error.message.includes('Failed to fetch')
        ) {
          setFormError(
            'Unable to connect to the server. Please check your internet connection and try again.'
          );
        } else if (error.message.includes('Username already taken')) {
          setFormError('This username is already taken. Please choose another.');
        } else if (error.message.includes('Email already in use')) {
          setFormError(
            'This email is already registered. Please use a different email or try logging in.'
          );
        } else {
          setFormError(error.message || 'Sign-up failed.');
        }
      },
    });
  };

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };

  // If user is authenticated, close the dialog
  if (user && isOpen) {
    setIsOpen(false);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent
        className={`
        ${isMobile ? 'sm:max-w-full max-h-[100dvh] rounded-none p-0' : 'sm:max-w-[450px] p-0'}
        overflow-hidden
      `}
      >
        {isMobile && (
          <div className="flex justify-between items-center px-4 py-3 border-b sticky top-0 bg-white z-10">
            <h2 className="text-lg font-medium text-gray-900">Session Hub</h2>
            <DialogClose className="p-2 rounded-full hover:bg-gray-100">
              <X className="h-5 w-5 text-gray-500" />
            </DialogClose>
          </div>
        )}

        <div
          className={`${isMobile ? 'p-4 pb-8' : 'p-6'} overflow-y-auto max-h-[calc(100dvh-4rem)]`}
        >
          {!isMobile && (
            <DialogHeader className="mb-4">
              <DialogTitle className="text-center font-display text-xl text-[#666666]">
                Welcome to Session Hub
              </DialogTitle>
              <DialogDescription className="text-center text-[#808080]">
                Connect with teachers and learners across the globe
              </DialogDescription>
            </DialogHeader>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6 bg-sage-50/60 rounded-2xl p-1.5 border border-sage-200/25">
              <TabsTrigger value="login" className="rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">Sign In</TabsTrigger>
              <TabsTrigger value="register" className="rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">Sign Up</TabsTrigger>
            </TabsList>

            <TabsContent value="login" className="space-y-4">
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                  <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#666666]">Email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <AtSign className="h-4 w-4" />
                            </div>
                            <Input
                              placeholder="Enter your email"
                              {...field}
                              className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                              inputMode="email"
                              type="email"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#666666]">Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <KeyRound className="h-4 w-4" />
                            </div>
                            <Input
                              type="password"
                              placeholder="••••••••"
                              {...field}
                              className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                              inputMode="text"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center justify-between">
                    <FormField
                      control={loginForm.control}
                      name="rememberMe"
                      render={({ field }) => (
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="remember-me"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className="border-[#D2B48C]/50 data-[state=checked]:bg-[#E07A5F] data-[state=checked]:border-[#E07A5F]"
                          />
                          <label
                            htmlFor="remember-me"
                            className="text-sm text-gray-600 cursor-pointer select-none"
                          >
                            Remember me
                          </label>
                        </div>
                      )}
                    />

                    <a
                      href="#"
                      className="text-sm text-primary hover:text-primary/80 transition-colors"
                      onClick={e => {
                        e.preventDefault();
                        toast({
                          title: 'Password Reset',
                          description: 'This feature is coming soon!',
                        });
                      }}
                    >
                      Forgot password?
                    </a>
                  </div>

                  <div className="grid gap-2">
                    {formError && (
                      <div className="text-destructive text-sm p-3 border border-destructive/30 rounded-md bg-destructive/5 flex items-start gap-2">
                        <div className="text-destructive mt-0.5">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="lucide lucide-alert-circle"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="12"></line>
                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                          </svg>
                        </div>
                        <div>
                          {formError}
                          {formError.includes('technical difficulties') && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Our team has been notified. Please try again later or contact support
                              if the issue persists.
                            </div>
                          )}
                          {formError.includes('Unable to connect') && (
                            <div className="text-xs text-muted-foreground mt-1">
                              This could be due to your internet connection or our servers may be
                              temporarily unavailable.
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    <Button
                      type="submit"
                      className="w-full rounded-lg bg-[#E07A5F] hover:bg-[#C15A40] mt-6"
                      disabled={loginMutation.isPending}
                    >
                      {loginMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <User className="h-4 w-4 mr-2" />
                      )}
                      Sign In
                    </Button>
                  </div>
                </form>
              </Form>

              <div className="relative my-6">
                <Separator className="absolute inset-0 h-[1px] m-auto bg-[#D2B48C]/20" />
                <div className="relative flex justify-center">
                  <span className="bg-white px-2 text-[#808080] text-xs">or continue with</span>
                </div>
              </div>

              <SocialLoginButtons onSuccess={handleClose} disabled={loginMutation.isPending} />
            </TabsContent>

            <TabsContent value="register" className="space-y-4">
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                  <FormField
                    control={registerForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#666666]">Full Name</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <User className="h-4 w-4" />
                            </div>
                            <Input
                              placeholder="Enter your full name"
                              {...field}
                              className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                              inputMode="text"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#666666]">Email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <AtSign className="h-4 w-4" />
                            </div>
                            <Input
                              type="email"
                              placeholder="Enter your email"
                              {...field}
                              className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                              inputMode="email"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-2 gap-4'}`}>
                    <FormField
                      control={registerForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#666666]">Username</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <User className="h-4 w-4" />
                              </div>
                              <Input
                                placeholder="Choose a username"
                                {...field}
                                className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                                inputMode="text"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={registerForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[#666666]">Password</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                <KeyRound className="h-4 w-4" />
                              </div>
                              <Input
                                type="password"
                                placeholder="••••••••"
                                {...field}
                                className="pl-10 rounded-lg border-[#D2B48C]/30 focus:border-[#E07A5F] text-base"
                                inputMode="text"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={registerForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#666666]">I want to join as</FormLabel>
                        <div className="flex gap-4 mt-1">
                          <Label
                            htmlFor="role-learner"
                            className={`flex-1 flex items-center gap-2 p-3 rounded-lg border cursor-pointer transition-all duration-200 ${field.value === 'Learner' ? 'border-[#E07A5F] bg-[#E07A5F]/5' : 'border-[#D2B48C]/30 hover:border-[#E07A5F]/30'}`}
                          >
                            <input
                              type="radio"
                              id="role-learner"
                              value="Learner"
                              className="sr-only"
                              checked={field.value === 'Learner'}
                              onChange={() => field.onChange('Learner')}
                            />
                            <User className="h-4 w-4 text-[#E07A5F]" />
                            <span className="text-sm text-[#666666]">Learner</span>
                          </Label>
                          <Label
                            htmlFor="role-teacher"
                            className={`flex-1 flex items-center gap-2 p-3 rounded-lg border cursor-pointer transition-all duration-200 ${field.value === 'Teacher' ? 'border-[#E07A5F] bg-[#E07A5F]/5' : 'border-[#D2B48C]/30 hover:border-[#E07A5F]/30'}`}
                          >
                            <input
                              type="radio"
                              id="role-teacher"
                              value="Teacher"
                              className="sr-only"
                              checked={field.value === 'Teacher'}
                              onChange={() => field.onChange('Teacher')}
                            />
                            <UserPlus className="h-4 w-4 text-[#E07A5F]" />
                            <span className="text-sm text-[#666666]">Teacher</span>
                          </Label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {formError && (
                    <div className="text-destructive text-sm p-3 border border-destructive/30 rounded-md bg-destructive/5 flex items-start gap-2">
                      <div className="text-destructive mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-alert-circle"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                      </div>
                      <div>
                        {formError}
                        {formError.includes('technical difficulties') && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Our team has been notified. Please try again later or contact support if
                            the issue persists.
                          </div>
                        )}
                        {formError.includes('Unable to connect') && (
                          <div className="text-xs text-muted-foreground mt-1">
                            This could be due to your internet connection or our servers may be
                            temporarily unavailable.
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <Button
                    type="submit"
                    className="w-full rounded-lg bg-[#E07A5F] hover:bg-[#C15A40] mt-6"
                    disabled={registerMutation.isPending}
                  >
                    {registerMutation.isPending ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <UserPlus className="h-4 w-4 mr-2" />
                    )}
                    Create Account
                  </Button>
                </form>
              </Form>

              {!isMobile && (
                <>
                  <div className="relative my-6">
                    <Separator className="absolute inset-0 h-[1px] m-auto bg-[#D2B48C]/20" />
                    <div className="relative flex justify-center">
                      <span className="bg-white px-2 text-[#808080] text-xs">or signup with</span>
                    </div>
                  </div>

                  <SocialLoginButtons
                    onSuccess={handleClose}
                    disabled={registerMutation.isPending}
                  />
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
