import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Facebook, Mail, Github } from 'lucide-react';
import { XLogo } from '@/features/icons/components/XLogo';
import { FaLinkedinIn, FaApple } from 'react-icons/fa';
import { FcGoogle } from 'react-icons/fc';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/AuthContext';

interface SocialLoginButtonsProps {
  onSuccess?: () => void;
  disabled?: boolean;
}

export function SocialLoginButtons({ onSuccess, disabled = false }: SocialLoginButtonsProps) {
  const { toast } = useToast();
  const { loginWithProvider } = useAuth();

  const handleSocialLogin = async (provider: string) => {
    try {
      // Store the current URL as the callback URL
      localStorage.setItem('authRedirectUrl', window.location.href);

      // Map provider names to Supabase provider names
      const providerMap: Record<string, 'google' | 'github' | 'apple'> = {
        google: 'google',
        github: 'github',
        apple: 'apple'
      };

      const supabaseProvider = providerMap[provider];

      if (supabaseProvider) {
        // Use Supabase OAuth
        await loginWithProvider(supabaseProvider);
        if (onSuccess) {
          onSuccess();
        }
        return;
      }

      // For unsupported providers
      toast({
        title: 'Not implemented',
        description: `Login with ${provider} is not yet implemented with Supabase.`,
        variant: 'destructive',
      });
    } catch (error) {
      console.error(`Error signing in with ${provider}:`, error);
      toast({
        title: 'Login failed',
        description: `Login with ${provider} failed. Please try again.`,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="grid grid-cols-5 gap-3">
      <Button
        variant="outline"
        className="rounded-lg border-[#D2B48C]/30 hover:bg-[#E07A5F]/10 hover:text-[#E07A5F] hover:border-[#E07A5F]/20"
        onClick={() => handleSocialLogin('google')}
        disabled={disabled}
      >
        <FcGoogle className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        className="rounded-lg border-[#D2B48C]/30 hover:bg-[#E07A5F]/10 hover:text-[#E07A5F] hover:border-[#E07A5F]/20"
        onClick={() => handleSocialLogin('facebook')}
        disabled={disabled}
      >
        <Facebook className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        className="rounded-lg border-[#D2B48C]/30 hover:bg-[#E07A5F]/10 hover:text-[#E07A5F] hover:border-[#E07A5F]/20"
        onClick={() => handleSocialLogin('twitter')}
        disabled={disabled}
      >
        <XLogo className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        className="rounded-lg border-[#D2B48C]/30 hover:bg-[#E07A5F]/10 hover:text-[#E07A5F] hover:border-[#E07A5F]/20"
        onClick={() => handleSocialLogin('linkedin')}
        disabled={disabled}
      >
        <FaLinkedinIn className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        className="rounded-lg border-[#D2B48C]/30 hover:bg-[#E07A5F]/10 hover:text-[#E07A5F] hover:border-[#E07A5F]/20"
        onClick={() => handleSocialLogin('apple')}
        disabled={disabled}
      >
        <FaApple className="h-4 w-4" />
      </Button>
    </div>
  );
}
