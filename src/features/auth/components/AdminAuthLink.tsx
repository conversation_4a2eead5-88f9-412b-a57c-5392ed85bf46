import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/features/auth';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, ShieldCheck } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';

const adminLoginSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type AdminLoginFormValues = z.infer<typeof adminLoginSchema>;

export function AdminAuthLink() {
  const { loginMutation } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const adminLoginForm = useForm<AdminLoginFormValues>({
    resolver: zodResolver(adminLoginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onAdminLoginSubmit = (data: AdminLoginFormValues) => {
    // For demo purposes, we'll use hardcoded admin credentials
    if (data.username === 'admin' && data.password === 'admin123') {
      setIsOpen(false);
      toast({
        title: 'Admin login successful',
        description: 'Welcome to the admin dashboard',
      });

      // Store admin session in localStorage
      localStorage.setItem(
        'admin_session',
        JSON.stringify({
          isAdmin: true,
          username: 'admin',
          name: 'Admin User',
          timestamp: Date.now(),
        })
      );

      // Navigate to the admin page
      window.location.href = '/admin';

      // Log for debugging
      console.log('Admin login successful, redirecting to /admin');
      return;
    }

    // If credentials don't match our hardcoded values
    toast({
      title: 'Invalid admin credentials',
      description: "Please use username 'admin' and password 'admin123'",
      variant: 'destructive',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <button className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors">
          Admin
        </button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="text-center text-[#666666] font-display">
            Admin Authentication
          </DialogTitle>
          <DialogDescription className="text-center text-[#808080]">
            Enter your admin credentials to access the administration panel.
          </DialogDescription>
        </DialogHeader>
        <Form {...adminLoginForm}>
          <form
            onSubmit={adminLoginForm.handleSubmit(onAdminLoginSubmit)}
            className="space-y-4 mt-4"
          >
            <FormField
              control={adminLoginForm.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#666666]">Admin Username</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter admin username"
                      {...field}
                      className="rounded-lg border-[#D2B48C]/30 focus:border-[#8FBC8F]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={adminLoginForm.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[#666666]">Admin Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="••••••••"
                      {...field}
                      className="rounded-lg border-[#D2B48C]/30 focus:border-[#8FBC8F]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full rounded-lg bg-[#8FBC8F] hover:bg-[#7CAB7C]"
              disabled={loginMutation?.isPending}
            >
              {loginMutation?.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <ShieldCheck className="h-4 w-4 mr-2" />
              )}
              Sign In as Admin
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
