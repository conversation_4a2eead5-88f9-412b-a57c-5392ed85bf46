import {
  createContext,
  ReactNode,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import { useQuery, useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { apiRequest } from '../../lib/queryClient';
import { useToast } from '../../hooks/use-toast';
import { AppError, ErrorCategory, ErrorSeverity, handleError } from '../../lib/error-handler';
import { supabase } from '../../lib/supabase-singleton';
import { Database } from '../../types/supabase';
import { SupabaseClient, User as SupabaseUser } from '@supabase/supabase-js';

// Add this to declare the global function for external updates
declare global {
  interface Window {
    __updateAuthContext?: (user: SupabaseUser) => void;
  }
}

interface AuthContextType {
  user: SupabaseUser | null;
  isLoading: boolean;
  isCheckingAuth: boolean;
  error: AppError | null;
  supabase: SupabaseClient<Database>;
  registerMutation: UseMutationResult<SupabaseUser, Error, InsertUser, unknown>;
  loginMutation: UseMutationResult<SupabaseUser, Error, LoginCredentials, unknown>;
  logoutMutation: UseMutationResult<any, Error, void>;
  refetchUser: () => Promise<void>;
  loginWithProvider: (provider: 'google' | 'github' | 'apple') => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | null>(null);

// Define LoginCredentials locally
interface LoginCredentials {
  email: string;
  password: string;
}

// Define InsertUser locally
interface InsertUser {
  email: string;
  password: string;
  name?: string;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true); // State for initial auth check
  const [error, setError] = useState<AppError | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const authCheckRef = useRef(false);

  // Expose a global function to update the auth context from outside
  useEffect(() => {
    // Define the global function to update auth context
    window.__updateAuthContext = (user: SupabaseUser) => {
      console.log('AuthContext updated externally with user:', user);
      setUser(user);
    };

    // Clean up
    return () => {
      delete window.__updateAuthContext;
    };
  }, []);

  // Listen for authentication state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('[SupabaseSingleton] Auth state changed:', event, session ? 'Session exists' : 'No session');
        // When auth state changes (e.g., after login/logout),
        // invalidate user queries to refetch and update UI
        queryClient.invalidateQueries({ queryKey: ['/api/user'] });

        if (session) {
          // Attempt to fetch the full user profile after authentication
          fetchUser();
        } else {
          // Clear user state if logged out
          setUser(null);
          setIsLoading(false);
          setIsCheckingAuth(false);
        }
      }
    );

    // Cleanup the listener
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [queryClient]); // Add queryClient as a dependency

  // Fetch the authenticated user and profile
  const fetchUser = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('[AuthContext] Fetching authenticated user');
      // Fetch the user from Supabase Auth
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.error('[AuthContext] Supabase auth.getUser() error:', authError);
        throw authError;
      }
      console.log('[AuthContext] Supabase auth user:', authUser);
      setUser(authUser);
      setIsLoading(false);
      setIsCheckingAuth(false);
    } catch (error) {
      console.error('[AuthContext] Error fetching user:', error);
      const appError = handleError(error, 'Error fetching user authentication status');
      setError(appError);
      setUser(null);
      setIsLoading(false);
      setIsCheckingAuth(false);
    }
  }, []);

  // Initial authentication check
  useEffect(() => {
    // Prevent duplicate checks on hot reload or multiple renders
    if (!authCheckRef.current) {
      authCheckRef.current = true;
      console.log('[AuthContext] Initial auth check');
      fetchUser();
    }
  }, [fetchUser]);

  // User registration mutation
  const registerMutation = useMutation<SupabaseUser, Error, InsertUser, unknown>({
    mutationFn: async (newUser: InsertUser) => {
      console.log('[AuthContext] Registering user', newUser);
      const { data, error } = await supabase.auth.signUp({
        email: newUser.email,
        password: newUser.password,
        options: { data: { name: newUser.name, ...newUser } }
      });
      if (error) throw new Error(error.message);
      return data.user as SupabaseUser;
    },
    onSuccess: (data) => {
      console.log('[AuthContext] Registration successful:', data);
      toast({
        title: 'Registration Successful',
        description: 'You can now log in.',
      });
    },
    onError: (error) => {
      console.error('[AuthContext] Registration failed:', error);
    },
  });

  // User login mutation
  const loginMutation = useMutation<SupabaseUser, Error, LoginCredentials, unknown>({
    mutationFn: async (credentials: LoginCredentials) => {
      console.log('[AuthContext] Logging in user', credentials.email);
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });
      if (error) throw new Error(error.message);
      return data.user as SupabaseUser;
    },
    onSuccess: (data) => {
      console.log('[AuthContext] Login successful:', data);
      toast({
        title: 'Login Successful',
        description: 'Welcome back!',
      });
    },
    onError: (error) => {
      console.error('[AuthContext] Login failed:', error);
    },
  });

  // User logout mutation
  const logoutMutation = useMutation<any, Error, void>({
    mutationFn: async () => {
      console.log('[AuthContext] Logging out user (Supabase only)');
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('[AuthContext] Supabase signOut error:', error);
        throw error;
      }
      setUser(null);
      queryClient.invalidateQueries({ queryKey: ['/api/user'] });
      console.log('[AuthContext] User logged out (Supabase)');
      return {};
    },
    onSuccess: () => {
      toast({
        title: 'Logged Out',
        description: 'You have been logged out.',
      });
    },
    onError: (error) => {
      console.error('[AuthContext] Logout failed:', error);
    },
  });

  // Login with OAuth provider (e.g., Google, GitHub)
  const loginWithProvider = useCallback(async (provider: 'google' | 'github' | 'apple') => {
    console.log('[AuthContext] Logging in with provider:', provider);
    // Use Supabase Auth directly for OAuth redirects
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) {
      console.error('[AuthContext] OAuth login error:', error);
      handleError(error, `OAuth login failed with ${provider}`);
      throw error;
    }

    console.log('[AuthContext] Redirecting for OAuth login:', data);
    // Redirect happens automatically after this
  }, [supabase]);

  // Expose context values
  const contextValue: AuthContextType = {
    user,
    isLoading,
    isCheckingAuth,
    error,
    supabase,
    registerMutation,
    loginMutation,
    logoutMutation,
    refetchUser: fetchUser,
    loginWithProvider,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use the Auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === null) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}