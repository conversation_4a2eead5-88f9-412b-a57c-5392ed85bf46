import {
  createContext,
  ReactNode,
  useContext,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import { useQuery, useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { apiRequest } from '../../lib/queryClient';
import { useToast } from '../../hooks/use-toast';
import { User, InsertUser } from '@shared/schema';
import { AppError, ErrorCategory, ErrorSeverity, handleError } from '../../lib/error-handler';

// Add this to declare the global function for external updates
declare global {
  interface Window {
    __updateAuthContext?: (user: User) => void;
  }
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isCheckingAuth: boolean;
  error: AppError | null;
  registerMutation: UseMutationResult<User, Error, InsertUser>;
  loginMutation: UseMutationResult<User, Error, LoginCredentials>;
  logoutMutation: UseMutationResult<any, Error, void>;
  refetchUser: () => Promise<User>;
  loginWithCognitoCode: (code: string) => Promise<User>;
}

// Define credential types
interface LoginCredentials {
  username: string;
  password: string;
  provider?: string; // For social login
}

// Auth check constants - used for localStorage keys
const AUTH_CACHE_KEY = 'cached_user';
const AUTH_RATE_LIMIT_KEY = 'auth_rate_limit_time';
const AUTH_CALLBACK_URL_KEY = 'auth_callback_url';
const COGNITO_TOKENS_KEY = 'cognito_tokens';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [user, setUser] = useState<User | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Expose a global function to update the auth context from outside
  useEffect(() => {
    // Define the global function to update auth context
    window.__updateAuthContext = (user: User) => {
      console.log('AuthContext updated externally with user:', user);
      setUser(user);
    };

    // Clean up
    return () => {
      delete window.__updateAuthContext;
    };
  }, []);

  // Prevent excessive auth checks
  const authCheckRef = useRef<boolean>(false);
  const authCheckAttempts = useRef<number>(0);
  const MAX_AUTH_ATTEMPTS = 3;

  // Check auth status on mount, but prevent excessive calls
  useEffect(() => {
    const checkAuth = async () => {
      // Don't check if we've already attempted several times
      if (authCheckAttempts.current >= MAX_AUTH_ATTEMPTS) {
        console.log('Max auth check attempts reached, not checking again');
        setIsCheckingAuth(false);
        return;
      }

      // Don't check if already authenticated
      if (user) {
        return;
      }

      // Prevent concurrent auth checks
      if (authCheckRef.current) {
        return;
      }

      try {
        authCheckRef.current = true;
        authCheckAttempts.current++;

        // Check if we've hit rate limits recently
        const lastRateLimitTime = localStorage.getItem(AUTH_RATE_LIMIT_KEY);
        if (lastRateLimitTime && Date.now() - parseInt(lastRateLimitTime, 10) < 10000) {
          // 10 seconds
          console.warn('Auth check rate limited, using cached user data');
          const cachedUser = localStorage.getItem(AUTH_CACHE_KEY);
          if (cachedUser) {
            try {
              const userData = JSON.parse(cachedUser);
              setUser(userData);
              setIsCheckingAuth(false);
              authCheckRef.current = false;
              return;
            } catch (e) {
              console.error('Error parsing cached user:', e);
            }
          }
        }

        // Check for Cognito tokens
        const cognitoTokensStr = localStorage.getItem(COGNITO_TOKENS_KEY);
        let headers: Record<string, string> = {
          'Cache-Control': 'max-age=60', // Allow caching for 60 seconds
          Pragma: 'cache',
        };

        // If we have Cognito tokens, add the Authorization header
        if (cognitoTokensStr) {
          try {
            const cognitoTokens = JSON.parse(cognitoTokensStr);

            // Check if tokens are expired
            if (cognitoTokens.expiresAt && Date.now() < cognitoTokens.expiresAt) {
              console.log('Using Cognito tokens for authentication');
              headers['Authorization'] = `Bearer ${cognitoTokens.accessToken}`;
            } else {
              console.log('Cognito tokens expired, attempting to refresh');

              // Try to refresh the tokens
              try {
                if (cognitoTokens.refreshToken) {
                  const response = await apiRequest('/api/auth/cognito/refresh', {
                    method: 'POST',
                    body: JSON.stringify({ refreshToken: cognitoTokens.refreshToken }),
                  });

                  if (response.data && response.data.tokens) {
                    console.log('Successfully refreshed tokens');
                    const { accessToken, idToken, refreshToken, expiresIn } = response.data.tokens;
                    const expiresAt = Date.now() + expiresIn * 1000;

                    // Update tokens in localStorage
                    const newTokens = {
                      accessToken,
                      idToken,
                      refreshToken,
                      expiresAt,
                    };

                    localStorage.setItem(COGNITO_TOKENS_KEY, JSON.stringify(newTokens));

                    // Use the new access token
                    headers['Authorization'] = `Bearer ${accessToken}`;
                    console.log('Using refreshed token for authentication');
                  } else {
                    console.log('Token refresh failed, removing from localStorage');
                    localStorage.removeItem(COGNITO_TOKENS_KEY);
                  }
                } else {
                  console.log('No refresh token available, removing from localStorage');
                  localStorage.removeItem(COGNITO_TOKENS_KEY);
                }
              } catch (refreshError) {
                console.error('Error refreshing tokens:', refreshError);
                localStorage.removeItem(COGNITO_TOKENS_KEY);
              }
            }
          } catch (e) {
            console.error('Error parsing Cognito tokens:', e);
            localStorage.removeItem(COGNITO_TOKENS_KEY);
          }
        }

        // Fetch current user with our apiRequest
        const { data } = await apiRequest('/api/user', {
          method: 'GET',
          headers,
        });

        // If we got here, the request was successful
        setUser(data);

        // Cache the user data for future use
        localStorage.setItem(AUTH_CACHE_KEY, JSON.stringify(data));

        // Reset attempts on success
        authCheckAttempts.current = 0;
      } catch (error) {
        // Check if this is a rate limit error
        if (error instanceof Error && error.message.includes('429')) {
          localStorage.setItem(AUTH_RATE_LIMIT_KEY, Date.now().toString());

          // Try to use cached user data
          const cachedUser = localStorage.getItem(AUTH_CACHE_KEY);
          if (cachedUser) {
            try {
              const userData = JSON.parse(cachedUser);
              setUser(userData);
              console.log('Using cached user data due to rate limiting');
            } catch (e) {
              console.error('Error parsing cached user:', e);
              setUser(null);
            }
          } else {
            setUser(null);
          }
        } else {
          // An error here likely means not authenticated (401)
          console.error('Error checking authentication:', error);
          setUser(null);
        }
      } finally {
        setIsCheckingAuth(false);
        authCheckRef.current = false;
      }
    };

    checkAuth();
  }, [user]);

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      console.log('Attempting login with credentials:', { ...credentials, password: '***' });
      try {
        // For social login, redirect to the appropriate endpoint
        if (credentials.provider) {
          // Store the current URL as the callback URL in session storage
          sessionStorage.setItem(AUTH_CALLBACK_URL_KEY, window.location.href);

          // For Google, use our Passport.js strategy
          if (credentials.provider === 'google') {
            // Redirect to the Google OAuth route
            window.location.href = `/api/auth/google?callbackUrl=${encodeURIComponent(window.location.href)}`;
            // Return a dummy user to prevent errors
            return {} as User;
          }

          // For other providers, throw an error
          throw new Error(`Login with ${credentials.provider} is not yet implemented`);
        } else {
          // For username/password login, use our API
          const { data } = await apiRequest('/api/login', {
            method: 'POST',
            body: JSON.stringify(credentials),
          });

          console.log('Login API response:', data);
          return data as User;
        }
      } catch (error) {
        console.error('Login API error:', error);
        // Rethrow to be caught by onError
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred during login');
        }
      }
    },
    onSuccess: (user: User) => {
      console.log('Login successful:', user);
      setUser(user);

      // Explicitly update the user in React Query cache for the auth system
      queryClient.setQueryData(['/api/user'], user);

      // Make sure other components know about the login
      if (window.__updateAuthContext) {
        window.__updateAuthContext(user);
      }

      toast({
        title: 'Login successful',
        description: `Welcome back, ${user.name || user.username}!`,
      });
    },
    onError: (error: Error) => {
      console.error('Login mutation error:', error);

      // Determine error category based on error message
      let category = ErrorCategory.UNKNOWN;
      let customMessage = '';

      // Check for email verification required error
      if (error.message.includes('verify your email')) {
        category = ErrorCategory.VALIDATION;
        customMessage = 'Please verify your email address before logging in.';
      } else if (
        error.message.includes('Network Error') ||
        error.message.includes('Failed to fetch') ||
        error.message.includes('The server is currently unavailable')
      ) {
        category = ErrorCategory.NETWORK;
      } else if (
        error.message.includes('401') ||
        error.message.includes('Invalid username or password')
      ) {
        category = ErrorCategory.AUTHENTICATION;
        customMessage = 'The username or password you entered is incorrect.';
      }

      // Use standardized error handling
      handleError(error, customMessage, undefined, {
        action: 'login',
        username: loginMutation.variables?.username,
      });
    },
  });

  // Register mutation
  const registerMutation = useMutation({
    // Add debug logging
    onMutate: variables => {
      console.log('Starting registration mutation with:', { ...variables, password: '***' });
    },
    mutationFn: async (credentials: InsertUser) => {
      try {
        // Add a default name based on username if not provided
        const registrationData = {
          ...credentials,
          name: credentials.name || credentials.username, // Use username as default name
        };

        console.log('Attempting registration with credentials:', {
          ...registrationData,
          password: '***',
        });

        // Use the apiRequest function
        const result = await apiRequest('/api/register', {
          method: 'POST',
          body: JSON.stringify(registrationData),
        });

        console.log('Registration API response:', result);
        return result.data;
      } catch (error) {
        console.error('[Registration] API error:', error);
        // Enhance error message based on type
        if (error instanceof Error) {
          if (error.message.includes('Network error')) {
            throw new Error(
              'Unable to connect to the server. Please check your internet connection and try again.'
            );
          } else if (error.message.includes('Username already taken')) {
            throw new Error('This username is already taken. Please choose another.');
          } else if (error.message.includes('Email already in use')) {
            throw new Error(
              'This email is already registered. Please use a different email or try logging in.'
            );
          }
        }
        throw error;
      }
    },
    onSuccess: (user: User) => {
      console.log('Registration successful:', { ...user, password: '***' });
      setUser(user);
      // Invalidate user-related queries
      queryClient.invalidateQueries({ queryKey: ['/api/user'] });

      toast({
        title: 'Registration successful',
        description: `Welcome to Session Hub, ${user.name || user.username}!`,
      });
    },
    onError: (error: Error) => {
      console.error('Registration mutation error:', error);

      // Determine error category based on error message
      let category = ErrorCategory.UNKNOWN;
      let customMessage = '';

      if (error.message.includes('Username already taken')) {
        category = ErrorCategory.VALIDATION;
        customMessage = 'This username is already taken. Please choose another.';
      } else if (error.message.includes('Email already in use')) {
        category = ErrorCategory.VALIDATION;
        customMessage =
          'This email is already registered. Please use a different email or try logging in.';
      } else if (
        error.message.includes('Network Error') ||
        error.message.includes('Failed to fetch')
      ) {
        category = ErrorCategory.NETWORK;
      }

      // Use standardized error handling
      handleError(error, customMessage, undefined, {
        action: 'register',
        email: registerMutation.variables?.email,
      });
    },
  });

  // Add a debounce flag to prevent multiple rapid logout requests
  const isLoggingOut = useRef<boolean>(false);

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      try {
        // Check for Cognito tokens
        const cognitoTokensStr = localStorage.getItem(COGNITO_TOKENS_KEY);
        let cognitoLogout = false;

        if (cognitoTokensStr) {
          try {
            const cognitoTokens = JSON.parse(cognitoTokensStr);

            // If we have a refresh token, we should sign out from Cognito
            if (cognitoTokens.refreshToken) {
              cognitoLogout = true;

              // Call Cognito logout endpoint if needed
              // This is optional as we'll remove the tokens anyway
              try {
                await apiRequest('/api/auth/cognito/logout', {
                  method: 'POST',
                  body: JSON.stringify({
                    refreshToken: cognitoTokens.refreshToken
                  }),
                });
              } catch (e) {
                console.error('Error logging out from Cognito:', e);
                // Continue with local logout even if Cognito logout fails
              }

              // Remove Cognito tokens
              localStorage.removeItem(COGNITO_TOKENS_KEY);
            }
          } catch (e) {
            console.error('Error parsing Cognito tokens:', e);
            localStorage.removeItem(COGNITO_TOKENS_KEY);
          }
        }

        // If not using Cognito, logout from our API
        if (!cognitoLogout) {
          const result = await apiRequest('/api/logout', {
            method: 'POST',
          });
          console.log('Logout API response:', result);
          return result.data;
        } else {
          return { success: true };
        }
      } catch (error) {
        console.error('[Logout] API error:', error);
        if (error instanceof Error) {
          if (error.message.includes('Network error')) {
            throw new Error('Unable to connect to the server. Your session may still be active.');
          }
          throw error;
        } else {
          throw new Error('An unexpected error occurred during logout');
        }
      }
    },
    onSuccess: () => {
      console.log('Logout successful');

      // Clear the user from state
      setUser(null);

      // Clear from query cache
      queryClient.setQueryData(['/api/user'], null);
      queryClient.removeQueries({ queryKey: ['/api/user'] });

      // Clear any Cognito tokens
      localStorage.removeItem(COGNITO_TOKENS_KEY);

      // Redirect to home page
      window.location.href = '/';

      toast({
        title: 'Logged out',
        description: "You've been successfully logged out",
      });
    },
    onError: (error: Error) => {
      console.error('Logout error:', error);

      // Use standardized error handling
      handleError(error, 'Failed to log out. Please try again.', undefined, {
        action: 'logout',
      });
    },
    onSettled: () => {
      isLoggingOut.current = false;
    },
  });

  // Function to refetch user data
  const refetchUser = useCallback(async () => {
    try {
      // Check for Cognito tokens
      const cognitoTokensStr = localStorage.getItem(COGNITO_TOKENS_KEY);
      let headers: Record<string, string> = {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      };

      // If we have Cognito tokens, add the Authorization header
      if (cognitoTokensStr) {
        try {
          const cognitoTokens = JSON.parse(cognitoTokensStr);

          // Check if tokens are expired
          if (cognitoTokens.expiresAt && Date.now() < cognitoTokens.expiresAt) {
            console.log('Using Cognito tokens for authentication in refetchUser');
            headers['Authorization'] = `Bearer ${cognitoTokens.accessToken}`;
          } else {
            console.log('Cognito tokens expired in refetchUser, attempting to refresh');

            // Try to refresh the tokens
            try {
              if (cognitoTokens.refreshToken) {
                const response = await apiRequest('/api/auth/cognito/refresh', {
                  method: 'POST',
                  body: JSON.stringify({ refreshToken: cognitoTokens.refreshToken }),
                });

                if (response.data && response.data.tokens) {
                  console.log('Successfully refreshed tokens in refetchUser');
                  const { accessToken, idToken, refreshToken, expiresIn } = response.data.tokens;
                  const expiresAt = Date.now() + expiresIn * 1000;

                  // Update tokens in localStorage
                  const newTokens = {
                    accessToken,
                    idToken,
                    refreshToken,
                    expiresAt,
                  };

                  localStorage.setItem(COGNITO_TOKENS_KEY, JSON.stringify(newTokens));

                  // Use the new access token
                  headers['Authorization'] = `Bearer ${accessToken}`;
                  console.log('Using refreshed token for authentication in refetchUser');
                } else {
                  console.log('Token refresh failed in refetchUser, removing from localStorage');
                  localStorage.removeItem(COGNITO_TOKENS_KEY);
                }
              } else {
                console.log('No refresh token available in refetchUser, removing from localStorage');
                localStorage.removeItem(COGNITO_TOKENS_KEY);
              }
            } catch (refreshError) {
              console.error('Error refreshing tokens in refetchUser:', refreshError);
              localStorage.removeItem(COGNITO_TOKENS_KEY);
            }
          }
        } catch (e) {
          console.error('Error parsing Cognito tokens:', e);
          localStorage.removeItem(COGNITO_TOKENS_KEY);
        }
      }

      const { data } = await apiRequest('/api/user', {
        method: 'GET',
        headers,
      });

      setUser(data);
      queryClient.setQueryData(['/api/user'], data);
      return data;
    } catch (error) {
      console.error('Error refetching user:', error);

      // Use standardized error handling
      handleError(error, 'Failed to refresh user data.', undefined, {
        action: 'refetchUser',
      });

      throw error;
    }
  }, [queryClient]);

  // Function to login with Cognito authorization code
  const loginWithCognitoCode = useCallback(async (code: string): Promise<User> => {
    try {
      console.log('Processing Cognito authorization code');

      // Exchange the code for tokens
      const { data } = await apiRequest('/api/auth/cognito/callback', {
        method: 'POST',
        body: JSON.stringify({ code }),
      });

      if (!data || !data.tokens) {
        throw new Error('Failed to exchange authorization code for tokens');
      }

      // Store tokens in localStorage with expiration
      const { accessToken, idToken, refreshToken, expiresIn } = data.tokens;
      const expiresAt = Date.now() + expiresIn * 1000;

      localStorage.setItem(
        COGNITO_TOKENS_KEY,
        JSON.stringify({
          accessToken,
          idToken,
          refreshToken,
          expiresAt,
        })
      );

      // Fetch user data with the new token
      const userResponse = await apiRequest('/api/user', {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      if (!userResponse.data) {
        throw new Error('Failed to fetch user data after Cognito authentication');
      }

      // Update user state and cache
      setUser(userResponse.data);
      queryClient.setQueryData(['/api/user'], userResponse.data);

      toast({
        title: 'Login successful',
        description: `Welcome, ${userResponse.data.name || userResponse.data.email}!`,
      });

      return userResponse.data;
    } catch (error) {
      console.error('Error during Cognito code exchange:', error);

      // Use standardized error handling
      handleError(
        error instanceof Error ? error : new Error('Unknown error during Cognito authentication'),
        'Failed to complete authentication with Cognito',
        ErrorSeverity.ERROR,
        { action: 'cognitoCallback' }
      );

      throw error;
    }
  }, [queryClient, toast]);

  // Create the context value
  const contextValue: AuthContextType = {
    user,
    isLoading: loginMutation.isPending || registerMutation.isPending || logoutMutation.isPending,
    isCheckingAuth,
    error:
      loginMutation.error || registerMutation.error || logoutMutation.error
        ? handleError(
          loginMutation.error ||
          registerMutation.error ||
          logoutMutation.error ||
          new Error('Unknown error'),
          'Authentication error',
          undefined,
          { silent: true }
        )
        : null,
    registerMutation,
    loginMutation,
    logoutMutation,
    refetchUser,
    loginWithCognitoCode,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
