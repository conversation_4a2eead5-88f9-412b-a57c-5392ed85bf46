import { SessionWithInstructor, BookingWithSession } from '@shared/schema';
import { apiRequest } from '@/lib/queryClient';

// Constants
export const FETCH_THROTTLE_KEY = 'last_sessions_fetch_time';
export const FETCH_THROTTLE_INTERVAL = 1000; // 1 second minimum between fetches
export const ITEMS_PER_PAGE = 12;
export const ENRICHED_SESSIONS_STORAGE_KEY_PREFIX = 'enriched_sessions_';

// Helper function to throttle API requests
export const shouldThrottleFetch = (): boolean => {
  const lastFetchTime = parseInt(localStorage.getItem(FETCH_THROTTLE_KEY) || '0');
  const now = Date.now();
  const timeSinceLastFetch = now - lastFetchTime;

  if (timeSinceLastFetch < FETCH_THROTTLE_INTERVAL) {
    console.log(`[SessionsUtils] Throttling fetch, last fetch was ${timeSinceLastFetch}ms ago`);
    return true;
  }

  localStorage.setItem(FETCH_THROTTLE_KEY, now.toString());
  return false;
};

// Helper function to fetch sessions with fallback strategies
export const fetchTeacherSessions = async (
  userId: number,
  page: number = 1
): Promise<{
  sessions: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
  };
}> => {
  try {
    // Check if we need to throttle this request to avoid rate limiting
    if (shouldThrottleFetch()) {
      await new Promise(resolve => setTimeout(resolve, FETCH_THROTTLE_INTERVAL));
    }

    // Add cache-busting query parameter and pagination
    const timestamp = new Date().getTime();
    const nonce = Math.random().toString(36).substring(2, 15);

    // Make the API request with explicit JSON request headers and pagination
    // First try without pagination parameters to check if the API supports it
    const response = await fetch(
      `/api/teachers/${userId}/sessions?_t=${timestamp}&nonce=${nonce}`,
      {
        headers: {
          Accept: 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      }
    );

    // Check if content type is actually JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('text/html')) {
      console.log('[SessionsUtils] API returned HTML instead of JSON, trying direct fetch');

      // Make a direct fetch to the server without using the Vite dev server
      const url =
        window.location.hostname === 'localhost'
          ? `http://localhost:4004/api/teachers/${userId}/sessions?_t=${timestamp}&direct=true`
          : `/api/teachers/${userId}/sessions?_t=${timestamp}&direct=true`;

      const directResponse = await fetch(url, {
        headers: {
          Accept: 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      if (!directResponse.ok) {
        throw new Error(`Direct API request failed with status: ${directResponse.status}`);
      }

      // Try to parse JSON from the direct response
      try {
        const data = await directResponse.json();

        // Check if the response is already in the expected format
        if (data && typeof data === 'object' && 'sessions' in data && 'pagination' in data) {
          console.log(
            `[SessionsUtils] Direct API returned ${data.sessions.length} sessions with pagination`
          );
          return data;
        }

        // If it's just an array of sessions, convert to expected format
        if (Array.isArray(data)) {
          console.log(`[SessionsUtils] Direct API returned ${data.length} sessions (array format)`);
          return {
            sessions: data,
            pagination: {
              page: page,
              limit: ITEMS_PER_PAGE,
              total: data.length,
              totalPages: Math.ceil(data.length / ITEMS_PER_PAGE),
              hasMore: false, // No way to know if there are more without proper pagination
            },
          };
        }

        // Fallback for unexpected response format
        console.warn('[SessionsUtils] Unexpected Direct API response format:', data);
        return {
          sessions: [],
          pagination: {
            page: page,
            limit: ITEMS_PER_PAGE,
            total: 0,
            totalPages: 0,
            hasMore: false,
          },
        };
      } catch (jsonError) {
        console.error('[SessionsUtils] Failed to parse JSON from direct API', jsonError);
        // Return empty array with pagination structure as fallback
        return {
          sessions: [],
          pagination: {
            page: page,
            limit: ITEMS_PER_PAGE,
            total: 0,
            totalPages: 0,
            hasMore: false,
          },
        };
      }
    }

    if (!response.ok) {
      throw new Error(`API request failed with status: ${response.status}`);
    }

    try {
      const data = await response.json();

      // Check if the response is already in the expected format
      if (data && typeof data === 'object' && 'sessions' in data && 'pagination' in data) {
        console.log(
          `[SessionsUtils] API returned ${data.sessions.length} sessions with pagination`
        );
        return data;
      }

      // If it's just an array of sessions, convert to expected format
      if (Array.isArray(data)) {
        console.log(`[SessionsUtils] API returned ${data.length} sessions (array format)`);
        return {
          sessions: data,
          pagination: {
            page: page,
            limit: ITEMS_PER_PAGE,
            total: data.length,
            totalPages: Math.ceil(data.length / ITEMS_PER_PAGE),
            hasMore: false, // No way to know if there are more without proper pagination
          },
        };
      }

      // Fallback for unexpected response format
      console.warn('[SessionsUtils] Unexpected API response format:', data);
      return {
        sessions: [],
        pagination: {
          page: page,
          limit: ITEMS_PER_PAGE,
          total: 0,
          totalPages: 0,
          hasMore: false,
        },
      };
    } catch (jsonError) {
      console.error('[SessionsUtils] Failed to parse JSON from API', jsonError);
      // Return empty array with pagination structure as fallback
      return {
        sessions: [],
        pagination: {
          page: page,
          limit: ITEMS_PER_PAGE,
          total: 0,
          totalPages: 0,
          hasMore: false,
        },
      };
    }
  } catch (error) {
    console.error('[SessionsUtils] Error fetching sessions:', error);

    // Try a backup direct approach as last resort
    try {
      console.log('[SessionsUtils] Attempting direct database fetch via special endpoint');
      const backupUrl =
        window.location.hostname === 'localhost'
          ? `http://localhost:4002/api/direct/sessions/teacher/${userId}?_t=${Date.now()}`
          : `/api/direct/sessions/teacher/${userId}?_t=${Date.now()}`;
      const backupResponse = await fetch(backupUrl);

      if (backupResponse.ok) {
        const data = await backupResponse.json();

        // Check if the response is already in the expected format
        if (data && typeof data === 'object' && 'sessions' in data && 'pagination' in data) {
          console.log(
            `[SessionsUtils] Backup API returned ${data.sessions.length} sessions with pagination`
          );
          return data;
        }

        // If it's just an array of sessions, convert to expected format
        if (Array.isArray(data)) {
          console.log(`[SessionsUtils] Backup API returned ${data.length} sessions (array format)`);
          return {
            sessions: data,
            pagination: {
              page: page,
              limit: ITEMS_PER_PAGE,
              total: data.length,
              totalPages: Math.ceil(data.length / ITEMS_PER_PAGE),
              hasMore: false, // No way to know if there are more without proper pagination
            },
          };
        }

        // Fallback for unexpected response format
        console.warn('[SessionsUtils] Unexpected Backup API response format:', data);
        return {
          sessions: [],
          pagination: {
            page: page,
            limit: ITEMS_PER_PAGE,
            total: 0,
            totalPages: 0,
            hasMore: false,
          },
        };
      }
    } catch (backupError) {
      console.error('[SessionsUtils] Backup fetch also failed:', backupError);
    }

    // Return empty array with pagination structure on error
    return {
      sessions: [],
      pagination: {
        page: page,
        limit: ITEMS_PER_PAGE,
        total: 0,
        totalPages: 0,
        hasMore: false,
      },
    };
  }
};

// Helper function to fetch user bookings
export const fetchUserBookings = async (userId: number): Promise<BookingWithSession[]> => {
  try {
    console.log(`[SessionsUtils] Fetching bookings for user ${userId}`);

    // Add cache-busting query parameter
    const timestamp = new Date().getTime();
    const nonce = Math.random().toString(36).substring(2, 15);

    // Use the proxy for all API calls
    const directUrl = `/api/direct/bookings/${userId}?_t=${timestamp}&nonce=${nonce}`;

    console.log(`[SessionsUtils] Trying direct bookings endpoint: ${directUrl}`);
    // Use fetch for now to avoid TypeScript errors
    const directResponse = await fetch(directUrl, {
      headers: {
        Accept: 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    if (directResponse.ok) {
      const data = await directResponse.json();
      console.log(`[SessionsUtils] Direct API returned ${data.length} bookings`);
      return data;
    }

    // Fall back to the standard endpoint
    console.log(`[SessionsUtils] Direct endpoint failed, trying standard endpoint`);
    // Use fetch for now to avoid TypeScript errors
    const response = await fetch(`/api/users/${userId}/bookings?_t=${timestamp}&nonce=${nonce}`, {
      headers: {
        Accept: 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed with status: ${response.status}`);
    }

    const data = await response.json();
    console.log(`[SessionsUtils] Standard API returned ${data.length} bookings`);
    return data;
  } catch (error) {
    console.error('[SessionsUtils] Error fetching bookings:', error);
    throw error;
  }
};
