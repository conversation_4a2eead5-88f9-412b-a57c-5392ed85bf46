import { createContext, ReactNode, useContext, useState, useMemo, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '../../lib/queryClient';
import { useToast } from '../../hooks/use-toast';
// import {
//   // TeachingSession,
//   // LearningSession,
//   Session,
//   SessionWithTeacher,
//   InsertSession,
//   InsertBooking,
//   Booking,
//   BookingWithSession,
// } from '@shared/schema';
// import { ExtendedSession, ExtendedSessionWithTeacher } from '../../types/session-context';
import { useAuth } from '../../hooks/use-auth';
import { supabase } from '../../lib/supabase-singleton';

// Integrated with Supabase services
// For now, we're keeping the same interface to maintain compatibility

interface SessionContextType {
  sessions: any[];
  publicSessions: any[];
  teachingSessions: any[];
  learningSessions: any[];
  bookings: any;
  isLoading: boolean;
  error: string | null;
  createSession: (sessionData: any) => Promise<any>;
  updateSession: (sessionId: string, sessionData: any) => Promise<any>;
  deleteSession: (sessionId: string) => Promise<any>;
  bookSession: (sessionId: string, bookingData: any) => Promise<any>;
  cancelBooking: (bookingId: string) => Promise<any>;
  refreshSessions: () => void;
  refreshBookings: () => void;
  getSessionById: (sessionId: string) => any;
  getBookingById: (bookingId: string) => any;
  getSessionsByTeacher: (teacherId: string) => any[];
  getBookingsByUser: (userId: string) => any[];
  searchSessions: (query: string) => any[];
  filterSessions: (filters: any) => any[];
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [error, setError] = useState<string | null>(null);

  // Query for all sessions with teacher data
  const {
    data: sessions = [],
    isLoading: sessionsLoading,
    error: sessionsError,
    refetch: refetchSessions,
  } = useQuery({
    queryKey: ['sessions'],
    queryFn: async () => {
      try {
        setError(null);
        console.log('Fetching sessions with teacher data using Supabase service layer');

        // Import the sessionService functions from our new service layer
        const { getPublishedSessions } = await import('../../lib/sessionService');

        // Get published sessions
        const sessions = await getPublishedSessions();

        console.log('Sessions fetched successfully:', sessions);
        return sessions || [];
      } catch (error) {
        console.error('Error fetching sessions:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch sessions');

        // Fallback to API endpoint
        try {
          console.log('Falling back to API endpoint');
          const response = await apiRequest('/api/sessions');
          console.log('API fallback successful:', response);
          return response || [];
        } catch (apiError) {
          console.error('API fallback failed:', apiError);
          return [];
        }
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Query for user's bookings
  const {
    data: bookings = [],
    isLoading: bookingsLoading,
    error: bookingsError,
    refetch: refetchBookings,
  } = useQuery({
    queryKey: ['bookings', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];
      try {
        setError(null);
        console.log('Fetching bookings for user:', user.id);

        // Use Supabase directly for bookings (read operation)
        const { data, error } = await supabase
          .from('bookings')
          .select(`
            *,
            session:sessions (
              *,
              teacher:user_profiles (
                id,
                name,
                avatar,
                bio
              )
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Supabase bookings error:', error);
          throw error;
        }

        console.log('Bookings fetched successfully:', data);
        return data || [];
      } catch (error) {
        console.error('Error fetching bookings:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch bookings');

        // Fallback to API endpoint
        try {
          console.log('Falling back to bookings API endpoint');
          const response = await apiRequest('/api/bookings');
          console.log('Bookings API fallback successful:', response);
          return response || [];
        } catch (apiError) {
          console.error('Bookings API fallback failed:', apiError);
          return [];
        }
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const isLoading = sessionsLoading || bookingsLoading;

  // Derived data
  const publicSessions = useMemo(() => {
    return sessions.filter((session: any) => {
      // Check if session is public and published
      const isPublic = session.isPublic !== false; // Default to true if not specified
      const isPublished = session.is_published === true;
      const hasValidType = session.type && session.type.trim() !== '';

      return isPublic && isPublished && hasValidType;
    });
  }, [sessions]);

  const teachingSessions = useMemo(() => {
    if (!user?.id) return [];
    return sessions.filter((session: any) => {
      const isTeacher = session.teacher?.id === user.id || session.teacher_id === user.id;
      const isPublic = session.isPublic !== false;
      return isTeacher && isPublic;
    });
  }, [sessions, user?.id]);

  const learningSessions = useMemo(() => {
    if (!user?.id) return [];
    const bookingsArray = Array.isArray(bookings) ? bookings : [];
    const userBookings = bookingsArray.filter((booking: any) => booking.user_id === user.id);
    const bookedSessionIds = userBookings.map((booking: any) => booking.session_id);
    return sessions.filter((session: any) => bookedSessionIds.includes(session.id));
  }, [sessions, bookings, user?.id]);

  // Mutations
  const createSessionMutation = useMutation({
    mutationFn: async (sessionData: any) => {
      try {
        console.log('Creating session with data:', sessionData);

        if (!user?.id) {
          throw new Error('User must be authenticated to create sessions');
        }

        // Ensure teacher_id is set
        const sessionWithTeacher = {
          ...sessionData,
          teacher_id: user.id,
        };

        console.log('Session data with teacher ID:', sessionWithTeacher);

        // Use the Supabase service layer for creating sessions
        console.log('Using Supabase service layer to create session');
        const { createSession } = await import('../../lib/sessionService');

        // Properly map all fields and ensure date is in correct format
        const mappedSessionData = {
          title: sessionWithTeacher.title,
          description: sessionWithTeacher.description,
          type: sessionWithTeacher.type,
          price: Number(sessionWithTeacher.price),
          duration: Number(sessionWithTeacher.duration),
          date: sessionWithTeacher.date, // Should be in YYYY-MM-DD format
          time_of_day: sessionWithTeacher.timeOfDay || sessionWithTeacher.time_of_day,
          language: sessionWithTeacher.language,
          skill_level: sessionWithTeacher.skillLevel || sessionWithTeacher.skill_level,
          format: sessionWithTeacher.format,
          max_participants: sessionWithTeacher.maxParticipants ? Number(sessionWithTeacher.maxParticipants) : undefined,
          zoom_link: sessionWithTeacher.zoomLink || sessionWithTeacher.zoom_link,
          learning_outcomes: sessionWithTeacher.learningOutcomes || sessionWithTeacher.learning_outcomes,
          requirements: sessionWithTeacher.requirements,
          image_url: sessionWithTeacher.imageUrl || sessionWithTeacher.image_url,
          teacher_id: sessionWithTeacher.teacher_id,
          is_published: sessionWithTeacher.isPublished !== false, // Default to true
          is_public: sessionWithTeacher.isPublic !== false, // Default to true
        };

        console.log('Mapped session data for API:', mappedSessionData);

        const result = await createSession(mappedSessionData);

        console.log('Session created successfully:', result);
        return result;
      } catch (error) {
        console.error('Error in createSessionMutation:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Session created successfully, invalidating queries');
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      toast({
        title: 'Success',
        description: 'Session created successfully!',
      });
    },
    onError: (error) => {
      console.error('Failed to create session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create session',
        variant: 'destructive',
      });
    },
  });

  const updateSessionMutation = useMutation({
    mutationFn: async ({ sessionId, sessionData }: { sessionId: string; sessionData: any }) => {
      console.log('Updating session:', sessionId, sessionData);
      return await apiRequest(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        body: JSON.stringify(sessionData),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      toast({
        title: 'Success',
        description: 'Session updated successfully!',
      });
    },
    onError: (error) => {
      console.error('Failed to update session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update session',
        variant: 'destructive',
      });
    },
  });

  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      console.log('Deleting session:', sessionId);
      return await apiRequest(`/api/sessions/${sessionId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      toast({
        title: 'Success',
        description: 'Session deleted successfully!',
      });
    },
    onError: (error) => {
      console.error('Failed to delete session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete session',
        variant: 'destructive',
      });
    },
  });

  const bookSessionMutation = useMutation({
    mutationFn: async ({ sessionId, bookingData }: { sessionId: string; bookingData: any }) => {
      console.log('Booking session:', sessionId, bookingData);
      return await apiRequest('/api/bookings', {
        method: 'POST',
        body: JSON.stringify({
          session_id: sessionId,
          ...bookingData,
        }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      toast({
        title: 'Success',
        description: 'Session booked successfully!',
      });
    },
    onError: (error) => {
      console.error('Failed to book session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to book session',
        variant: 'destructive',
      });
    },
  });

  const cancelBookingMutation = useMutation({
    mutationFn: async (bookingId: string) => {
      console.log('Canceling booking:', bookingId);
      return await apiRequest(`/api/bookings/${bookingId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      toast({
        title: 'Success',
        description: 'Booking canceled successfully!',
      });
    },
    onError: (error) => {
      console.error('Failed to cancel booking:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel booking',
        variant: 'destructive',
      });
    },
  });

  // Helper functions
  const getSessionById = (sessionId: string) => {
    return sessions.find((session: any) => session.id === sessionId);
  };

  const getBookingById = (bookingId: string) => {
    const bookingsArray = Array.isArray(bookings) ? bookings : [];
    return bookingsArray.find((booking: any) => booking.id === bookingId);
  };

  const getSessionsByTeacher = (teacherId: string) => {
    return sessions.filter((session: any) => session.teacherId === teacherId);
  };

  const getBookingsByUser = (userId: string) => {
    const bookingsArray = Array.isArray(bookings) ? bookings : [];
    return bookingsArray.filter((booking: any) => booking.user_id === userId);
  };

  const searchSessions = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    return publicSessions.filter((session: any) =>
      session.title?.toLowerCase().includes(lowercaseQuery) ||
      session.description?.toLowerCase().includes(lowercaseQuery) ||
      session.type?.toLowerCase().includes(lowercaseQuery) ||
      session.teacher?.name?.toLowerCase().includes(lowercaseQuery)
    );
  };

  const filterSessions = (filters: any) => {
    return publicSessions.filter((session: any) => {
      if (filters.type && session.type !== filters.type) return false;
      if (filters.language && session.language !== filters.language) return false;
      if (filters.skillLevel && session.skill_level !== filters.skillLevel) return false;
      if (filters.format && session.format !== filters.format) return false;
      if (filters.minPrice && session.price < filters.minPrice) return false;
      if (filters.maxPrice && session.price > filters.maxPrice) return false;
      if (filters.duration && session.duration !== filters.duration) return false;
      return true;
    });
  };

  // Set up error handling
  useEffect(() => {
    if (sessionsError) {
      console.error('Sessions query error:', sessionsError);
      setError(sessionsError instanceof Error ? sessionsError.message : 'Failed to fetch sessions');
    }
    if (bookingsError) {
      console.error('Bookings query error:', bookingsError);
      setError(bookingsError instanceof Error ? bookingsError.message : 'Failed to fetch bookings');
    }
  }, [sessionsError, bookingsError]);

  const value: SessionContextType = {
    sessions,
    publicSessions,
    teachingSessions,
    learningSessions,
    bookings,
    isLoading,
    error,
    createSession: (sessionData: any) => createSessionMutation.mutateAsync(sessionData),
    updateSession: (sessionId: string, sessionData: any) =>
      updateSessionMutation.mutateAsync({ sessionId, sessionData }),
    deleteSession: (sessionId: string) => deleteSessionMutation.mutateAsync(sessionId),
    bookSession: (sessionId: string, bookingData: any) =>
      bookSessionMutation.mutateAsync({ sessionId, bookingData }),
    cancelBooking: (bookingId: string) => cancelBookingMutation.mutateAsync(bookingId),
    refreshSessions: () => refetchSessions(),
    refreshBookings: () => refetchBookings(),
    getSessionById,
    getBookingById,
    getSessionsByTeacher,
    getBookingsByUser,
    searchSessions,
    filterSessions,
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}

export default SessionContext;