import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, Info } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
// import { ExtendedSession } from './SessionForm';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SessionPricingProps {
  sessionId: string;
  onBookNow?: () => void;
}

export function SessionPricing({ sessionId, onBookNow }: SessionPricingProps) {
  // Fetch session data
  const {
    data: session,
    isLoading,
    error,
  } = useQuery<any>({
    queryKey: [`/api/sessions/${sessionId}`],
    enabled: !!sessionId,
    refetchOnMount: true,
    staleTime: 0,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !session) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Pricing</CardTitle>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the session pricing. Please try again later.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Session Pricing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Price */}
        <div className="text-center">
          <div className="text-3xl font-bold">{formatCurrency(session.price)}</div>
          <p className="text-gray-500">
            {session.recurringPattern ? 'per session' : 'one-time payment'}
          </p>
        </div>

        {/* What's Included */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium">What's Included</h3>
          <ul className="space-y-2">
            <li className="flex items-start">
              <Check className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
              <span>
                {session.duration} minute {session.format.toLowerCase()} session
              </span>
            </li>
            {session.maxParticipants === 1 && (
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
                <span>Private 1-on-1 instruction</span>
              </li>
            )}
            {session.maxParticipants > 1 && (
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
                <span>Small group (max {session.maxParticipants} participants)</span>
              </li>
            )}
            <li className="flex items-start">
              <Check className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
              <span>Personalized instruction</span>
            </li>
            {session.recurringPattern && (
              <li className="flex items-start">
                <Check className="h-5 w-5 mr-2 text-green-500 flex-shrink-0 mt-0.5" />
                <span>
                  {session.numOccurrences} {session.recurringPattern.toLowerCase()} sessions
                </span>
              </li>
            )}
          </ul>
        </div>

        {/* Cancellation Policy */}
        {session.cancellationTimeframe && (
          <div className="space-y-2">
            <div className="flex items-center">
              <h3 className="text-sm font-medium">Cancellation Policy</h3>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 ml-1 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">
                      {formatCancellationPolicy(
                        session.cancellationTimeframe,
                        session.cancellationFeePercentage
                      )}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <p className="text-xs text-gray-500">
              {formatCancellationPolicy(
                session.cancellationTimeframe,
                session.cancellationFeePercentage
              )}
            </p>
          </div>
        )}

        {/* Book Now Button */}
        {onBookNow && (
          <Button onClick={onBookNow} className="w-full mt-4">
            Book Now
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

// Helper function to format cancellation policy
function formatCancellationPolicy(timeframe: string, feePercentage: number = 50): string {
  let timeframeText = '';

  switch (timeframe) {
    case '12_hours':
      timeframeText = '12 hours';
      break;
    case '24_hours':
      timeframeText = '24 hours';
      break;
    case '48_hours':
      timeframeText = '48 hours';
      break;
    case '72_hours':
      timeframeText = '3 days';
      break;
    case '4_days':
      timeframeText = '4 days';
      break;
    case '1_week':
      timeframeText = '1 week';
      break;
    case '2_weeks':
      timeframeText = '2 weeks';
      break;
    default:
      timeframeText = '24 hours';
  }

  return `Free cancellation up to ${timeframeText} before the session. After that, a ${feePercentage}% cancellation fee applies.`;
}
