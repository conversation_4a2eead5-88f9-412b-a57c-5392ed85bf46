import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { sessionSchema, Session } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { FileUploader } from '@/components/ui/file-uploader';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SessionDetailsTab } from '@/features/sessions/components/tabs/SessionDetailsTab';
import { SchedulingTab } from '@/features/sessions/components/tabs/SchedulingTab';
import { AutomatedMessagesTab } from '@/features/sessions/components/tabs/AutomatedMessagesTab';
import { CancellationTab } from '@/features/sessions/components/tabs/CancellationTab';
import { LegalAgreementTab } from '@/features/sessions/components/tabs/LegalAgreementTab';
import { useState, useEffect, useCallback } from 'react';
import { MediaGalleryManager } from '@/components/sessions/media-gallery-manager';
import { useLocation } from '../../../lib/next-router-utils';

// Update form schema to include mediaGallery
const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  type: z.string().min(1, 'Session type is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.coerce
    .number()
    .min(0, 'Price must be at least $0')
    .max(10000, 'Price cannot exceed $10,000'),
  duration: z.coerce
    .number()
    .min(15, 'Duration must be at least 15 minutes')
    .max(480, 'Duration cannot exceed 8 hours'),
  date: z.coerce.date().optional().refine((date) => {
    if (!date) return true; // Allow optional dates
    const now = new Date();
    return date > now;
  }, {
    message: "Please select a date and time in the future"
  }),
  maxParticipants: z.coerce.number().optional(),
  isPublic: z.boolean().default(true),
  language: z.string().min(1, 'Language is required'),
  skillLevel: z.string().min(1, 'Skill level is required'),
  format: z.string().min(1, 'Format is required'),
  locationType: z.enum(['online', 'in_person', 'hybrid']).default('online'),
  location: z.string().optional(),
  onlineHostingService: z.enum(['zoom', 'google_meet', 'microsoft_teams', 'webex', 'skype', 'other']).default('zoom'),
  imageUrl: z.string().optional(),
  schedulingMode: z.enum(['fixed', 'recurring', 'availability']).default('fixed'),
  recurringPattern: z.enum(['daily', 'weekly', 'fortnightly', 'monthly', 'yearly']).optional(),
  recurringDay: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']).optional(),
  numOccurrences: z.coerce.number().optional().nullable(),
  // Support for multiple fixed dates
  fixedDates: z.array(z.date()).optional().refine((dates) => {
    if (!dates || dates.length === 0) return true; // Allow empty arrays
    const now = new Date();
    const futureDates = dates.filter(date => date > now);
    return futureDates.length === dates.length; // All dates must be in the future
  }, {
    message: "All selected dates must be in the future"
  }),
  cancellationPolicy: z.string().optional(),
  cancellationTimeframe: z
    .enum(['12_hours', '24_hours', '48_hours', '72_hours', '4_days', '1_week', '2_weeks', 'custom'])
    .default('24_hours'),
  cancellationFeePercentage: z.coerce.number().min(0).max(100).default(50),
  noShowFeePercentage: z.coerce.number().min(0).max(100).default(100),
  agreementType: z
    .string()
    .min(1, 'Agreement type is required')
    .refine((val) => ['general', 'therapy', 'fitness', 'cooking', 'art', 'music', 'academic', 'custom'].includes(val), {
      message: 'Invalid agreement type',
    }),
  customAgreement: z.boolean().default(false),
  legalAgreement: z.string().optional(),
  timezone: z.string().default(() => Intl.DateTimeFormat().resolvedOptions().timeZone),
  mediaGallery: z.array(z.object({
    id: z.string(),
    type: z.enum(['image', 'video', 'document']),
    url: z.string(),
    title: z.string(),
    description: z.string().optional(),
    upload_date: z.string(),
    file_size: z.number().optional(),
    storage_path: z.string().optional(),
    metadata: z.record(z.any()).optional(),
  })).optional().default([]),
}).superRefine((data, ctx) => {
  // Validate numOccurrences based on recurringPattern
  if (data.numOccurrences && data.recurringPattern) {
    const limits = {
      daily: 365,      // 1 year of daily sessions
      weekly: 104,     // 2 years of weekly sessions  
      fortnightly: 104, // 4 years of fortnightly sessions
      monthly: 120,    // 10 years of monthly sessions
      yearly: 10,      // 10 years of yearly sessions
    };

    const maxLimit = limits[data.recurringPattern as keyof typeof limits] || 52;

    if (data.numOccurrences < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['numOccurrences'],
        message: "Number of occurrences must be at least 1"
      });
    }

    if (data.numOccurrences > maxLimit) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['numOccurrences'],
        message: `Number of occurrences cannot exceed ${maxLimit} for ${data.recurringPattern} sessions`
      });
    }
  }
});

export type SessionFormValues = z.infer<typeof formSchema>;

// Import types from centralized location
import { ExtendedSession, SessionFormValues as BaseSessionFormValues } from '../../../types/session';

interface SessionFormProps {
  initialData?: ExtendedSession;
  relatedSessions?: ExtendedSession[];
  onSubmit: (data: SessionFormValues) => void;
  isSubmitting: boolean;
  userId: string;
  imageUrl?: string;
  onImageChange?: (url: string) => void;
}

export function SessionForm({ initialData, relatedSessions, onSubmit, isSubmitting, userId, imageUrl, onImageChange }: SessionFormProps) {
  const [, setLocation] = useLocation();

  // Get active tab from URL or default to 'details'
  const getActiveTabFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const tabFromUrl = urlParams.get('tab');
    const validTabs = ['details', 'scheduling', 'media', 'messages', 'cancellation', 'legal'];
    return validTabs.includes(tabFromUrl || '') ? tabFromUrl : 'details';
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromUrl());
  const [automatedMessages, setAutomatedMessages] = useState<any[]>([]);
  const [sessionDate, setSessionDate] = useState<Date | undefined>(undefined);
  const [mediaItems, setMediaItems] = useState<any[]>([]);
  const { toast } = useToast();

  // Update URL when tab changes
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);

    // Update URL with new tab parameter
    const url = new URL(window.location.href);
    url.searchParams.set('tab', newTab);
    window.history.replaceState({}, '', url.toString());
  };

  // Listen for browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      setActiveTab(getActiveTabFromUrl());
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  console.log('[SessionForm] Component rendered with:', {
    hasInitialData: !!initialData,
    initialDataTitle: initialData?.title,
    hasRelatedSessions: !!relatedSessions,
    relatedSessionsLength: relatedSessions?.length || 0,
    relatedSessionsType: Array.isArray(relatedSessions) ? 'array' : typeof relatedSessions
  });

  // Calculate multiple dates from related sessions
  const getMultipleDatesFromRelated = useCallback(() => {
    console.log('[SessionForm] getMultipleDatesFromRelated called:', {
      hasInitialData: !!initialData,
      hasRelatedSessions: !!relatedSessions,
      relatedSessionsLength: relatedSessions?.length || 0,
      initialDataTitle: initialData?.title
    });

    if (!initialData || !relatedSessions || relatedSessions.length === 0) {
      console.log('[SessionForm] No initial data or related sessions, returning single date');
      return initialData?.date ? [new Date(initialData.date)] : [];
    }

    // Filter sessions with the same title (case-insensitive and trimmed)
    const currentTitle = initialData.title?.toLowerCase().trim();

    // Log all related sessions for debugging
    console.log('[SessionForm] All related sessions:', relatedSessions.map(s => ({
      id: s.id,
      title: s.title,
      date: s.date,
      titleLower: s?.title?.toLowerCase().trim()
    })));

    const sessionsWithSameTitle = relatedSessions.filter(
      s => s?.title?.toLowerCase().trim() === currentTitle
    );

    console.log('[SessionForm] Sessions with same title:', {
      currentTitle,
      totalRelatedSessions: relatedSessions.length,
      sessionsWithSameTitle: sessionsWithSameTitle.length,
      sessionDates: sessionsWithSameTitle.map(s => ({ id: s.id, date: s.date, title: s.title }))
    });

    if (sessionsWithSameTitle.length > 1) {
      // Sort by date and return all dates
      const dates = sessionsWithSameTitle
        .map(s => new Date(s.date))
        .sort((a, b) => a.getTime() - b.getTime());

      console.log('[SessionForm] Multiple sessions found, returning sorted dates:', dates.map(d => d.toISOString()));
      return dates;
    }

    // If only one session or no matching sessions, return the initial date
    const singleDate = initialData?.date ? [new Date(initialData.date)] : [];
    console.log('[SessionForm] Single session, returning:', singleDate.map(d => d.toISOString()));
    return singleDate;
  }, [initialData, relatedSessions]);

  // Create form with default values
  const form = useForm<SessionFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialData?.title || '',
      type: initialData?.type || 'Yoga',
      description: initialData?.description || '',
      price: initialData?.price || 0,
      duration: initialData?.duration || 60,
      skillLevel: ((initialData as any)?.skillLevel as 'Beginner' | 'Intermediate' | 'Advanced') || 'Beginner',
      format: ((initialData as any)?.format as 'One-on-One' | 'Group' | 'Workshop') || 'One-on-One',
      language: (initialData as any)?.language || 'English',
      locationType: ((initialData as any)?.locationType as 'online' | 'in_person' | 'hybrid') || 'online',
      location: (initialData as any)?.location || '',
      onlineHostingService: ((initialData as any)?.onlineHostingService as 'zoom' | 'google_meet' | 'microsoft_teams' | 'webex' | 'skype' | 'other') || 'zoom',
      imageUrl: (initialData as any)?.imageUrl || imageUrl || '',
      isPublic: (initialData as any)?.isPublic ?? true,
      date: (initialData as any)?.date ? new Date((initialData as any).date) : undefined,
      fixedDates: getMultipleDatesFromRelated(),
      schedulingMode: ((initialData as any)?.schedulingMode as 'fixed' | 'recurring' | 'availability') || 'fixed',
      recurringPattern: (initialData as any)?.recurringPattern || undefined,
      recurringDay: (initialData as any)?.recurringDay || undefined,
      numOccurrences: (initialData as any)?.numOccurrences || undefined,
      cancellationTimeframe: ((initialData as any)?.cancellationTimeframe as '12_hours' | '24_hours' | '48_hours' | '72_hours' | '4_days' | '1_week' | '2_weeks' | 'custom') ?? '24_hours',
      cancellationFeePercentage: (initialData as any)?.cancellationFeePercentage || 50,
      noShowFeePercentage: (initialData as any)?.noShowFeePercentage || 100,
      agreementType: ((initialData as any)?.agreementType as 'general' | 'therapy' | 'fitness' | 'cooking' | 'art' | 'music' | 'academic' | 'custom') || 'general-agreement',
      customAgreement: (initialData as any)?.customAgreement || false,
      legalAgreement: (initialData as any)?.legalAgreement || '',
      timezone: (initialData as any)?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      mediaGallery: (initialData as any)?.mediaGallery || [],
    },
  });

  // Update form when related sessions change
  useEffect(() => {
    if (initialData && relatedSessions) {
      const multipleDates = getMultipleDatesFromRelated();
      console.log('[SessionForm] Related sessions changed, calculating multiple dates:', {
        initialDataTitle: initialData.title,
        relatedSessionsCount: relatedSessions.length,
        multipleDatesCount: multipleDates.length,
        multipleDates: multipleDates.map(d => d.toISOString())
      });

      if (multipleDates.length > 0) {
        console.log('[SessionForm] Setting multiple dates from related sessions:', multipleDates);
        form.setValue('fixedDates', multipleDates);

        // If we have multiple dates, set scheduling mode to fixed
        if (multipleDates.length > 1) {
          form.setValue('schedulingMode', 'fixed');
        }

        // Ensure the first date is also set as the main date
        if (multipleDates.length > 0) {
          form.setValue('date', multipleDates[0]);
        }
      }
    }
  }, [initialData, relatedSessions, form, getMultipleDatesFromRelated]);

  // Force update when related sessions array changes (for post-mutation updates)
  useEffect(() => {
    if (relatedSessions && relatedSessions.length > 0) {
      console.log('[SessionForm] Related sessions array updated, forcing form recalculation');
      const multipleDates = getMultipleDatesFromRelated();
      if (multipleDates.length > 0) {
        console.log('[SessionForm] Force updating fixedDates:', multipleDates);
        form.setValue('fixedDates', multipleDates, { shouldValidate: true });

        if (multipleDates.length > 1) {
          form.setValue('schedulingMode', 'fixed');
        }

        if (multipleDates.length > 0) {
          form.setValue('date', multipleDates[0]);
        }
      }
    }
  }, [relatedSessions?.length, JSON.stringify(relatedSessions?.map(s => ({ id: s.id, date: s.date, title: s.title }))), form, getMultipleDatesFromRelated]);

  // Initial setup when component mounts
  useEffect(() => {
    if (initialData) {
      console.log('[SessionForm] Initial data loaded:', {
        title: initialData.title,
        date: initialData.date,
        hasRelatedSessions: !!relatedSessions,
        relatedSessionsCount: relatedSessions?.length || 0
      });

      // If we have related sessions, calculate multiple dates immediately
      if (relatedSessions && relatedSessions.length > 0) {
        const multipleDates = getMultipleDatesFromRelated();
        if (multipleDates.length > 0) {
          console.log('[SessionForm] Setting initial multiple dates:', multipleDates);
          form.setValue('fixedDates', multipleDates);

          if (multipleDates.length > 1) {
            form.setValue('schedulingMode', 'fixed');
          }
        }
      }
    }
  }, [initialData, relatedSessions, form]);

  // Update imageUrl when it changes
  useEffect(() => {
    if ((initialData as any)?.imageUrl && (initialData as any).imageUrl !== form.getValues('imageUrl')) {
      console.log('[SessionForm] Updating imageUrl from initialData:', (initialData as any).imageUrl);
      form.setValue('imageUrl', (initialData as any).imageUrl);
    }
  }, [(initialData as any)?.imageUrl, form]);

  // Update imageUrl when external imageUrl prop changes
  useEffect(() => {
    if (imageUrl && imageUrl !== form.getValues('imageUrl')) {
      console.log('[SessionForm] Updating imageUrl from prop:', imageUrl);
      form.setValue('imageUrl', imageUrl);
    }
  }, [imageUrl, form]);

  // Initialize media items from initial data
  useEffect(() => {
    if ((initialData as any)?.mediaGallery) {
      setMediaItems((initialData as any).mediaGallery);
    }
  }, [(initialData as any)?.mediaGallery]);

  const [formSubmitted, setFormSubmitted] = useState(false);
  const [tabErrors, setTabErrors] = useState({
    details: false,
    scheduling: false,
    cancellation: false,
    legal: false,
    messages: false,
    media: false,
  });

  const handleSubmit = (data: SessionFormValues) => {
    setFormSubmitted(true);

    // Set flag to indicate this is an explicit form submission
    // This will allow the toast notification to show
    sessionStorage.setItem('explicit_session_update', 'true');

    // Include media items in the submission data
    const submissionData = {
      ...data,
      mediaGallery: mediaItems,
    };

    // Validate all tabs
    const hasDetailsErrors = validateTab('details');
    const hasSchedulingErrors = validateTab('scheduling');
    const hasCancellationErrors = validateTab('cancellation');
    const hasLegalErrors = validateTab('legal');
    const hasMediaErrors = validateTab('media');

    if (hasDetailsErrors || hasSchedulingErrors || hasCancellationErrors || hasLegalErrors || hasMediaErrors) {
      // Remove the flag if validation fails
      sessionStorage.removeItem('explicit_session_update');

      // Check for specific date validation errors
      const dateFieldState = form.getFieldState('date');
      const fixedDatesFieldState = form.getFieldState('fixedDates');

      if (dateFieldState.error?.message?.includes('future')) {
        toast({
          title: 'Invalid Date',
          description: 'Please select a date and time in the future for your session.',
          variant: 'destructive',
        });
      } else if (fixedDatesFieldState.error?.message?.includes('future')) {
        toast({
          title: 'Invalid Dates',
          description: 'All selected dates must be in the future.',
          variant: 'destructive',
        });
      } else {
        // Generic validation error message
        toast({
          title: 'Validation Error',
          description: 'Please check the highlighted fields and correct any errors.',
          variant: 'destructive',
        });
      }
      return;
    }

    onSubmit(submissionData);
  };

  // Check for errors in each tab
  const validateTab = (tabName: string) => {
    const fields: Record<string, string[]> = {
      details: [
        'title',
        'type',
        'description',
        'price',
        'duration',
        'language',
        'skillLevel',
        'format',
      ],
      scheduling: ['date', 'schedulingMode'],
      cancellation: ['cancellationTimeframe', 'cancellationFeePercentage', 'noShowFeePercentage'],
      legal: ['agreementType'], // Only agreementType is required, legalAgreement is optional
      messages: [],
      media: [],
    };

    const tabFields = fields[tabName] || [];
    let hasErrors = false;

    tabFields.forEach(fieldName => {
      const fieldState = form.getFieldState(fieldName as any);
      if (fieldState.invalid) {
        hasErrors = true;
      }

      const value = form.getValues(fieldName as any);
      if (
        fieldName === 'date' ||
        fieldName === 'title' ||
        fieldName === 'type' ||
        fieldName === 'description' ||
        fieldName === 'price' ||
        fieldName === 'duration' ||
        fieldName === 'language' ||
        fieldName === 'skillLevel' ||
        fieldName === 'format' ||
        fieldName === 'cancellationTimeframe' ||
        fieldName === 'agreementType'
      ) {
        if (!value && value !== 0) {
          hasErrors = true;
        }
      }
    });

    setTabErrors(prev => ({ ...prev, [tabName]: hasErrors }));
    return hasErrors;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content Container - matching session detail page */}
      <div className="max-w-5xl mx-auto px-6 lg:px-8 py-8">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-8"
          >
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              {/* Simple Button-Style Tab Navigation */}
              <div className="flex flex-wrap gap-2 mb-8 justify-center">
                <button
                  type="button"
                  onClick={() => handleTabChange('details')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'details'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Details
                  {tabErrors.details && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => handleTabChange('scheduling')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'scheduling'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Scheduling
                  {tabErrors.scheduling && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => handleTabChange('media')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'media'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Media
                  {tabErrors.media && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => handleTabChange('cancellation')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'cancellation'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Cancellation
                  {tabErrors.cancellation && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => handleTabChange('legal')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'legal'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Legal
                  {tabErrors.legal && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => handleTabChange('messages')}
                  className={`relative px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'messages'
                    ? 'bg-accent/90 text-white shadow-sm shadow-accent/20'
                    : 'bg-sage-50/60 text-sage-600 hover:bg-sage-100/60 hover:text-sage-700'
                    }`}
                >
                  Messages
                  {tabErrors.messages && formSubmitted && (
                    <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-400 border-2 border-white"></span>
                  )}
                </button>
              </div>

              {/* Tab Content with Minimal Styling - No Borders */}
              <TabsContent value="details" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Session Details</h2>
                    <p className="text-sage-500">Configure the basic information for your session</p>
                  </div>
                  <SessionDetailsTab
                    form={form}
                    onImageChange={onImageChange}
                    imageUrl={imageUrl}
                  />
                </div>
              </TabsContent>

              <TabsContent value="scheduling" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Scheduling Options</h2>
                    <p className="text-sage-500">Set up when and how often this session will occur</p>
                  </div>
                  <SchedulingTab form={form} />
                </div>
              </TabsContent>

              <TabsContent value="media" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Session Media Gallery</h2>
                    <p className="text-sage-500">
                      Add images, videos, and documents to enhance your session. Organize them in any order you prefer.
                    </p>
                  </div>

                  <MediaGalleryManager
                    mediaItems={mediaItems}
                    onMediaItemsChange={setMediaItems}
                    sessionId={initialData?.id || 'new-session'}
                    userId={userId}
                    className="w-full"
                  />
                </div>
              </TabsContent>

              <TabsContent value="cancellation" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Cancellation Policy</h2>
                    <p className="text-sage-500">Define your cancellation terms and fee structure</p>
                  </div>
                  <CancellationTab form={form} />
                </div>
              </TabsContent>

              <TabsContent value="legal" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Legal Agreement</h2>
                    <p className="text-sage-500">Set up terms and conditions for your session</p>
                  </div>
                  <LegalAgreementTab form={form} />
                </div>
              </TabsContent>

              <TabsContent value="messages" className="space-y-0">
                <div className="bg-sage-50/20 rounded-3xl p-8">
                  <div className="mb-6">
                    <h2 className="text-2xl font-medium text-sage-700 mb-2">Automated Messages</h2>
                    <p className="text-sage-500">Configure automatic notifications for your participants</p>
                  </div>
                  <AutomatedMessagesTab
                    messages={automatedMessages}
                    onChangeMessages={setAutomatedMessages}
                    onSaveMessages={() => { }}
                    isSaving={false}
                    disabled={isSubmitting}
                  />
                </div>
              </TabsContent>
            </Tabs>

            {/* Action Bar with Minimal Styling - No Borders */}
            <div className="bg-sage-50/20 rounded-3xl p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <FormField
                  control={form.control}
                  name="isPublic"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-base font-medium text-sage-600">Public Session</FormLabel>
                        <FormDescription className="text-sm text-sage-500">
                          Make this session visible to everyone
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="h-12 px-8 text-base font-semibold rounded-2xl bg-accent/85 hover:bg-accent text-white shadow-sm shadow-accent/20 hover:shadow-md hover:shadow-accent/30 transition-all duration-300 border-0"
                  size="lg"
                >
                  {isSubmitting && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
                  {initialData ? 'Update Session' : 'Create Session'}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
