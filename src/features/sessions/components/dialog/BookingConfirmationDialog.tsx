import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2, Calendar, Clock, Users, CreditCard, AlertTriangle } from 'lucide-react';
import { formatPrice, formatDate, formatDuration } from '@/lib/utils';
import { SessionWithTeacher, User } from '@shared/schema';
import { StripeCheckoutRedirect } from '@/features/payment/components/StripeCheckoutRedirect';

interface BookingConfirmationDialogProps {
  session: SessionWithTeacher;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  isPaid?: boolean; // Flag to indicate if this is a paid session
}

export function BookingConfirmationDialog({
  session,
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  isPaid = false,
}: BookingConfirmationDialogProps) {
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [cancellationPolicyRead, setCancellationPolicyRead] = useState(false);
  const [showStripeRedirect, setShowStripeRedirect] = useState(false);
  const [redirectError, setRedirectError] = useState<string | null>(null);

  // Format the cancellation policy or use a default one
  const cancellationPolicy =
    (session as any).cancellationPolicy || (session as any).cancellation_policy ||
    'You may cancel free of charge up to 24 hours before the session start time. Cancellations within 24 hours are subject to a 50% cancellation fee. No-shows will be charged 100% of the session fee.';

  // Handle the confirmation for paid sessions
  const handlePaidConfirmation = () => {
    if (isPaid && session.price > 0) {
      setShowStripeRedirect(true);
    } else {
      onConfirm();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle className="text-xl">Confirm Booking</DialogTitle>
          <DialogDescription>
            Please review the session details before confirming your booking.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 my-4">
          {/* Session Summary */}
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium text-lg mb-2">{session.title}</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span>{formatDate(session.date)}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                <span>{formatDuration(session.duration)}</span>
              </div>
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-gray-500" />
                <span>
                  With {(session.teacher as any)?.name || (session.teacher as any)?.full_name || 'Teacher'}
                </span>
              </div>
              <div className="flex items-center font-medium">
                <CreditCard className="h-4 w-4 mr-2 text-gray-500" />
                <span>{formatPrice(session.price)}</span>
              </div>
            </div>
          </div>

          {/* Cancellation Policy */}
          <div className="border border-amber-200 bg-amber-50 p-4 rounded-md">
            <div className="flex items-start mb-2">
              <AlertTriangle className="h-5 w-5 mr-2 text-amber-500 flex-shrink-0 mt-0.5" />
              <h4 className="font-medium text-amber-800">Cancellation Policy</h4>
            </div>
            <p className="text-sm text-amber-700 mb-3">{cancellationPolicy}</p>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="cancellation-policy"
                checked={cancellationPolicyRead}
                onCheckedChange={checked => setCancellationPolicyRead(checked as boolean)}
              />
              <Label htmlFor="cancellation-policy" className="text-sm text-amber-800">
                I have read and understand the cancellation policy
              </Label>
            </div>
          </div>

          {/* Terms and Conditions */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="terms"
              checked={termsAccepted}
              onCheckedChange={checked => setTermsAccepted(checked as boolean)}
            />
            <Label htmlFor="terms" className="text-sm">
              I agree to the{' '}
              <a href="/terms" className="text-blue-600 hover:underline" target="_blank">
                Terms and Conditions
              </a>
            </Label>
          </div>
        </div>

        {showStripeRedirect ? (
          <div className="py-4">
            {redirectError ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                {redirectError}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowStripeRedirect(false)}
                  className="mt-3 w-full"
                >
                  Go Back
                </Button>
              </div>
            ) : (
              <StripeCheckoutRedirect
                sessionId={session.id}
                onError={error => setRedirectError(error)}
              />
            )}
          </div>
        ) : (
          <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-3">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handlePaidConfirmation}
              disabled={!termsAccepted || !cancellationPolicyRead || isLoading}
              className="bg-primary hover:bg-primary/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : isPaid && session.price > 0 ? (
                'Proceed to Payment'
              ) : (
                'Confirm Booking'
              )}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
