import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Loader2, Clock, CheckCircle } from 'lucide-react';
import { format, parseISO, isAfter, isBefore, addDays, formatISO } from 'date-fns';
import { format as formatInTimeZone, toZonedTime } from 'date-fns-tz';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { apiRequest } from '@/lib/queryClient';

// Types
interface TimeSlot {
  id: number;
  availability_id: number;
  start_time: string;
  end_time: string;
  is_booked: boolean;
  booking_id?: number;
}

interface SessionDetails {
  id: number;
  title: string;
  teacher: {
    id: number;
    name: string;
    username: string;
    timezone?: string;
  };
}

interface TimeSlotBookingProps {
  sessionId: string;
  onBookingComplete?: () => void;
  onSelectTimeSlot?: (timeSlot: TimeSlot | null) => void;
  className?: string;
}

export function TimeSlotBooking({
  sessionId,
  onBookingComplete,
  onSelectTimeSlot,
  className,
}: TimeSlotBookingProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [session, setSession] = useState<SessionDetails | null>(null);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [filteredSlots, setFilteredSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [booking, setBooking] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [teacherTimezone, setTeacherTimezone] = useState('UTC');
  const [userTimezone, setUserTimezone] = useState(() => {
    // Try to get the user's timezone from the browser
    return Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
  });
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);

  // Fetch session details and time slots
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch session details
        const { data: sessionData, response: sessionResponse } = await apiRequest(
          `/api/sessions/${sessionId}`
        );
        if (!sessionResponse.ok) {
          throw new Error('Failed to fetch session details');
        }
        setSession(sessionData);

        // Set teacher's timezone
        if (sessionData?.teacher?.timezone) {
          setTeacherTimezone(sessionData.teacher.timezone);
        }

        // Fetch teacher's availability and time slots
        const { data: availabilityData, response: availabilityResponse } = await apiRequest(
          `/api/teacher/${sessionData.teacher.id}/availability?sessionId=${sessionId}`
        );
        if (!availabilityResponse.ok) {
          throw new Error('Failed to fetch teacher availability');
        }

        // If the availability response includes timezone data, use it
        if (availabilityData.teacherTimezone) {
          setTeacherTimezone(availabilityData.teacherTimezone);
        }

        if (availabilityData.userTimezone && (user as any)?.timezone) {
          setUserTimezone((user as any).timezone);
        }

        // Get all time slots for the next month
        const startDate = new Date();
        const endDate = addDays(startDate, 30);

        const { data: timeSlotData, response: timeSlotResponse } = await apiRequest(
          `/api/time-slots?startDate=${formatISO(startDate)}&endDate=${formatISO(endDate)}`
        );

        if (!timeSlotResponse.ok) {
          throw new Error('Failed to fetch time slots');
        }
        setTimeSlots(timeSlotData);

        // Filter time slots for the selected date initially
        filterTimeSlotsByDate(timeSlotData, selectedDate);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load booking information. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sessionId, toast, (user as any)?.timezone]);

  // Filter time slots whenever the selected date changes
  useEffect(() => {
    filterTimeSlotsByDate(timeSlots, selectedDate);
  }, [selectedDate, timeSlots]);

  // Filter time slots by date
  const filterTimeSlotsByDate = (slots: TimeSlot[], date?: Date) => {
    if (!date) {
      setFilteredSlots([]);
      return;
    }

    const filteredByDate = slots.filter(slot => {
      const slotDate = parseISO(slot.start_time);
      return (
        slotDate.getDate() === date.getDate() &&
        slotDate.getMonth() === date.getMonth() &&
        slotDate.getFullYear() === date.getFullYear()
      );
    });

    // Sort by start time
    const sorted = [...filteredByDate].sort((a, b) => {
      return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
    });

    setFilteredSlots(sorted);
  };

  // Book a time slot
  const handleBookTimeSlot = async (timeSlotId: number) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to book a session.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setBooking(true);

      const { response } = await apiRequest('/api/bookings/time-slot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timeSlotId,
          sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to book time slot');
      }

      // Update the time slot in the UI
      setTimeSlots(prev =>
        prev.map(slot => (slot.id === timeSlotId ? { ...slot, is_booked: true } : slot))
      );

      // Show success message
      toast({
        title: 'Booking Successful',
        description: 'Your session has been booked successfully.',
      });

      // Call the callback if provided
      if (onBookingComplete) {
        onBookingComplete();
      }
    } catch (error) {
      console.error('Error booking time slot:', error);
      toast({
        title: 'Booking Failed',
        description: 'Failed to book the session. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setBooking(false);
    }
  };

  // Convert time from teacher timezone to user timezone for display
  const convertTime = (timeString: string): string => {
    try {
      // Parse the date in teacher's timezone
      const teacherDate = parseISO(timeString);

      // Convert to user's timezone
      const userDate = toZonedTime(teacherDate, userTimezone);

      return format(userDate, 'h:mm a');
    } catch (error) {
      console.error('Error converting time:', error);
      return format(parseISO(timeString), 'h:mm a');
    }
  };

  // Get days with available time slots
  const getDaysWithSlots = () => {
    const days = new Set<string>();

    timeSlots.forEach(slot => {
      if (!slot.is_booked) {
        const date = parseISO(slot.start_time);
        days.add(format(date, 'yyyy-MM-dd'));
      }
    });

    return Array.from(days).map(day => parseISO(day));
  };

  // Check if there are available slots for a date
  const hasAvailableSlots = (date: Date): boolean => {
    return getDaysWithSlots().some(
      d =>
        d.getDate() === date.getDate() &&
        d.getMonth() === date.getMonth() &&
        d.getFullYear() === date.getFullYear()
    );
  };

  // Update the handleSelectTimeSlot function to call onSelectTimeSlot
  const handleSelectTimeSlot = (timeSlot: TimeSlot) => {
    setSelectedTimeSlot(timeSlot);
    if (onSelectTimeSlot) {
      onSelectTimeSlot(timeSlot);
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/2 space-y-4">
          <h3 className="text-lg font-medium">Book a Time Slot</h3>
          <p className="text-sm text-muted-foreground">
            Select a date to see available time slots in{' '}
            <strong>your timezone ({userTimezone})</strong>.
            {teacherTimezone !== userTimezone && (
              <span className="block mt-1">
                Note: The teacher is in {teacherTimezone} timezone.
              </span>
            )}
          </p>

          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              disabled={date => {
                // Disable dates in the past
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                // Disable dates with no available slots
                return isBefore(date, today) || !hasAvailableSlots(date);
              }}
              modifiers={{
                available: getDaysWithSlots(),
              }}
              modifiersStyles={{
                available: { color: 'var(--primary)', fontWeight: 'bold' },
              }}
              className="border rounded-md"
            />
          )}
        </div>

        <div className="md:w-1/2 space-y-4">
          <h3 className="text-lg font-medium">Available Times</h3>

          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredSlots.length === 0 ? (
            <div className="text-center py-8 border rounded-md bg-muted/30">
              <Clock className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-base font-medium">No Time Slots Available</h3>
              <p className="mt-2 text-sm text-muted-foreground max-w-md mx-auto">
                {selectedDate
                  ? 'There are no available time slots for the selected date. Please select another date.'
                  : 'Please select a date to view available time slots.'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-2">
              {filteredSlots
                .filter(slot => !slot.is_booked)
                .map(slot => (
                  <Button
                    key={slot.id}
                    variant={selectedTimeSlot?.id === slot.id ? 'default' : 'outline'}
                    className="justify-start h-auto py-3 px-4"
                    onClick={() => handleSelectTimeSlot(slot)}
                  >
                    <div className="text-left">
                      <p className="font-medium">
                        {convertTime(slot.start_time)} - {convertTime(slot.end_time)}
                      </p>
                      <p className="text-xs text-muted-foreground">Your time ({userTimezone})</p>
                    </div>
                  </Button>
                ))}
            </div>
          )}

          {session && (
            <div className="mt-6 p-4 border rounded-md bg-muted/20">
              <h4 className="font-medium">{session.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                Teacher: {session.teacher.name}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
