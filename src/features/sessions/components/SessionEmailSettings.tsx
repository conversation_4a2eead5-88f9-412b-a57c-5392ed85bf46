import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { MessageSection } from '@/features/teacher/components/settings';

// Import the same email templates used in TeacherSettingsForm
// import {
//   welcomeEmailTemplates,
//   reminderEmailTemplates,
//   followUpEmailTemplates,
// } from '@/features/teacher/components/TeacherSettingsForm';

// Mock email templates for now
const welcomeEmailTemplates = [
  {
    id: 'default',
    name: 'Default Welcome',
    content: 'Welcome to the session!'
  }
];
const reminderEmailTemplates = [
  {
    id: 'default',
    name: 'Default Reminder',
    content: 'Don\'t forget your session!'
  }
];
const followUpEmailTemplates = [
  {
    id: 'default',
    name: 'Default Follow-up',
    content: 'Thank you for attending!'
  }
];

// Schema for session-specific email settings
const sessionEmailSettingsSchema = z.object({
  // Override flags
  overrideWelcomeMessage: z.boolean().default(false),
  overrideReminderMessage: z.boolean().default(false),
  overrideFollowUpMessage: z.boolean().default(false),

  // Welcome Message
  welcomeMessage: z.string().optional(),
  welcomeMessageTiming: z.enum(['on_booking', 'custom_time', 'manual']).optional(),
  welcomeMessageCustomTime: z.string().optional(),
  welcomeMessageCustomDate: z.date().optional().nullable(),
  welcomeMessageDelivery: z.enum(['email', 'message']).optional(),
  welcomeMessageTemplate: z.string().optional(),

  // Reminder Message
  reminderMessage: z.string().optional(),
  reminderMessageTiming: z
    .enum([
      '24_hours_before',
      '12_hours_before',
      '6_hours_before',
      '1_hour_before',
      'custom_time',
      'manual',
    ])
    .optional(),
  reminderMessageCustomTime: z.string().optional(),
  reminderMessageCustomDate: z.date().optional().nullable(),
  reminderMessageDelivery: z.enum(['email', 'message']).optional(),
  reminderMessageTemplate: z.string().optional(),

  // Follow-up Message
  followUpMessage: z.string().optional(),
  followUpMessageTiming: z
    .enum([
      'immediately_after',
      '1_hour_after',
      '6_hours_after',
      '24_hours_after',
      'custom_time',
      'manual',
    ])
    .optional(),
  followUpMessageCustomTime: z.string().optional(),
  followUpMessageCustomDate: z.date().optional().nullable(),
  followUpMessageDelivery: z.enum(['email', 'message']).optional(),
  followUpMessageTemplate: z.string().optional(),
});

type SessionEmailSettingsFormValues = z.infer<typeof sessionEmailSettingsSchema>;

interface SessionEmailSettingsProps {
  sessionId: string;
  defaultSettings?: any; // Global default settings from teacher profile
  sessionSettings?: any; // Session-specific settings (if any)
  onSave: (settings: SessionEmailSettingsFormValues) => void;
}

export function SessionEmailSettings({
  sessionId,
  defaultSettings,
  sessionSettings,
  onSave,
}: SessionEmailSettingsProps) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('welcome');

  // Merge default settings with session-specific overrides
  const mergedSettings = {
    // Override flags
    overrideWelcomeMessage: !!sessionSettings?.welcomeMessage,
    overrideReminderMessage: !!sessionSettings?.reminderMessage,
    overrideFollowUpMessage: !!sessionSettings?.followUpMessage,

    // Welcome Message
    welcomeMessage: sessionSettings?.welcomeMessage || defaultSettings?.defaultWelcomeMessage || '',
    welcomeMessageTiming: sessionSettings?.welcomeMessageTiming || defaultSettings?.defaultWelcomeMessageTiming || 'on_booking',
    welcomeMessageCustomTime: sessionSettings?.welcomeMessageCustomTime || defaultSettings?.defaultWelcomeMessageCustomTime || '',
    welcomeMessageCustomDate: sessionSettings?.welcomeMessageCustomDate || defaultSettings?.defaultWelcomeMessageCustomDate || null,
    welcomeMessageDelivery: sessionSettings?.welcomeMessageDelivery || defaultSettings?.defaultWelcomeMessageDelivery || 'email',
    welcomeMessageTemplate: sessionSettings?.welcomeMessageTemplate || defaultSettings?.defaultWelcomeMessageTemplate || '',

    // Reminder Message
    reminderMessage: sessionSettings?.reminderMessage || defaultSettings?.defaultReminderMessage || '',
    reminderMessageTiming: sessionSettings?.reminderMessageTiming || defaultSettings?.defaultReminderMessageTiming || '24_hours_before',
    reminderMessageCustomTime: sessionSettings?.reminderMessageCustomTime || defaultSettings?.defaultReminderMessageCustomTime || '',
    reminderMessageCustomDate: sessionSettings?.reminderMessageCustomDate || defaultSettings?.defaultReminderMessageCustomDate || null,
    reminderMessageDelivery: sessionSettings?.reminderMessageDelivery || defaultSettings?.defaultReminderMessageDelivery || 'email',
    reminderMessageTemplate: sessionSettings?.reminderMessageTemplate || defaultSettings?.defaultReminderMessageTemplate || '',

    // Follow-up Message
    followUpMessage: sessionSettings?.followUpMessage || defaultSettings?.defaultFollowUpMessage || '',
    followUpMessageTiming: sessionSettings?.followUpMessageTiming || defaultSettings?.defaultFollowUpMessageTiming || 'immediately_after',
    followUpMessageCustomTime: sessionSettings?.followUpMessageCustomTime || defaultSettings?.defaultFollowUpMessageCustomTime || '',
    followUpMessageCustomDate: sessionSettings?.followUpMessageCustomDate || defaultSettings?.defaultFollowUpMessageCustomDate || null,
    followUpMessageDelivery: sessionSettings?.followUpMessageDelivery || defaultSettings?.defaultFollowUpMessageDelivery || 'email',
    followUpMessageTemplate: sessionSettings?.followUpMessageTemplate || defaultSettings?.defaultFollowUpMessageTemplate || '',
  };

  const form = useForm<SessionEmailSettingsFormValues>({
    resolver: zodResolver(sessionEmailSettingsSchema),
    defaultValues: mergedSettings,
  });

  const handleSubmit = (values: SessionEmailSettingsFormValues) => {
    onSave(values);
    setOpen(false);
  };

  // Watch override flags to conditionally show message sections
  const overrideWelcome = form.watch('overrideWelcomeMessage');
  const overrideReminder = form.watch('overrideReminderMessage');
  const overrideFollowUp = form.watch('overrideFollowUpMessage');

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Email Settings</Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Session Email Settings</DialogTitle>
          <DialogDescription>
            Customize automated emails for this specific session. Any settings you customize here will
            override your global default settings.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 mb-6">
                <TabsTrigger value="welcome">Welcome Message</TabsTrigger>
                <TabsTrigger value="reminder">Reminder Message</TabsTrigger>
                <TabsTrigger value="followup">Follow-up Message</TabsTrigger>
              </TabsList>

              <TabsContent value="welcome" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Welcome Message</h3>
                  <FormField
                    control={form.control}
                    name="overrideWelcomeMessage"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Override Default Settings</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {overrideWelcome ? (
                  <MessageSection
                    control={form.control}
                    watch={form.watch}
                    setValue={form.setValue}
                    title="Welcome Message"
                    messageFieldName="welcomeMessage"
                    deliveryFieldName="welcomeMessageDelivery"
                    timingFieldName="welcomeMessageTiming"
                    dateFieldName="welcomeMessageCustomDate"
                    timeFieldName="welcomeMessageCustomTime"
                    templateFieldName="welcomeMessageTemplate"
                    timingOptions={[
                      { value: "on_booking", label: "Send on booking" }
                    ]}
                    placeholder="Enter your welcome message"
                    isWelcomeMessage={true}
                    emailTemplates={welcomeEmailTemplates}
                  />
                ) : (
                  <div className="p-6 bg-gray-50 rounded-md text-center text-gray-500">
                    Using your default welcome message settings. Toggle the switch above to customize.
                  </div>
                )}
              </TabsContent>

              <TabsContent value="reminder" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Reminder Message</h3>
                  <FormField
                    control={form.control}
                    name="overrideReminderMessage"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Override Default Settings</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {overrideReminder ? (
                  <MessageSection
                    control={form.control}
                    watch={form.watch}
                    setValue={form.setValue}
                    title="Reminder Message"
                    messageFieldName="reminderMessage"
                    deliveryFieldName="reminderMessageDelivery"
                    timingFieldName="reminderMessageTiming"
                    dateFieldName="reminderMessageCustomDate"
                    timeFieldName="reminderMessageCustomTime"
                    templateFieldName="reminderMessageTemplate"
                    timingOptions={[
                      { value: "24_hours_before", label: "24 hours before" },
                      { value: "12_hours_before", label: "12 hours before" },
                      { value: "6_hours_before", label: "6 hours before" },
                      { value: "1_hour_before", label: "1 hour before" },
                      { value: "custom_time", label: "Custom time" },
                      { value: "manual", label: "Send manually" }
                    ]}
                    placeholder="Enter your reminder message"
                    emailTemplates={reminderEmailTemplates}
                  />
                ) : (
                  <div className="p-6 bg-gray-50 rounded-md text-center text-gray-500">
                    Using your default reminder message settings. Toggle the switch above to customize.
                  </div>
                )}
              </TabsContent>

              <TabsContent value="followup" className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Follow-up Message</h3>
                  <FormField
                    control={form.control}
                    name="overrideFollowUpMessage"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Override Default Settings</FormLabel>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                {overrideFollowUp ? (
                  <MessageSection
                    control={form.control}
                    watch={form.watch}
                    setValue={form.setValue}
                    title="Follow-up Message"
                    messageFieldName="followUpMessage"
                    deliveryFieldName="followUpMessageDelivery"
                    timingFieldName="followUpMessageTiming"
                    dateFieldName="followUpMessageCustomDate"
                    timeFieldName="followUpMessageCustomTime"
                    templateFieldName="followUpMessageTemplate"
                    timingOptions={[
                      { value: "immediately_after", label: "Immediately after" },
                      { value: "1_hour_after", label: "1 hour after" },
                      { value: "6_hours_after", label: "6 hours after" },
                      { value: "24_hours_after", label: "24 hours after" },
                      { value: "custom_time", label: "Custom time" },
                      { value: "manual", label: "Send manually" }
                    ]}
                    placeholder="Enter your follow-up message"
                    emailTemplates={followUpEmailTemplates}
                  />
                ) : (
                  <div className="p-6 bg-gray-50 rounded-md text-center text-gray-500">
                    Using your default follow-up message settings. Toggle the switch above to customize.
                  </div>
                )}
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
