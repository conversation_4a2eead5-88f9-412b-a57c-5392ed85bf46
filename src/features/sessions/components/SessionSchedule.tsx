import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, ArrowRight, Check } from 'lucide-react';
import { formatDate, formatTime, formatDuration } from '@/lib/utils';
// import { ExtendedSession } from './SessionForm';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface SessionScheduleProps {
  sessionId: string;
  userId?: number;
  onTimeSelected?: (time: Date) => void;
  onBookNow?: () => void;
}

export function SessionSchedule({
  sessionId,
  userId,
  onTimeSelected,
  onBookNow,
}: SessionScheduleProps) {
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [availableTimes, setAvailableTimes] = useState<Date[]>([]);
  const [selectedTime, setSelectedTime] = useState<Date | null>(null);
  const [isAlreadyBooked, setIsAlreadyBooked] = useState(false);

  // Fetch session data
  const {
    data: session,
    isLoading: isSessionLoading,
    error: sessionError,
  } = useQuery<any>({
    queryKey: [`/api/sessions/${sessionId}`],
    enabled: !!sessionId,
    refetchOnMount: true,
    staleTime: 0,
  });

  // Fetch booking status if userId is provided
  const { data: bookingStatus, isLoading: isBookingStatusLoading } = useQuery<{
    isBooked: boolean;
  }>({
    queryKey: [`/api/sessions/${sessionId}/booking-status`, userId],
    enabled: !!sessionId && !!userId,
    refetchOnMount: true,
    staleTime: 0,
  });

  // Set available times when session data is loaded
  useEffect(() => {
    if (session) {
      // For a fixed session, there's only one time
      if (!session.recurringPattern) {
        const sessionDate = new Date(session.date);
        setAvailableTimes([sessionDate]);
        setSelectedDate(sessionDate);
        setSelectedTime(sessionDate);
      } else {
        // For recurring sessions, generate the next few occurrences
        const occurrences = generateOccurrences(session);
        setAvailableTimes(occurrences);
        if (occurrences.length > 0) {
          setSelectedDate(occurrences[0]);
          setSelectedTime(occurrences[0]);
        }
      }
    }
  }, [session]);

  // Check if user has already booked this session
  useEffect(() => {
    if (bookingStatus) {
      setIsAlreadyBooked(bookingStatus.isBooked);
    }
  }, [bookingStatus]);

  // Generate occurrences for recurring sessions
  const generateOccurrences = (session: any): Date[] => {
    const occurrences: Date[] = [];
    const startDate = new Date(session.date);
    const numOccurrences = session.numOccurrences || 4;

    // Add the first occurrence
    occurrences.push(startDate);

    // Generate subsequent occurrences based on the pattern
    let currentDate = new Date(startDate);
    for (let i = 1; i < numOccurrences; i++) {
      switch (session.recurringPattern) {
        case 'daily':
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case 'weekly':
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case 'fortnightly':
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + 14);
          break;
        case 'monthly':
          currentDate = new Date(currentDate);
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case 'yearly':
          currentDate = new Date(currentDate);
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
      }
      occurrences.push(new Date(currentDate));
    }

    return occurrences;
  };

  // Handle time selection
  const handleTimeSelection = (time: Date) => {
    setSelectedTime(time);
    if (onTimeSelected) {
      onTimeSelected(time);
    }
  };

  // Handle book now button click
  const handleBookNow = () => {
    if (!selectedTime) {
      toast({
        title: 'No time selected',
        description: 'Please select a time for your session',
        variant: 'destructive',
      });
      return;
    }

    if (onBookNow) {
      onBookNow();
    }
  };

  if (isSessionLoading || isBookingStatusLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </CardContent>
      </Card>
    );
  }

  if (sessionError || !session) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the session schedule. Please try again later.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Session Schedule</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Session Time */}
        <div className="space-y-4">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-gray-500" />
            <span className="font-medium">
              {session.recurringPattern
                ? `${session.recurringPattern.charAt(0).toUpperCase() + session.recurringPattern.slice(1)} sessions`
                : 'One-time session'}
            </span>
          </div>
          <div className="flex items-center">
            <Clock className="h-5 w-5 mr-2 text-gray-500" />
            <span>{formatDuration(session.duration)}</span>
          </div>
        </div>

        {/* Available Times */}
        {availableTimes.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Available Times</h3>
            <RadioGroup
              value={selectedTime?.toISOString()}
              onValueChange={value => handleTimeSelection(new Date(value))}
              className="space-y-2"
            >
              {availableTimes.map((time, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={time.toISOString()} id={`time-${index}`} />
                  <Label htmlFor={`time-${index}`} className="flex-1 cursor-pointer">
                    <div className="flex justify-between items-center p-2 rounded-md hover:bg-gray-50">
                      <div>
                        <p className="font-medium">{formatDate(time)}</p>
                        <p className="text-gray-500">{formatTime(time)}</p>
                      </div>
                      {selectedTime?.toISOString() === time.toISOString() && (
                        <Check className="h-5 w-5 text-green-500" />
                      )}
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Book Now Button */}
        {userId && onBookNow && (
          <Button
            onClick={handleBookNow}
            className="w-full mt-4"
            disabled={isAlreadyBooked || !selectedTime}
          >
            {isAlreadyBooked ? 'Already Booked' : 'Book Now'}
            {!isAlreadyBooked && <ArrowRight className="ml-2 h-4 w-4" />}
          </Button>
        )}

        {/* Already Booked Message */}
        {isAlreadyBooked && (
          <div className="bg-green-50 text-green-700 p-3 rounded-md flex items-center">
            <Check className="h-5 w-5 mr-2" />
            <p>You've already booked this session.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
