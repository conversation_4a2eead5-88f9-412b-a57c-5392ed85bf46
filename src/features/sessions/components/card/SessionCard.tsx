import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Link } from '../../../../components/ui/NextLink';
import { SessionWithTeacher, TeacherProfile } from '@/types/app-types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  BookOpen,
  Calendar,
  Clock,
  Share,
  Trash2,
  Loader2,
  Video,
  MapPin,
  Users,
  EyeOff,
  Eye,
  Edit,
} from 'lucide-react';
import {
  formatDate,
  formatDateLong,
  formatTime,
  formatDuration as formatDurationUtil,
  getInitials,
  getSessionCoverImage,
  handleImageError,
} from '@/lib/utils';
import { useSession } from '@/features/sessions';
import { useAuth } from '@/features/auth/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { useQueryClient } from '@tanstack/react-query';
// import { startChatWithUser } from '@/features/messaging/components/ChatPopupManager';
import { checkServerConnection } from '@/lib/queryClient';
import lodash from 'lodash';
import { cn } from '@/lib/utils';

// Utility functions for formatting
const formatCurrency = (amount: number) => {
  // Check if amount is a valid number
  if (amount === undefined || amount === null || isNaN(amount)) {
    console.warn('Invalid price amount:', amount);
    return 'Price unavailable';
  }

  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return 'Price unavailable';
  }
};

// Ultra-compact duration formatting (e.g., "1:30" instead of "1 hour 30 minutes")
const formatDurationCompact = (minutes: number) => {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (remainingMinutes === 0) {
    return `${hours}:00`;
  }
  return `${hours}:${remainingMinutes.toString().padStart(2, '0')}`;
};

// Ultra-compact date formatting (no year, shorter month)
const formatDateCompact = (date: Date) => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Check if it's today or tomorrow
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  }
  if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow';
  }

  // Format as "Jan 15"
  const month = date.toLocaleString('default', { month: 'short' });
  const day = date.getDate();
  return `${month} ${day}`;
};

// Ultra-compact time formatting (e.g., "3pm" instead of "3:00 PM")
const formatTimeCompact = (date: Date) => {
  let hours = date.getHours();
  const ampm = hours >= 12 ? 'pm' : 'am';
  hours = hours % 12;
  hours = hours ? hours : 12;
  const minutes = date.getMinutes();

  // Only show minutes if they're not zero
  if (minutes === 0) {
    return `${hours}${ampm}`;
  }
  return `${hours}:${minutes.toString().padStart(2, '0')}${ampm}`;
};

// Define a type that extends SessionWithTeacher with additional properties
export type SessionCardSession = SessionWithTeacher & {
  teacher?: TeacherProfile;
  // Snake_case properties for database compatibility
  image_url?: string;
  session_type?: string;
  is_published?: boolean;
  is_public?: boolean;
  teacher_id?: string;
  max_participants?: number;
  created_at?: string | Date;
  updated_at?: string | Date;
  teacher_name?: string;
  teacher_username?: string;
  teacher_avatar?: string;
  user_profiles?: {
    id: string | number;
    name?: string;
    username?: string;
    avatar?: string;
  };
};

export interface SessionCardProps {
  session: SessionCardSession;
  type?: 'teaching' | 'learning'; // Optional now, with a default in the component
  onDelete?: (sessionId: string) => void;
  refetchTeachingSessions?: () => void;
  bookingStatus?: string;
  onCancelCallback?: () => void;
  isCurrentUserProfile?: boolean;
  bookingId?: number;
  showInstructor?: boolean;
  className?: string;
}

// Use React.memo to prevent unnecessary re-renders
export const SessionCard = React.memo(function SessionCard({
  session,
  type = 'learning', // Default to 'learning' if not provided
  onDelete,
  refetchTeachingSessions,
  bookingStatus,
  onCancelCallback,
  isCurrentUserProfile = false,
  bookingId,
  showInstructor = true,
  className = '',
}: SessionCardProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { user } = useAuth();
  const { toggleVisibilityMutation, cancelBookingMutation } = useSession();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isCanceling, setIsCanceling] = useState(false);
  const [forceUpdateKey, setForceUpdateKey] = useState(Date.now());
  const [localIsPublic, setLocalIsPublic] = useState(session.isPublic ?? true);
  const [sessionData, setSessionData] = useState(session);

  // Debug log to see what data we're receiving
  console.log('SessionCard received session:', {
    id: session.id,
    title: session.title,
    teacher: session.teacher,
    imageUrl: session.imageUrl || session.image_url,
    type: session.type || session.session_type,
    isPublic: session.isPublic || session.is_published,
    teacherId: session.teacherId || session.teacher_id,
    teacher_id: session.teacher_id,
    userId: user?.id,
    isOwner: user?.id === (session.teacherId || session.teacher_id),
    cardType: type,
    onDeleteProvided: !!onDelete,
  });

  // Normalize session data to handle both camelCase and snake_case properties
  useEffect(() => {
    // Direct Supabase data will use snake_case, so we need to normalize it
    const normalizedSession = {
      ...session,
      id: session.id,
      title: session.title,
      description: session.description || '',
      date: session.date,
      duration: session.duration || 60,
      price: session.price || 0,
      // Handle image URLs - prefer imageUrl if it exists, otherwise use image_url
      imageUrl: session.imageUrl || session.image_url || '',
      // Ensure image_url is also set for components that expect it
      image_url: session.imageUrl || session.image_url || '',
      // Handle session type
      type: session.type || session.session_type || 'other',
      session_type: session.type || session.session_type || 'other',
      // Handle visibility flags - prioritize isPublic from API response (which is most reliable)
      isPublic: session.isPublic ?? session.is_public ?? session.is_published ?? true,
      is_published: session.isPublic ?? session.is_public ?? session.is_published ?? true,
      // Handle teacher ID
      teacherId: session.teacherId || session.teacher_id || 0,
      teacher_id: session.teacherId || session.teacher_id || 0,
      // Handle other properties
      maxParticipants: session.maxParticipants || session.max_participants || 10,
      max_participants: session.maxParticipants || session.max_participants || 10,
      createdAt: session.createdAt || session.created_at,
      created_at: session.createdAt || session.created_at,
      updatedAt: session.updatedAt || session.updated_at,
      updated_at: session.updatedAt || session.updated_at,
      // Handle teacher data
      teacher: session.teacher || session.user_profiles || {
        id: session.teacher_id || 'unknown',
        name: session.teacher_name || 'Unknown Teacher',
        username: session.teacher_username || 'unknown',
        avatar: session.teacher_avatar || '',
      },
    };

    console.log('Normalized session:', normalizedSession);

    setSessionData(normalizedSession as SessionCardSession);
    setLocalIsPublic(normalizedSession.isPublic ?? true);
  }, [session]);

  const isOwner = user?.id === (sessionData.teacherId || session.teacher_id);

  // Update local state when session props change
  useEffect(() => {
    const isPublicValue = session.isPublic ?? session.is_public ?? session.is_published ?? true;
    setLocalIsPublic(isPublicValue);
  }, [session.isPublic, session.is_public, session.is_published, session.id, session.title, session.price]);

  // Listen for changes to the session update localStorage value
  useEffect(() => {
    let lastKnownUpdate = 0;

    // Function to check for session updates and trigger a re-render only if there's a new update
    const checkForUpdates = () => {
      const lastUpdateStr = localStorage.getItem('last_session_update');
      if (lastUpdateStr) {
        const lastUpdate = parseInt(lastUpdateStr, 10);
        // Only trigger update if this is a newer timestamp than what we've seen
        if (lastUpdate > lastKnownUpdate) {
          lastKnownUpdate = lastUpdate;
          setForceUpdateKey(Date.now());
        }
      }
    };

    // Create a storage event listener to detect changes from other components
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'last_session_update') {
        checkForUpdates();
      }
    };

    // Set up event listener
    window.addEventListener('storage', handleStorageChange);

    // Check immediately when component mounts
    checkForUpdates();

    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [session.id]);

  // Create a key for the session card that includes date and updatedAt to force re-renders on changes
  const sessionKey = useMemo(() => {
    try {
      // Normalize date to ISO string to ensure consistent representation
      const dateStr = session.date ? new Date(session.date).toISOString() : 'no-date';
      // Add the force update key and timestamp to ensure component updates
      return `session-${session.id}-${session.updatedAt}-${dateStr}-${forceUpdateKey}`;
    } catch (e) {
      console.error('Error creating session key:', e);
      return `session-${session.id}-${forceUpdateKey}`;
    }
  }, [session.id, session.date, session.updatedAt, forceUpdateKey]);

  // Format the session date - wrapped in useMemo with dependencies for the date and force update key
  const formattedDate = useMemo(() => {
    try {
      // Make sure we have a valid date first
      if (!session.date) {
        console.log('Missing date for session:', session.id);
        return 'Date unavailable';
      }

      const date = new Date(session.date);
      if (isNaN(date.getTime())) {
        console.log('Invalid date for session:', session.id, session.date);
        return 'Invalid date';
      }

      return formatDate(date);
    } catch (error) {
      console.error('Error formatting date for session:', session.id, error);
      return 'Date error';
    }
  }, [session.date, session.id, forceUpdateKey]);

  // Format the session time
  const formattedTime = useMemo(() => {
    try {
      if (!session.date) return '';
      const date = new Date(session.date);
      if (isNaN(date.getTime())) return '';
      return formatTime(date);
    } catch (error) {
      console.error('Error formatting time for session:', session.id, error);
      return '';
    }
  }, [session.date, session.id]);

  // Format the price
  const formattedPrice = useMemo(() => {
    return formatCurrency(session.price || 0);
  }, [session.price]);

  // Handle visibility toggle
  const handleVisibilityToggle = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      const newVisibility = !localIsPublic;
      setLocalIsPublic(newVisibility);

      await toggleVisibilityMutation.mutateAsync({
        sessionId: session.id,
        isPublic: newVisibility,
      });

      // Update the session data to reflect the change
      setSessionData(prev => ({
        ...prev,
        isPublic: newVisibility,
        is_published: newVisibility,
      }));

      // Store the update timestamp to trigger re-renders in other components
      localStorage.setItem('last_session_update', Date.now().toString());

      toast({
        title: 'Session updated',
        description: `Session is now ${newVisibility ? 'public' : 'hidden'}`,
      });
    } catch (error) {
      console.error('Error toggling visibility:', error);
      // Revert the local state if the mutation failed
      setLocalIsPublic(!localIsPublic);
      toast({
        title: 'Error',
        description: 'Failed to update session visibility',
        variant: 'destructive',
      });
    }
  };

  const confirmDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowDeleteConfirm(true);
  };

  const deleteSession = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete?.(session.id);
    setShowDeleteConfirm(false);
  };

  const cancelDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowDeleteConfirm(false);
  };

  const handleCancelBooking = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!bookingId) {
      console.error('No booking ID provided for cancellation');
      return;
    }

    setIsCanceling(true);

    try {
      await cancelBookingMutation.mutateAsync(bookingId);
      toast({
        title: 'Booking cancelled',
        description: 'Your booking has been successfully cancelled.',
      });
      onCancelCallback?.();
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel booking. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsCanceling(false);
    }
  };

  const getBookingStatusBadge = () => {
    if (!bookingStatus) return null;

    const statusConfig = {
      confirmed: { label: 'Confirmed', className: 'bg-green-100 text-green-800' },
      pending: { label: 'Pending', className: 'bg-yellow-100 text-yellow-800' },
      cancelled: { label: 'Cancelled', className: 'bg-red-100 text-red-800' },
      completed: { label: 'Completed', className: 'bg-blue-100 text-blue-800' },
    };

    const config = statusConfig[bookingStatus as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    );
  };

  const getSessionTypeDisplay = () => {
    const sessionType = session.type || session.session_type;
    if (!sessionType) return null;

    const typeConfig = {
      'Yoga': '🧘',
      'Language': '🗣️',
      'Music': '🎵',
      'Dance': '💃',
      'Fitness': '💪',
      'Art': '🎨',
      'Cooking': '👨‍🍳',
      'Technology': '💻',
      'Business': '💼',
      'Therapy': '🧠',
      'Pilates': '🧘‍♀️',
      'Meditation': '🧘‍♂️',
      'other': '📚',
    };

    const emoji = typeConfig[sessionType as keyof typeof typeConfig] || typeConfig.other;

    return (
      <div className="flex items-center">
        <span className="text-sm">{emoji}</span>
      </div>
    );
  };

  // Get the session image
  const sessionImage = useMemo(() => {
    const imageUrl = sessionData.imageUrl || sessionData.image_url;
    if (imageUrl) {
      return imageUrl;
    }
    // Fallback to session type image
    return getSessionCoverImage(
      sessionData.type || sessionData.session_type || 'other',
      sessionData.id,
      null
    );
  }, [sessionData.imageUrl, sessionData.image_url, sessionData.type, sessionData.session_type]);

  // Main card content
  const cardContent = (
    <Card
      key={sessionKey}
      className={cn(
        "group h-full flex flex-col overflow-hidden transition-all duration-300 hover:shadow-lg bg-white/80 backdrop-blur-sm border border-white/20 shadow-sm rounded-xl",
        className
      )}
    >
      <CardHeader className="p-0 relative">
        <div className="relative h-48 overflow-hidden rounded-t-lg">
          <img
            src={sessionImage}
            alt={sessionData.title}
            className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
            onError={(e) => handleImageError(e, 'session-card')}
          />

          {/* Overlay with session info */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex flex-col justify-end p-4">
            <CardTitle className="text-white text-xl truncate">{sessionData.title}</CardTitle>

            {/* Teacher with avatar */}
            {showInstructor && sessionData.teacher && (
              <div className="flex items-center mt-2">
                {(() => {
                  const teacher = sessionData.teacher;
                  console.log('Teacher data:', teacher);

                  // Debug teacher data
                  if (!teacher && sessionData.teacher_id) {
                    console.log('Missing teacher data but have teacher_id:', sessionData.teacher_id);
                  }

                  if (!teacher) return null;

                  return (
                    <div
                      className="flex items-center gap-1.5 hover:opacity-90 cursor-pointer"
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        window.location.href = `/teachers/${teacher.id}`;
                      }}
                    >
                      <Avatar className="h-6 w-6 border border-white/50">
                        {(teacher as any).avatar ? (
                          <AvatarImage
                            src={(teacher as any).avatar}
                            alt={(teacher as any).name || 'Teacher'}
                            onError={() => {
                              console.log(`Avatar error for teacher: ${(teacher as any).name || 'unknown'}`);
                            }}
                          />
                        ) : null}
                        <AvatarFallback className="text-xs">
                          {getInitials((teacher as any).name || 'Unknown Teacher')}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-white/90 truncate">{(teacher as any).name || 'Unknown Teacher'}</span>
                    </div>
                  );
                })()}
              </div>
            )}

            {/* Price badge */}
            <div className="absolute top-3 right-3 bg-black/60 text-white text-sm font-medium px-2 py-1 rounded">
              {sessionData.price === 0 ? 'Free' : formattedPrice}
            </div>

            {/* Session type emoji in bottom right corner */}
            <div className="absolute bottom-3 right-3">
              {getSessionTypeDisplay()}
            </div>
          </div>

          {/* Visibility toggle for teaching sessions */}
          {type === 'teaching' && isOwner && (
            <div className="absolute top-3 right-3 flex gap-1.5" onClick={(e) => e.stopPropagation()}>
              <Button
                variant={localIsPublic ? 'secondary' : 'outline'}
                size="sm"
                className="h-8 px-2 text-xs rounded-full"
                onClick={handleVisibilityToggle}
              >
                {localIsPublic ? (
                  <>
                    <Eye className="h-3.5 w-3.5 mr-1" /> Public
                  </>
                ) : (
                  <>
                    <EyeOff className="h-3.5 w-3.5 mr-1" /> Hidden
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-4 bg-gradient-to-t from-primary/5 to-white/90 backdrop-blur-sm">
        {/* Session details with icons */}
        <div className="flex items-center justify-center text-xs text-gray-700 mb-3">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <Calendar className="h-3.5 w-3.5 text-primary/70" />
              <span>{session.date ? `${formatDateCompact(new Date(session.date))}, ${formatTimeCompact(new Date(session.date))}` : 'Flexible'}</span>
            </div>

            <div className="flex items-center gap-1">
              <Clock className="h-3.5 w-3.5 text-primary/70" />
              <span>{formatDurationCompact(session.duration || 60)}</span>
            </div>

            {/* Format indicator */}
            {session.format && (
              <div className="flex items-center gap-1">
                {session.format.toLowerCase() === 'in_person' ? (
                  <MapPin className="h-3.5 w-3.5 text-primary/70" />
                ) : (
                  <Video className="h-3.5 w-3.5 text-primary/70" />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Multiple dates indicator */}
        {(session as any).hasMultipleDates && (
          <div className="mb-3 flex items-center justify-center gap-1.5 text-xs text-gray-700">
            <span className="font-medium">
              +{((session as any).additionalDates?.length || (session as any).totalSessions - 1) || 0} more date{(((session as any).additionalDates?.length || (session as any).totalSessions - 1) || 0) !== 1 ? 's' : ''}
            </span>
            <span className="text-primary/40">•</span>
            <span className="text-gray-600">
              {(session as any).totalSessions || 1} total
            </span>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-wrap gap-2" onClick={(e) => e.stopPropagation()}>
          {/* Show edit button for teaching sessions */}
          {type === 'teaching' && (
            <Button
              variant="outline"
              className="flex-1 border-primary/30 text-primary hover:bg-primary/5"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                window.location.href = `/edit-session/${session.id}`;
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}

          {/* Show delete button for teaching sessions */}
          {type === 'teaching' && isOwner && !showDeleteConfirm && (
            <Button
              variant="outline"
              className="border-red-300 text-red-600 hover:bg-red-50"
              onClick={confirmDelete}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}

          {/* Show delete confirmation buttons */}
          {type === 'teaching' && isOwner && showDeleteConfirm && (
            <>
              <Button variant="destructive" className="flex-1" onClick={deleteSession}>
                Confirm Delete
              </Button>
              <Button variant="outline" className="flex-1" onClick={cancelDelete}>
                Cancel
              </Button>
            </>
          )}

          {/* Show cancel button for booked sessions */}
          {bookingStatus === 'confirmed' && (
            <Button
              variant="outline"
              className="flex-1 border-red-300 text-red-600 hover:bg-red-50"
              onClick={handleCancelBooking}
              disabled={isCanceling}
            >
              {isCanceling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Canceling...
                </>
              ) : (
                <>Cancel Booking</>
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Make all session cards clickable to go to session detail page
  return (
    <Link href={`/session/${session.id}`} className="block h-full transition-all duration-300 hover:scale-[1.02] hover:shadow-xl group">
      {cardContent}
    </Link>
  );
});

// Also export a named component for compatibility
export { SessionCard as SessionCardComponent };
