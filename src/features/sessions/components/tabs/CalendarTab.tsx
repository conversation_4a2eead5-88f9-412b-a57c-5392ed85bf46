import { useQuery } from '@tanstack/react-query';
// import { SessionWithInstructor } from '@shared/schema';
import { SessionCalendar } from '@/features/calendar/components/SessionCalendar';
import { LoadingSkeleton } from '@/components/ui/loading-skeleton';
import { EmptyState } from '@/components/ui/empty-state';
import { CalendarX } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface CalendarTabProps {
  userId: number;
  isCurrentUserProfile: boolean;
}

export function CalendarTab({ userId, isCurrentUserProfile }: CalendarTabProps) {
  // Query for teaching sessions
  const {
    data: teachingSessions = [],
    isLoading: isLoadingTeaching,
    error: teachingError,
  } = useQuery<any[]>({
    queryKey: [`/api/teachers/${userId}/sessions`],
    queryFn: async () => {
      const { data, response } = await apiRequest(`/api/teachers/${userId}/sessions`);
      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      return data;
    },
    enabled: !!userId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });

  // Query for learning sessions (only for current user)
  const {
    data: learningSessions = [],
    isLoading: isLoadingLearning,
    error: learningError,
  } = useQuery<any[]>({
    queryKey: [`/api/users/${userId}/bookings`],
    queryFn: async () => {
      const { data, response } = await apiRequest(`/api/users/${userId}/bookings`);
      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      // Transform bookings to sessions format
      return data.map(booking => booking.session);
    },
    enabled: !!userId && isCurrentUserProfile, // Only fetch for current user
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
  });

  const isLoading = isLoadingTeaching || (isCurrentUserProfile && isLoadingLearning);
  const error = teachingError || (isCurrentUserProfile && learningError);

  // Show loading skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  // Show error state
  if (error) {
    return (
      <EmptyState
        icon={<CalendarX className="h-12 w-12 text-muted-foreground" />}
        title="Error Loading Calendar"
        description={`There was an error loading the calendar. ${(error as Error).message}`}
      />
    );
  }

  return (
    <SessionCalendar
      teachingSessions={teachingSessions}
      learningSessions={isCurrentUserProfile ? learningSessions : []}
      isOwnProfile={isCurrentUserProfile}
    />
  );
}
