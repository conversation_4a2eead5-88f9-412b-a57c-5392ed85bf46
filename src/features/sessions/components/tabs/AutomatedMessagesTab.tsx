import { useState } from 'react';
import { messageTemplates, getTemplatesByCategory } from '@/lib/message-templates';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Plus,
  Edit,
  Trash2,
  MessageSquare,
  ToggleLeft,
  ToggleRight,
  Calendar as CalendarIcon,
  Clock as ClockIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileUploader } from '@/components/ui/FileUploader';

// Message form schema for validation
const messageFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Message content is required'),
  triggerType: z.enum(['before', 'after', 'specific']).default('before'),
  triggerTime: z.coerce.number().min(0),
  triggerUnit: z.enum(['hours', 'days', 'weeks']).default('hours'),
  specificDate: z.date().optional(),
  specificTime: z.string().optional(),
  enabled: z.boolean().default(true),
  frequency: z.coerce.number().min(1).default(1),
  attachments: z
    .array(
      z.object({
        name: z.string(),
        size: z.number(),
        url: z.string().url(),
        type: z.string(),
      })
    )
    .optional()
    .default([]),
  links: z
    .array(
      z.object({
        label: z.string().min(1, 'Label required'),
        url: z.string().url('Invalid URL'),
      })
    )
    .optional()
    .default([]),
});

// Export the type for use in other components
export type AutomatedMessage = z.infer<typeof messageFormSchema> & { id: string };

// Link item type
export interface LinkItem {
  label: string;
  url: string;
}

/**
 * Props for AutomatedMessagesTab
 */
interface AutomatedMessagesTabProps {
  messages: AutomatedMessage[];
  onChangeMessages: (messages: AutomatedMessage[]) => void;
  onSaveMessages: () => void;
  isSaving?: boolean;
  disabled?: boolean;
}

/**
 * Shared Automated Messages Tab for Create/Edit Session
 */
export function AutomatedMessagesTab({
  messages,
  onChangeMessages,
  onSaveMessages,
  isSaving = false,
  disabled = false,
}: AutomatedMessagesTabProps) {
  const [showMessageForm, setShowMessageForm] = useState(false);
  const [editingMessageIndex, setEditingMessageIndex] = useState<number | null>(null);

  // Message form logic
  const messageForm = useForm<z.infer<typeof messageFormSchema>>({
    resolver: zodResolver(messageFormSchema),
    defaultValues: {
      title: '',
      content: '',
      triggerType: 'before',
      triggerTime: 24,
      triggerUnit: 'hours',
      specificDate: undefined,
      specificTime: '',
      enabled: true,
      frequency: 1,
      attachments: [],
      links: [],
    },
  });

  // Open form for new or edit
  const openForm = (index: number | null = null) => {
    setEditingMessageIndex(index);
    if (index !== null && messages[index]) {
      messageForm.reset(messages[index]);
    } else {
      messageForm.reset();
    }
    setShowMessageForm(true);
  };

  // Save message (add or edit)
  const handleSaveMessage = (data: z.infer<typeof messageFormSchema>) => {
    if (editingMessageIndex !== null && messages[editingMessageIndex]) {
      // Edit
      const updated = [...messages];
      updated[editingMessageIndex] = { ...data, id: messages[editingMessageIndex].id };
      onChangeMessages(updated);
      // Auto-save messages when editing
      onSaveMessages();
    } else {
      // Add
      onChangeMessages([...messages, { ...data, id: crypto.randomUUID() }]);
      // Auto-save messages when adding
      onSaveMessages();
    }
    setShowMessageForm(false);
    setEditingMessageIndex(null);
  };

  // Delete message
  const handleDeleteMessage = (index: number) => {
    const updated = [...messages];
    updated.splice(index, 1);
    onChangeMessages(updated);
    // Auto-save after deleting a message
    onSaveMessages();
  };

  // Toggle enabled
  const handleToggleMessageEnabled = (index: number) => {
    const updated = [...messages];
    updated[index] = { ...updated[index], enabled: !updated[index].enabled };
    onChangeMessages(updated);
    // Auto-save after toggling message enabled state
    onSaveMessages();
  };

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <h3 className="text-xl font-semibold mb-2">Automated Messages</h3>
        <p className="text-gray-500 text-sm">
          Create automated messages that will be sent to your students before or after the session.
          <span className="flex flex-wrap gap-2 mt-1">
            <span className="inline-flex items-center text-xs bg-gray-100 px-1.5 py-0.5 rounded">
              <code>{'{student}'}</code> <span className="mx-1">→</span> Student's name
            </span>
            <span className="inline-flex items-center text-xs bg-gray-100 px-1.5 py-0.5 rounded">
              <code>{'{teacher}'}</code> <span className="mx-1">→</span> Your name
            </span>
            <span className="inline-flex items-center text-xs bg-gray-100 px-1.5 py-0.5 rounded">
              <code>{'{session}'}</code> <span className="mx-1">→</span> Session title
            </span>
          </span>
        </p>
      </div>

      {showMessageForm && (
        <Card className="mb-6 border-gray-200 shadow-sm">
          <CardHeader className="bg-gray-50 border-b border-gray-200 pb-4">
            <CardTitle className="text-xl">
              {editingMessageIndex !== null ? 'Edit Message' : 'Create Message'}
            </CardTitle>
            <CardDescription className="text-gray-600">
              Configure when and how this message will be sent to participants
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <form onSubmit={messageForm.handleSubmit(handleSaveMessage)} className="space-y-5">
              <div className="space-y-5">
                <div className="space-y-5">
                  {/* Template Selector */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Message Template</label>
                    <div className="flex gap-2">
                      <select
                        className="border-gray-300 rounded-md flex-grow"
                        onChange={e => {
                          const templateId = e.target.value;
                          if (templateId) {
                            const template = messageTemplates.find(t => t.id === templateId);
                            if (template) {
                              messageForm.setValue('title', template.title);
                              messageForm.setValue('content', template.content);
                            }
                          }
                        }}
                        defaultValue=""
                      >
                        <option value="">Select a template...</option>
                        <optgroup label="Welcome Messages">
                          {getTemplatesByCategory('welcome').map(template => (
                            <option key={template.id} value={template.id}>
                              {template.name}
                            </option>
                          ))}
                        </optgroup>
                        <optgroup label="Reminder Messages">
                          {getTemplatesByCategory('reminder').map(template => (
                            <option key={template.id} value={template.id}>
                              {template.name}
                            </option>
                          ))}
                        </optgroup>
                        <optgroup label="Follow-up Messages">
                          {getTemplatesByCategory('follow-up').map(template => (
                            <option key={template.id} value={template.id}>
                              {template.name}
                            </option>
                          ))}
                        </optgroup>
                        <optgroup label="Other Messages">
                          {getTemplatesByCategory('other').map(template => (
                            <option key={template.id} value={template.id}>
                              {template.name}
                            </option>
                          ))}
                        </optgroup>
                      </select>
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-shrink-0"
                        onClick={() => {
                          messageForm.setValue('title', '');
                          messageForm.setValue('content', '');
                        }}
                      >
                        Clear
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Select a template or create your own
                    </p>
                  </div>

                  <Input
                    {...messageForm.register('title')}
                    placeholder="Message Title"
                    className="border-gray-300"
                  />
                  <Textarea
                    {...messageForm.register('content')}
                    placeholder="Hello {student}, thank you for booking {session} with {teacher}..."
                    className="border-gray-300"
                    rows={6}
                  />
                  {/* File Uploader for attachments */}
                  <div>
                    <label className="block text-sm font-medium mb-1">Attachments</label>
                    <FileUploader
                      onFilesUploaded={files => messageForm.setValue('attachments', files)}
                      initialFiles={messageForm.watch('attachments') as any}
                      multiple={true}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Upload files to include as resources for this message.
                    </p>
                  </div>
                  {/* Links input */}
                  <div>
                    <label className="block text-sm font-medium mb-1">Links</label>
                    <LinksInput
                      value={messageForm.watch('links') as any}
                      onChange={links => messageForm.setValue('links', links)}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Add links (e.g., Zoom, Google Docs, etc.)
                    </p>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">When to send</label>
                      <select
                        {...messageForm.register('triggerType')}
                        className="border-gray-300 rounded-md w-full"
                      >
                        <option value="before">Before session</option>
                        <option value="after">After session</option>
                        <option value="specific">On specific date and time</option>
                      </select>
                    </div>

                    {messageForm.watch('triggerType') !== 'specific' ? (
                      <div className="flex gap-4">
                        <div className="w-1/3">
                          <label className="block text-sm font-medium mb-1">Time</label>
                          <Input
                            type="number"
                            min={0}
                            {...messageForm.register('triggerTime', { valueAsNumber: true })}
                            className="border-gray-300"
                          />
                        </div>
                        <div className="w-2/3">
                          <label className="block text-sm font-medium mb-1">Unit</label>
                          <select
                            {...messageForm.register('triggerUnit')}
                            className="border-gray-300 rounded-md w-full"
                          >
                            <option value="hours">Hours</option>
                            <option value="days">Days</option>
                            <option value="weeks">Weeks</option>
                          </select>
                        </div>
                      </div>
                    ) : (
                      <div className="flex gap-4">
                        <div className="w-1/2">
                          <label className="block text-sm font-medium mb-1">Date</label>
                          <Input
                            type="date"
                            {...messageForm.register('specificDate', {
                              setValueAs: v => (v ? new Date(v) : undefined),
                            })}
                            className="border-gray-300"
                          />
                        </div>
                        <div className="w-1/2">
                          <label className="block text-sm font-medium mb-1">Time</label>
                          <Input
                            type="time"
                            {...messageForm.register('specificTime')}
                            className="border-gray-300"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <label className="text-sm">Enabled</label>
                    <input type="checkbox" {...messageForm.register('enabled')} />
                  </div>
                  <Input
                    type="number"
                    min={1}
                    {...messageForm.register('frequency', { valueAsNumber: true })}
                    className="border-gray-300 w-24"
                    placeholder="Frequency"
                  />
                </div>
              </div>
              <div className="flex gap-2 justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowMessageForm(false);
                    setEditingMessageIndex(null);
                  }}
                  className="border-gray-300 hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingMessageIndex !== null ? 'Save Changes' : 'Add Message'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {!showMessageForm && messages.length > 0 && (
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-700">Your Automated Messages</h3>
          <Button
            type="button"
            onClick={() => openForm(null)}
            variant="outline"
            disabled={disabled}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Message
          </Button>
        </div>
      )}

      <div className="space-y-4">
        {messages.length > 0 ? (
          messages.map((message, index) => (
            <Card
              key={message.id}
              className={cn(
                'overflow-hidden border-gray-200 hover:shadow-sm transition-shadow',
                !message.enabled && 'opacity-70'
              )}
            >
              <div className="flex justify-between items-center p-3 bg-gray-50 border-b border-gray-100">
                <div className="flex items-center">
                  <MessageSquare className="h-3.5 w-3.5 mr-1.5 text-gray-500" />
                  <h3 className="font-medium text-sm">{message.title}</h3>
                  {!message.enabled && (
                    <span className="ml-2 text-xs text-gray-500">(Disabled)</span>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => handleToggleMessageEnabled(index)}
                  >
                    {message.enabled ? (
                      <ToggleRight className="h-3.5 w-3.5 text-gray-600" />
                    ) : (
                      <ToggleLeft className="h-3.5 w-3.5 text-gray-400" />
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => openForm(index)}
                  >
                    <Edit className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => handleDeleteMessage(index)}
                  >
                    <Trash2 className="h-3.5 w-3.5 text-red-500" />
                  </Button>
                </div>
              </div>
              <CardContent className="p-3">
                <div className="mb-2 text-xs text-gray-500 flex items-center">
                  {message.triggerType === 'specific' ? (
                    <>
                      <CalendarIcon className="h-3 w-3 mr-1 text-gray-400" />
                      Send on{' '}
                      <span className="font-medium mx-1">
                        {message.specificDate
                          ? new Date(message.specificDate).toLocaleDateString()
                          : 'date not set'}
                      </span>{' '}
                      at{' '}
                      <span className="font-medium mx-1">
                        {message.specificTime || 'time not set'}
                      </span>
                    </>
                  ) : (
                    <>
                      <ClockIcon className="h-3 w-3 mr-1 text-gray-400" />
                      Send{' '}
                      <span className="font-medium mx-1">
                        {message.triggerTime} {message.triggerUnit}
                      </span>
                      {message.triggerType === 'before' ? ' before ' : ' after '}
                      session
                      {message.frequency > 1 && (
                        <span className="text-gray-400 ml-1">({message.frequency} times)</span>
                      )}
                    </>
                  )}
                </div>
                <div
                  className="text-xs text-gray-600 whitespace-pre-wrap break-words bg-gray-50 p-2 rounded border border-gray-100"
                  dangerouslySetInnerHTML={{
                    __html: message.content
                      .replace(/\{student\}/g, '<span class="text-blue-500">student</span>')
                      .replace(/\{teacher\}/g, '<span class="text-blue-500">teacher</span>')
                      .replace(/\{session\}/g, '<span class="text-blue-500">session</span>'),
                  }}
                />
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200 border-dashed">
            <MessageSquare className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-xl font-medium mb-2">No messages yet</h3>
            <p className="text-gray-600 mb-6">
              Add automated messages to communicate with your participants before or after sessions
            </p>
            <Button type="button" onClick={() => openForm(null)} disabled={disabled}>
              <Plus className="mr-2 h-4 w-4" />
              Add Message
            </Button>
          </div>
        )}
      </div>

      {/* Only show this section if there are messages and we're in edit mode (not create) */}
      {messages.length > 0 && window.location.pathname.includes('/edit') && (
        <div className="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-start justify-between">
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-1">Apply to Existing Bookings</h4>
              <p className="text-sm text-gray-600 mb-0">
                Apply these automated messages to participants who have already booked this session.
                This is useful when you've added new messages after people have already booked.
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              className="ml-4"
              onClick={() => {
                // Call API to schedule messages for existing bookings
                const sessionId = window.location.pathname.split('/').pop();
                if (!sessionId) return;

                fetch(`/api/sessions/${sessionId}/schedule-messages`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                })
                  .then(response => response.json())
                  .then(data => {
                    if (data.success) {
                      alert(
                        `Success! Scheduled messages for ${data.bookingsProcessed} existing bookings.`
                      );
                    } else {
                      alert(`Error: ${data.message || 'Failed to schedule messages'}`);
                    }
                  })
                  .catch(error => {
                    console.error('Error scheduling messages:', error);
                    alert('Failed to schedule messages. See console for details.');
                  });
              }}
              disabled={isSaving || disabled || messages.length === 0}
            >
              Apply Messages
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

function LinksInput({
  value,
  onChange,
}: {
  value: LinkItem[];
  onChange: (links: LinkItem[]) => void;
}) {
  const [links, setLinks] = useState<LinkItem[]>(value || []);
  const [newLabel, setNewLabel] = useState('');
  const [newUrl, setNewUrl] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleAdd = () => {
    if (!newLabel.trim() || !newUrl.trim()) {
      setError('Both label and URL are required');
      return;
    }
    try {
      new URL(newUrl);
    } catch {
      setError('Invalid URL');
      return;
    }
    const updated = [...links, { label: newLabel, url: newUrl }];
    setLinks(updated);
    onChange(updated);
    setNewLabel('');
    setNewUrl('');
    setError(null);
  };
  const handleRemove = (idx: number) => {
    const updated = links.filter((_, i) => i !== idx);
    setLinks(updated);
    onChange(updated);
  };
  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <Input
          type="text"
          value={newLabel}
          onChange={e => setNewLabel(e.target.value)}
          placeholder="Label (e.g. Zoom)"
          className="w-1/3"
        />
        <Input
          type="url"
          value={newUrl}
          onChange={e => setNewUrl(e.target.value)}
          placeholder="URL (e.g. https://zoom.us/j/123)"
          className="w-2/3"
        />
        <Button type="button" onClick={handleAdd} variant="outline" className="flex-shrink-0">
          Add
        </Button>
      </div>
      {error && <p className="text-red-500 text-xs">{error}</p>}
      <div className="space-y-1">
        {links.map((link, idx) => (
          <div
            key={idx}
            className="flex items-center justify-between bg-gray-50 p-1.5 rounded text-sm"
          >
            <div className="flex-1 truncate">
              <span className="font-medium">{link.label}:</span>{' '}
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:underline truncate"
              >
                {link.url}
              </a>
            </div>
            <Button
              type="button"
              onClick={() => handleRemove(idx)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              <Trash2 className="h-3 w-3 text-red-500" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
