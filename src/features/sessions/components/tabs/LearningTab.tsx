import { useEffect, useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { BookingWithSession, SessionWithTeacher } from '@shared/schema';
import { SessionList } from './SessionList';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/features/auth/AuthContext';
import { groupSessionsByTitle } from '@/lib/utils';

interface LearningTabProps {
  userId: number;
  isCurrentUserProfile: boolean;
}

export function LearningTab({ userId, isCurrentUserProfile }: LearningTabProps) {
  const [sessions, setSessions] = useState<SessionWithTeacher[]>([]);
  const { user } = useAuth();

  // Set up the query for the user's bookings
  const {
    data: bookingsData,
    isLoading,
    error,
  } = useQuery<BookingWithSession[]>({
    queryKey: [`/api/users/${userId}/bookings`],
    queryFn: async () => {
      try {
        // Add cache-busting query parameter
        const timestamp = new Date().getTime();
        const nonce = Math.random().toString(36).substring(2, 15);

        const { data, response } = await apiRequest(
          `/api/users/${userId}/bookings?_t=${timestamp}&nonce=${nonce}`,
          {
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              Expires: '0',
            },
          }
        );

        if (!response.ok) {
          // For 404 errors, return empty array instead of throwing
          if (response.status === 404) {
            console.log('No bookings found for user, returning empty array');
            return [];
          }
          throw new Error(`API request failed with status: ${response.status}`);
        }

        return data || [];
      } catch (error) {
        console.error('Error fetching bookings:', error);
        // If it's a 404 or "not found" error, return empty array
        if (error instanceof Error && (
          error.message.includes('404') ||
          error.message.includes('Not Found')
        )) {
          console.log('Treating 404 error as empty bookings list');
          return [];
        }
        throw error;
      }
    },
    enabled: !!userId,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 1 minute
    retry: (failureCount, error) => {
      // Don't retry for 404 errors
      if (error instanceof Error && error.message.includes('404')) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Process bookings into sessions
  useEffect(() => {
    if (bookingsData) {
      const allSessions: SessionWithTeacher[] = [];

      bookingsData.forEach(booking => {
        if (booking.session) {
          // Skip canceled bookings
          if (booking.status === 'cancelled') return;

          // Skip sessions where the current user is the teacher
          if (user && ((booking.session as any).teacherId || (booking.session as any).teacher_id) === user.id) {
            console.log(
              `Filtering out teaching session from learning tab: ${booking.session.title} (ID: ${booking.session.id})`
            );
            return;
          }

          // Convert booking.session to SessionWithTeacher
          const session: any = {
            ...booking.session,
          };

          allSessions.push(session);
        }
      });

      // Sort sessions by date (upcoming first, then past)
      const now = new Date();
      allSessions.sort((a, b) => {
        const aDate = new Date(a.date);
        const bDate = new Date(b.date);

        // If both are in the future or both are in the past, sort by date
        if ((aDate > now && bDate > now) || (aDate <= now && bDate <= now)) {
          return aDate.getTime() - bDate.getTime();
        }

        // If one is in the future and one is in the past, put future first
        return aDate > now ? -1 : 1;
      });

      console.log(
        `Learning tab: Showing ${allSessions.length} sessions after filtering out teaching sessions`
      );
      setSessions(allSessions);
    }
  }, [bookingsData, user]);

  // Group sessions by title to show sessions with multiple dates properly
  const groupedSessions = useMemo(() => {
    if (sessions.length === 0) return [];

    const grouped = groupSessionsByTitle(sessions);

    console.log('[LearningTab] Grouped sessions:', {
      originalCount: sessions.length,
      groupedCount: grouped.length,
      groupedSessions: grouped.map((s: any) => ({
        id: s.id,
        title: s.title,
        hasMultipleDates: s.hasMultipleDates,
        totalSessions: s.totalSessions,
        additionalDates: s.additionalDates?.length || 0
      }))
    });

    return grouped;
  }, [sessions]);

  // Filter out 404 errors from being passed to SessionList
  const displayError = error && !error.message.includes('404') ? error : null;

  return (
    <SessionList
      sessions={groupedSessions}
      isLoading={isLoading}
      error={displayError as Error | null}
      emptyMessage={
        isCurrentUserProfile
          ? "You haven't booked any sessions yet."
          : "This user hasn't booked any sessions yet."
      }
      showInstructorInfo={true}
    />
  );
}
