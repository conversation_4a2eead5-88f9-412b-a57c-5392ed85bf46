import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Globe, Bookmark, ArrowLeft, Edit } from 'lucide-react';
import { formatDate, formatTime, formatDuration, formatCurrency } from '@/lib/utils';
// import { ExtendedSession } from './SessionForm';
import { Link } from '../../../components/ui/NextLink';

interface SessionDetailsProps {
  sessionId: string;
  isOwnSession: boolean;
  onEdit?: () => void;
}

export function SessionDetails({ sessionId, isOwnSession, onEdit }: SessionDetailsProps) {
  const [formattedDate, setFormattedDate] = useState<string>('');
  const [formattedTime, setFormattedTime] = useState<string>('');

  // Fetch session data
  const {
    data: session,
    isLoading,
    error,
  } = useQuery<any>({
    queryKey: [`/api/sessions/${sessionId}`],
    enabled: !!sessionId,
    refetchOnMount: true,
    staleTime: 0,
  });

  // Format date and time when session data is loaded
  useEffect(() => {
    if (session) {
      const sessionDate = new Date(session.date);
      setFormattedDate(formatDate(sessionDate));
      setFormattedTime(formatTime(sessionDate));
    }
  }, [session]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <div className="grid grid-cols-2 gap-4 mt-6">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !session) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Session</CardTitle>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the session details. Please try again later.</p>
          <Button asChild className="mt-4">
            <Link href="/sessions">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sessions
            </Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{session.title}</CardTitle>
        {isOwnSession && onEdit && (
          <Button variant="outline" size="sm" onClick={onEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Session
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Session Type and Skill Level */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">{session.type}</Badge>
          <Badge variant="outline">{session.skillLevel}</Badge>
          <Badge variant="outline">{session.format}</Badge>
        </div>

        {/* Session Description */}
        <div>
          <h3 className="text-lg font-medium mb-2">Description</h3>
          <p className="text-gray-700 whitespace-pre-wrap">{session.description}</p>
        </div>

        {/* Session Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="space-y-4">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-gray-500" />
              <span>{formattedDate}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-5 w-5 mr-2 text-gray-500" />
              <span>
                {formattedTime} ({formatDuration(session.duration)})
              </span>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-gray-500" />
              <span>
                {session.maxParticipants === 1
                  ? 'Private (1-on-1)'
                  : `Up to ${session.maxParticipants} participants`}
              </span>
            </div>
            <div className="flex items-center">
              <Globe className="h-5 w-5 mr-2 text-gray-500" />
              <span>{session.language}</span>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <Bookmark className="h-5 w-5 mr-2 text-gray-500" />
              <span>
                {session.recurringPattern
                  ? `Recurring (${session.recurringPattern})`
                  : 'One-time session'}
              </span>
            </div>
            {session.location && (
              <div className="flex items-center">
                <svg
                  className="h-5 w-5 mr-2 text-gray-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span>{session.location}</span>
              </div>
            )}
            <div className="flex items-center">
              <svg
                className="h-5 w-5 mr-2 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{formatCurrency(session.price)}</span>
            </div>
          </div>
        </div>

        {/* Learning Outcomes */}
        {session.learningOutcomes && (
          <div>
            <h3 className="text-lg font-medium mb-2">What You'll Learn</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{session.learningOutcomes}</p>
          </div>
        )}

        {/* Requirements */}
        {session.requirements && (
          <div>
            <h3 className="text-lg font-medium mb-2">Requirements</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{session.requirements}</p>
          </div>
        )}

        {/* Cancellation Policy */}
        {session.cancellationPolicy && (
          <div>
            <h3 className="text-lg font-medium mb-2">Cancellation Policy</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{session.cancellationPolicy}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}