// Export the context provider and hook
export { SessionProvider, useSession } from './SessionContext';

// Export query client options
export { queryClientOptions } from './queryClientOptions';

// This file serves as the main entry point for the sessions feature
// It will be expanded as we add more functionality
// Fully integrated with Supabase services
// while keeping the same interface to maintain compatibility

// Components exports
export { CreateSessionForm } from './components/CreateSessionForm';
export { SessionDetails } from './components/SessionDetails';
export type { SessionFormValues } from './components/SessionForm';
export { SessionForm } from './components/SessionForm';
export { SessionPricing } from './components/SessionPricing';
export { SessionSchedule } from './components/SessionSchedule';
export { SessionsTabs } from './components/SessionsTabs';
export { TimeSlotBooking } from './components/booking/TimeSlotBooking';
export type { SessionCardProps, SessionCardSession } from './components/card/SessionCard';
export { SessionCard } from './components/card/SessionCard';
export {
  SessionCardSkeleton,
  SessionCardSkeletonGrid,
} from './components/card/SessionCardSkeleton';
export { BookingConfirmationDialog } from './components/dialog/BookingConfirmationDialog';
export { CreateSessionDialog } from './components/dialog/CreateSessionDialog';
export { DuplicateSessionDialog } from './components/dialog/DuplicateSessionDialog';
export { SessionGrid } from './components/grid/SessionGrid';
export { SessionList } from './components/list/SessionList';
export { SessionListItem } from './components/list/SessionListItem';
export type { AutomatedMessage, LinkItem } from './components/tabs/AutomatedMessagesTab';
export { AutomatedMessagesTab } from './components/tabs/AutomatedMessagesTab';
export { CancellationTab } from './components/tabs/CancellationTab';
export { LearningTab } from './components/tabs/LearningTab';
export { LegalAgreementTab } from './components/tabs/LegalAgreementTab';
export { MessagesTab } from './components/tabs/MessagesTab';
export { SavedTab } from './components/tabs/SavedTab';
export { SchedulingTab } from './components/tabs/SchedulingTab';
// Export SessionDetailsTab from detail folder as is, and from tabs folder as SessionDetailsTabComponent
export { SessionDetailsTab } from './components/detail';
export { SessionDetailsTab as SessionDetailsTabComponent } from './components/tabs/SessionDetailsTab';
export { TeachingTab } from './components/tabs/TeachingTab';

// Utility exports
export {
  FETCH_THROTTLE_KEY,
  FETCH_THROTTLE_INTERVAL,
  ITEMS_PER_PAGE,
  ENRICHED_SESSIONS_STORAGE_KEY_PREFIX,
  shouldThrottleFetch,
  fetchTeacherSessions,
  fetchUserBookings,
} from './sessionsUtils';
