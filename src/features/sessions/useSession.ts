import { useContext } from 'react';
import SessionContext from './SessionContext';

/**
 * Custom hook to use the session context
 * This provides access to all session-related functionality
 *
 * @returns The session context value
 * @throws Error if used outside of a SessionProvider
 */
export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}
