import { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { UserWithProfile, SessionWithTeacher } from '@shared/schema';
import { useAuth } from '@/hooks/use-auth';

interface FavoritesContextType {
  favoriteSessions: string[];
  favoriteTeachers: string[];
  toggleSessionFavorite: (sessionId: string) => void;
  toggleTeacherFavorite: (teacherId: string) => void;
  isSessionFavorite: (sessionId: string) => boolean;
  isTeacherFavorite: (teacherId: string) => boolean;
}

const FavoritesContext = createContext<FavoritesContextType | null>(null);

export function FavoritesProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [favoriteSessions, setFavoriteSessions] = useState<string[]>([]);
  const [favoriteTeachers, setFavoriteTeachers] = useState<string[]>([]);

  // Load favorites from localStorage when component mounts or user changes
  useEffect(() => {
    if (user) {
      const userKey = `user-${user.id}`;

      const storedSessions = localStorage.getItem(`${userKey}-favoriteSessions`);
      if (storedSessions) {
        try {
          setFavoriteSessions(JSON.parse(storedSessions));
        } catch (error) {
          console.error('Error parsing favorite sessions:', error);
          setFavoriteSessions([]);
        }
      }

      const storedTeachers = localStorage.getItem(`${userKey}-favoriteTeachers`);
      if (storedTeachers) {
        try {
          setFavoriteTeachers(JSON.parse(storedTeachers));
        } catch (error) {
          console.error('Error parsing favorite teachers:', error);
          setFavoriteTeachers([]);
        }
      }
    } else {
      // Clear favorites when user logs out
      setFavoriteSessions([]);
      setFavoriteTeachers([]);
    }
  }, [user]);

  // Save favorites to localStorage whenever they change
  useEffect(() => {
    if (user) {
      const userKey = `user-${user.id}`;
      localStorage.setItem(`${userKey}-favoriteSessions`, JSON.stringify(favoriteSessions));
    }
  }, [favoriteSessions, user]);

  useEffect(() => {
    if (user) {
      const userKey = `user-${user.id}`;
      localStorage.setItem(`${userKey}-favoriteTeachers`, JSON.stringify(favoriteTeachers));
    }
  }, [favoriteTeachers, user]);

  const toggleSessionFavorite = (sessionId: string) => {
    setFavoriteSessions(prev => {
      if (prev.includes(sessionId)) {
        return prev.filter(id => id !== sessionId);
      } else {
        return [...prev, sessionId];
      }
    });
  };

  const toggleTeacherFavorite = (teacherId: string) => {
    setFavoriteTeachers(prev => {
      if (prev.includes(teacherId)) {
        return prev.filter(id => id !== teacherId);
      } else {
        return [...prev, teacherId];
      }
    });
  };

  const isSessionFavorite = (sessionId: string) => {
    return favoriteSessions.includes(sessionId);
  };

  const isTeacherFavorite = (teacherId: string) => {
    return favoriteTeachers.includes(teacherId);
  };

  return (
    <FavoritesContext.Provider
      value={{
        favoriteSessions,
        favoriteTeachers,
        toggleSessionFavorite,
        toggleTeacherFavorite,
        isSessionFavorite,
        isTeacherFavorite,
      }}
    >
      {children}
    </FavoritesContext.Provider>
  );
}

export function useFavorites() {
  const context = useContext(FavoritesContext);
  if (context === null) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}
