import {
  createContext,
  ReactNode,
  useContext,
  useState,
  useMemo,
  useCallback,
} from 'react';
import { useSession } from '@/contexts/SessionContext';
import { useProfile } from '@/features/profile/ProfileContext';

interface FilterState {
  sessionTypes: string[];
  skillLevels: string[];
  formats: string[];
  languages: string[];
  dateRange: { start: Date; end: Date } | null;
  priceRange: { min: number; max: number } | null;
  rating: number | null;
  tags: string[];
  searchMode: 'sessions' | 'teachers';
}

interface FilterContextType {
  filterState: FilterState;
  filteredSessions: any[];
  filteredTeachers: any[];
  sortedTeachers: any[];
  searchQuery: string;
  sortOption: string;
  isLoading: boolean;
  error: string | null;

  // Filter actions
  updateFilter: (updates: Partial<FilterState>) => void;
  clearAllFilters: () => void;
  setSearchQuery: (query: string) => void;
  setSortOption: (option: string) => void;
  setSearchMode: (mode: 'sessions' | 'teachers') => void;
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

interface FilterProviderProps {
  children: ReactNode;
}

export function FilterProvider({ children }: FilterProviderProps) {
  const { sessions, isLoading: sessionsLoading, error: sessionsError } = useSession();
  const { topTeachers: teachers, isLoadingTeachers: teachersLoading } = useProfile();

  // Filter state
  const [filterState, setFilterState] = useState<FilterState>({
    sessionTypes: [],
    skillLevels: [],
    formats: [],
    languages: [],
    dateRange: null,
    priceRange: null,
    rating: null,
    tags: [],
    searchMode: 'sessions',
  });

  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState('Most Recent');

  // Loading and error state
  const isLoading = sessionsLoading || teachersLoading;
  const error = sessionsError ? String(sessionsError) : null;

  // Filter sessions based on current filter state
  const filteredSessions = useMemo(() => {
    if (!sessions) return [];

    let filtered = [...sessions];

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((session: any) =>
        session.title?.toLowerCase().includes(query) ||
        session.description?.toLowerCase().includes(query) ||
        session.teacher?.full_name?.toLowerCase().includes(query) ||
        session.teacher?.name?.toLowerCase().includes(query)
      );
    }

    // Apply session type filter
    if (filterState.sessionTypes.length > 0) {
      filtered = filtered.filter((session: any) =>
        filterState.sessionTypes.includes(session.type || '')
      );
    }

    // Apply rating filter
    if (filterState.rating) {
      filtered = filtered.filter((session: any) => {
        // Simple rating filter - can be enhanced later
        return true; // For now, don't filter by rating
      });
    }

    return filtered;
  }, [sessions, filterState, searchQuery]);

  // Filter teachers based on current filter state
  const filteredTeachers = useMemo(() => {
    if (!teachers) return [];

    let filtered = [...teachers];

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter((teacher: any) =>
        teacher.full_name?.toLowerCase().includes(query) ||
        teacher.name?.toLowerCase().includes(query) ||
        teacher.bio?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [teachers, searchQuery]);

  // Sorted teachers (alias for compatibility)
  const sortedTeachers = filteredTeachers;

  // Filter actions
  const updateFilter = useCallback((updates: Partial<FilterState>) => {
    setFilterState(prev => ({ ...prev, ...updates }));
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilterState({
      sessionTypes: [],
      skillLevels: [],
      formats: [],
      languages: [],
      dateRange: null,
      priceRange: null,
      rating: null,
      tags: [],
      searchMode: 'sessions',
    });
    setSearchQuery('');
  }, []);

  const setSearchMode = useCallback((mode: 'sessions' | 'teachers') => {
    setFilterState(prev => ({ ...prev, searchMode: mode }));
  }, []);

  const value: FilterContextType = {
    filterState,
    filteredSessions,
    filteredTeachers,
    sortedTeachers,
    searchQuery,
    sortOption,
    isLoading,
    error,
    updateFilter,
    clearAllFilters,
    setSearchQuery,
    setSortOption,
    setSearchMode,
  };

  return <FilterContext.Provider value={value}>{children}</FilterContext.Provider>;
}

export function useFilter() {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilter must be used within a FilterProvider');
  }
  return context;
}

export { FilterContext };
export type { FilterContextType };