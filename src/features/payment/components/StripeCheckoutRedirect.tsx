import { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';

interface StripeCheckoutRedirectProps {
  sessionId: string;
  onError: (error: string) => void;
}

export function StripeCheckoutRedirect({ sessionId, onError }: StripeCheckoutRedirectProps) {
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const [retryCount, setRetryCount] = useState(0);

  async function redirectToCheckout() {
    try {
      setIsLoading(true);
      console.log('Starting checkout process for session ID:', sessionId);

      // Create a checkout session
      const response = await fetch('/api/payments/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
        }),
        credentials: 'include', // Important for cookies/auth
      });

      console.log('Checkout API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response text:', errorText);

        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          console.error('Failed to parse error response as JSON:', e);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }

        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { url } = await response.json();

      if (!url) {
        throw new Error('No checkout URL returned from server');
      }

      console.log('Redirecting to Stripe Checkout:', url);

      // Redirect to Stripe Checkout
      window.location.href = url;
    } catch (err) {
      console.error('Error redirecting to checkout:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to redirect to payment page';

      toast({
        title: 'Payment setup failed',
        description: errorMessage,
        variant: 'destructive',
      });

      onError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }

  // Handle retry
  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    redirectToCheckout();
  };

  useEffect(() => {
    redirectToCheckout();
  }, [sessionId]);

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      {isLoading ? (
        <>
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-lg font-medium">Redirecting to secure payment...</p>
          <p className="text-sm text-muted-foreground mt-2">
            Please wait while we prepare your payment page.
          </p>
        </>
      ) : (
        <div className="flex flex-col items-center">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 max-w-md">
            <p className="font-medium">Payment setup failed</p>
            <p className="text-sm mt-1">We couldn't connect to our payment processor.</p>
            <Button onClick={handleRetry} className="mt-3 w-full" variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
