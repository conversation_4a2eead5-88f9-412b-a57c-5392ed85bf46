import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { PaymentForm } from './PaymentForm';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (paymentIntentId: string) => void;
  sessionId: string;
  sessionTitle: string;
  amount: number;
}

export function PaymentDialog({
  isOpen,
  onClose,
  onSuccess,
  sessionId,
  sessionTitle,
  amount,
}: PaymentDialogProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      // Reset state when dialog opens
      setIsLoading(true);
      setError(null);
      setClientSecret(null);

      // Create a payment intent
      createPaymentIntent();
    }
  }, [isOpen, sessionId, amount]);

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('http://localhost:4002/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          amount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      const data = await response.json();
      setClientSecret(data.clientSecret);
    } catch (err) {
      console.error('Error creating payment intent:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      toast({
        title: 'Payment setup failed',
        description: err instanceof Error ? err.message : 'Failed to set up payment',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Complete Your Payment</DialogTitle>
          <DialogDescription>
            Please provide your payment details to book this session.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        ) : clientSecret ? (
          <PaymentForm
            clientSecret={clientSecret}
            onSuccess={onSuccess}
            onCancel={handleCancel}
            amount={amount}
            sessionTitle={sessionTitle}
          />
        ) : (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            Failed to initialize payment. Please try again.
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
