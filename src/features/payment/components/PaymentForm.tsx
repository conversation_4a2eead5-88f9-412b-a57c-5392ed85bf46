import { useState, useEffect } from 'react';
import { useStripe } from '@/providers/StripeProvider';
import {
  Elements,
  PaymentElement,
  useStripe as useStripeElement,
  useElements,
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PaymentFormProps {
  clientSecret: string;
  onSuccess: (paymentIntentId: string) => void;
  onCancel: () => void;
  amount: number;
  sessionTitle: string;
}

// Inner form component that uses the Stripe hooks
function CheckoutForm({
  onSuccess,
  onCancel,
  amount,
  sessionTitle,
}: Omit<PaymentFormProps, 'clientSecret'>) {
  const stripe = useStripeElement();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Confirm the payment
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin + '/payment-success',
        },
        redirect: 'if_required',
      });

      if (error) {
        setErrorMessage(error.message || 'An error occurred during payment processing');
        toast({
          title: 'Payment failed',
          description: error.message || 'An error occurred during payment processing',
          variant: 'destructive',
        });
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        toast({
          title: 'Payment successful',
          description: 'Your payment was processed successfully',
        });
        onSuccess(paymentIntent.id);
      } else {
        // Handle other statuses or show appropriate messages
        setErrorMessage('Payment processing failed. Please try again.');
        toast({
          title: 'Payment processing failed',
          description: 'Please try again or contact support',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Payment error:', err);
      setErrorMessage('An unexpected error occurred. Please try again.');
      toast({
        title: 'Payment error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Payment Details</h3>
        <p className="text-sm text-gray-600 mb-4">
          You are about to pay ${amount.toFixed(2)} for the session: {sessionTitle}
        </p>
        <PaymentElement />
      </div>

      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {errorMessage}
        </div>
      )}

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            `Pay $${amount.toFixed(2)}`
          )}
        </Button>
      </div>
    </form>
  );
}

// Wrapper component that provides the Stripe Elements
export function PaymentForm({
  clientSecret,
  onSuccess,
  onCancel,
  amount,
  sessionTitle,
}: PaymentFormProps) {
  const { stripe } = useStripe();

  if (!stripe) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Elements stripe={stripe} options={{ clientSecret }}>
      <CheckoutForm
        onSuccess={onSuccess}
        onCancel={onCancel}
        amount={amount}
        sessionTitle={sessionTitle}
      />
    </Elements>
  );
}
