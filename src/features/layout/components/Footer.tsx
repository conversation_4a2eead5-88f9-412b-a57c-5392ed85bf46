import { Link } from '../../../components/ui/NextLink';
import { CalendarCheck, Facebook, Instagram, Linkedin } from 'lucide-react';
import { XLogo } from '@/features/icons/components/XLogo';
import { AboutDialog } from '@/features/about/components/AboutDialog';
import { AdminAuthLink } from '@/features/auth/components/AdminAuthLink';

export function Footer() {
  return (
    <footer className="bg-gradient-to-b from-[#F2F0EB]/60 to-[#F2F0EB] border-t border-[#D2B48C]/20">
      <div className="max-w-5xl mx-auto py-8 px-5 md:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center mb-6">
          <div className="flex items-center mb-4 md:mb-0">
            <CalendarCheck className="mr-2 h-4 w-4 text-[#8FBC8F]" />
            <span className="text-sm font-medium text-[#666666] font-display">Session Hub</span>
          </div>

          <div className="flex space-x-8">
            <Link
              href="/"
              className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors"
            >
              Home
            </Link>
            <AboutDialog
              trigger={
                <button className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors">
                  About
                </button>
              }
            />
            <Link
              href="/create-session"
              className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors"
            >
              Become an Instructor
            </Link>
            <Link
              href="/"
              className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors"
            >
              Contact
            </Link>
          </div>
        </div>

        <div className="border-t border-[#D2B48C]/20 pt-5 flex flex-col md:flex-row justify-between items-center">
          <p className="text-[#999999] text-xs mb-3 md:mb-0">
            &copy; {new Date().getFullYear()} Session Hub. All rights reserved.
          </p>
          <div className="flex items-center">
            <div className="flex space-x-4 mr-8">
              <a href="#" className="text-[#ADADAD] hover:text-[#8FBC8F] transition-colors">
                <Facebook className="h-3.5 w-3.5" />
              </a>
              <a href="#" className="text-[#ADADAD] hover:text-[#8FBC8F] transition-colors">
                <XLogo className="h-3.5 w-3.5" />
              </a>
              <a href="#" className="text-[#ADADAD] hover:text-[#8FBC8F] transition-colors">
                <Instagram className="h-3.5 w-3.5" />
              </a>
              <a href="#" className="text-[#ADADAD] hover:text-[#8FBC8F] transition-colors">
                <Linkedin className="h-3.5 w-3.5" />
              </a>
            </div>
            <div className="flex space-x-6">
              <Link
                href="/"
                className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors"
              >
                Privacy
              </Link>
              <Link
                href="/"
                className="text-[#808080] hover:text-[#8FBC8F] text-xs transition-colors"
              >
                Terms
              </Link>
              <AdminAuthLink />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
