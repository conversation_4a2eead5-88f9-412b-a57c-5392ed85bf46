import { useState, useEffect } from 'react';
import { Link } from '../../../components/ui/NextLink';
import { useLocation } from '../../../lib/next-router-utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/features/auth';
import { getInitials } from '@/lib/utils';
import { Settings, LogIn, UserPlus, Loader2, X, Heart, Clock, User } from 'lucide-react';
import { AuthDialog } from '@/features/auth/components/AuthDialog';
import { Button } from '@/components/ui/button';
import { signOut } from '@/lib/supabase-singleton';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  user: any;
  unreadMessageCount: number;
  unreadNotificationCount: number;
  onLogout: () => void;
}

export function MobileMenu({
  isOpen,
  onClose,
  user,
  unreadMessageCount,
  unreadNotificationCount,
  onLogout,
}: MobileMenuProps) {
  const [location] = useLocation();

  // Close menu when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (e: MouseEvent) => {
      const menu = document.getElementById('mobile-menu');
      if (menu && !menu.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  const handleLogout = async () => {
    console.log('[MobileMenu] Handling logout click');

    try {
      // First try to use the provided onLogout function
      onLogout();

      // Also try direct signOut as a backup
      console.log('[MobileMenu] Using direct signOut function');
      await signOut();

      // Close the menu
      onClose();

      // Force reload the page to ensure clean state
      console.log('[MobileMenu] Reloading page after signOut');
      setTimeout(() => {
        window.location.href = '/';
      }, 500);
    } catch (error) {
      console.error('[MobileMenu] Error during logout:', error);

      // Last resort - clear localStorage and reload
      console.log('[MobileMenu] Using last resort logout method');
      localStorage.clear();
      window.location.href = '/';
    }
  };

  return (
    <>
      {/* Backdrop overlay that covers the entire screen */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/30 sm:hidden z-40 backdrop-blur-[2px] transition-opacity duration-300"
          aria-hidden="true"
        />
      )}

      <div
        className={`sm:hidden fixed top-0 left-0 right-0 z-50 w-full max-w-md mx-auto bg-white shadow-xl border border-gray-200 rounded-b-xl transform transition-all duration-300 ease-in-out overflow-auto ${isOpen ? 'max-h-[80vh] opacity-100' : 'max-h-0 opacity-0'}`}
        id="mobile-menu"
      >
        {/* Close button */}
        <button
          className="absolute top-4 right-4 p-2 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-800 shadow-sm"
          onClick={onClose}
          aria-label="Close menu"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="pt-16 pb-3 space-y-1">
          <Link
            href="/"
            className={`${location === '/'
              ? 'bg-[#8FBC8F]/10 border-[#8FBC8F] text-[#5F8575]'
              : 'border-transparent text-[#666666] hover:bg-[#F2F0EB] hover:border-[#D2B48C]/30 hover:text-[#5F8575]'
              } block pl-4 pr-4 py-2.5 border-l-4 text-sm font-medium transition-colors rounded-r-lg`}
            onClick={onClose}
          >
            Explore
          </Link>
          <Link
            href="/create-session"
            className={`${location === '/create-session'
              ? 'bg-[#8FBC8F]/10 border-[#8FBC8F] text-[#5F8575]'
              : 'border-transparent text-[#666666] hover:bg-[#F2F0EB] hover:border-[#D2B48C]/30 hover:text-[#5F8575]'
              } block pl-4 pr-4 py-2.5 border-l-4 text-sm font-medium transition-colors rounded-r-lg`}
            onClick={onClose}
          >
            Create Session
          </Link>
        </div>

        {user ? (
          <div className="pt-4 pb-3 border-t border-[#D2B48C]/20">
            <div className="flex items-center px-4">
              <div className="flex-shrink-0">
                <Avatar className="h-10 w-10 ring-2 ring-[#F2F0EB] shadow-sm">
                  <AvatarImage
                    src={user?.avatar || undefined}
                    alt={user?.name || 'User'}
                    loading="eager"
                    onError={e => {
                      // Don't hide the image element, let the AvatarFallback component handle it
                      console.log('[MobileMenu] Avatar image failed to load:', user?.avatar);
                    }}
                  />
                  <AvatarFallback className="bg-[#F2F0EB] text-[#8FBC8F]">
                    {getInitials(user?.name || 'User')}
                  </AvatarFallback>
                </Avatar>
              </div>
              <div className="ml-3">
                <div className="text-sm font-medium text-[#666666] font-display">{user.name}</div>
                <div className="text-xs font-medium text-[#999999]">{user.email}</div>
              </div>
            </div>
            <div className="mt-4 space-y-0.5">
              <Link
                href="/profile"
                className="block px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#5F8575] hover:bg-[#F2F0EB] rounded-lg transition-colors"
                onClick={onClose}
              >
                <span className="inline-flex mr-3">
                  <User className="h-5 w-5 text-[#8FBC8F]" aria-hidden="false" />
                </span>
                My Profile
              </Link>
              <Link
                href="/messages"
                className="block px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#5F8575] hover:bg-[#F2F0EB] rounded-lg transition-colors"
                onClick={onClose}
              >
                <span className="inline-block w-5 mr-3 opacity-80">💬</span>
                Messages
                {unreadMessageCount > 0 && (
                  <span className="ml-2 inline-flex items-center justify-center h-5 w-5 text-xs font-medium text-white bg-[#E07A5F] rounded-full">
                    {unreadMessageCount > 99 ? '99+' : unreadMessageCount}
                  </span>
                )}
              </Link>

              <Link
                href="/favorites"
                className="block px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#5F8575] hover:bg-[#F2F0EB] rounded-lg transition-colors"
                onClick={onClose}
              >
                <span className="inline-flex mr-3">
                  <Heart className="h-5 w-5 text-[#8FBC8F]" aria-hidden="false" />
                </span>
                My Favorites
              </Link>

              {((user as any)?.isTeacher || (user as any)?.isInstructor) && (
                <Link
                  href="/teacher/payment-setup"
                  className="block px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#5F8575] hover:bg-[#F2F0EB] rounded-lg transition-colors"
                  onClick={onClose}
                >
                  <span className="inline-flex mr-3">
                    <Clock className="h-5 w-5 text-[#E07A5F]" aria-hidden="false" />
                  </span>
                  Payment Setup
                </Link>
              )}
              <button
                className="block w-full text-left px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#5F8575] hover:bg-[#F2F0EB] rounded-lg transition-colors"
                onClick={() => {
                  window.dispatchEvent(new CustomEvent('open-settings-dialog'));
                  onClose();
                }}
              >
                <span className="inline-flex mr-3">
                  <Settings className="h-5 w-5 text-[#8FBC8F]" aria-hidden="false" />
                </span>
                Settings
              </button>
              <div className="border-t border-[#D2B48C]/20 my-2.5"></div>
              <button
                onClick={handleLogout}
                className="block w-full text-left px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#E05252] rounded-lg transition-colors"
              >
                <span className="inline-block w-5 mr-3 opacity-80">🚪</span>
                Sign out
              </button>
            </div>
          </div>
        ) : (
          <div className="pt-4 pb-3 border-t border-[#D2B48C]/20">
            <div className="mt-3 space-y-1">
              <AuthDialog
                trigger={
                  <Button
                    variant="ghost"
                    className="w-full justify-start px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#E07A5F] hover:bg-[#F2F0EB] rounded-lg transition-colors h-auto"
                    onClick={onClose}
                  >
                    <LogIn className="h-5 w-5 mr-3 text-[#E07A5F]" />
                    Sign in
                  </Button>
                }
                defaultTab="login"
                onClose={onClose}
              />
              <AuthDialog
                trigger={
                  <Button
                    variant="ghost"
                    className="w-full justify-start px-4 py-2.5 text-sm font-medium text-[#666666] hover:text-[#E07A5F] hover:bg-[#F2F0EB] rounded-lg transition-colors h-auto"
                    onClick={onClose}
                  >
                    <UserPlus className="h-5 w-5 mr-3 text-[#E07A5F]" />
                    Create account
                  </Button>
                }
                defaultTab="register"
                onClose={onClose}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
