import { useState, useEffect } from 'react';
import { Link } from '../../../components/ui/NextLink';
import { useLocation } from '../../../lib/next-router-utils';
import { useAuth } from '@/features/auth';
import { useMessages } from '@/contexts/MessageContext';
import { useConversation } from '@/features/messaging/ConversationContext';
import { MobileMenu } from './MobileMenu';
import { DirectSettingsDialog } from '@/features/settings/components/DirectSettingsDialog';
import { AuthDialog } from '@/features/auth/components/AuthDialog';
import { signOut } from '@/lib/supabase-singleton';
import {
  MessageSquare,
  Menu,
  CalendarCheck,
  LogIn,
  Settings,
  ShieldCheck,
  Bell,
  Loader2,
  UserPlus,
  Heart,
  Plus,
  Calendar,
  CheckCircle,
  Clock,
  User,
  ChevronRight,
  Info,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getInitials, formatDistanceToNow } from '@/lib/utils';
import { MessageDropdown } from '@/features/messaging/components/MessageDropdown';

// import { startChatWithUser } from '@/features/messaging/components/ChatPopupManager'; // Temporarily removed

// Add ConversationWithMessages type definition with unreadCount
// Using a fallback type definition if the import fails
let BaseConversationWithMessages: any;
try {
  BaseConversationWithMessages = require('@shared/schema').ConversationWithMessages;
} catch (error) {
  // Fallback type if the import fails
  BaseConversationWithMessages = {
    id: '',
    participants: [],
    createdAt: '',
    updatedAt: '',
    messages: [],
  };
}

// Extend the ConversationWithMessages type to include unreadCount
interface ConversationWithMessages {
  id: string;
  participants: any[];
  createdAt: string;
  updatedAt: string;
  messages: any[];
  unreadCount?: number;
}

export function Header() {
  const [, setLocation] = useLocation();
  const { user, logoutMutation } = useAuth();

  // Safely try to use messaging contexts
  let unreadCount = 0;
  let conversations: any[] = [];

  try {
    const messagesContext = useMessages();
    unreadCount = messagesContext.unreadCount || 0;
  } catch (error) {
    console.warn('Messages context not available in Header:', error);
  }

  try {
    const conversationContext = useConversation();
    conversations = conversationContext.conversations || [];
  } catch (error) {
    console.warn('Conversation context not available in Header:', error);
  }

  // Use user from AuthContext
  const displayUser = user;
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMessagesOpen, setMessagesOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(prev => !prev);
  };

  const handleLogout = async () => {
    console.log('[Header] Handling logout click');

    try {
      // First try to use the mutation if available
      if (logoutMutation) {
        console.log('[Header] Using logoutMutation');
        logoutMutation.mutate();
      } else {
        // Fallback to direct signOut
        console.log('[Header] Using direct signOut function');
        await signOut();

        // Force reload the page to ensure clean state
        console.log('[Header] Reloading page after signOut');
        setTimeout(() => {
          window.location.href = '/';
        }, 500);
      }
    } catch (error) {
      console.error('[Header] Error during logout:', error);

      // Last resort - clear localStorage and reload
      console.log('[Header] Using last resort logout method');
      localStorage.clear();
      window.location.href = '/';
    }
  };

  // Filter and prepare conversations for the dropdown
  const validConversations = displayUser
    ? conversations
      .filter(conversation => {
        return (
          conversation.participants &&
          conversation.participants.some(p => p.id !== displayUser.id)
        );
      })
      .slice(0, 6)
    : [];

  return (
    <header
      className="fixed top-0 left-0 right-0 bg-white shadow-md z-[9999] border-b border-gray-200 w-full h-16"
      id="main-header"
    >
      <div className="max-w-6xl mx-auto px-5 md:px-6 h-full">
        <div className="flex justify-between h-full items-center">
          {/* Logo and Main Navigation */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="font-medium flex items-center">
                <div className="ml-1">
                  <span className="text-xl font-bold">
                    <span className="text-[#84a59d]/80">session</span>
                    <span className="text-[#e07a5f]/80">hub</span>
                  </span>
                </div>
              </Link>
            </div>
            {/* Removed Explore navigation as requested */}
          </div>

          {/* Desktop Right Navigation */}
          <div className="hidden sm:flex sm:items-center sm:space-x-2">
            {displayUser ? (
              <>
                {/* Messages Dropdown */}
                <DropdownMenu open={isMessagesOpen} onOpenChange={setMessagesOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative p-2 rounded-full"
                      id="messages-trigger"
                    >
                      <MessageSquare className="h-5 w-5" />
                      {unreadCount > 0 && (
                        <Badge
                          className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-[10px] bg-[#E07A5F] text-white rounded-full"
                          aria-label={`${unreadCount} unread messages`}
                        >
                          {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-80 p-0 header-dropdown messages-dropdown"
                    align="end"
                    side="bottom"
                    sideOffset={8}
                    avoidCollisions={true}
                    sticky="always"
                  >
                    <div className="flex justify-between items-center px-4 py-3 border-b">
                      <h3 className="font-semibold">Messages</h3>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full"
                          onClick={() => setLocation('/messages')}
                        >
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full"
                          onClick={() => {
                            // Add the logic to open a new conversation
                            setMessagesOpen(false);
                            setLocation('/messages');
                          }}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="max-h-[400px] overflow-y-auto">
                      {validConversations.length > 0 ? (
                        validConversations.map(conversation => {
                          // Get the other participant
                          const otherParticipant = conversation.participants.find(
                            p => p.id !== displayUser.id
                          );
                          if (!otherParticipant) return null;

                          // Get last message info
                          const lastMessage =
                            conversation.messages && conversation.messages.length > 0
                              ? conversation.messages[conversation.messages.length - 1]
                              : null;

                          // Check if there are unread messages in this conversation
                          const hasUnread =
                            conversation.unreadCount !== undefined && conversation.unreadCount > 0;

                          return (
                            <div
                              key={conversation.id}
                              className={`flex items-start gap-3 p-3 hover:bg-gray-50 cursor-pointer border-b ${hasUnread ? 'bg-blue-50/30' : ''}`}
                              onClick={() => {
                                // startChatWithUser(otherParticipant.id); // Temporarily disabled
                                setMessagesOpen(false);
                              }}
                            >
                              <Avatar className="h-10 w-10 mt-1">
                                <AvatarImage
                                  src={otherParticipant.avatar || ''}
                                  alt={otherParticipant.name || ''}
                                />
                                <AvatarFallback>
                                  {getInitials(otherParticipant.name || '')}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <div className="flex justify-between items-start">
                                  <p
                                    className={`text-sm font-medium truncate ${hasUnread ? 'text-black' : 'text-gray-700'}`}
                                  >
                                    {otherParticipant.name}
                                  </p>
                                  {lastMessage && (
                                    <p className="text-xs text-gray-500">
                                      {formatDistanceToNow(new Date(lastMessage.createdAt))}
                                    </p>
                                  )}
                                </div>
                                <p
                                  className={`text-xs truncate ${hasUnread ? 'font-medium text-black' : 'text-gray-600'}`}
                                >
                                  {lastMessage ? lastMessage.content : 'No messages yet'}
                                </p>
                              </div>
                              {hasUnread && (
                                <div className="h-2.5 w-2.5 rounded-full bg-blue-500 flex-shrink-0 mt-2"></div>
                              )}
                            </div>
                          );
                        })
                      ) : (
                        <div className="p-4 text-center text-sm text-gray-500">
                          <p>No conversations yet</p>
                          <p className="text-xs mt-1">Start chatting with teachers!</p>
                        </div>
                      )}
                    </div>

                    <div className="p-3 border-t text-center">
                      <Button
                        variant="ghost"
                        className="text-sm w-full text-primary"
                        onClick={() => {
                          setMessagesOpen(false);
                          setLocation('/messages');
                        }}
                      >
                        See all in Messages
                      </Button>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-8 w-8 rounded-full ml-2 ring-2 ring-white"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={displayUser?.user_metadata?.avatar_url || undefined}
                          alt={displayUser?.user_metadata?.full_name || 'User'}
                          loading="eager"
                          onError={e => {
                            console.log('[Header Trigger] Avatar image failed to load:', {
                              avatar_url: displayUser?.user_metadata?.avatar_url,
                              user_metadata: displayUser?.user_metadata
                            });
                          }}
                        />
                        <AvatarFallback className="bg-primary/10 text-primary">
                          {getInitials(displayUser?.user_metadata?.full_name || 'User')}
                        </AvatarFallback>
                      </Avatar>
                      {unreadCount > 0 && (
                        <div
                          className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-[#E07A5F] border-2 border-white"
                          title={`You have unread messages`}
                        />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-64 p-0 header-dropdown profile-dropdown shadow-xl border-0"
                    align="end"
                    side="bottom"
                    sideOffset={8}
                    avoidCollisions={true}
                    sticky="always"
                  >
                    {/* Header with profile picture and info */}
                    <div className="flex items-center gap-3 px-4 py-4 bg-gradient-to-r from-gray-50 to-white border-b">
                      <Avatar className="h-10 w-10 ring-2 ring-white shadow-sm">
                        <AvatarImage
                          src={displayUser?.user_metadata?.avatar_url || undefined}
                          alt={displayUser?.user_metadata?.full_name || 'User'}
                          loading="eager"
                          onError={e => {
                            console.log('[Header Dropdown] Avatar image failed to load:', {
                              avatar_url: displayUser?.user_metadata?.avatar_url,
                              displayUser: displayUser
                            });
                          }}
                          onLoad={() => {
                            console.log('[Header Dropdown] Avatar image loaded successfully:', displayUser?.user_metadata?.avatar_url);
                          }}
                        />
                        <AvatarFallback className="bg-primary/10 text-primary font-medium">
                          {getInitials(displayUser?.user_metadata?.full_name || 'User')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm text-gray-900 truncate">{displayUser?.user_metadata?.full_name || 'User'}</p>
                        <p className="text-xs text-gray-500 truncate">{displayUser?.email}</p>
                      </div>
                    </div>

                    {/* Menu items */}
                    <div className="py-1">
                      <Link href="/profile">
                        <div className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700">My Profile</span>
                        </div>
                      </Link>

                      <Link href="/settings">
                        <div className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150">
                          <Settings className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700">Settings</span>
                        </div>
                      </Link>

                      <Link href="/about">
                        <div className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150">
                          <Info className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700">About</span>
                        </div>
                      </Link>
                    </div>

                    {/* Sign out button */}
                    <div className="py-1 border-t">
                      <button
                        className="flex items-center gap-3 w-full px-4 py-3 text-left hover:bg-red-50 cursor-pointer transition-colors duration-150"
                        onClick={handleLogout}
                      >
                        <LogIn className="h-4 w-4 text-red-500 rotate-180" />
                        <span className="text-sm font-medium text-red-500">Sign Out</span>
                      </button>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <>
                <AuthDialog
                  trigger={
                    <Button variant="ghost" className="rounded-full">
                      <User className="h-4 w-4 mr-2" />
                      Sign In
                    </Button>
                  }
                  defaultTab="login"
                />
                <AuthDialog
                  trigger={
                    <Button className="rounded-full bg-[#E07A5F] hover:bg-[#C15A40]">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Sign Up
                    </Button>
                  }
                  defaultTab="register"
                />
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="sm:hidden">
            <Button
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full"
              onClick={toggleMobileMenu}
            >
              <Menu className="h-5 w-5" />
              {displayUser && unreadCount > 0 && (
                <div className="absolute top-0 right-0 h-2.5 w-2.5 rounded-full bg-[#E07A5F]" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        user={displayUser}
        unreadMessageCount={unreadCount}
        unreadNotificationCount={0}
        onLogout={handleLogout}
      />
    </header>
  );
}