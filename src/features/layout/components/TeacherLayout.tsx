import React, { ReactNode } from 'react';
import { Container, Row, Col, Nav } from 'react-bootstrap';
import { Link } from '../../../components/ui/NextLink';
import { useLocation } from '../../../lib/next-router-utils';
import { MainLayout } from './MainLayout';

interface TeacherLayoutProps {
  children: ReactNode;
}

export function TeacherLayout({ children }: TeacherLayoutProps) {
  const [location] = useLocation();

  const isActive = (path: string) => {
    return location === path || location.startsWith(`${path}/`);
  };

  return (
    <MainLayout>
      <Container className="py-4">
        <Row>
          <Col md={3} lg={2} className="mb-4">
            <div className="teacher-sidebar">
              <h5 className="mb-3">Teacher Dashboard</h5>
              <Nav className="flex-column">
                <Link href="/teacher/dashboard">
                  <Nav.Link active={isActive('/teacher/dashboard')}>Dashboard</Nav.Link>
                </Link>
                <Link href="/teacher/sessions">
                  <Nav.Link active={isActive('/teacher/sessions')}>My Sessions</Nav.Link>
                </Link>
                <Link href="/teacher/bookings">
                  <Nav.Link active={isActive('/teacher/bookings')}>Bookings</Nav.Link>
                </Link>
                <Link href="/teacher/payment-setup">
                  <Nav.Link active={isActive('/teacher/payment-setup')}>Payment Setup</Nav.Link>
                </Link>
                <Link href="/teacher/payment-dashboard">
                  <Nav.Link active={isActive('/teacher/payment-dashboard')}>
                    Payment Dashboard
                  </Nav.Link>
                </Link>
                <Link href="/teacher/payouts">
                  <Nav.Link active={isActive('/teacher/payouts')}>Payouts</Nav.Link>
                </Link>
                <Link href="/teacher/profile">
                  <Nav.Link active={isActive('/teacher/profile')}>Profile</Nav.Link>
                </Link>
              </Nav>
            </div>
          </Col>
          <Col md={9} lg={10}>
            {children}
          </Col>
        </Row>
      </Container>
    </MainLayout>
  );
}
