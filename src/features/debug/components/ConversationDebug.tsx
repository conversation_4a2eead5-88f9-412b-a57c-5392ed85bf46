import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { clearConversationCache } from '@/features/messaging/MessagingProvider';
import { apiRequest } from '@/lib/queryClient';

export function ConversationDebug() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchConversationsDirectly = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to debug conversations',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Clear localStorage cache first
      clearConversationCache();

      // Fetch conversations directly using a GET request with special marker
      // Using a special debug endpoint that we can ensure works
      const response = await fetch(`/api/direct/conversations/${user.id}?force=json`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'X-Force-Json': 'true',
        },
        credentials: 'include',
      });

      // Get both the text and status
      const text = await response.text();

      // Get header content type
      const contentType = response.headers.get('content-type') || '';

      // Try to parse as JSON
      try {
        const data = JSON.parse(text);
        setResult({
          status: response.status,
          statusText: response.statusText,
          isJson: true,
          data: data,
          conversationCount: Array.isArray(data) ? data.length : 'Not an array',
          contentType,
        });

        // If successful, replace data in cache to help the app
        if (Array.isArray(data) && data.length > 0) {
          localStorage.setItem(
            'cached_conversations',
            JSON.stringify({
              data: data,
              timestamp: Date.now(),
            })
          );

          toast({
            title: 'Success!',
            description: `Found ${data.length} conversations and updated your local cache.`,
          });

          // Force a page reload after 1 second
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      } catch (e) {
        // Not valid JSON, show as text
        setResult({
          status: response.status,
          statusText: response.statusText,
          isJson: false,
          text: text.substring(0, 500) + (text.length > 500 ? '...' : ''),
          contentType,
        });
      }
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Conversation Debugging</CardTitle>
        <CardDescription>Debug your conversations in the database directly</CardDescription>
      </CardHeader>
      <CardContent>
        {result && (
          <div className="text-sm border rounded-md p-4 bg-gray-50 overflow-auto max-h-80">
            <div>
              <strong>Status:</strong> {result.status} {result.statusText}
            </div>
            <div>
              <strong>Content-Type:</strong> {result.contentType}
            </div>

            {result.isJson ? (
              <>
                <div className="mb-2">
                  <strong>Conversation Count:</strong> {result.conversationCount}
                </div>
                <div className="font-mono text-xs whitespace-pre overflow-x-auto">
                  {JSON.stringify(result.data, null, 2)}
                </div>
              </>
            ) : (
              <>
                <div className="mb-2">
                  <strong>Response is not JSON</strong>
                </div>
                <div className="font-mono text-xs whitespace-pre overflow-x-auto border p-2 bg-gray-100">
                  {result.text}
                </div>
              </>
            )}
          </div>
        )}

        {error && (
          <div className="text-red-500 text-sm p-4 border border-red-200 rounded-md bg-red-50">
            {error}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={fetchConversationsDirectly} disabled={isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking Conversations...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Debug & Fix Conversations
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
