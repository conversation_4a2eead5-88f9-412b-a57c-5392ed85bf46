import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Download, Calendar, Copy, Check, AlertCircle } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface CalendarIntegrationProps {
  title: string;
  description: string;
  date: Date | string;
  duration: number;
  zoomLink?: string;
}

export function CalendarIntegration({
  title,
  description,
  date,
  duration,
  zoomLink = '',
}: CalendarIntegrationProps) {
  const [open, setOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  // Format the session date and time for calendar integrations
  const formatCalendarDate = (date: Date): string => {
    return date.toISOString().replace(/-|:|\.\d+/g, '');
  };

  // Set session end time based on duration
  const sessionStart = new Date(date);
  const sessionEnd = new Date(sessionStart);
  sessionEnd.setMinutes(sessionEnd.getMinutes() + duration);

  // Format dates for calendar links
  const startFormatted = formatCalendarDate(sessionStart);
  const endFormatted = formatCalendarDate(sessionEnd);

  // Create Google Calendar link
  const googleCalendarLink = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${startFormatted}/${endFormatted}&details=${encodeURIComponent(description)}&location=${encodeURIComponent(zoomLink)}&sprop=website:${encodeURIComponent(window.location.href)}`;

  // Create iCal/Outlook data
  const getICalData = () => {
    return `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//SessionHub//Calendar Integration//EN
CALSCALE:GREGORIAN
BEGIN:VEVENT
SUMMARY:${title}
DTSTART:${startFormatted}
DTEND:${endFormatted}
DESCRIPTION:${description.replace(/\\n/g, '\\n')}
LOCATION:${zoomLink}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR`;
  };

  // Create URL for iCal/Outlook download
  const createICalFileUrl = () => {
    const data = getICalData();
    const blob = new Blob([data], { type: 'text/calendar;charset=utf-8' });
    return URL.createObjectURL(blob);
  };

  // Handle copying calendar invite to clipboard
  const handleCopyCalendarInvite = () => {
    navigator.clipboard.writeText(getICalData());
    setCopied(true);

    toast({
      title: 'Calendar invite copied!',
      description: 'You can now paste it into your calendar application.',
    });

    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          <Calendar className="h-4 w-4" />
          <span>Add to Calendar</span>
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add to Calendar</DialogTitle>
          <DialogDescription>
            Choose your preferred calendar platform to add this session.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="google" className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="google">Google</TabsTrigger>
            <TabsTrigger value="outlook">Outlook/iCal</TabsTrigger>
            <TabsTrigger value="copy">Copy</TabsTrigger>
          </TabsList>

          <TabsContent value="google" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="py-4">
                <CardTitle className="text-base">Google Calendar</CardTitle>
                <CardDescription>Add this session directly to your Google Calendar</CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <div className="text-sm text-muted-foreground">Session: {formatDate(date)}</div>
                <a href={googleCalendarLink} target="_blank" rel="noopener noreferrer">
                  <Button size="sm">Add to Google Calendar</Button>
                </a>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="outlook" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="py-4">
                <CardTitle className="text-base">Outlook/iCal</CardTitle>
                <CardDescription>
                  Download a calendar file to import into Outlook, Apple Calendar, or other
                  applications
                </CardDescription>
              </CardHeader>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <div className="text-sm text-muted-foreground">Session: {formatDate(date)}</div>
                <a href={createICalFileUrl()} download={`${title.replace(/\s+/g, '-')}.ics`}>
                  <Button size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download iCal File
                  </Button>
                </a>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="copy" className="mt-4 space-y-4">
            <Card>
              <CardHeader className="py-4">
                <CardTitle className="text-base">Copy Calendar Invite</CardTitle>
                <CardDescription>
                  Copy the calendar data to your clipboard to paste into any calendar application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Tip</AlertTitle>
                  <AlertDescription>
                    Many calendar applications allow you to create events by pasting in iCal
                    formatted data
                  </AlertDescription>
                </Alert>
              </CardContent>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <div className="text-sm text-muted-foreground">Session: {formatDate(date)}</div>
                <Button size="sm" onClick={handleCopyCalendarInvite}>
                  {copied ? (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy to Clipboard
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
