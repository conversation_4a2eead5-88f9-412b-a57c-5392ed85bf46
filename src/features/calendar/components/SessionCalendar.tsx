import React, { useState, useMemo } from 'react';
import { format, isSameDay, startOfMonth, endOfMonth, startOfWeek, endOfWeek, eachDayOfInterval } from 'date-fns';
import { ChevronLeft, ChevronRight, Clock, GraduationCap, BookOpen, MapPin, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { SessionWithTeacher } from '@shared/schema';

interface SessionCalendarProps {
  teachingSessions: SessionWithTeacher[];
  learningSessions: SessionWithTeacher[];
  isLoading?: boolean;
  isOwnProfile: boolean;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  sessions: {
    teaching: SessionWithTeacher[];
    learning: SessionWithTeacher[];
  };
}

export function SessionCalendar({
  teachingSessions,
  learningSessions,
  isLoading = false,
  isOwnProfile,
}: SessionCalendarProps) {
  const navigate = useNavigate();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedSession, setSelectedSession] = useState<SessionWithTeacher | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [calendarView, setCalendarView] = useState<'all' | 'teaching' | 'learning'>('all');

  // Navigation functions
  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  // Helper function to safely parse session date
  const parseSessionDate = (dateString: string | undefined): Date | null => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  };

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 }); // Monday
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });

    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

    return days.map(date => {
      const isCurrentMonth = date.getMonth() === currentDate.getMonth();
      
      return {
        date,
        isCurrentMonth,
        sessions: {
          teaching: teachingSessions.filter(session => {
            const sessionDate = parseSessionDate(session.date);
            return sessionDate && isSameDay(sessionDate, date);
          }),
          learning: learningSessions.filter(session => {
            const sessionDate = parseSessionDate(session.date);
            return sessionDate && isSameDay(sessionDate, date);
          }),
        },
      };
    });
  }, [currentDate, teachingSessions, learningSessions]);

  // Handle session click
  const handleSessionClick = (session: SessionWithTeacher) => {
    setSelectedSession(session);
    setDialogOpen(true);
  };

  // Navigate to edit session page (replaces the manageAvailability function)
  const goToEditSession = (sessionId: string) => {
    setDialogOpen(false);
    navigate(`/edit-session/${sessionId}`);
  };

  // Navigate to session details
  const goToSession = (sessionId: string) => {
    setDialogOpen(false);
    navigate(`/sessions/${sessionId}`);
  };

  // Filter sessions based on view
  const getVisibleSessions = (day: CalendarDay) => {
    switch (calendarView) {
      case 'teaching':
        return day.sessions.teaching;
      case 'learning':
        return day.sessions.learning;
      default:
        return [...day.sessions.teaching, ...day.sessions.learning];
    }
  };

  // Get session type badge
  const getSessionTypeBadge = (session: SessionWithTeacher) => {
    const isTeaching = teachingSessions.some(s => s.id === session.id);

    return isTeaching ? (
      <Badge className="bg-blue-500 hover:bg-blue-600 text-white text-xs px-1.5 py-0.5 flex items-center gap-1">
        <GraduationCap className="h-3 w-3" />
        <span className="hidden sm:inline">Teaching</span>
      </Badge>
    ) : (
      <Badge className="bg-purple-500 hover:bg-purple-600 text-white text-xs px-1.5 py-0.5 flex items-center gap-1">
        <BookOpen className="h-3 w-3" />
        <span className="hidden sm:inline">Learning</span>
      </Badge>
    );
  };

  // Check if session is a teaching session
  const isTeachingSession = (session: SessionWithTeacher) => {
    return teachingSessions.some(s => s.id === session.id);
  };

  // Format session time safely
  const formatSessionTime = (session: SessionWithTeacher) => {
    const sessionDate = parseSessionDate(session.date);
    if (!sessionDate) return session.time_of_day || 'Time TBD';
    
    try {
      return format(sessionDate, 'HH:mm');
    } catch {
      return session.time_of_day || 'Time TBD';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">{format(currentDate, 'MMMM yyyy')}</h2>

          <div className="flex items-center ml-2">
            <Button variant="outline" size="icon" onClick={prevMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={nextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs
          value={calendarView}
          onValueChange={value => setCalendarView(value as 'all' | 'teaching' | 'learning')}
        >
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="teaching">Teaching</TabsTrigger>
            <TabsTrigger value="learning">Learning</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid grid-cols-7 gap-1 text-sm font-medium text-center">
        <div className="py-2">Mon</div>
        <div className="py-2">Tue</div>
        <div className="py-2">Wed</div>
        <div className="py-2">Thu</div>
        <div className="py-2">Fri</div>
        <div className="py-2">Sat</div>
        <div className="py-2">Sun</div>
      </div>

      <div className="grid grid-cols-7 gap-1 h-[600px]">
        {calendarDays.map((day, index) => {
          const visibleSessions = getVisibleSessions(day);
          const isToday = isSameDay(day.date, new Date());

          return (
            <div
              key={index}
              className={cn(
                'rounded-md border border-gray-200 p-1 overflow-hidden',
                'h-full min-h-[80px] flex flex-col',
                day.isCurrentMonth ? 'bg-white' : 'bg-gray-50',
                isToday && 'border-blue-400 border-2'
              )}
            >
              <div className="text-right p-1">
                <span
                  className={cn(
                    'text-xs font-medium',
                    isToday &&
                    'bg-blue-500 text-white rounded-full h-5 w-5 flex items-center justify-center float-right'
                  )}
                >
                  {format(day.date, 'd')}
                </span>
              </div>

              <div className="flex-1 overflow-y-auto p-0.5 space-y-1">
                {visibleSessions.length > 0 ? (
                  visibleSessions.map(session => (
                    <div
                      key={`${day.date.toISOString().split('T')[0]}-${session.id}-${isTeachingSession(session) ? 'teaching' : 'learning'}`}
                      className={cn(
                        'px-1.5 py-1 text-xs rounded cursor-pointer',
                        'hover:bg-gray-100 transition-colors duration-200',
                        'flex flex-col',
                        isTeachingSession(session)
                          ? 'bg-blue-50 border-l-2 border-blue-500'
                          : 'bg-purple-50 border-l-2 border-purple-500'
                      )}
                      onClick={() => handleSessionClick(session)}
                      data-session-id={session.id}
                      data-date={day.date.toISOString().split('T')[0]}
                    >
                      <div className="font-medium truncate">
                        {session.title || 'Untitled Session'}
                      </div>

                      <div className="flex items-center gap-1 text-gray-500 mt-0.5">
                        <Clock className="h-3 w-3" />
                        <span>{formatSessionTime(session)}</span>
                        <span className="ml-auto">
                          {isTeachingSession(session) ? (
                            <GraduationCap className="h-3 w-3 text-blue-500" />
                          ) : (
                            <BookOpen className="h-3 w-3 text-purple-500" />
                          )}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-xs text-gray-400 text-center py-2">
                    No sessions
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Session Details Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedSession?.title || 'Session Details'}</DialogTitle>
          </DialogHeader>

          {selectedSession && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {getSessionTypeBadge(selectedSession)}
                <Badge variant="outline">{selectedSession.type}</Badge>
                <Badge variant="outline">{selectedSession.skill_level}</Badge>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>{formatSessionTime(selectedSession)}</span>
                  <span className="text-gray-500">({selectedSession.duration_minutes} min)</span>
                </div>

                {selectedSession.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{selectedSession.location}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span>Max {selectedSession.max_participants} participants</span>
                </div>

                {selectedSession.price > 0 && (
                  <div className="text-lg font-semibold text-green-600">
                    ${selectedSession.price}
                  </div>
                )}
              </div>

              {selectedSession.description && (
                <div>
                  <h4 className="font-medium mb-1">Description</h4>
                  <p className="text-sm text-gray-600">{selectedSession.description}</p>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={() => goToSession(selectedSession.id)}
                  className="flex-1"
                >
                  View Details
                </Button>
                {isOwnProfile && isTeachingSession(selectedSession) && (
                  <Button
                    variant="outline"
                    onClick={() => goToEditSession(selectedSession.id)}
                  >
                    Edit
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}