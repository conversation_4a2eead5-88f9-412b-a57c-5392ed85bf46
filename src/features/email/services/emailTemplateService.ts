import React from 'react';
import { EmailTemplate, EmailTemplateCategory, EmailTemplateType, EmailTemplateData } from '../types/emailTypes';

// Define mock templates for each category
const welcomeTemplates: EmailTemplate[] = [
  {
    id: 'welcome-email-standard',
    name: 'Standard Welcome Email',
    subject: 'Your Session Booking Confirmation',
    preheader: 'Thank you for booking a session with {{teacherName}}',
    render: () => React.createElement('div', null, 'Welcome Email Template'),
  },
  {
    id: 'welcome-email-friendly',
    name: 'Friendly Welcome Email',
    subject: 'Welcome to Your Session!',
    preheader: 'We\'re excited to have you join us',
    render: () => React.createElement('div', null, 'Friendly Welcome Email Template'),
  },
];

const reminderTemplates: EmailTemplate[] = [
  {
    id: 'reminder-email-standard',
    name: 'Standard Reminder Email',
    subject: 'Reminder: Your Session is Coming Up',
    preheader: 'Your session with {{teacherN<PERSON>}} is coming up soon',
    render: () => React.createElement('div', null, 'Reminder Email Template'),
  },
  {
    id: 'reminder-email-friendly',
    name: 'Friendly Reminder Email',
    subject: 'Don\'t Forget Your Session Tomorrow!',
    preheader: 'A quick reminder about your upcoming session',
    render: () => React.createElement('div', null, 'Friendly Reminder Email Template'),
  },
];

const followUpTemplates: EmailTemplate[] = [
  {
    id: 'follow-up-email-standard',
    name: 'Standard Follow-up Email',
    subject: 'Thank You for Attending Our Session',
    preheader: 'We hope you enjoyed your session with {{teacherName}}',
    render: () => React.createElement('div', null, 'Follow-up Email Template'),
  },
  {
    id: 'follow-up-email-feedback',
    name: 'Feedback Request Email',
    subject: 'Your Feedback is Important to Us',
    preheader: 'Please share your thoughts on our recent session',
    render: () => React.createElement('div', null, 'Feedback Request Email Template'),
  },
];

// Organize templates into categories
const templateCategories: EmailTemplateCategory[] = [
  {
    id: EmailTemplateType.WELCOME,
    name: 'Welcome Emails',
    templates: welcomeTemplates,
  },
  {
    id: EmailTemplateType.REMINDER,
    name: 'Reminder Emails',
    templates: reminderTemplates,
  },
  {
    id: EmailTemplateType.FOLLOW_UP,
    name: 'Follow-up Emails',
    templates: followUpTemplates,
  },
];

/**
 * Gets all email template categories
 * 
 * @returns Array of email template categories
 */
export function getAllTemplateCategories(): EmailTemplateCategory[] {
  return templateCategories;
}

/**
 * Gets templates for a specific category
 * 
 * @param categoryId - Category ID
 * @returns Array of email templates or undefined if category not found
 */
export function getTemplatesByCategory(categoryId: string): EmailTemplate[] | undefined {
  const category = templateCategories.find(cat => cat.id === categoryId);
  return category?.templates;
}

/**
 * Gets a specific template by ID
 * 
 * @param templateId - Template ID
 * @returns Email template or undefined if not found
 */
export function getTemplateById(templateId: string): EmailTemplate | undefined {
  for (const category of templateCategories) {
    const template = category.templates.find(t => t.id === templateId);
    if (template) {
      return template;
    }
  }
  return undefined;
}

/**
 * Renders an email template to HTML
 * 
 * @param templateId - Template ID
 * @param data - Template data
 * @returns HTML string or null if template not found
 */
export function renderTemplateToHtml(templateId: string, data: EmailTemplateData): string | null {
  // In a real implementation, this would render the React component to HTML
  // For now, we'll just return a mock HTML string
  return `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
    <h1 style="color: #333;">Session Booking Confirmation</h1>
    <p>Hello ${data.studentName},</p>
    <p>Thank you for booking a session with ${data.teacherName}. We're looking forward to seeing you!</p>
    <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <h2 style="margin-top: 0; color: #555;">${data.sessionTitle}</h2>
      <p><strong>Date:</strong> ${data.sessionDate}</p>
      <p><strong>Time:</strong> ${data.sessionTime}</p>
      <p><strong>Duration:</strong> ${data.sessionDuration}</p>
      <p><strong>Teacher:</strong> ${data.teacherName}</p>
    </div>
    <p>If you need to reschedule or have any questions, please contact your teacher directly.</p>
    <p>Best regards,<br />The SessionHub Team</p>
  </div>`;
}

/**
 * Gets the subject line for an email template with merge tags processed
 * 
 * @param templateId - Template ID
 * @param data - Template data
 * @returns Processed subject line or null if template not found
 */
export function getProcessedSubject(templateId: string, data: EmailTemplateData): string | null {
  const template = getTemplateById(templateId);
  if (!template) {
    return null;
  }
  
  // Process merge tags in subject
  let subject = template.subject;
  Object.entries(data).forEach(([key, value]) => {
    if (value) {
      subject = subject.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
  });
  
  return subject;
}

/**
 * Gets the preheader for an email template with merge tags processed
 * 
 * @param templateId - Template ID
 * @param data - Template data
 * @returns Processed preheader or null if template not found or has no preheader
 */
export function getProcessedPreheader(templateId: string, data: EmailTemplateData): string | null {
  const template = getTemplateById(templateId);
  if (!template || !template.preheader) {
    return null;
  }
  
  // Process merge tags in preheader
  let preheader = template.preheader;
  Object.entries(data).forEach(([key, value]) => {
    if (value) {
      preheader = preheader.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }
  });
  
  return preheader;
}
