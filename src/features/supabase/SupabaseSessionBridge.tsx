import { useEffect, useState, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useFilter } from '@/contexts/FilterContext';
import { useSession } from '@/contexts/SessionContext';
import { getAvatarUrl, getCoverPhotoUrl, getSessionImageUrl } from '@/lib/utils';

/**
 * This component acts as a bridge between the Supabase data and the application contexts.
 * It fetches sessions and teachers from Supabase and makes them available to the app.
 */
export function SupabaseSessionBridge() {
  const queryClient = useQueryClient();
  const { setSearchMode } = useFilter();
  const sessionContext = useSession();
  const [initialized, setInitialized] = useState(false);
  const dataInjectedRef = useRef(false);
  const [sessions, setSessions] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);

  // Directly fetch data from the API
  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch sessions
        const sessionsResponse = await fetch('http://localhost:4004/api/supabase/sessions');
        if (sessionsResponse.ok) {
          const sessionsData = await sessionsResponse.json();
          console.log('[SupabaseSessionBridge] Fetched sessions from API:', sessionsData.length);
          setSessions(sessionsData);
        } else {
          console.error('[SupabaseSessionBridge] Failed to fetch sessions from API');
        }

        // Fetch teachers
        const teachersResponse = await fetch('http://localhost:4004/api/supabase/teachers');
        if (teachersResponse.ok) {
          const teachersData = await teachersResponse.json();
          console.log('[SupabaseSessionBridge] Fetched teachers from API:', teachersData.length);
          setTeachers(teachersData);
        } else {
          console.error('[SupabaseSessionBridge] Failed to fetch teachers from API');
        }
      } catch (error) {
        console.error('[SupabaseSessionBridge] Error fetching data:', error);
      }
    }

    fetchData();
  }, []);

  // Directly inject the sessions and teachers into the application
  useEffect(() => {
    // Only run this effect if we have data and haven't injected it yet
    if (sessions.length === 0 || teachers.length === 0 || dataInjectedRef.current) return;

    // Set the ref to true to prevent multiple injections
    dataInjectedRef.current = true;

    console.log('[SupabaseSessionBridge] Injecting data into application');

    try {
      // Map the Supabase sessions to the format expected by the application
      const mappedSessions = sessions.map((session: any) => {
        // Find the teacher for this session
        const teacher = teachers?.find((teacher: any) =>
          teacher.id === session.teacher_id ||
          teacher.user_id === session.teacher_id
        );

        // Log the session data to debug
        console.log(`[SupabaseSessionBridge] Processing session:`, {
          id: session.id,
          title: session.title,
          imageUrl: session.image_url,
          coverPhoto: session.cover_photo,
          teacherId: session.teacher_id,
          fullSession: session
        });

        // Use the utility function to get the session image URL
        let imageUrl = getSessionImageUrl(session.image_url || '');

        return {
          id: session.id,
          title: session.title,
          description: session.description || '',
          date: session.date,
          duration: session.duration || 60,
          price: session.price || 0,
          currency: session.currency || 'USD',
          maxParticipants: session.max_participants || 10,
          type: session.session_type || session.type || 'other',
          skillLevel: session.skill_level || 'All Levels',
          format: session.format || 'Online',
          language: session.language || 'English',
          isPublic: true, // Force to true to ensure visibility
          is_public: true, // Force to true to ensure visibility
          is_published: true, // Force to true to ensure visibility
          teacherId: session.teacher_id,
          imageUrl: imageUrl,
          coverPhoto: session.cover_photo || '',
          reviewCount: session.review_count || 0,
          rating: session.rating || 0,
          teacher: teacher ? {
            id: teacher.id || teacher.user_id,
            name: teacher.name || 'Unknown',
            username: teacher.username || 'unknown',
            avatar: teacher.avatar || teacher.profile_image || '',
          } : {
            id: session.teacher_id || 'unknown',
            name: 'Unknown Teacher',
            username: 'unknown',
            avatar: '',
          },
          // Add teacher property for backward compatibility
          teacher: teacher ? {
            id: teacher.id || teacher.user_id,
            name: teacher.name || 'Unknown',
            username: teacher.username || 'unknown',
            avatar: teacher.avatar || teacher.profile_image || '',
          } : {
            id: session.teacher_id || 'unknown',
            name: 'Unknown Teacher',
            username: 'unknown',
            avatar: '',
          },
        };
      });

      // Map the Supabase teachers to the format expected by the application
      const mappedTeachers = teachers.map((teacher: any) => {
        // Log the teacher data to debug
        console.log(`[SupabaseSessionBridge] Processing teacher:`, {
          id: teacher.id || teacher.user_id,
          name: teacher.name,
          avatar: teacher.avatar || teacher.profile_image,
          coverPhoto: teacher.cover_photo || teacher.coverPhoto,
          fullTeacher: teacher
        });

        // Use the utility functions to get the avatar and cover photo URLs
        let avatarUrl = getAvatarUrl(teacher.avatar || teacher.profile_image || '');
        let coverPhotoUrl = getCoverPhotoUrl(teacher.cover_photo || teacher.coverPhoto || '');

        return {
          id: teacher.id || teacher.user_id,
          name: teacher.name || 'Unknown',
          username: teacher.username || 'unknown',
          avatar: avatarUrl,
          coverPhoto: coverPhotoUrl,
          bio: teacher.bio || '',
          isTeacher: true,
          is_teacher: true,
          specializations: teacher.specializations || [],
          location: teacher.location || '',
          rating: teacher.rating || 0,
          reviewCount: teacher.review_count || 0,
        };
      });

      // Update the data in the query client cache with all possible query keys
      // This ensures that no matter which key is used, the data will be available
      queryClient.setQueryData(['/api/supabase/sessions'], mappedSessions);
      queryClient.setQueryData(['/api/sessions'], mappedSessions);
      queryClient.setQueryData(['supabase-sessions'], mappedSessions);
      queryClient.setQueryData(['sessions'], mappedSessions);

      queryClient.setQueryData(['/api/supabase/teachers'], mappedTeachers);
      queryClient.setQueryData(['/api/teachers'], mappedTeachers);
      queryClient.setQueryData(['/api/teachers/top'], mappedTeachers);
      queryClient.setQueryData(['supabase-teachers'], mappedTeachers);
      queryClient.setQueryData(['teachers'], mappedTeachers);

      // Force a refetch of the sessions query to ensure the SessionContext gets updated
      queryClient.invalidateQueries({ queryKey: ['/api/supabase/sessions'] });

      console.log('[SupabaseSessionBridge] Data injected:', {
        sessions: mappedSessions.length,
        teachers: mappedTeachers.length
      });

      // Set initialized to true after the first injection
      if (!initialized) {
        setInitialized(true);

        // Set search mode to sessions to ensure sessions are displayed
        setSearchMode('sessions');
      }
    } catch (error) {
      console.error('[SupabaseSessionBridge] Error injecting data:', error);
      // Reset the ref so we can try again
      dataInjectedRef.current = false;
    }
  }, [sessions, teachers, queryClient, initialized, setSearchMode]);

  // This component doesn't render anything
  return null;
}
