import { createContext, useContext, ReactNode } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseData } from '../../hooks/use-supabase-data';
import { getAvatarUrl, getCoverPhotoUrl } from '../../lib/utils';

// Mock data generation functions removed - only using real database data

// Define the context type
interface SupabaseDataContextType {
  sessions: any[];
  teachers: any[];
  isLoading: boolean;
  error: Error | null;
}

// Create the context
const DirectSupabaseContext = createContext<SupabaseDataContextType | undefined>(undefined);

// Provider component
export function DirectSupabaseProvider({ children }: { children: ReactNode }) {
  const queryClient = useQueryClient();
  const { sessions, teachers, isLoading, error } = useSupabaseData();

  // Only log important data in development mode and only once per render cycle
  if (process.env.NODE_ENV === 'development') {
    // Use a simple cache to prevent excessive logging
    const logKey = `${sessions.length}-${teachers.length}-${isLoading}`;
    const lastLogKey = (window as any).__lastDirectSupabaseLogKey;

    if (lastLogKey !== logKey) {
      console.log('[DirectSupabaseProvider] Data summary:', {
        sessions: sessions.length,
        teachers: teachers.length,
        isLoading,
        hasError: !!error
      });
      (window as any).__lastDirectSupabaseLogKey = logKey;
    }
  }

  // Process teachers to ensure they have specializations properly set and enhanced image URLs
  const processedTeachers = teachers.map(teacher => {
    // Ensure specializations is an array
    const specializations = Array.isArray(teacher.specializations) ? teacher.specializations :
      (typeof teacher.specializations === 'string' ? [teacher.specializations] : []);

    // Process image URLs using utility functions
    const avatarUrl = getAvatarUrl(teacher.avatar || '');
    const originalCoverPhoto = teacher.cover_photo || teacher.coverPhoto;
    const coverPhotoUrl = originalCoverPhoto ? getCoverPhotoUrl(originalCoverPhoto) : '';

    // Use a fallback cover photo URL if none exists
    const finalCoverPhotoUrl = coverPhotoUrl || 'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop';

    return {
      ...teacher,
      specializations: specializations,
      // Enhanced image URLs
      avatar: avatarUrl,
      coverPhoto: finalCoverPhotoUrl,
      cover_photo: finalCoverPhotoUrl,
      // Make sure profile is included if it exists
      profile: teacher.profile || {
        specializations: specializations
      }
    };
  });

  // Process sessions to ensure they have the correct public/published flags, image URLs, and teacher data
  const processedSessions = sessions.map(session => {
    // Process image URL if needed
    let imageUrl = session.imageUrl || session.image_url || '';

    // Make sure we have both imageUrl and image_url properties
    const processedSession = {
      ...session,
      // Ensure both property names are present for compatibility
      isPublic: session.is_published !== undefined ? session.is_published :
        (session.isPublic !== undefined ? session.isPublic : true),
      is_public: session.is_published !== undefined ? session.is_published :
        (session.is_public !== undefined ? session.is_public : true),
      // Ensure both image URL properties are present
      imageUrl: imageUrl,
      image_url: imageUrl,
    };

    // Ensure teacher data is properly set
    if (session.teacher) {
      // Make sure teacher has all required properties
      processedSession.teacher = {
        ...session.teacher,
        id: session.teacher.id || session.teacher.user_id || session.teacher_id,
        user_id: session.teacher.user_id || session.teacher.id,
        isTeacher: session.teacher.is_teacher !== undefined ? session.teacher.is_teacher : true,
      };
    } else if (session.teacher_id) {
      // Try to find the teacher in the teachers array
      const matchingTeacher = teachers.find(teacher =>
        teacher.id === session.teacher_id || teacher.user_id === session.teacher_id
      );

      if (matchingTeacher) {
        processedSession.teacher = matchingTeacher;
      }
    }

    return processedSession;
  });

  // Always set the data in the query cache, even if empty (to avoid stale data)
  queryClient.setQueryData(['sessions'], processedSessions);
  queryClient.setQueryData(['/api/sessions'], processedSessions);
  queryClient.setQueryData(['supabase-sessions'], processedSessions);
  queryClient.setQueryData(['/api/supabase/sessions'], processedSessions);

  // Use the processed teachers with fixed specializations
  queryClient.setQueryData(['teachers'], processedTeachers);
  queryClient.setQueryData(['/api/teachers'], processedTeachers);
  queryClient.setQueryData(['supabase-teachers'], processedTeachers);
  queryClient.setQueryData(['/api/supabase/teachers'], processedTeachers);
  queryClient.setQueryData(['/api/teachers/top'], processedTeachers);

  // Provide the context value with processed data
  const contextValue = {
    sessions: processedSessions,
    teachers: processedTeachers,
    isLoading,
    error,
  };

  return (
    <DirectSupabaseContext.Provider value={contextValue}>
      {children}
    </DirectSupabaseContext.Provider>
  );
}

// Custom hook to use the Supabase data context
export const useDirectSupabase = () => {
  const context = useContext(DirectSupabaseContext);
  if (context === undefined) {
    throw new Error('useDirectSupabase must be used within a DirectSupabaseProvider');
  }
  return context;
}