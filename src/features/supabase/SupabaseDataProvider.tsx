import { createContext, ReactNode, useContext } from 'react';
import { useSupabaseData } from '@/hooks/useSupabaseData';

// Define the context type
interface SupabaseDataContextType {
  sessions: any[];
  teachers: any[];
  isLoading: boolean;
  error: Error | null;
}

// Create the context
const SupabaseDataContext = createContext<SupabaseDataContextType | undefined>(undefined);

// Provider component
export function SupabaseDataProvider({ children }: { children: ReactNode }) {
  const { sessions = [], teachers = [], isLoading, error } = useSupabaseData();

  // Provide the context value
  const contextValue = {
    sessions,
    teachers,
    isLoading,
    error: error as Error | null,
  };

  return (
    <SupabaseDataContext.Provider value={contextValue}>
      {children}
    </SupabaseDataContext.Provider>
  );
}

// Custom hook to use the context
export function useSupabaseDataContext() {
  const context = useContext(SupabaseDataContext);
  if (context === undefined) {
    throw new Error('useSupabaseDataContext must be used within a SupabaseDataProvider');
  }
  return context;
}
