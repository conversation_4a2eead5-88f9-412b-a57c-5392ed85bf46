import { useState } from 'react';
import { Review } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useAuth } from '@/features/auth/AuthContext';
import { ReviewList } from './ReviewList';
import { ReviewForm } from './ReviewForm';

interface ReviewSectionProps {
  sessionId: string;
  reviews: Review[];
  isLoading?: boolean;
}

export function ReviewSection({ sessionId, reviews, isLoading = false }: ReviewSectionProps) {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const { user, isLoading: isUserLoading } = useAuth();

  // Check if the current user has already reviewed this session
  const hasReviewed = user && reviews.some(review => review.reviewer_id === user.id);

  // Calculate average rating
  const averageRating =
    reviews.length > 0
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
      : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Reviews</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {reviews.length} {reviews.length === 1 ? 'review' : 'reviews'}
            {reviews.length > 0 && ` • ${averageRating.toFixed(1)} average rating`}
          </p>
        </div>

        {user && !hasReviewed && (
          <Button onClick={() => setShowReviewForm(true)}>Write a Review</Button>
        )}
      </div>

      <ReviewList reviews={reviews} isLoading={isLoading} />

      <Dialog open={showReviewForm} onOpenChange={setShowReviewForm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Write a Review</DialogTitle>
          </DialogHeader>
          <ReviewForm
            sessionId={sessionId}
            onSuccess={() => setShowReviewForm(false)}
            onCancel={() => setShowReviewForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
