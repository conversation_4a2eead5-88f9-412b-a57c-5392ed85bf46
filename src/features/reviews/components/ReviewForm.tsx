import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { StarRating } from '@/components/ui/star-rating';
import { reviewsApi } from '@/lib/api';
import { useQueryClient } from '@tanstack/react-query';

interface ReviewFormProps {
  sessionId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ReviewForm({ sessionId, onSuccess, onCancel }: ReviewFormProps) {
  const [rating, setRating] = useState<number>(0);
  const [comment, setComment] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast({
        title: 'Rating required',
        description: 'Please select a rating before submitting your review.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await reviewsApi.create(sessionId, {
        rating,
        comment: comment.trim() || undefined,
      });

      if (result) {
        toast({
          title: 'Review submitted',
          description: 'Thank you for your feedback!',
        });

        // Invalidate the reviews query to refresh the list
        queryClient.invalidateQueries({ queryKey: [`/api/sessions/${sessionId}/reviews`] });

        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: 'Error',
          description: 'Failed to submit your review. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">Your Rating</label>
        <StarRating value={rating} onChange={setRating} size="lg" interactive />
      </div>

      <div className="space-y-2">
        <label htmlFor="comment" className="text-sm font-medium">
          Your Review (Optional)
        </label>
        <Textarea
          id="comment"
          placeholder="Share your experience with this session..."
          value={comment}
          onChange={e => setComment(e.target.value)}
          rows={4}
        />
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting || rating === 0}>
          {isSubmitting ? 'Submitting...' : 'Submit Review'}
        </Button>
      </div>
    </form>
  );
}
