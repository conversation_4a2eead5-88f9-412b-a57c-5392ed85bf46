import { createContext, ReactNode, useContext, useState, useEffect, useCallback } from 'react';
import { useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { apiRequest } from '../../lib/queryClient';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../auth/AuthContext';
import { UserWithProfile } from '../../lib/types';
import { AppError, ErrorCategory, ErrorSeverity, handleError } from '../../lib/error-handler';
import { supabase } from '../../lib/supabase';

interface ProfileContextType {
  updateUserMutation: UseMutationResult<UserWithProfile, Error, { id: string; userData: Partial<UserWithProfile> }>;
  updateTeacherStatusMutation: UseMutationResult<
    UserWithProfile,
    Error,
    {
      userId: string;
      teacherData: Partial<UserWithProfile>;
    }
  >;
  updateTeacherProfileMutation: UseMutationResult<
    any,
    Error,
    {
      userId: string;
      profileData: any;
    }
  >;
  topTeachers: UserWithProfile[];
  getTeacher: (id: string) => Promise<UserWithProfile | null>;
  isLoadingTeachers: boolean;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Map Supabase user/profile data to UserWithProfile (which is UserProfile)
function mapSupabaseProfile(data: any): UserWithProfile {
  console.log('Raw profile data from Supabase:', data);

  return {
    id: data.user_id || data.id,
    user_id: data.user_id || data.id,
    full_name: data.name || data.full_name,
    email: data.email || data.user_email,
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString(),
    bio: data.bio,
    avatar_url: data.avatar || data.avatar_url,
    location: data.location,
    timezone: data.timezone,
    phone: data.phone,
    date_of_birth: data.date_of_birth,
    emergency_contact: data.emergency_contact,
  } as UserWithProfile;
}

export function ProfileProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user, refetchUser } = useAuth();
  const [topTeachers, setTopTeachers] = useState<UserWithProfile[]>([]);
  const [isLoadingTeachers, setIsLoadingTeachers] = useState(false);

  // Fetch top teachers from Supabase on mount
  useEffect(() => {
    const fetchTopTeachers = async () => {
      try {
        setIsLoadingTeachers(true);

        // First try to fetch from Supabase API
        try {
          const { data } = await apiRequest('/api/supabase/teachers', {
            method: 'GET',
          });

          console.log('Supabase teacher data:', data);

          // Filter to only include teachers (is_teacher = true)
          const teachersOnly = Array.isArray(data) ? (data as any[]).filter((teacher: any) => teacher.is_teacher === true) : [];

          console.log('Filtered Supabase teachers (is_teacher=true):', teachersOnly);

          // Transform snake_case properties to camelCase
          const transformedData = teachersOnly.map(mapSupabaseProfile);

          console.log('Transformed Supabase teacher data:', transformedData);
          setTopTeachers(transformedData);
          return; // Exit if Supabase data was successfully fetched
        } catch (supabaseError) {
          console.error('Error fetching teachers from Supabase:', supabaseError);
          // Continue to fallback
        }

        // Fallback to original API if Supabase fails
        console.log('Falling back to original API for teachers');
        const { data } = await apiRequest('/api/teachers/top?limit=10', {
          method: 'GET',
        });

        console.log('Original teacher data from API:', data);

        // Transform snake_case properties to camelCase
        const transformedDataFallback = Array.isArray(data) ? (data as any[]).map(mapSupabaseProfile) : [];

        console.log('All transformed teacher data:', transformedDataFallback);
        setTopTeachers(transformedDataFallback);
      } catch (error) {
        console.error('Error fetching top teachers:', error);

        // Use standardized error handling
        handleError(error, 'Failed to load top teachers', undefined, {
          action: 'fetchTopTeachers',
          silent: true, // Don't show toast for this background operation
        });
      } finally {
        setIsLoadingTeachers(false);
      }
    };

    fetchTopTeachers();
  }, []);

  // Mutation for updating user
  const updateUserMutation = useMutation({
    mutationFn: async ({ id, userData }: { id: string; userData: Partial<UserWithProfile> }) => {
      try {
        console.log(`Sending PUT request to /api/users/${id} with data:`, userData);

        const { data } = await apiRequest(`/api/users/${id}`, {
          method: 'PUT',
          body: JSON.stringify(userData),
        });

        console.log('User update response:', data);
        return data as unknown as UserWithProfile;
      } catch (error) {
        console.error('Update user error:', error);
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred while updating your profile');
        }
      }
    },
    onSuccess: async (updatedUser: UserWithProfile) => {
      console.log('[ProfileContext] User update successful:', updatedUser);

      // Set the recently updated flag immediately for cache bypass
      localStorage.setItem('profile_recently_updated', Date.now().toString());

      // Clear all cached profile data for this user
      if (updatedUser?.id) {
        localStorage.removeItem(`user_profile_${updatedUser.id}`);
        localStorage.removeItem(`user_profile_${updatedUser.id}_time`);
        localStorage.removeItem(`user_${updatedUser.id}_profile_data`);
        localStorage.removeItem(`user_${updatedUser.id}_social_accounts`);
        console.log(`[ProfileContext] Cleared all cached data for updated user ${updatedUser.id}`);
      }

      if (user && updatedUser.id === user.id) {
        // Update the current user in the auth context
        await refetchUser();
      }

      // Invalidate any queries that might use this user data
      queryClient.invalidateQueries({ queryKey: ['/api/users/profile'] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${updatedUser.id}`] });

      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Update user error:', error);

      // Determine error category based on error message
      let category = ErrorCategory.UNKNOWN;
      let customMessage = '';

      if (error.message.includes('validation')) {
        category = ErrorCategory.VALIDATION;
        customMessage = 'Please check the information you provided and try again.';
      } else if (
        error.message.includes('Network Error') ||
        error.message.includes('Failed to fetch')
      ) {
        category = ErrorCategory.NETWORK;
      } else if (error.message.includes('permission') || error.message.includes('not authorized')) {
        category = ErrorCategory.PERMISSION;
        customMessage = "You don't have permission to update this profile.";
      }

      // Use standardized error handling with the determined category
      handleError(error, customMessage || 'Failed to update your profile', undefined, {
        category,
        action: 'updateUser',
        userId: updateUserMutation.variables?.id,
      });
    },
  });

  // Mutation for updating teacher status
  const updateTeacherStatusMutation = useMutation({
    mutationFn: async ({ userId, teacherData }: { userId: string; teacherData: Partial<UserWithProfile> }) => {
      try {
        console.log(
          `Sending PUT request to /api/users/${userId}/teacher-status with data:`,
          teacherData
        );

        const { data } = await apiRequest(`/api/users/${userId}/teacher-status`, {
          method: 'PUT',
          body: JSON.stringify(teacherData),
        });

        console.log('Teacher status update response:', data);
        return data as unknown as UserWithProfile;
      } catch (error) {
        console.error('Update teacher status error:', error);
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred while updating teacher status');
        }
      }
    },
    onSuccess: async (updatedUser: UserWithProfile) => {
      console.log('[ProfileContext] Teacher status update successful:', updatedUser);

      if (user && updatedUser.id === user.id) {
        // Update the current user in the auth context
        await refetchUser();
      }

      // Invalidate any queries that might use this user data
      queryClient.invalidateQueries({ queryKey: ['/api/users/profile'] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${updatedUser.id}`] });

      toast({
        title: 'Teacher status updated',
        description: updatedUser.isTeacher
          ? 'You are now registered as a teacher'
          : 'Your teacher status has been updated',
      });
    },
    onError: (error: Error) => {
      console.error('Update teacher status error:', error);

      // Determine error category based on error message
      let category = ErrorCategory.UNKNOWN;
      let customMessage = '';

      if (error.message.includes('validation')) {
        category = ErrorCategory.VALIDATION;
        customMessage = 'Please check the information you provided and try again.';
      } else if (error.message.includes('permission') || error.message.includes('not authorized')) {
        category = ErrorCategory.PERMISSION;
        customMessage = "You don't have permission to update teacher status.";
      }

      // Use standardized error handling with the determined category
      handleError(error, customMessage || 'Failed to update teacher status', undefined, {
        category,
        action: 'updateTeacherStatus',
        userId: updateTeacherStatusMutation.variables?.userId,
      });
    },
  });

  // Mutation for updating teacher profile
  const updateTeacherProfileMutation = useMutation({
    mutationFn: async ({ userId, profileData }: { userId: string; profileData: any }) => {
      try {
        console.log(`Sending PUT request to /api/users/${userId}/profile with data:`, profileData);

        const { data } = await apiRequest(`/api/users/${userId}/profile`, {
          method: 'PUT',
          body: JSON.stringify(profileData),
        });

        console.log('Teacher profile update response:', data);
        return data;
      } catch (error) {
        console.error('Update teacher profile error:', error);
        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error('An unexpected error occurred while updating teacher profile');
        }
      }
    },
    onSuccess: async (data: any) => {
      console.log('[ProfileContext] Teacher profile update successful:', data);

      if (user && data.userId === user.id) {
        // Update the current user in the auth context
        await refetchUser();
      }

      // Invalidate any queries that might use this user data
      queryClient.invalidateQueries({ queryKey: ['/api/users/profile'] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${data.userId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/users/${data.userId}/profile`] });

      toast({
        title: 'Profile updated',
        description: 'Your teacher profile has been updated successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Update teacher profile error:', error);

      // Use standardized error handling with appropriate context
      handleError(error, 'Failed to update your teacher profile', undefined, {
        action: 'updateTeacherProfile',
        userId: updateTeacherProfileMutation.variables?.userId,
      });
    },
  });

  // Function to get a teacher by ID from Supabase
  const getTeacher = useCallback(async (id: string): Promise<UserWithProfile | null> => {
    try {
      // Check if profile was recently updated - if so, COMPLETELY BYPASS CACHE
      const recentlyUpdated = localStorage.getItem('profile_recently_updated');
      let shouldBypassCache = false;

      if (recentlyUpdated) {
        const timeSinceUpdate = Date.now() - parseInt(recentlyUpdated);
        if (timeSinceUpdate < 30000) { // 30 seconds
          console.log(`Profile was recently updated (${Math.round(timeSinceUpdate / 1000)}s ago), forcing fresh fetch`);
          shouldBypassCache = true;

          // Clear ALL cached data for this user immediately
          localStorage.removeItem(`user_profile_${id}`);
          localStorage.removeItem(`user_profile_${id}_time`);
          localStorage.removeItem(`user_${id}_profile_data`);
          localStorage.removeItem(`user_${id}_social_accounts`);

          console.log(`[ProfileContext] Cleared all cached data for user ${id} due to recent update`);
        }
      }

      // Only check cache if we're not bypassing it
      if (!shouldBypassCache) {
        const cachedUserData = localStorage.getItem(`user_profile_${id}`);
        const cachedTime = localStorage.getItem(`user_profile_${id}_time`);
        let cachedUser = null;

        if (cachedUserData && cachedTime) {
          try {
            cachedUser = JSON.parse(cachedUserData);
            const timeSinceCache = Date.now() - parseInt(cachedTime);
            console.log(`Found cached user data for ID ${id}`, cachedUser);

            // Only use cache if it's less than 5 minutes old (300000ms) instead of 1 hour
            if (timeSinceCache < 300000) {
              console.log(`Using recent cached profile for ${id} (${Math.round(timeSinceCache / 1000)}s old)`);
              return cachedUser;
            } else {
              console.log(`Cached profile for ${id} is too old (${Math.round(timeSinceCache / 1000)}s), fetching fresh data`);
            }
          } catch (e) {
            console.log(`Error parsing cached data for ${id}, will fetch fresh:`, e);
          }
        }
      }

      console.log(`Fetching fresh profile data for user ${id} from Supabase...`);

      // Fetch fresh data from Supabase
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', id)  // Try user_id first since that's the auth.users.id
        .single();

      if (error) {
        console.error('Error fetching teacher from Supabase:', error);

        // If profile doesn't exist (PGRST116 error), try alternative fetch by id field
        if (error.code === 'PGRST116') {
          console.log(`[ProfileContext] No profile found by user_id, trying id field for user ${id}`);

          // Try fetching by id field instead
          const { data: altData, error: altError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', id)
            .single();

          if (altError) {
            console.error('Error fetching by id field:', altError);

            // If still no profile found, try to create it (only for authenticated user)
            if (altError.code === 'PGRST116') {
              console.log(`[ProfileContext] No profile found for user ${id}, attempting to create one`);

              try {
                const { data: authData, error: authError } = await supabase.auth.getUser();

                if (authError || !authData?.user) {
                  console.error('Error getting authenticated user for profile creation:', authError);
                  return null;
                }

                // Only create profile if this is the current authenticated user
                if (authData.user.id === id) {
                  console.log(`[ProfileContext] Creating profile for authenticated user ${id}`);

                  const newProfile = {
                    id: id, // This should be the user's UUID
                    user_id: id,
                    name: authData.user.user_metadata?.full_name ||
                      authData.user.user_metadata?.name ||
                      authData.user.email?.split('@')[0] ||
                      'User',
                    email: authData.user.email,
                    avatar: authData.user.user_metadata?.avatar_url || null,
                    bio: null,
                    timezone: 'UTC',
                    is_teacher: false,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                  };

                  const { data: createdProfile, error: createError } = await supabase
                    .from('user_profiles')
                    .insert([newProfile])
                    .select('*')
                    .single();

                  if (createError) {
                    console.error('Error creating profile:', createError);

                    // If creation failed due to duplicate key, try fetching the existing profile again
                    if (createError.code === '23505') {
                      console.log(`[ProfileContext] Profile creation failed due to duplicate key, trying to fetch existing profile for ${id}`);

                      // Try both user_id and id fields
                      let existingProfile: any = null;

                      const { data: existingByUserId } = await supabase
                        .from('user_profiles')
                        .select('*')
                        .eq('user_id', id)
                        .single();

                      if (existingByUserId) {
                        existingProfile = existingByUserId;
                      } else {
                        const { data: existingById } = await supabase
                          .from('user_profiles')
                          .select('*')
                          .eq('id', id)
                          .single();

                        if (existingById) {
                          existingProfile = existingById;
                        }
                      }

                      if (existingProfile) {
                        console.log('Found existing profile after duplicate error:', existingProfile);
                        const transformedUser = mapSupabaseProfile(existingProfile);

                        // Cache the found profile
                        if (!shouldBypassCache) {
                          try {
                            localStorage.setItem(`user_profile_${id}`, JSON.stringify(transformedUser));
                            localStorage.setItem(`user_profile_${id}_time`, Date.now().toString());
                            console.log(`Cached found existing profile for user ${id}`);
                          } catch (error) {
                            console.error('Error caching user data:', error);
                          }
                        }

                        return transformedUser;
                      }
                    }

                    return null;
                  }

                  console.log('Successfully created profile:', createdProfile);
                  const transformedUser = mapSupabaseProfile(createdProfile);

                  // Cache the newly created profile
                  if (!shouldBypassCache) {
                    try {
                      localStorage.setItem(`user_profile_${id}`, JSON.stringify(transformedUser));
                      localStorage.setItem(`user_profile_${id}_time`, Date.now().toString());
                      console.log(`Cached newly created profile data for user ${id}`);
                    } catch (error) {
                      console.error('Error caching user data:', error);
                    }
                  }

                  return transformedUser;
                } else {
                  console.log(`[ProfileContext] Cannot create profile for user ${id} - not authenticated as this user`);
                  return null;
                }
              } catch (createError) {
                console.error('Error in profile creation process:', createError);
                return null;
              }
            }

            return null;
          } else {
            // Found profile by id field
            console.log('Found profile by id field:', altData);
            const transformedUser = mapSupabaseProfile(altData);

            // Cache the fresh data
            if (!shouldBypassCache) {
              try {
                localStorage.setItem(`user_profile_${id}`, JSON.stringify(transformedUser));
                localStorage.setItem(`user_profile_${id}_time`, Date.now().toString());
                console.log(`Cached profile found by id field for user ${id}`);
              } catch (error) {
                console.error('Error caching user data:', error);
              }
            }

            return transformedUser;
          }
        }

        return null;
      }

      if (!data) {
        console.log('No teacher found with ID:', id);
        return null;
      }

      console.log('Fresh profile data fetched from Supabase:', data);
      const transformedUser = mapSupabaseProfile(data);

      // Cache the fresh data (but only if we're not in bypass mode)
      if (!shouldBypassCache) {
        try {
          localStorage.setItem(`user_profile_${id}`, JSON.stringify(transformedUser));
          localStorage.setItem(`user_profile_${id}_time`, Date.now().toString());
          console.log(`Cached fresh profile data for user ${id}`);
        } catch (error) {
          console.error('Error caching user data:', error);
        }
      } else {
        console.log(`Skipped caching for user ${id} due to bypass mode`);
      }

      return transformedUser;
    } catch (error) {
      console.error('Error in getTeacher:', error);
      return null;
    }
  }, []);

  // Create the context value
  const contextValue: ProfileContextType = {
    updateUserMutation,
    updateTeacherStatusMutation,
    updateTeacherProfileMutation,
    topTeachers,
    getTeacher,
    isLoadingTeachers,
  };

  return <ProfileContext.Provider value={contextValue}>{children}</ProfileContext.Provider>;
}

// Custom hook to use the profile context
export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}
