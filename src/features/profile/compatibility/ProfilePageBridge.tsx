import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ProfileHeader } from '../components/ProfileHeader';
import { ProfileEditForm } from '../components/ProfileEditForm';
import { SessionsTabs } from '@/features/sessions/components/SessionsTabs';
import { TeacherReviews } from '@/features/reviews/components/TeacherReviews';

// Simple auth context hook - using any to bypass type issues
const useAuthContext = () => {
  try {
    const AuthContext = require('@/contexts/AuthContext');
    return AuthContext.useAuthContext() || { authUser: null };
  } catch {
    return { authUser: null };
  }
};

// Simple profile hook - using any to bypass type issues  
const useProfile = () => {
  try {
    const profileHook = require('@/hooks/use-profile');
    return profileHook.useProfile() || {};
  } catch {
    return {};
  }
};

interface SocialAccount {
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'website';
  username: string;
  url: string;
}

export function ProfilePageBridge() {
  const { authUser } = useAuthContext();
  const queryClient = useQueryClient();
  const profileData = useProfile();

  // State management
  const [isEditing, setIsEditing] = useState(false);
  const [socialAccounts, setSocialAccounts] = useState<SocialAccount[]>([]);
  const [isProfilePrivate, setIsProfilePrivate] = useState(false);
  const [coverPhotoPosition, setCoverPhotoPosition] = useState('center');

  // Fetch user profile data with proper error handling
  const {
    data: userWithProfile,
    isLoading: profileLoading,
    refetch: refetchProfile,
    error: profileError,
  } = useQuery({
    queryKey: ['/api/users/profile'],
    queryFn: async () => {
      if (!authUser?.id) {
        console.log('[ProfilePageBridge] No auth user, skipping profile fetch');
        return null;
      }

      console.log(`[ProfilePageBridge] Fetching user profile for user ${authUser.id}`);

      // Check if profile was recently updated - if so, force fresh fetch
      const recentlyUpdated = localStorage.getItem('profile_recently_updated');
      const bypassCache = recentlyUpdated && (Date.now() - parseInt(recentlyUpdated)) < 30000;

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (bypassCache) {
        headers['Cache-Control'] = 'no-cache';
        headers['Pragma'] = 'no-cache';
        console.log('[ProfilePageBridge] Bypassing cache due to recent update');
      }

      try {
        // Fetch from the profile endpoint
        const response = await fetch(`/api/users/profile`, {
          credentials: 'include',
          headers,
        });

        if (!response.ok) {
          if (response.status === 401) {
            console.error('[ProfilePageBridge] Unauthorized - user may need to log in again');
            return null;
          }
          if (response.status === 404) {
            console.error('[ProfilePageBridge] Profile not found');
            return null;
          }
          throw new Error(`Failed to fetch user profile: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('[ProfilePageBridge] Successfully fetched profile data:', data);

        // Clear the recently updated flag if fetch was successful
        if (bypassCache) {
          localStorage.removeItem('profile_recently_updated');
          console.log('[ProfilePageBridge] Cleared recently updated flag after successful fetch');
        }

        return data;
      } catch (error) {
        console.error('[ProfilePageBridge] Error fetching profile:', error);
        throw error;
      }
    },
    enabled: !!authUser?.id,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 or 404 errors
      if (error?.message?.includes('401') || error?.message?.includes('404')) {
        return false;
      }
      return failureCount < 3;
    },
    staleTime: 1000 * 60 * 2, // 2 minutes
    gcTime: 1000 * 60 * 5, // 5 minutes
  } as any);

  // Handle social media updates
  const handleSocialMediaUpdate = async (accounts: SocialAccount[]) => {
    if (!(userWithProfile as any)?.id || !profileData.updateInstructorProfileMutation) {
      console.error('[ProfilePageBridge] Missing userWithProfile or updateInstructorProfileMutation');
      return;
    }

    try {
      console.log('[ProfilePageBridge] Updating social media accounts:', accounts);

      // Save to localStorage first for immediate feedback
      localStorage.setItem(`user_${(userWithProfile as any).id}_social_accounts`, JSON.stringify(accounts));

      // Prepare the profile update data
      const profileDataToUpdate: any = {
        instagramUrl: accounts.find(acc => acc.platform === 'instagram')?.url || null,
        facebookUrl: accounts.find(acc => acc.platform === 'facebook')?.url || null,
        twitterUrl: accounts.find(acc => acc.platform === 'twitter')?.url || null,
        linkedinUrl: accounts.find(acc => acc.platform === 'linkedin')?.url || null,
        youtubeUrl: accounts.find(acc => acc.platform === 'youtube')?.url || null,
        tiktokUrl: accounts.find(acc => acc.platform === 'tiktok')?.url || null,
        website: accounts.find(acc => acc.platform === 'website')?.url || null,
      };

      // Update the profile
      await profileData.updateInstructorProfileMutation.mutateAsync({
        userId: (userWithProfile as any).id.toString(),
        profileData: profileDataToUpdate,
      });

      console.log('[ProfilePageBridge] Social media accounts updated successfully');
    } catch (error) {
      console.error('[ProfilePageBridge] Error updating social media accounts:', error);
      // Remove from localStorage if update failed
      if ((userWithProfile as any)?.id) {
        localStorage.removeItem(`user_${(userWithProfile as any).id}_social_accounts`);
      }
    }
  };

  // Initialize social accounts
  const initializeSocialAccounts = useCallback(() => {
    if (!userWithProfile) return;

    console.log('[ProfilePageBridge] Initializing social accounts from profile data');

    let accounts: SocialAccount[] = [];

    // Try to get saved accounts from localStorage first
    let savedAccounts = null;
    try {
      const savedAccountsStr = localStorage.getItem(`user_${(userWithProfile as any).id}_social_accounts`);
      if (savedAccountsStr) {
        savedAccounts = JSON.parse(savedAccountsStr);
        console.log('[ProfilePageBridge] Found saved social accounts in localStorage:', savedAccounts);
      }
    } catch (error) {
      console.error('[ProfilePageBridge] Error parsing saved social accounts:', error);
    }

    // If we have saved accounts and they're recent, use them
    if (savedAccounts && Array.isArray(savedAccounts)) {
      accounts = savedAccounts;
    } else {
      // Build from profile data (using any type to bypass strict typing)
      const profile = userWithProfile as any;

      if (profile.instagramUrl) {
        accounts.push({
          platform: 'instagram',
          username: profile.instagramUrl.split('/').pop() || 'instagram',
          url: profile.instagramUrl,
        });
      }

      if (profile.facebookUrl) {
        accounts.push({
          platform: 'facebook',
          username: profile.facebookUrl.split('/').pop() || 'facebook',
          url: profile.facebookUrl,
        });
      }

      if (profile.twitterUrl) {
        accounts.push({
          platform: 'twitter',
          username: profile.twitterUrl.split('/').pop() || 'twitter',
          url: profile.twitterUrl,
        });
      }

      if (profile.linkedinUrl) {
        accounts.push({
          platform: 'linkedin',
          username: profile.linkedinUrl.split('/').pop() || 'linkedin',
          url: profile.linkedinUrl,
        });
      }

      if (profile.youtubeUrl) {
        let youtubeUsername = profile.youtubeUrl.split('/').pop() || 'youtube';
        youtubeUsername = youtubeUsername.startsWith('@') ? youtubeUsername : `@${youtubeUsername}`;
        accounts.push({
          platform: 'youtube',
          username: youtubeUsername,
          url: profile.youtubeUrl,
        });
      }

      if (profile.tiktokUrl) {
        let tiktokUsername = profile.tiktokUrl.split('/').pop() || 'tiktok';
        tiktokUsername = tiktokUsername.startsWith('@') ? tiktokUsername : `@${tiktokUsername}`;
        accounts.push({
          platform: 'tiktok',
          username: tiktokUsername,
          url: profile.tiktokUrl,
        });
      }

      if (profile.website) {
        try {
          accounts.push({
            platform: 'website',
            username: new URL(profile.website).hostname,
            url: profile.website,
          });
        } catch (e) {
          accounts.push({
            platform: 'website',
            username: profile.website,
            url: profile.website.startsWith('http') ? profile.website : `https://${profile.website}`,
          });
        }
      }
    }

    setSocialAccounts(accounts);

    // Save to localStorage for future use
    try {
      localStorage.setItem(`user_${(userWithProfile as any).id}_social_accounts`, JSON.stringify(accounts));
    } catch (error) {
      console.error('[ProfilePageBridge] Error saving social accounts to localStorage:', error);
    }
  }, [userWithProfile]);

  // Effects
  useEffect(() => {
    if (userWithProfile) {
      console.log('[ProfilePageBridge] Profile loaded, initializing UI state');

      // Set initial privacy state based on profile data
      const profile = userWithProfile as any;
      setIsProfilePrivate(profile.profile?.showProfile === false);

      // Load cover photo position from local storage if available
      const savedPosition = localStorage.getItem(`coverPosition_${(userWithProfile as any).id}`);
      if (savedPosition) {
        setCoverPhotoPosition(savedPosition);
      }

      // Initialize social accounts
      initializeSocialAccounts();
    }
  }, [userWithProfile, initializeSocialAccounts]);

  // Loading state
  if (profileLoading || !authUser) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mb-4"></div>
        <p className="text-gray-600 text-center">Loading profile...</p>
      </div>
    );
  }

  // Error state
  if (profileError) {
    console.error('[ProfilePageBridge] Profile error:', profileError);
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <p className="text-red-600 text-center mb-4">
          Error loading profile: {(profileError as any)?.message || 'Unknown error'}
        </p>
        <button
          onClick={() => refetchProfile()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  // No profile data
  if (!userWithProfile) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <p className="text-gray-600 text-center mb-4">Profile not found</p>
        <button
          onClick={() => refetchProfile()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  // Determine if this is the user's own profile
  const isOwnProfile = authUser?.id === (userWithProfile as any).id;

  console.log('[ProfilePageBridge] Rendering profile page:', {
    userWithProfile: userWithProfile,
    isOwnProfile,
    isEditing,
    authUserId: authUser?.id,
    profileUserId: (userWithProfile as any)?.id
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {!isEditing ? (
          <div className="space-y-0">
            {/* Profile Header - Using minimal props to avoid type conflicts */}
            <ProfileHeader
              userWithProfile={userWithProfile as any}
              isOwnProfile={isOwnProfile}
              isEditing={isEditing}
              setIsEditing={setIsEditing}
              isRepositioningCover={false}
              setIsRepositioningCover={() => { }}
              coverPhotoPosition={coverPhotoPosition}
              setCoverPhotoPosition={setCoverPhotoPosition}
              isLoading={{ avatar: false, coverPhoto: false, coverPosition: false }}
              setIsLoading={() => { }}
              socialAccounts={socialAccounts}
            />

            {/* Sessions Section */}
            <div className="mt-8 mb-8">
              <SessionsTabs userId={(userWithProfile as any).id} isOwnProfile={isOwnProfile} />
            </div>

            {/* Reviews Section */}
            {((userWithProfile as any).isInstructor || (userWithProfile as any).isTeacher) && (
              <div className="mt-12 mb-8">
                <div className="max-w-5xl mx-auto px-4 sm:px-6 md:px-8">
                  <TeacherReviews teacherId={(userWithProfile as any).id.toString()} />
                </div>
              </div>
            )}
          </div>
        ) : (
          /* Profile Edit Form */
          <ProfileEditForm
            userWithProfile={userWithProfile as any}
            onCancel={() => setIsEditing(false)}
            isProfilePrivate={isProfilePrivate}
            setIsProfilePrivate={setIsProfilePrivate}
            socialAccounts={socialAccounts}
            setSocialAccounts={setSocialAccounts}
          />
        )}
      </div>
    </div>
  );
}
