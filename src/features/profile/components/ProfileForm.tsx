import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { UserProfile } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Loader2, User, Globe, Lock } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { PhoneInput } from '@/components/ui/phone-input';
import { CustomTimezoneSelect } from '@/components/ui/timezone-select';
import { SocialMediaPicker } from '@/features/common/components/SocialMediaPicker';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Social media account interface
interface SocialAccount {
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'website';
  username: string;
  url: string;
}

// Profile form schema
const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email'),
  bio: z.string().optional(),
  avatar: z.string().optional(),
  coverPhoto: z.string().optional(),
  timezone: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional().or(z.literal('')),
  instagram: z.string().optional(),
  facebook: z.string().optional(),
  twitter: z.string().optional(),
  linkedin: z.string().optional(),
  youtube: z.string().optional(),
  tiktok: z.string().optional(),
  specializations: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  education: z.string().optional(),
  experience: z.string().optional(),
  isInstructor: z.boolean().optional(),
});

export type ProfileFormValues = z.infer<typeof profileSchema>;

interface ProfileFormProps {
  user: UserProfile;
  initialValues: ProfileFormValues;
  socialAccounts: SocialAccount[];
  isProfilePrivate: boolean;
  isLoading: {
    profile: boolean;
    privacy: boolean;
  };
  isEditingSocialMedia: boolean;
  onSubmit: (values: ProfileFormValues) => void;
  onCancel: () => void;
  onSocialAccountsChange: (accounts: SocialAccount[]) => void;
  onProfilePrivacyChange: (isPrivate: boolean) => void;
}

export const ProfileForm: React.FC<ProfileFormProps> = ({
  user,
  initialValues,
  socialAccounts,
  isProfilePrivate,
  isLoading,
  isEditingSocialMedia,
  onSubmit,
  onCancel,
  onSocialAccountsChange,
  onProfilePrivacyChange,
}) => {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: initialValues,
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-8">
          {/* Personal Information Section */}
          <div className="border-t border-gray-200 pt-8">
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-[#84a59d]" />
              <h3 className="text-lg font-medium">Personal Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column - Basic Info */}
              <div className="space-y-5">
                {/* Full Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your full name" {...field} className="rounded-lg" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          className="rounded-lg"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <PhoneInput
                          placeholder="Enter phone number"
                          value={field.value}
                          onChange={field.onChange}
                          defaultCountry="US"
                          className="rounded-lg"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Right Column - Website and Timezone */}
              <div className="space-y-5">
                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website</FormLabel>
                      <FormControl>
                        <Input placeholder="yourwebsite.com" {...field} className="rounded-lg" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timezone</FormLabel>
                      <FormControl>
                        <CustomTimezoneSelect
                          value={field.value || ''}
                          onChange={value => {
                            console.log('[ProfileForm] Timezone changed to:', value);
                            field.onChange(value);
                          }}
                          className="rounded-lg"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Username (Read-only) */}
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-sm font-medium">
                    Username
                  </Label>
                  <div className="flex items-center">
                    <div className="relative w-full">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                        @
                      </span>
                      <Input
                        id="username"
                        value={user.email || ''}
                        className="rounded-lg bg-gray-50 pl-8"
                        readOnly
                        disabled
                      />
                    </div>
                    <div className="ml-2 text-xs text-gray-500">
                      <Lock className="h-3.5 w-3.5 inline-block" />
                    </div>
                  </div>
                </div>

                {/* Hidden fields for YouTube and TikTok */}
                <FormField
                  control={form.control}
                  name="youtube"
                  render={({ field }) => (
                    <FormItem className="hidden">
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="tiktok"
                  render={({ field }) => (
                    <FormItem className="hidden">
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Bio - Full width with Rich Text Editor */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-[#84a59d]" />
              <h3 className="text-lg font-medium">Bio</h3>
            </div>
            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RichTextEditor
                      value={field.value || ''}
                      onChange={field.onChange}
                      placeholder="Tell us about yourself"
                      minHeight="250px"
                      className="border-gray-200 focus-within:border-[#84a59d] focus-within:ring-1 focus-within:ring-[#84a59d]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Social Media and Profile Settings - Side by Side */}
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Left Column - Social Media */}
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Globe className="h-5 w-5 text-[#84a59d]" />
                  <h3 className="text-lg font-medium">Social Media</h3>
                </div>

                <div className="space-y-4">
                  <SocialMediaPicker
                    accounts={socialAccounts}
                    onChange={newAccounts => {
                      try {
                        console.log('Social media accounts changed');

                        // Update social accounts
                        onSocialAccountsChange(newAccounts);

                        // Update form values based on the new accounts
                        // Reset all social media URLs first
                        form.setValue('instagram', '', { shouldDirty: true });
                        form.setValue('facebook', '', { shouldDirty: true });
                        form.setValue('twitter', '', { shouldDirty: true });
                        form.setValue('linkedin', '', { shouldDirty: true });
                        form.setValue('website', '', { shouldDirty: true });
                        form.setValue('youtube', '', { shouldDirty: true });
                        form.setValue('tiktok', '', { shouldDirty: true });

                        // Set values based on new accounts
                        newAccounts.forEach(account => {
                          switch (account.platform) {
                            case 'instagram':
                              form.setValue('instagram', account.url, { shouldDirty: true });
                              break;
                            case 'facebook':
                              form.setValue('facebook', account.url, { shouldDirty: true });
                              break;
                            case 'twitter':
                              form.setValue('twitter', account.url, { shouldDirty: true });
                              break;
                            case 'linkedin':
                              form.setValue('linkedin', account.url, { shouldDirty: true });
                              break;
                            case 'website':
                              form.setValue('website', account.url, { shouldDirty: true });
                              break;
                            case 'youtube':
                              form.setValue('youtube', account.url, { shouldDirty: true });
                              console.log('Setting YouTube URL in form:', account.url);
                              break;
                            case 'tiktok':
                              form.setValue('tiktok', account.url, { shouldDirty: true });
                              console.log('Setting TikTok URL in form:', account.url);
                              break;
                          }
                        });

                        // Log all form values for debugging
                        const formValues = form.getValues();
                        console.log('Updated social media form values:', formValues);
                      } catch (error) {
                        console.error('Error updating social media accounts:', error);
                      }
                    }}
                  />
                </div>
              </div>

              {/* Right Column - Profile Settings */}
              <div className="space-y-6">
                {/* Profile Visibility */}
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <Lock className="h-5 w-5 text-[#84a59d]" />
                    <h3 className="text-lg font-medium">Profile Settings</h3>
                  </div>

                  <div className="space-y-4">
                    {/* Profile Visibility Card */}
                    <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <div className="flex-grow">
                        <h4 className="font-medium">Profile Visibility</h4>
                        <p className="text-sm text-gray-500">Control who can see your profile</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label
                          htmlFor="edit-profile-privacy"
                          className={isProfilePrivate ? 'text-gray-500' : 'text-green-600'}
                        >
                          {isProfilePrivate ? 'Private' : 'Public'}
                        </Label>
                        <div className="flex items-center gap-2">
                          <Switch
                            id="edit-profile-privacy"
                            checked={!isProfilePrivate}
                            onCheckedChange={checked => onProfilePrivacyChange(!checked)}
                            disabled={isLoading.privacy}
                          />
                          {isLoading.privacy && (
                            <Loader2 className="h-3.5 w-3.5 animate-spin text-gray-500" />
                          )}
                        </div>
                        {isProfilePrivate ? (
                          <Lock className="h-3.5 w-3.5 text-gray-500 ml-1" />
                        ) : (
                          <Globe className="h-3.5 w-3.5 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>

                    {/* Teacher Profile Toggle Card */}
                    <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                      <div className="flex-grow">
                        <h4 className="font-medium">Enable Teacher Profile</h4>
                        <p className="text-sm text-gray-500">
                          Allow others to book sessions with you
                        </p>
                      </div>
                      <FormField
                        control={form.control}
                        name="isInstructor"
                        render={({ field }) => {
                          // Force the value to be a boolean
                          const isEnabled = Boolean(field.value);

                          return (
                            <FormItem className="flex items-center gap-2">
                              <FormControl>
                                <Switch
                                  checked={isEnabled}
                                  onCheckedChange={checked => {
                                    console.log(
                                      '[ProfileForm] Teacher toggle changed to:',
                                      checked
                                    );
                                    field.onChange(checked);
                                    // Update the form value immediately to ensure UI consistency
                                    form.setValue('isInstructor', checked, {
                                      shouldDirty: true,
                                      shouldTouch: true,
                                      shouldValidate: true,
                                    });
                                  }}
                                />
                              </FormControl>
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-4 mt-8">
          <Button type="button" variant="outline" onClick={onCancel} className="rounded-full">
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading.profile || isEditingSocialMedia}
            className="rounded-full"
          >
            {isLoading.profile && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </form>
    </Form>
  );
};
