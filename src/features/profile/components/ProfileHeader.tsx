import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import {
  Edit,
  Mail,
  Phone,
  Globe,
  MapPin,
  Clock,
  Camera,
  Move
} from 'lucide-react';
import { UserProfile } from '@shared/schema';

interface SocialAccount {
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'website';
  username: string;
  url: string;
}

interface ProfileHeaderProps {
  userWithProfile: UserProfile;
  isOwnProfile: boolean;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  isRepositioningCover: boolean;
  setIsRepositioningCover: (value: boolean) => void;
  coverPhotoPosition: string;
  setCoverPhotoPosition: (value: string) => void;
  isLoading: {
    avatar: boolean;
    coverPhoto: boolean;
    coverPosition: boolean;
  };
  setIsLoading: (value: any) => void;
  socialAccounts?: SocialAccount[];
  setUserWithProfile?: (value: any) => void;
}

export const ProfileHeader = React.memo(function ProfileHeader({
  userWithProfile,
  isOwnProfile,
  isEditing,
  setIsEditing,
  isRepositioningCover,
  setIsRepositioningCover,
  coverPhotoPosition,
  setCoverPhotoPosition,
  isLoading,
  setIsLoading,
  socialAccounts = [],
  setUserWithProfile,
}: ProfileHeaderProps) {
  const { toast } = useToast();

  const formatTimezone = (timezone: string) => {
    try {
      const now = new Date();
      const timeInZone = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }).format(now);

      const zoneName = timezone.split('/').pop()?.replace(/_/g, ' ') || timezone;
      return `${timeInZone} (${zoneName})`;
    } catch (error) {
      return timezone;
    }
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(prev => ({ ...prev, avatar: true }));

    try {
      // Placeholder for avatar upload logic
      toast({
        title: 'Avatar Upload',
        description: 'Avatar upload functionality will be implemented.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Upload Failed',
        description: 'Failed to upload avatar. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(prev => ({ ...prev, avatar: false }));
    }
  };

  const handleCoverPhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsLoading(prev => ({ ...prev, coverPhoto: true }));

    try {
      // Placeholder for cover photo upload logic
      toast({
        title: 'Cover Photo Upload',
        description: 'Cover photo upload functionality will be implemented.',
        variant: 'default',
      });
    } catch (error) {
      toast({
        title: 'Upload Failed',
        description: 'Failed to upload cover photo. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(prev => ({ ...prev, coverPhoto: false }));
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Cover Photo Section */}
      <div className="relative h-48 bg-gradient-to-r from-[#84a59d] to-[#a8c8c0] overflow-hidden">
        {/* Cover photo not available in UserProfile schema */}

        {/* Cover Photo Upload Button */}
        {isOwnProfile && (
          <div className="absolute top-4 right-4 flex gap-2">
            {false && ( // coverPhoto not available in UserProfile schema
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsRepositioningCover(!isRepositioningCover)}
                className="rounded-full bg-white/70 hover:bg-white/90 text-gray-700 hover:text-gray-900 shadow-sm border border-gray-200"
                disabled={isLoading.coverPosition}
              >
                <Move className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="rounded-full bg-white/70 hover:bg-white/90 text-gray-700 hover:text-gray-900 shadow-sm border border-gray-200"
              disabled={isLoading.coverPhoto}
              onClick={() => document.getElementById('cover-photo-upload')?.click()}
            >
              <Camera className="h-4 w-4" />
            </Button>
            <input
              id="cover-photo-upload"
              type="file"
              accept="image/*"
              onChange={handleCoverPhotoUpload}
              className="hidden"
            />
          </div>
        )}

        {/* Avatar Section */}
        <div className="absolute -bottom-16 left-8">
          <div className="relative">
            <div className="w-32 h-32 rounded-full border-4 border-white bg-gray-100 overflow-hidden shadow-lg">
              {userWithProfile.avatar_url ? (
                <img
                  src={userWithProfile.avatar_url}
                  alt={userWithProfile.full_name || userWithProfile.email}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-[#84a59d] to-[#a8c8c0] flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">
                    {(userWithProfile.full_name || userWithProfile.email)?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            {/* Avatar Upload Button */}
            {isOwnProfile && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute bottom-0 right-0 rounded-full bg-white hover:bg-gray-50 text-gray-700 hover:text-gray-900 shadow-sm border border-gray-200 p-2 h-8 w-8"
                disabled={isLoading.avatar}
                onClick={() => document.getElementById('avatar-upload')?.click()}
              >
                <Camera className="h-3.5 w-3.5" />
              </Button>
            )}
            <input
              id="avatar-upload"
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
            />
          </div>
        </div>

        {/* User Info and Actions */}
        <div className="pt-20 px-4 flex flex-col md:flex-row md:items-start justify-between gap-6">
          <div className="space-y-3 flex-1">
            <div>
              <div className="flex items-center gap-3">
                <h1 className="text-2xl font-bold text-gray-900">
                  {userWithProfile.full_name || userWithProfile.email}
                </h1>
                {isOwnProfile && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="rounded-full bg-white/70 hover:bg-white/90 text-gray-700 hover:text-gray-900 shadow-sm border border-gray-200 p-2 h-8 w-8"
                    aria-label="Edit Profile"
                  >
                    <Edit className="h-3.5 w-3.5" />
                  </Button>
                )}
              </div>
              {/* isInstructor not available in UserProfile schema */}
            </div>

            {/* Contact and Personal Info */}
            <div className="flex items-center gap-3 text-gray-600">
              {userWithProfile.email && (
                <a
                  href={`mailto:${userWithProfile.email}`}
                  className="hover:text-gray-800 transition-colors"
                  title={userWithProfile.email}
                >
                  <Mail className="h-4 w-4" />
                </a>
              )}

              {userWithProfile.phone && (
                <a
                  href={`tel:${userWithProfile.phone}`}
                  className="hover:text-gray-800 transition-colors"
                  title={userWithProfile.phone}
                >
                  <Phone className="h-4 w-4" />
                </a>
              )}

              {/* Website not available in UserProfile schema */}

              {userWithProfile.location && (
                <div
                  className="hover:text-gray-800 transition-colors cursor-help"
                  title={userWithProfile.location}
                >
                  <MapPin className="h-4 w-4" />
                </div>
              )}

              {socialAccounts
                .filter(account => account.platform !== 'website')
                .map(account => (
                  <a
                    key={account.platform}
                    href={account.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-gray-800 transition-colors"
                    title={account.username}
                  >
                    <SocialIcon platform={account.platform} className="h-4 w-4" />
                  </a>
                ))}
            </div>

            {/* Timezone */}
            {userWithProfile.timezone && (
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-2 text-gray-400" />
                <span>{formatTimezone(userWithProfile.timezone)}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bio Section */}
      {userWithProfile.bio && (
        <div className="px-12 pb-6">
          {(() => {
            const bioText = userWithProfile.bio.replace(/<[^>]*>/g, '');
            const isLongBio = bioText.length > 300;

            const bioContent = (
              <div
                className="prose prose-sm max-w-none prose-p:my-2 prose-ul:my-2 prose-ol:my-2 prose-li:my-1 prose-headings:my-2"
                dangerouslySetInnerHTML={{ __html: userWithProfile.bio }}
                style={{
                  lineHeight: '1.5',
                }}
              />
            );

            return isLongBio ? (
              <ScrollArea className="h-32 w-full rounded-md border border-gray-200 p-4">
                {bioContent}
              </ScrollArea>
            ) : (
              bioContent
            );
          })()}
        </div>
      )}
    </div>
  );
});

// Helper component for social icons
function SocialIcon({ platform, className }: { platform: string; className?: string }) {
  switch (platform) {
    case 'instagram':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
        </svg>
      );
    case 'facebook':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385h-3.047v-3.47h3.047v-2.642c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953h-1.514c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385c5.738-.9 10.126-5.864 10.126-11.854z" />
        </svg>
      );
    case 'twitter':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
        </svg>
      );
    case 'linkedin':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
        </svg>
      );
    case 'youtube':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
        </svg>
      );
    case 'tiktok':
      return (
        <svg className={className} viewBox="0 0 24 24" fill="currentColor">
          <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z" />
        </svg>
      );
    case 'website':
      return (
        <svg
          className={className}
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="2" y1="12" x2="22" y2="12" />
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
        </svg>
      );
    default:
      return null;
  }
}