import React, { useMemo } from 'react';
import { UserProfile } from '@shared/schema';
import { SessionsTabs } from '@/features/sessions/components/SessionsTabs';
import { TeacherReviews } from '@/features/reviews/components/TeacherReviews';

interface ProfileTabsProps {
  userWithProfile: UserProfile;
  isOwnProfile: boolean;
}

export const ProfileTabs = React.memo(function ProfileTabs({
  userWithProfile,
  isOwnProfile,
}: ProfileTabsProps) {
  // Memoize tab visibility calculations to prevent unnecessary re-renders
  const { showTeachingTab, showLearningTab, defaultTab } = useMemo(() => {
    // If user is not a teacher, don't show the teaching tab
    // Note: isTeacher and profile properties not available in UserProfile schema
    const showTeaching = false; // Simplified for build compatibility

    // If user has hidden their learning sessions, don't show the learning tab
    const showLearning = isOwnProfile; // Simplified for build compatibility

    // Determine the default tab
    const defaultTabValue = showTeaching ? 'teaching' : 'learning';

    return {
      showTeachingTab: showTeaching,
      showLearningTab: showLearning,
      defaultTab: defaultTabValue,
    };
  }, [userWithProfile, isOwnProfile]);

  // If neither tab should be shown, don't render the component
  if (!showTeachingTab && !showLearningTab) {
    return null;
  }

  // Debug logs
  console.log('[ProfileTabs] Props:', { userWithProfile, isOwnProfile });
  console.log('[ProfileTabs] Tab visibility:', { showTeachingTab, showLearningTab, defaultTab });

  // Directly use SessionsTabs component which already has the proper styling
  return (
    <div className="w-full">
      <SessionsTabs
        userId={parseInt(userWithProfile.id)}
        mode={defaultTab as 'teaching' | 'learning'}
        isOwnProfile={isOwnProfile}
      />
    </div>
  );
});
