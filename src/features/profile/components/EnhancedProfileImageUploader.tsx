/**
 * Enhanced Profile Image Uploader Component
 * Uses advanced image processing, cropping, and drag-drop functionality
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { DragDropUploader } from '@/components/ui/drag-drop-uploader';
import { ImageCropper } from '@/components/ui/image-cropper';
import { CachedAvatar, CachedCoverPhoto } from '@/components/ui/cached-image';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { Edit } from 'lucide-react';
import { useAuth } from '@/features/auth/AuthContext';
import { updateProfileImage } from '@/services/profile.service';

interface EnhancedProfileImageUploaderProps {
  type: 'avatar' | 'cover';
  currentImageUrl?: string;
  onImageUploaded: (url: string) => void;
  className?: string;
  onEditTriggered?: () => void;
}

/**
 * EnhancedProfileImageUploader component
 * A comprehensive image uploader for profile avatars and cover photos
 */
export function EnhancedProfileImageUploader({
  type,
  currentImageUrl,
  onImageUploaded,
  className,
  onEditTriggered
}: EnhancedProfileImageUploaderProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [croppingImage, setCroppingImage] = useState<string | null>(null);
  const { toast } = useToast();

  // Use a try-catch block to handle the case when AuthContext isn't available yet
  let user: any = null;
  try {
    const authContext = useAuth();
    user = authContext.user;
    console.log('[EnhancedProfileImageUploader] Auth user:', user);
  } catch (error) {
    console.warn('[EnhancedProfileImageUploader] AuthContext not available yet:', error);
  }

  // Get the current user ID from the URL if possible
  const [, params] = window.location.pathname.match(/\/profile\/([^\/]+)/) || [];
  const urlUserId = params;

  if (urlUserId) {
    console.log('[EnhancedProfileImageUploader] User ID from URL:', urlUserId);
  }

  // If we have both a user from auth and a URL user ID, log if they don't match
  if (user?.id && urlUserId && String(user.id) !== urlUserId) {
    console.warn('[EnhancedProfileImageUploader] Auth user ID does not match URL user ID');
    console.warn(`[EnhancedProfileImageUploader] Auth: ${user.id}, URL: ${urlUserId}`);
  }

  // Check Supabase auth session on component mount
  React.useEffect(() => {
    const checkSupabaseSession = async () => {
      try {
        // Import the Supabase auth helper
        const { getSupabaseUser } = await import('@/lib/supabase-auth');

        // Get the current user
        const user = await getSupabaseUser();

        if (user) {
          console.log('[EnhancedProfileImageUploader] Authentication check successful, user is authenticated:', user.id);
        } else {
          console.warn('[EnhancedProfileImageUploader] No authenticated user found');

          toast({
            title: 'Authentication Notice',
            description: 'Your session may have expired. Please refresh the page and try again.',
            variant: 'default'
          });
        }
      } catch (error) {
        console.error('[EnhancedProfileImageUploader] Error checking auth session:', error);
      }
    };

    checkSupabaseSession();
  }, [toast]);

  // Handle external edit triggering
  useEffect(() => {
    if (onEditTriggered) {
      // Store the reference to open dialog function for external access
      (window as any).openCoverPhotoDialog = () => setIsDialogOpen(true);
    }

    // Always set up avatar dialog function for external triggering when type is avatar
    if (type === 'avatar') {
      (window as any).openAvatarDialog = () => {
        setIsDialogOpen(true);
      };
    }

    return () => {
      if ((window as any).openCoverPhotoDialog) {
        delete (window as any).openCoverPhotoDialog;
      }
      if ((window as any).openAvatarDialog) {
        delete (window as any).openAvatarDialog;
      }
    };
  }, [onEditTriggered, type]);

  // Handle crop completion
  const handleCropComplete = useCallback(async (croppedBlob: Blob) => {
    if (!user?.id) {
      toast({
        title: 'Upload failed',
        description: 'You must be logged in to upload images',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsUploading(true);
      console.log('[EnhancedProfileImageUploader] Starting cropped upload process for', type);

      // Convert blob to file
      const fileName = `${type}-cropped-${Date.now()}.jpg`;
      const croppedFile = new File([croppedBlob], fileName, { type: 'image/jpeg' });

      // Show a loading toast
      toast({
        title: 'Uploading...',
        description: 'Please wait while we upload your cropped image',
        variant: 'default',
      });

      // Use our profile service to upload the image
      // This handles all the authentication and RLS policy compliance
      console.log('[EnhancedProfileImageUploader] Calling updateProfileImage with cropped file:', croppedFile);
      console.log('[EnhancedProfileImageUploader] Upload type:', type);
      const updatedProfile = await updateProfileImage(croppedFile, type);

      // Get the image URL from the updated profile
      const imageUrl = type === 'avatar' ? updatedProfile.avatar : updatedProfile.cover_photo;

      if (!imageUrl) {
        throw new Error(`Failed to get ${type} URL from updated profile`);
      }

      console.log('[EnhancedProfileImageUploader] Cropped upload successful, URL:', imageUrl);

      // Add a cache-busting parameter to the URL
      const cachedUrl = `${imageUrl}?t=${Date.now()}`;

      // Update the UI immediately with the new image
      if (type === 'cover') {
        const coverElements = document.querySelectorAll('.cover-photo, [alt*="cover"]');
        if (coverElements.length > 0) {
          console.log('[EnhancedProfileImageUploader] Updating cover elements in the DOM:', coverElements.length);
          coverElements.forEach(img => {
            if (img instanceof HTMLImageElement) {
              img.src = cachedUrl;
            } else if (img instanceof HTMLElement) {
              img.style.backgroundImage = `url(${cachedUrl})`;
            }
          });
        }
      } else {
        const avatarElements = document.querySelectorAll('img[alt*="avatar"], img[alt*="profile"]');
        if (avatarElements.length > 0) {
          console.log('[EnhancedProfileImageUploader] Updating avatar elements in the DOM:', avatarElements.length);
          avatarElements.forEach(img => {
            if (img instanceof HTMLImageElement) {
              img.src = cachedUrl;
            }
          });
        }
      }

      // Notify parent component with the URL
      onImageUploaded(cachedUrl);

      // Close the dialog
      setIsDialogOpen(false);
      setCroppingImage(null);
      setSelectedFile(null);

      // Show success toast
      toast({
        title: 'Upload successful',
        description: `Your ${type === 'avatar' ? 'profile picture' : 'cover photo'} has been updated`,
        variant: 'default'
      });
    } catch (error) {
      console.error('[EnhancedProfileImageUploader] Cropped upload failed:', error);

      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  }, [type, user, onImageUploaded, toast]);

  // Handle direct upload (without cropping)
  const handleDirectUpload = useCallback(async (file: File) => {
    if (!user?.id) {
      toast({
        title: 'Upload failed',
        description: 'You must be logged in to upload images',
        variant: 'destructive'
      });
      throw new Error('No user ID available');
    }

    try {
      setIsUploading(true);
      console.log('[EnhancedProfileImageUploader] Starting direct upload process for', type);

      // Show a loading toast
      toast({
        title: 'Uploading...',
        description: 'Please wait while we upload your image',
        variant: 'default',
      });

      console.log('[EnhancedProfileImageUploader] Preparing for auth checks');
      // Check authentication before upload
      console.log('[EnhancedProfileImageUploader] Starting auth checks');
      try {
        // Import the Supabase auth helper
        const { getWorkingUserId } = await import('@/lib/supabase-auth');

        // Get a working user ID
        console.log('[EnhancedProfileImageUploader] Attempting to get working user ID');
        try {
          const userId = await getWorkingUserId();
          console.log('[EnhancedProfileImageUploader] Got working user ID for upload:', userId);
        } catch (idError) {
          console.error('[EnhancedProfileImageUploader] Error getting working user ID:', idError);
          throw new Error('Authentication error: Unable to determine your user ID. Please refresh the page and try again.');
        }
      } catch (sessionError) {
        console.error('[EnhancedProfileImageUploader] Error checking authentication:', sessionError);

        throw new Error('Authentication error: Unable to verify your session. Please refresh the page and try again.');
      }



      // Use our profile service to upload the image
      // This handles all the authentication and RLS policy compliance
      console.log('[EnhancedProfileImageUploader] Proceeding to call updateProfileImage');
      console.log(`[EnhancedProfileImageUploader] Calling updateProfileImage for ${type}`);
      console.log('[EnhancedProfileImageUploader] Calling updateProfileImage with file:', file);
      const updatedProfile = await updateProfileImage(file, type);

      // Get the image URL from the updated profile
      const imageUrl = type === 'avatar' ? updatedProfile.avatar : updatedProfile.cover_photo;

      if (!imageUrl) {
        throw new Error(`Failed to get ${type} URL from updated profile`);
      }

      console.log('[EnhancedProfileImageUploader] Direct upload successful, URL:', imageUrl);

      // Add a cache-busting parameter to the URL
      const cachedUrl = `${imageUrl}?t=${Date.now()}`;

      // Update the UI immediately with the new image
      if (type === 'cover') {
        const coverElements = document.querySelectorAll('.cover-photo, [alt*="cover"]');
        if (coverElements.length > 0) {
          console.log('[EnhancedProfileImageUploader] Updating cover elements in the DOM:', coverElements.length);
          coverElements.forEach(img => {
            if (img instanceof HTMLImageElement) {
              img.src = cachedUrl;
            } else if (img instanceof HTMLElement) {
              img.style.backgroundImage = `url(${cachedUrl})`;
            }
          });
        }
      } else {
        const avatarElements = document.querySelectorAll('img[alt*="avatar"], img[alt*="profile"]');
        if (avatarElements.length > 0) {
          console.log('[EnhancedProfileImageUploader] Updating avatar elements in the DOM:', avatarElements.length);
          avatarElements.forEach(img => {
            if (img instanceof HTMLImageElement) {
              img.src = cachedUrl;
            }
          });
        }
      }

      // Notify parent component with the URL
      onImageUploaded(cachedUrl);

      // Show success toast
      toast({
        title: 'Upload successful',
        description: `Your ${type === 'avatar' ? 'profile picture' : 'cover photo'} has been updated`,
        variant: 'default'
      });

      // Close the dialog
      setIsDialogOpen(false);

      return cachedUrl;
    } catch (error) {
      console.error('[EnhancedProfileImageUploader] Upload failed:', error);

      // Check if this is an authentication error
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      const isAuthError = errorMessage.toLowerCase().includes('auth') ||
        errorMessage.toLowerCase().includes('session') ||
        errorMessage.toLowerCase().includes('jwt');

      if (isAuthError) {
        // Show a more specific toast for auth errors
        toast({
          title: 'Authentication Error',
          description: 'Your session has expired. Please refresh the page and try again.',
          variant: 'destructive'
        });

        // Try to refresh the auth session
        try {
          const { supabase } = await import('@/lib/supabase-singleton');
          await supabase.auth.refreshSession();
        } catch (refreshError) {
          console.error('[EnhancedProfileImageUploader] Failed to refresh session after error:', refreshError);
        }
      } else {
        // Show a generic error toast
        toast({
          title: 'Upload failed',
          description: errorMessage,
          variant: 'destructive'
        });
      }

      throw error;
    } finally {
      setIsUploading(false);
    }
  }, [type, user, onImageUploaded, toast, setIsDialogOpen]);

  // Render the current image
  const renderCurrentImage = () => {
    if (type === 'avatar') {
      console.log('DEBUG EnhancedProfileImageUploader - Rendering avatar uploader');
      return (
        <div className={cn('relative', className)}>
          <CachedAvatar
            src={currentImageUrl || ''}
            alt="Profile"
            className="transition-opacity cursor-pointer"
            onClick={() => {
              console.log('DEBUG: Avatar image clicked');
              setIsDialogOpen(true);
            }}
          />
          {/* Avatar edit button is now handled externally in ProfileHeader */}
        </div>
      );
    } else {
      // Add debug logging for cover photo uploader


      return (
        <div
          className={cn('relative w-full h-full', className)}
          role="region"
          aria-label="Cover photo"
        >
          <CachedCoverPhoto
            src={currentImageUrl || ''}
            alt="Cover"
            className="w-full h-full"
          />

          {/* Loading overlay when uploading */}
          {isUploading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20 rounded-md">
              <div className="text-white text-center p-4">
                <svg className="animate-spin h-8 w-8 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p>Uploading...</p>
              </div>
            </div>
          )}

          {/* Edit Cover Photo button - only show if not externally controlled */}
          {!onEditTriggered && (
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 rounded-full bg-white/70 hover:bg-white/90 z-10 cursor-pointer p-2"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[EnhancedProfileImageUploader] Edit Cover Photo button clicked');
                // Force dialog to open
                setIsDialogOpen(true);
              }}
              disabled={isUploading}
              aria-label="Edit Cover Photo"
            >
              {isUploading ? (
                <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              )}
            </Button>
          )}
        </div>
      );
    }
  };

  return (
    <>
      {/* Current image display */}
      {renderCurrentImage()}

      {/* Upload dialog */}
      <Dialog open={isDialogOpen} onOpenChange={(open) => {
        // Prevent closing the dialog during upload
        if (isUploading && !open) {
          return;
        }
        setIsDialogOpen(open);
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {type === 'avatar' ? 'Update Profile Picture' : 'Update Cover Photo'}
            </DialogTitle>
          </DialogHeader>

          {/* Loading overlay */}
          {isUploading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 rounded-md">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <svg className="animate-spin h-10 w-10 mx-auto mb-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <h3 className="text-lg font-medium mb-2">Uploading...</h3>
                <p className="text-gray-500 text-sm">Please wait while we upload and process your image.</p>
              </div>
            </div>
          )}

          {croppingImage ? (
            // Image cropping view
            <ImageCropper
              src={croppingImage}
              onCropComplete={handleCropComplete}
              aspectRatio={type === 'avatar' ? 1 : 3 / 1}
              circularCrop={type === 'avatar'}
              minWidth={type === 'avatar' ? 200 : 600}
              minHeight={type === 'avatar' ? 200 : 200}
            />
          ) : (
            // Image selection view
            <Tabs defaultValue="upload">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="upload">
                  📤 Upload
                </TabsTrigger>
                <TabsTrigger value="camera">
                  📷 Camera
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="pt-4">
                <DragDropUploader
                  onUpload={isUploading ? () => Promise.reject(new Error('Upload in progress')) : handleDirectUpload}
                  accept="image/*"
                  maxSize={5 * 1024 * 1024} // 5MB
                  showPreview={true}
                  label={`Upload ${type === 'avatar' ? 'Profile Picture' : 'Cover Photo'}`}
                  description={isUploading ? "Upload in progress..." : "Drag and drop an image here, or click to select"}
                >
                  <div className="flex justify-center mt-4">
                    <Button
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (selectedFile) {
                          const objectUrl = URL.createObjectURL(selectedFile);
                          setCroppingImage(objectUrl);
                        }
                      }}
                      disabled={!selectedFile || isUploading}
                    >
                      ✂️ Crop Image
                    </Button>
                  </div>
                </DragDropUploader>
              </TabsContent>

              <TabsContent value="camera" className="pt-4">
                <div className="space-y-4">
                  <div className="relative aspect-video bg-muted rounded-md overflow-hidden">
                    <video
                      id="camera-preview"
                      className="w-full h-full object-cover"
                      autoPlay
                      playsInline
                      muted
                    />

                    <div className="absolute inset-0 flex items-center justify-center">
                      <Button variant="secondary" onClick={() => {
                        // Initialize camera
                        navigator.mediaDevices.getUserMedia({ video: true })
                          .then(stream => {
                            const video = document.getElementById('camera-preview') as HTMLVideoElement;
                            if (video) {
                              video.srcObject = stream;
                            }
                          })
                          .catch(() => {
                            toast({
                              title: 'Camera access denied',
                              description: 'Please allow camera access to use this feature',
                              variant: 'destructive'
                            });
                          });
                      }}>
                        📷 Start Camera
                      </Button>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button onClick={() => {
                      // Capture image from camera
                      const video = document.getElementById('camera-preview') as HTMLVideoElement;
                      if (video && video.srcObject) {
                        const canvas = document.createElement('canvas');
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        const ctx = canvas.getContext('2d');
                        if (ctx) {
                          ctx.drawImage(video, 0, 0);
                          canvas.toBlob(blob => {
                            if (blob) {
                              const file = new File([blob], `camera-${Date.now()}.jpg`, { type: 'image/jpeg' });
                              setSelectedFile(file);
                              const objectUrl = URL.createObjectURL(file);
                              setCroppingImage(objectUrl);

                              // Stop the camera
                              const stream = video.srcObject as MediaStream;
                              stream.getTracks().forEach(track => track.stop());
                              video.srcObject = null;
                            }
                          }, 'image/jpeg', 0.95);
                        }
                      }
                    }}>
                      📸 Capture Photo
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
