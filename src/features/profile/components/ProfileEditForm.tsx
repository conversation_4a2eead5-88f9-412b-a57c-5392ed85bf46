import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UserProfile } from '@shared/schema';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/AuthContext';
import { supabase } from '@/lib/supabase';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { PhoneInput } from '@/components/ui/phone-input';
import { CustomTimezoneSelect } from '@/components/ui/timezone-select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SocialMediaPicker } from '@/features/common/components/SocialMediaPicker';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { getInitials, getDefaultCoverPhoto } from '@/lib/utils';
import {
  Loader2,
  User,
  Mail,
  Phone,
  Globe,
  MapPin,
  Shield,
  BookOpen,
  Presentation,
  Star,
  Edit,
  Share2,
  Video,
  Clock,
  AlertTriangle,
  MessageSquare,
  Trash2,
  Plus,
} from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

// Profile form schema
const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  bio: z.string().optional(),
  timezone: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional().refine((val) => {
    if (!val || val === '') return true; // Allow empty values
    // Add protocol if missing
    const urlToTest = val.startsWith('http://') || val.startsWith('https://') ? val : `https://${val}`;
    try {
      new URL(urlToTest);
      return true;
    } catch {
      return false;
    }
  }, {
    message: 'Please enter a valid website URL (e.g., example.com or https://example.com)'
  }),
  isInstructor: z.boolean().optional(),
  // Privacy settings
  showProfile: z.boolean().optional(),
  showTeachingSessions: z.boolean().optional(),
  showLearningSessions: z.boolean().optional(),
  showSocialLinks: z.boolean().optional(),
  showContact: z.boolean().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

interface SocialAccount {
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'website';
  username: string;
  url: string;
}

interface ProfileEditFormProps {
  userWithProfile: UserProfile;
  onCancel: () => void;
  isProfilePrivate: boolean;
  setIsProfilePrivate: (value: boolean) => void;
  socialAccounts: SocialAccount[];
  setSocialAccounts: (accounts: SocialAccount[]) => void;
}

export function ProfileEditForm({
  userWithProfile,
  onCancel,
  isProfilePrivate,
  setIsProfilePrivate,
  socialAccounts,
  setSocialAccounts,
}: ProfileEditFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user: authUser } = useAuth();

  // Profile form
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: userWithProfile.name || '',
      bio: userWithProfile.bio || '',
      timezone: userWithProfile.timezone || 'UTC',
      phone: userWithProfile.phone || '',
      website: userWithProfile.profile?.website || '',
      isInstructor: userWithProfile.isInstructor || false,
      // Privacy settings
      showProfile: userWithProfile.profile?.showProfile !== false,
      showTeachingSessions: userWithProfile.profile?.showTeachingSessions !== false,
      showLearningSessions: userWithProfile.profile?.showLearningSessions !== false,
      showSocialLinks: userWithProfile.profile?.showSocialLinks !== false,
      showContact: userWithProfile.profile?.showContact !== false,
    },
  });

  // Create a profile update mutation with improved error handling and timeout
  const updateProfileMutation = useMutation({
    mutationFn: async (profileData: ProfileFormValues) => {
      // Save form data to localStorage for backup
      try {
        localStorage.setItem(
          `user_${userWithProfile.id}_profile_data`,
          JSON.stringify(profileData)
        );
      } catch (error) {
        console.error('Error saving profile data to localStorage:', error);
      }

      // Update privacy settings based on form values
      setIsProfilePrivate(!profileData.showProfile);

      // Get the current Supabase session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error('No valid session found. Please log in again.');
      }

      // Create abort controller for timeout handling
      const abortController = new AbortController();
      const timeoutId = setTimeout(() => {
        abortController.abort();
      }, 30000); // 30 second timeout

      try {
        const { data, response } = await apiRequest('/api/users/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          credentials: 'include',
          signal: abortController.signal,
          body: JSON.stringify({
            displayName: profileData.name,
            bio: profileData.bio,
            website: profileData.website,
            phone: profileData.phone,
            timezone: profileData.timezone,
            profileImageUrl: userWithProfile.avatar,
            coverImageUrl: userWithProfile.coverPhoto,
            isTeacher: userWithProfile.isTeacher,
            // Include individual privacy settings as top-level fields
            showProfile: profileData.showProfile,
            showTeachingSessions: profileData.showTeachingSessions,
            showLearningSessions: profileData.showLearningSessions,
            showSocialLinks: profileData.showSocialLinks,
            showContact: profileData.showContact,
            // Include social media URLs from the current socialAccounts state
            instagramUrl: socialAccounts.find(acc => acc.platform === 'instagram')?.url || null,
            facebookUrl: socialAccounts.find(acc => acc.platform === 'facebook')?.url || null,
            twitterUrl: socialAccounts.find(acc => acc.platform === 'twitter')?.url || null,
            linkedinUrl: socialAccounts.find(acc => acc.platform === 'linkedin')?.url || null,
            youtubeUrl: socialAccounts.find(acc => acc.platform === 'youtube')?.url || null,
            tiktokUrl: socialAccounts.find(acc => acc.platform === 'tiktok')?.url || null,
          }),
        });

        clearTimeout(timeoutId);

        // Check for response success
        if (!response.ok) {
          const errorText = await response.text();
          let errorMessage = `Failed to update profile: ${response.status}`;

          if (response.status === 401) {
            errorMessage = 'Authentication expired. Please log in again.';
          } else if (response.status === 404) {
            errorMessage = 'Profile not found. Creating new profile...';
          } else if (response.status >= 500) {
            errorMessage = 'Server error. Please try again later.';
          } else if (errorText) {
            errorMessage += ` ${errorText}`;
          }

          throw new Error(errorMessage);
        }

        return data;
      } catch (error: any) {
        clearTimeout(timeoutId);

        if (error.name === 'AbortError') {
          throw new Error('Request timed out. Please check your connection and try again.');
        }

        throw error;
      }
    },
    onSuccess: (responseData) => {
      console.log('[ProfileEditForm] Profile update successful:', responseData);

      // PRESERVE SOCIAL ACCOUNTS DATA BEFORE CLEARING CACHE
      const userIdsToClean = [
        userWithProfile?.id,
        authUser?.id,
        String(userWithProfile?.id),
        String(authUser?.id)
      ].filter(Boolean);

      console.log('[ProfileEditForm] Clearing cached data for user IDs:', userIdsToClean);

      // Save current social accounts before clearing cache
      const currentSocialAccounts = socialAccounts;
      const socialAccountsBackup = JSON.stringify(currentSocialAccounts);

      userIdsToClean.forEach(userId => {
        try {
          localStorage.removeItem(`user_profile_${userId}`);
          localStorage.removeItem(`user_profile_${userId}_time`);
          localStorage.removeItem(`user_${userId}_profile_data`);
          // Don't remove social accounts - they should persist
          // localStorage.removeItem(`user_${userId}_social_accounts`);
          localStorage.removeItem(`coverPosition_${userId}`);
          console.log(`[ProfileEditForm] Cleared cached data for user ${userId} (kept social accounts)`);
        } catch (error: any) {
          console.error(`[ProfileEditForm] Error clearing cache for user ${userId}:`, error);
        }
      });

      // Restore social accounts immediately and update with latest URLs from the response
      if (responseData && userWithProfile?.id) {
        try {
          // Create updated social accounts based on the response data
          const updatedSocialAccounts = [];

          if (responseData.instagram_url) {
            updatedSocialAccounts.push({
              platform: 'instagram',
              username: responseData.instagram_url.split('/').pop() || 'instagram',
              url: responseData.instagram_url,
            });
          }

          if (responseData.facebook_url) {
            updatedSocialAccounts.push({
              platform: 'facebook',
              username: responseData.facebook_url.split('/').pop() || 'facebook',
              url: responseData.facebook_url,
            });
          }

          if (responseData.twitter_url) {
            updatedSocialAccounts.push({
              platform: 'twitter',
              username: responseData.twitter_url.split('/').pop() || 'twitter',
              url: responseData.twitter_url,
            });
          }

          if (responseData.linkedin_url) {
            updatedSocialAccounts.push({
              platform: 'linkedin',
              username: responseData.linkedin_url.split('/').pop() || 'linkedin',
              url: responseData.linkedin_url,
            });
          }

          if (responseData.youtube_url) {
            updatedSocialAccounts.push({
              platform: 'youtube',
              username: responseData.youtube_url.split('/').pop() || 'youtube',
              url: responseData.youtube_url,
            });
          }

          if (responseData.tiktok_url) {
            updatedSocialAccounts.push({
              platform: 'tiktok',
              username: responseData.tiktok_url.split('/').pop() || 'tiktok',
              url: responseData.tiktok_url,
            });
          }

          if (responseData.website) {
            try {
              let websiteUrl = responseData.website;
              if (!websiteUrl.startsWith('http://') && !websiteUrl.startsWith('https://')) {
                websiteUrl = `https://${websiteUrl}`;
              }
              const urlObj = new URL(websiteUrl);
              updatedSocialAccounts.push({
                platform: 'website',
                username: urlObj.hostname,
                url: websiteUrl,
              });
            } catch (error) {
              console.error('Error parsing website URL from response:', error);
              updatedSocialAccounts.push({
                platform: 'website',
                username: responseData.website.replace(/^https?:\/\//, '').replace(/\/$/, ''),
                url: responseData.website.startsWith('http')
                  ? responseData.website
                  : `https://${responseData.website}`,
              });
            }
          }

          // Save updated social accounts
          localStorage.setItem(
            `user_${userWithProfile.id}_social_accounts`,
            JSON.stringify(updatedSocialAccounts)
          );

          console.log('[ProfileEditForm] Updated social accounts based on response data:', updatedSocialAccounts);

        } catch (error) {
          console.error('[ProfileEditForm] Error updating social accounts:', error);
          // Fallback: restore from backup
          if (userWithProfile?.id) {
            localStorage.setItem(
              `user_${userWithProfile.id}_social_accounts`,
              socialAccountsBackup
            );
          }
        }
      }

      // Set a flag to force fresh data fetching for the next 30 seconds
      localStorage.setItem('profile_recently_updated', Date.now().toString());
      console.log('[ProfileEditForm] Set profile_recently_updated flag');

      // Clear React Query cache completely
      queryClient.clear();
      console.log('[ProfileEditForm] Cleared entire React Query cache');

      // Show success notification immediately
      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      });

      // Stop editing mode immediately
      onCancel();

      // Force immediate data refresh with a shorter delay
      setTimeout(() => {
        console.log('[ProfileEditForm] Reloading page to ensure fresh data');
        if (typeof window !== 'undefined') {
          window.location.reload();
        }
      }, 500); // Reduced from 1000ms to 500ms
    },
    onError: (error: unknown) => {
      console.error('Error updating profile:', error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Provide specific error messages based on error type
      let displayMessage = 'Failed to update profile. Please try again.';
      let shouldShowRetry = true;

      if (errorMessage.includes('Authentication expired')) {
        displayMessage = 'Your session has expired. Please log in again.';
        shouldShowRetry = false;
        // Optionally redirect to login
        setTimeout(() => {
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
        }, 3000);
      } else if (errorMessage.includes('timed out')) {
        displayMessage = 'Request timed out. Please check your connection and try again.';
      } else if (errorMessage.includes('Server error')) {
        displayMessage = 'Server is currently unavailable. Please try again in a few minutes.';
      } else if (errorMessage.includes('Profile not found')) {
        displayMessage = 'Creating your profile for the first time. Please try again.';
      }

      toast({
        title: 'Error',
        description: displayMessage,
        variant: 'destructive',
      });
    },
    // Add retry configuration
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      const errorMessage = error instanceof Error ? error.message : '';
      if (errorMessage.includes('Authentication expired') || errorMessage.includes('401')) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  // Handle form submission
  const onSubmit = (data: ProfileFormValues) => {
    updateProfileMutation.mutate(data);
  };

  return (
    <div className="w-full p-6">
      {/* Header - removed close button since Dialog has built-in close */}
      <div className="mb-6 pb-4 border-b border-gray-200">
        <h2 className="text-2xl font-semibold text-gray-900">Edit Profile</h2>
      </div>

      <Form {...profileForm}>
        <form onSubmit={profileForm.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="mb-6 grid grid-cols-3 w-full bg-gray-50 rounded-lg p-1">
              <TabsTrigger value="basic" className="flex items-center gap-2 rounded-md">
                <User className="h-4 w-4" />
                <span>Basic Info</span>
              </TabsTrigger>
              <TabsTrigger value="social" className="flex items-center gap-2 rounded-md">
                <Share2 className="h-4 w-4" />
                <span>Social Media</span>
              </TabsTrigger>
              <TabsTrigger value="privacy" className="flex items-center gap-2 rounded-md">
                <Shield className="h-4 w-4" />
                <span>Privacy</span>
              </TabsTrigger>
            </TabsList>

            {/* Basic Information Tab */}
            <TabsContent value="basic" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={profileForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-gray-700">
                        <User className="h-4 w-4 text-gray-500" />
                        Name
                      </FormLabel>
                      <FormControl>
                        <Input {...field} className="border-gray-200 focus:border-gray-400 focus:ring-1 focus:ring-gray-400" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-gray-700">
                        <Phone className="h-4 w-4 text-gray-500" />
                        Phone
                      </FormLabel>
                      <FormControl>
                        <PhoneInput {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-gray-700">
                        <Globe className="h-4 w-4 text-gray-500" />
                        Website
                      </FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://yourwebsite.com" className="border-gray-200 focus:border-gray-400 focus:ring-1 focus:ring-gray-400" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-gray-700">
                        <Clock className="h-4 w-4 text-gray-500" />
                        Timezone
                      </FormLabel>
                      <FormControl>
                        <CustomTimezoneSelect
                          value={field.value || 'UTC'}
                          onValueChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="col-span-1 md:col-span-2">
                  <FormField
                    control={profileForm.control}
                    name="isInstructor"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base text-gray-700">Teaching Account</FormLabel>
                          <FormDescription className="text-gray-500">
                            Enable this to offer sessions and teach others
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="col-span-1 md:col-span-2">
                  <FormField
                    control={profileForm.control}
                    name="bio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700">Bio</FormLabel>
                        <FormControl>
                          <RichTextEditor
                            value={field.value || ''}
                            onChange={field.onChange}
                            placeholder="Tell us about yourself"
                            minHeight="200px"
                            className="border-gray-200 focus-within:border-gray-400 focus-within:ring-1 focus-within:ring-gray-400"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Social Media Tab */}
            <TabsContent value="social" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2 text-gray-700">
                  <Share2 className="h-5 w-5 text-gray-500" />
                  Social Media Links
                </h3>
                <SocialMediaPicker
                  accounts={socialAccounts}
                  onAccountsChange={setSocialAccounts}
                />
              </div>
            </TabsContent>

            {/* Privacy Tab */}
            <TabsContent value="privacy" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2 text-gray-700">
                  <Shield className="h-5 w-5 text-gray-500" />
                  Privacy Settings
                </h3>

                <FormField
                  control={profileForm.control}
                  name="showProfile"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-gray-700">Public Profile</FormLabel>
                        <FormDescription className="text-gray-500">Allow others to view your profile</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="showContact"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-gray-700">Show Contact Information</FormLabel>
                        <FormDescription className="text-gray-500">
                          Allow others to see your email and phone
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="showSocialLinks"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-gray-700">Show Social Links</FormLabel>
                        <FormDescription className="text-gray-500">
                          Allow others to see your social media profiles
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {profileForm.watch('isInstructor') && (
                  <>
                    <FormField
                      control={profileForm.control}
                      name="showTeachingSessions"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base text-gray-700">
                              Show Teaching Sessions
                            </FormLabel>
                            <FormDescription className="text-gray-500">
                              Allow others to see sessions you're teaching
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </>
                )}

                <FormField
                  control={profileForm.control}
                  name="showLearningSessions"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base text-gray-700">Show Learning Sessions</FormLabel>
                        <FormDescription className="text-gray-500">
                          Allow others to see sessions you're attending
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="rounded-full border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateProfileMutation.isPending}
              className="rounded-full bg-gray-900 hover:bg-gray-800"
            >
              {updateProfileMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

// New Dialog wrapper component
interface DialogProfileEditFormProps extends ProfileEditFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DialogProfileEditForm({
  open,
  onOpenChange,
  onCancel,
  ...props
}: DialogProfileEditFormProps) {
  const handleCancel = () => {
    onCancel();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[85vh] overflow-hidden bg-transparent border-none shadow-none p-0">
        <DialogHeader className="sr-only">
          <DialogTitle>Edit Profile</DialogTitle>
          <DialogDescription>Update your profile information and privacy settings</DialogDescription>
        </DialogHeader>
        <div className="bg-white rounded-lg shadow-xl mx-4 my-8 max-h-[calc(85vh-4rem)] overflow-y-auto">
          <ProfileEditForm
            {...props}
            onCancel={handleCancel}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
