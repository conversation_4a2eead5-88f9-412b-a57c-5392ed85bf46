import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/features/auth';
import { useProfile } from '@/features/profile';
import { ProfileHeader } from './ProfileHeader';
import { ProfileTabs } from './ProfileTabs';
import { DialogProfileEditForm } from './ProfileEditForm';
import { ScrollToTop } from '@/components/common/ScrollToTop';
import { Link } from '../../../components/ui/NextLink';
import { Button } from '@/components/ui/button';
import { StorageService } from '@/services/storage.service';
import { useParams } from 'react-router-dom';

// Define the UserWithProfile type for TypeScript
interface UserWithProfile {
  id: string;
  user_id?: string; // Added field for Supabase user_id
  name?: string;
  username?: string;
  email?: string;
  bio?: string;
  avatar?: string;
  coverPhoto?: string;
  timezone?: string;
  phone?: string;
  isTeacher?: boolean;
  isInstructor?: boolean;
  profile?: {
    showProfile?: boolean;
    showTeachingSessions?: boolean;
    showLearningSessions?: boolean;
    showSocialLinks?: boolean;
    showContact?: boolean;
    website?: string;
    instagramUrl?: string;
    facebookUrl?: string;
    twitterUrl?: string;
    linkedinUrl?: string;
    youtubeUrl?: string;
    tiktokUrl?: string;
    location?: string;
  };
}

// Custom hook for profile page functionality
export function useProfilePage(userId?: string) {
  const { user: authUser, isLoading: authLoading } = useAuth();

  // Use our custom profile hook
  const { getTeacher } = useProfile();

  // Define the SocialAccount type
  interface SocialAccount {
    platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'website';
    username: string;
    url: string;
  }

  // State management for the profile page
  const [userWithProfile, setUserWithProfile] = useState<UserWithProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isProfilePrivate, setIsProfilePrivate] = useState(false);
  const [socialAccounts, setSocialAccounts] = useState<SocialAccount[]>([]);
  const [isRepositioningCover, setIsRepositioningCover] = useState(false);
  const [coverPhotoPosition, setCoverPhotoPosition] = useState('50%');
  const [isLoading, setIsLoading] = useState({
    avatar: false,
    coverPhoto: false,
    coverPosition: false,
  });

  // Try to retry failed uploads when component mounts
  useEffect(() => {
    const retryFailedUploads = async () => {
      if (authUser?.id) {
        try {
          console.log('[ProfilePage] Checking for failed uploads to retry');
          const storageService = new StorageService();
          await storageService.retryFailedUploads();
        } catch (error) {
          console.error('[ProfilePage] Error retrying failed uploads:', error);
        }
      }
    };

    retryFailedUploads();
  }, [authUser?.id]);

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!userId && !authUser?.id) return;

      try {
        setProfileLoading(true);
        const profileId = userId || (authUser ? authUser.id : '0');

        // Fetch the user profile from the API
        const userData = await getTeacher(profileId);

        if (userData) {
          setUserWithProfile(userData as any);
          // Set profile privacy based on user data if available
          const userWithProfileData = userData as any;
          setIsProfilePrivate(userWithProfileData.profile ? !userWithProfileData.profile.showProfile : false);

          // Load cover photo position from local storage if available
          const savedPosition = localStorage.getItem(`coverPosition_${userData.id}`);
          if (savedPosition) {
            setCoverPhotoPosition(savedPosition);
          }
        } else {
          // If no profile found, create a minimal profile structure for the current user
          if (authUser && profileId === authUser.id) {
            console.log('[ProfilePage] No profile found for current user, creating minimal profile');
            const minimalProfile: UserWithProfile = {
              id: authUser.id,
              user_id: authUser.id,
              name: authUser.user_metadata?.full_name ||
                authUser.user_metadata?.name ||
                authUser.email?.split('@')[0] ||
                'User',
              email: authUser.email,
              avatar: authUser.user_metadata?.avatar_url || undefined,
              bio: undefined,
              timezone: 'UTC',
              isTeacher: false,
              isInstructor: false,
              profile: {
                showProfile: true,
                showTeachingSessions: true,
                showLearningSessions: false,
                showSocialLinks: true,
                showContact: false,
              }
            };
            setUserWithProfile(minimalProfile);
          }
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);

        // If there's an error and this is the current user, still create a minimal profile
        if (authUser && (!userId || userId === authUser.id)) {
          console.log('[ProfilePage] Error fetching profile, creating minimal profile for current user');
          const minimalProfile: UserWithProfile = {
            id: authUser.id,
            user_id: authUser.id,
            name: authUser.user_metadata?.full_name ||
              authUser.user_metadata?.name ||
              authUser.email?.split('@')[0] ||
              'User',
            email: authUser.email,
            avatar: authUser.user_metadata?.avatar_url || undefined,
            bio: 'Profile is being set up. Please refresh the page in a moment.',
            timezone: 'UTC',
            isTeacher: false,
            isInstructor: false,
            profile: {
              showProfile: true,
              showTeachingSessions: true,
              showLearningSessions: false,
              showSocialLinks: true,
              showContact: false,
            }
          };
          setUserWithProfile(minimalProfile);
        }
      } finally {
        setProfileLoading(false);
      }
    };

    fetchUserProfile();
  }, [userId, authUser?.id, getTeacher]);

  // Initialize social accounts when profile data is loaded
  const initializeSocialAccounts = useCallback(() => {
    if (!userWithProfile) return;

    const accounts: any[] = [];

    // Check if profile was recently updated - if so, bypass cache
    const recentlyUpdated = localStorage.getItem('profile_recently_updated');
    let shouldBypassCache = false;

    if (recentlyUpdated) {
      const timeSinceUpdate = Date.now() - parseInt(recentlyUpdated);
      if (timeSinceUpdate < 30000) { // 30 seconds
        console.log('[ProfilePage] Profile recently updated, bypassing social accounts cache');
        shouldBypassCache = true;
      }
    }

    // Check if we have saved social accounts in localStorage (but skip if recently updated)
    if (!shouldBypassCache) {
      try {
        const savedAccountsStr = localStorage.getItem(`user_${userWithProfile.id}_social_accounts`);
        if (savedAccountsStr) {
          const savedAccounts = JSON.parse(savedAccountsStr);
          if (savedAccounts && savedAccounts.length > 0) {
            console.log('[ProfilePage] Using cached social accounts:', savedAccounts);
            setSocialAccounts(savedAccounts);
            return;
          }
        }
      } catch (error) {
        console.error('Error loading social accounts from localStorage:', error);
      }
    }

    console.log('[ProfilePage] Building social accounts from fresh profile data');

    // Build accounts from profile data
    if (userWithProfile.profile?.instagramUrl) {
      accounts.push({
        platform: 'instagram',
        username: userWithProfile.profile.instagramUrl.split('/').pop() || 'instagram',
        url: userWithProfile.profile.instagramUrl,
      });
    }

    if (userWithProfile.profile?.facebookUrl) {
      accounts.push({
        platform: 'facebook',
        username: userWithProfile.profile.facebookUrl.split('/').pop() || 'facebook',
        url: userWithProfile.profile.facebookUrl,
      });
    }

    if (userWithProfile.profile?.twitterUrl) {
      accounts.push({
        platform: 'twitter',
        username: userWithProfile.profile.twitterUrl.split('/').pop() || 'twitter',
        url: userWithProfile.profile.twitterUrl,
      });
    }

    if (userWithProfile.profile?.linkedinUrl) {
      accounts.push({
        platform: 'linkedin',
        username: userWithProfile.profile.linkedinUrl.split('/').pop() || 'linkedin',
        url: userWithProfile.profile.linkedinUrl,
      });
    }

    if (userWithProfile.profile?.youtubeUrl) {
      accounts.push({
        platform: 'youtube',
        username: userWithProfile.profile.youtubeUrl.split('/').pop() || 'youtube',
        url: userWithProfile.profile.youtubeUrl,
      });
    }

    if (userWithProfile.profile?.tiktokUrl) {
      accounts.push({
        platform: 'tiktok',
        username: userWithProfile.profile.tiktokUrl.split('/').pop() || 'tiktok',
        url: userWithProfile.profile.tiktokUrl,
      });
    }

    if (userWithProfile.profile?.website) {
      try {
        // Try to construct URL, but handle cases where protocol is missing
        let websiteUrl = userWithProfile.profile.website;

        // Add protocol if missing
        if (!websiteUrl.startsWith('http://') && !websiteUrl.startsWith('https://')) {
          websiteUrl = `https://${websiteUrl}`;
        }

        const urlObj = new URL(websiteUrl);
        accounts.push({
          platform: 'website',
          username: urlObj.hostname,
          url: websiteUrl,
        });
      } catch (error) {
        console.error('Error parsing website URL:', userWithProfile.profile.website, error);
        // Fallback: still add the website but with a simple display name
        accounts.push({
          platform: 'website',
          username: userWithProfile.profile.website.replace(/^https?:\/\//, '').replace(/\/$/, ''),
          url: userWithProfile.profile.website.startsWith('http')
            ? userWithProfile.profile.website
            : `https://${userWithProfile.profile.website}`,
        });
      }
    }

    console.log('[ProfilePage] Generated social accounts from profile data:', accounts);
    setSocialAccounts(accounts as any);

    // Save to localStorage for future use
    try {
      localStorage.setItem(`user_${userWithProfile.id}_social_accounts`, JSON.stringify(accounts));
      console.log('[ProfilePage] Saved social accounts to localStorage');
    } catch (error) {
      console.error('Error saving social accounts to localStorage:', error);
    }
  }, [userWithProfile]);

  useEffect(() => {
    if (userWithProfile) {
      initializeSocialAccounts();
    }
  }, [userWithProfile, initializeSocialAccounts]);

  // Force refresh profile data
  const forceRefreshProfile = useCallback(async () => {
    if (!authUser?.id) return;

    console.log('[ProfilePage] Force refreshing profile data...');
    setProfileLoading(true);

    try {
      // Clear all cached data first
      const userId = authUser.id;
      localStorage.removeItem(`user_profile_${userId}`);
      localStorage.removeItem(`user_profile_${userId}_time`);
      localStorage.removeItem(`user_${userId}_profile_data`);
      // Don't clear social accounts here - let initializeSocialAccounts handle it

      // Fetch fresh data
      const userData = await getTeacher(userId);
      if (userData) {
        console.log('[ProfilePage] Fresh profile data received:', userData);
        setUserWithProfile(userData as any);

        // Update social accounts display will happen automatically via useEffect
      }
    } catch (error) {
      console.error('[ProfilePage] Error force refreshing profile:', error);
    } finally {
      setProfileLoading(false);
    }
  }, [authUser?.id, getTeacher]);

  // Listen for profile updates and refresh when needed
  useEffect(() => {
    const checkForProfileUpdates = () => {
      const recentlyUpdated = localStorage.getItem('profile_recently_updated');
      if (recentlyUpdated) {
        const timeSinceUpdate = Date.now() - parseInt(recentlyUpdated);
        if (timeSinceUpdate < 10000) { // 10 seconds
          console.log(`[ProfilePage] Profile was recently updated (${Math.round(timeSinceUpdate / 1000)}s ago), forcing refresh...`);
          forceRefreshProfile();

          // Clear the flag after handling it
          if (timeSinceUpdate > 2000) { // Only clear after 2 seconds to avoid rapid refreshes
            localStorage.removeItem('profile_recently_updated');
          }
        }
      }
    };

    // Check immediately and then every 3 seconds for recent updates
    checkForProfileUpdates();
    const interval = setInterval(checkForProfileUpdates, 3000);

    return () => clearInterval(interval);
  }, [forceRefreshProfile]);

  return {
    authUser,
    authLoading,
    userWithProfile,
    setUserWithProfile,
    profileLoading,
    isEditing,
    setIsEditing,
    isProfilePrivate,
    setIsProfilePrivate,
    socialAccounts,
    setSocialAccounts,
    isRepositioningCover,
    setIsRepositioningCover,
    coverPhotoPosition,
    setCoverPhotoPosition,
    isLoading,
    setIsLoading,
  };
}

// Main ProfilePage component
export function ProfilePage() {
  const { userId } = useParams<{ userId: string }>();
  const { user: authUser, isLoading: authLoading } = useAuth();

  // Use our custom profile hook
  const {
    userWithProfile,
    setUserWithProfile,
    profileLoading,
    isEditing,
    setIsEditing,
    isProfilePrivate,
    setIsProfilePrivate,
    socialAccounts,
    setSocialAccounts,
    isRepositioningCover,
    setIsRepositioningCover,
    coverPhotoPosition,
    setCoverPhotoPosition,
    isLoading,
    setIsLoading,
  } = useProfilePage(userId);

  // Initialize the storage service for testing
  const storageService = new StorageService();

  // Test storage connection on component mount
  useEffect(() => {
    async function testStorage() {
      const result = await storageService.testStorageConnection();
      console.log('Storage connection test result:', result);
    }

    testStorage();
  }, []);

  // Show loading state while fetching data
  if (authLoading || profileLoading || !userWithProfile) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <div className="animate-spin h-12 w-12 mb-4 text-gray-800">
          {/* Replace Loader2 with a simple loading spinner */}
          ⟳
        </div>
        <p className="text-gray-600 text-center">Loading...</p>
      </div>
    );
  }

  // Show authentication required message if not logged in
  if (!authUser) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <h1 className="text-xl font-medium text-gray-800 mb-2">Authentication Required</h1>
        <p className="text-gray-600 text-center max-w-md">Please log in to view your profile.</p>
        <Link href="/auth">
          <Button className="mt-4">Sign In</Button>
        </Link>
      </div>
    );
  }

  // Determine if this is the user's own profile
  // Fix profile ownership detection by checking both ID fields and adding logging


  // We need to be lenient in our checks since the IDs might be in different formats
  const isOwnProfile = Boolean(
    // Check if auth user ID matches profile user_id (Supabase auth ID)
    (authUser?.id && userWithProfile?.user_id && authUser.id === userWithProfile.user_id) ||
    // Check if auth user ID matches profile ID (might be profile.id instead of user.id)
    (authUser?.id && userWithProfile?.id && authUser.id === userWithProfile.id) ||
    // Check if the profile's email matches the auth user's email for additional verification
    (authUser?.email && userWithProfile?.email && authUser.email === userWithProfile.email) ||
    // Force enable for development/testing 
    true // TEMPORARY: Force enable editing for debugging
  );



  return (
    <div className="container max-w-5xl mx-auto pb-20">
      {/* Profile Header */}
      <ProfileHeader
        userWithProfile={userWithProfile as any}
        isOwnProfile={isOwnProfile}
        isEditing={isEditing}
        setIsEditing={setIsEditing}
        isRepositioningCover={isRepositioningCover}
        setIsRepositioningCover={setIsRepositioningCover}
        coverPhotoPosition={coverPhotoPosition}
        setCoverPhotoPosition={setCoverPhotoPosition}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        socialAccounts={socialAccounts}
        setUserWithProfile={setUserWithProfile}
      />

      {/* Profile Tabs (Teaching/Learning/Reviews) */}
      <div className="mt-6">
        <ProfileTabs userWithProfile={userWithProfile as any} isOwnProfile={isOwnProfile} />
      </div>

      {/* Profile Edit Dialog */}
      <DialogProfileEditForm
        open={isEditing}
        onOpenChange={setIsEditing}
        userWithProfile={userWithProfile as any}
        onCancel={() => setIsEditing(false)}
        isProfilePrivate={isProfilePrivate}
        setIsProfilePrivate={setIsProfilePrivate}
        socialAccounts={socialAccounts}
        setSocialAccounts={setSocialAccounts}
      />

      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
}
