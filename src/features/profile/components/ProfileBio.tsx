import { UserProfile } from '@shared/schema';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, Phone, Clock, Globe, MapPin } from 'lucide-react';

interface ProfileBioProps {
  userWithProfile: UserProfile;
  isPrivate: boolean;
}

export function ProfileBio({ userWithProfile, isPrivate }: ProfileBioProps) {
  // Format timezone for display
  const formatTimezone = (timezone: string) => {
    try {
      // Try to format the timezone nicely
      const now = new Date();
      const options: Intl.DateTimeFormatOptions = {
        timeZoneName: 'long',
        timeZone: timezone,
      };
      const formatter = new Intl.DateTimeFormat('en-US', options);
      const timeZoneParts = formatter.formatToParts(now);
      const timeZoneName =
        timeZoneParts.find(part => part.type === 'timeZoneName')?.value || timezone;

      // Get the offset
      const offset =
        new Intl.DateTimeFormat('en-US', {
          timeZone: timezone,
          timeZoneName: 'short',
        })
          .formatToParts(now)
          .find(part => part.type === 'timeZoneName')?.value || '';

      return `${timeZoneName} (${offset})`;
    } catch (error) {
      console.error('Error formatting timezone:', error);
      return timezone;
    }
  };

  return (
    <Card className="mt-6">
      <CardContent className="pt-6">
        {/* Bio */}
        {userWithProfile.bio && (
          <div className="mb-6">
            <div
              className="prose prose-sm max-w-none prose-p:my-2 prose-ul:my-2 prose-ol:my-2 prose-li:my-1 prose-headings:my-2"
              dangerouslySetInnerHTML={{ __html: userWithProfile.bio }}
              style={{
                lineHeight: '1.5',
              }}
            />
          </div>
        )}

        {/* Contact Information */}
        {!isPrivate && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {userWithProfile.email && (
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                <a href={`mailto:${userWithProfile.email}`} className="text-sm hover:underline">
                  {userWithProfile.email}
                </a>
              </div>
            )}

            {userWithProfile.phone && (
              <div className="flex items-center">
                <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                <a href={`tel:${userWithProfile.phone}`} className="text-sm hover:underline">
                  {userWithProfile.phone}
                </a>
              </div>
            )}

            {userWithProfile.timezone && (
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">{formatTimezone(userWithProfile.timezone)}</span>
              </div>
            )}

            {/* Website field not available in UserProfile schema */}

            {userWithProfile.location && (
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">{userWithProfile.location}</span>
              </div>
            )}
          </div>
        )}

        {/* Skills and Specializations - These fields are in InstructorProfile, not UserProfile */}

        {/* Education and Experience - These fields are in InstructorProfile, not UserProfile */}
      </CardContent>
    </Card>
  );
}
