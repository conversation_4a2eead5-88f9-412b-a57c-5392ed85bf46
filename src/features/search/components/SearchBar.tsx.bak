import { ChangeEvent, FormEvent, useState, useRef, useEffect, useCallback } from 'react';
import { useFilter } from '@/contexts/FilterContext';
import { Search, X, Command } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

// Debounce hook for performance
function useDebounce(value: string, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function SearchBar() {
  const { setSearch, filterState } = useFilter();
  const [searchInput, setSearchInput] = useState(filterState.search);
  const [isFocused, setIsFocused] = useState(false);
  const [showClearButton, setShowClearButton] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounce search input with 300ms delay
  const debouncedSearchInput = useDebounce(searchInput, 300);

  // Update search context when debounced value changes
  useEffect(() => {
    setSearch(debouncedSearchInput);
  }, [debouncedSearchInput, setSearch]);

  // Auto-resize the search input based on content
  useEffect(() => {
    setShowClearButton(searchInput.length > 0);
  }, [searchInput]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command/Ctrl + K to focus search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        inputRef.current?.focus();
      }

      // Escape to blur search
      if (e.key === 'Escape' && document.activeElement === inputRef.current) {
        inputRef.current?.blur();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInput(value);
    // If input is cleared, immediately update search context for instant clearing
    if (value === '') {
      setSearch('');
    }
  };

  const handleClearSearch = () => {
    setSearchInput('');
    setSearch('');
    inputRef.current?.focus();
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setSearch(searchInput);
    inputRef.current?.blur(); // Blur input after submitting to hide mobile keyboard
  };

  return (
    <form onSubmit={handleSubmit} className="relative flex items-center w-full max-w-2xl mx-auto">
      <div
        className={`absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none transition-colors duration-200 ${isFocused ? 'text-[#E07A5F]' : 'text-gray-400'
          }`}
      >
        <Search className="h-4 w-4" />
      </div>

      <Input
        ref={inputRef}
        type="text"
        value={searchInput}
        onChange={handleChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className="w-full pl-9 pr-12 py-2 h-10 rounded-md bg-white/90 border border-gray-200
                 text-gray-900 placeholder-gray-500 shadow-sm
                 focus:outline-none focus:ring-0 focus:border-gray-200
                 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0
                 transition-all duration-200
                 [&::-webkit-search-decoration]:hidden
                 [&::-webkit-search-cancel-button]:hidden
                 [&::-webkit-search-results-button]:hidden
                 [&::-webkit-search-results-decoration]:hidden"
        placeholder="What are you looking for?"
        aria-label="Search"
        autoComplete="off"
      />

      {/* Clear button */}
      {showClearButton && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleClearSearch}
          className="absolute right-10 h-6 w-6 p-0 rounded-full text-gray-400 hover:text-[#E07A5F] hover:bg-transparent"
        >
          <X className="h-3.5 w-3.5" />
          <span className="sr-only">Clear search</span>
        </Button>
      )}

      {/* Search button */}
      <div className="absolute inset-y-0 right-1 flex items-center">
        <Button
          type="submit"
          size="sm"
          className="rounded-md w-7 h-7 bg-[#E07A5F] hover:bg-[#C86A52] text-white p-0 flex items-center justify-center"
        >
          <Search className="h-3.5 w-3.5" />
          <span className="sr-only">Search</span>
        </Button>
      </div>
    </form>
  );
}
