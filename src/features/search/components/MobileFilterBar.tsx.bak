import { useState, useRef, useEffect } from 'react';
import { useFilter } from '@/contexts/FilterContext';
import { FilterTag } from './FilterTag';
import { cleanupBackdrops } from '@/lib/utils';
import { FilterSelect } from '@/components/ui/filter-select';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  SESSION_TYPES,
  DATE_OPTIONS,
  SKILL_LEVEL_OPTIONS,
  FORMAT_OPTIONS,
  LANGUAGE_OPTIONS,
  PRICE_OPTIONS,
  CATEGORIES,
} from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronRight, ChevronLeft, Filter, X, Sliders } from 'lucide-react';

// Define option types for type safety
type FilterOption = {
  label: string;
  value: string;
  group?: string;
};

export function MobileFilterBar() {
  const {
    filterState,
    setSessionType,
    setDate,
    setSkillLevel,
    setFormat,
    setLanguage,
    setPrice,
    removeTag,
    clearAllTags,
  } = useFilter();

  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update scrollbar thumb position
  useEffect(() => {
    const scrollElement = scrollRef.current;
    const containerElement = containerRef.current;

    if (!scrollElement || !containerElement) return;

    const updateScrollThumb = () => {
      const scrollLeft = scrollElement.scrollLeft;
      const scrollWidth = scrollElement.scrollWidth;
      const clientWidth = scrollElement.clientWidth;

      // Calculate available track width (container width minus margins)
      const trackWidth = containerElement.offsetWidth - 20; // 10px margin on each side

      // Calculate thumb width based on visible portion
      const thumbWidthPx = Math.max(30, (clientWidth / scrollWidth) * trackWidth);
      containerElement.style.setProperty('--thumb-width', `${thumbWidthPx}px`);

      // Calculate maximum scroll offset
      const maxScroll = scrollWidth - clientWidth;

      // Calculate maximum thumb offset
      const maxThumbOffset = trackWidth - thumbWidthPx;

      // Calculate thumb position in pixels
      const thumbOffsetPx = maxScroll > 0 ? (scrollLeft / maxScroll) * maxThumbOffset : 0;

      containerElement.style.setProperty('--thumb-offset', `${thumbOffsetPx}px`);
    };

    // Add scroll event listener
    scrollElement.addEventListener('scroll', updateScrollThumb);

    // Initial update
    updateScrollThumb();

    return () => {
      scrollElement.removeEventListener('scroll', updateScrollThumb);
    };
  }, []);
  // Define filter options
  const dateFilters: FilterOption[] = DATE_OPTIONS.map(date => ({ label: date, value: date }));
  const skillFilters: FilterOption[] = SKILL_LEVEL_OPTIONS.map(skill => ({
    label: skill,
    value: skill,
  }));
  const priceFilters: FilterOption[] = PRICE_OPTIONS.map(price => ({ label: price, value: price }));
  const formatFilters: FilterOption[] = FORMAT_OPTIONS.map(format => ({
    label: format,
    value: format,
  }));
  const languageFilters: FilterOption[] = LANGUAGE_OPTIONS.map(lang => ({
    label: lang,
    value: lang,
  }));

  // Clean up filter state on unmount to prevent stale state issues
  useEffect(() => {
    return () => {
      // Reset to default state when component unmounts
      setIsFilterExpanded(false);
      // Clean up any backdrop elements
      cleanupBackdrops();
    };
  }, []);

  const toggleFilterExpansion = () => {
    // Make sure we don't break the app when toggling filter expansion
    try {
      setIsFilterExpanded(prev => !prev);
      // Clean up any backdrop elements that might be stuck
      cleanupBackdrops();
    } catch (error) {
      console.error('Error toggling filter expansion:', error);
      // Ensure we don't end up in a broken state
      setIsFilterExpanded(false);
    }
  };

  // Handle filter changes
  const handleDateChange = (value: string) => {
    setDate(value as any);
  };

  const handleSkillChange = (value: string) => {
    setSkillLevel(value as any);
  };

  const handlePriceChange = (value: string) => {
    setPrice(value as any);
  };

  const handleFormatChange = (value: string) => {
    setFormat(value as any);
  };

  const handleLanguageChange = (value: string) => {
    setLanguage(value as any);
  };

  return (
    <section className="mt-1 space-y-3" aria-labelledby="filter-heading">
      <h2 id="filter-heading" className="sr-only">
        Session filters
      </h2>

      {/* Category pills with native scrolling - mobile version */}
      <div
        className="relative max-w-full mt-1 pill-container"
        ref={containerRef}
        onClick={e => {
          // Handle click on the scrollbar track
          const container = e.currentTarget;
          const scrollElement = scrollRef.current;
          if (!scrollElement) return;

          // Get the position of the container
          const rect = container.getBoundingClientRect();

          // Only handle clicks in the scrollbar area (bottom 20px)
          const clickY = e.clientY - rect.top;
          if (clickY < rect.height - 20) return;

          // Calculate the position to scroll to
          // Account for the 10px margin on each side
          const clickX = e.clientX - rect.left - 10;
          const trackWidth = rect.width - 20; // Subtract 20px (10px on each side)

          // Ensure click is within track bounds
          if (clickX < 0 || clickX > trackWidth) return;

          const ratio = clickX / trackWidth;
          const scrollWidth = scrollElement.scrollWidth - scrollElement.clientWidth;
          scrollElement.scrollLeft = scrollWidth * ratio;
        }}
      >
        <div
          className="flex items-center py-2 space-x-1.5 overflow-x-auto custom-scrollbar"
          role="tablist"
          aria-label="Category filters"
          ref={scrollRef}
        >
          {CATEGORIES.map(category => {
            const isSelected = filterState.sessionType === category.name;

            return (
              <button
                key={category.id}
                onClick={() => setSessionType(category.name as any)}
                role="tab"
                aria-selected={isSelected}
                className={`flex-none whitespace-nowrap px-3 py-1.5 rounded-full text-sm transition-all focus:outline-none font-outfit ${isSelected
                    ? 'bg-accent text-white font-medium'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
              >
                {category.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* More filters button for mobile */}
      <div className="mt-2 flex justify-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleFilterExpansion}
          className="flex items-center gap-2 text-gray-600 hover:bg-gray-100/50 rounded-full px-4 py-1.5 font-outfit"
          aria-expanded={isFilterExpanded}
          aria-controls="mobile-filters"
        >
          <Sliders className="h-3.5 w-3.5" aria-hidden="true" />
          <span className="text-xs">{isFilterExpanded ? 'Hide filters' : 'Show more filters'}</span>
        </Button>
      </div>

      {/* Active Filter Tags */}
      {filterState.activeTags.length > 0 && (
        <div className="flex flex-wrap gap-1.5 my-2 px-1">
          {filterState.activeTags.map(tag => (
            <FilterTag key={tag.id} tag={tag} onRemove={() => removeTag(tag.id)} />
          ))}

          {filterState.activeTags.length > 1 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-gray-600 hover:text-accent px-2 py-1 h-auto font-outfit"
              onClick={clearAllTags}
            >
              <X className="h-3 w-3 mr-1" />
              Clear all
            </Button>
          )}
        </div>
      )}

      {/* Updated Expanded Filters for Mobile - Using Select component */}
      {isFilterExpanded && (
        <div className="bg-white rounded-xl p-3 shadow-sm border border-[#F0EEE9] animate-fadeIn">
          <div className="grid grid-cols-1 gap-3">
            {/* Date Filter */}
            <FilterSelect
              label="Date"
              value={filterState.date || 'All Dates'}
              onChange={handleDateChange}
              options={dateFilters}
              placeholder="Select date"
              align="start"
              className=""
            />

            {/* Skill Level Filter */}
            <FilterSelect
              label="Skill Level"
              value={filterState.skillLevel || 'All Skill Levels'}
              onChange={handleSkillChange}
              options={skillFilters}
              placeholder="Select skill level"
              align="start"
              className=""
            />

            {/* Price Filter */}
            <FilterSelect
              label="Price"
              value={filterState.price || 'All Prices'}
              onChange={handlePriceChange}
              options={priceFilters}
              placeholder="Select price range"
              align="start"
              className=""
            />

            {/* Format Filter */}
            <FilterSelect
              label="Format"
              value={filterState.format || 'All Formats'}
              onChange={handleFormatChange}
              options={formatFilters}
              placeholder="Select format"
              align="start"
              className=""
            />

            {/* Language Filter */}
            <FilterSelect
              label="Language"
              value={filterState.language || 'All Languages'}
              onChange={handleLanguageChange}
              options={languageFilters}
              placeholder="Select language"
              align="start"
              className=""
            />
          </div>
        </div>
      )}
    </section>
  );
}
