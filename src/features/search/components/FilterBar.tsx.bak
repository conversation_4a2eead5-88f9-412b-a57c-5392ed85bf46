import { ChangeEvent, FormEvent, useState, useRef, useEffect, useCallback } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useFilter } from '@/contexts/FilterContext';
import { FilterTag } from './FilterTag';
import { DateRangePicker } from './DateRangePicker';
import {
  SESSION_TYPES,
  DATE_OPTIONS,
  SKILL_LEVEL_OPTIONS,
  FORMAT_OPTIONS,
  LANGUAGE_OPTIONS,
  PRICE_OPTIONS,
  CATEGORIES,
} from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { FilterSelect } from '@/components/ui/filter-select';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Filter,
  X,
  Calendar,
  BookOpen,
  Monitor,
  Users,
  MapPin,
  DollarSign,
  Globe,
  Clock,
  Star,
  LayoutGrid,
  List,
} from 'lucide-react';
import { format } from 'date-fns';

interface CategoryWithCount {
  name: string;
  emoji: string;
}

interface ActiveFilter {
  type: string;
  label: string;
  emoji?: string;
  onRemove: () => void;
}

export function FilterBar() {
  const isMobile = useIsMobile();
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const {
    filterState,
    setSessionType,
    setDate,
    setDateRange,
    setSkillLevel,
    setFormat,
    setLanguage,
    setPrice,
    clearAllTags,
    setSearchMode,
    viewMode,
    toggleViewMode,
    sortOption,
    setSortOption,
  } = useFilter();

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  // All categories in one array
  const allCategories: CategoryWithCount[] = [
    { name: 'Yoga', emoji: '🧘' },
    { name: 'Language', emoji: '🗣️' },
    { name: 'Music', emoji: '🎵' },
    { name: 'Fitness', emoji: '💪' },
    { name: 'Art', emoji: '🎨' },
    { name: 'Cooking', emoji: '👨‍🍳' },
    { name: 'Academic', emoji: '📚' },
    { name: 'Professional', emoji: '💼' },
    { name: 'Therapy', emoji: '🧠' },
    { name: 'Dance', emoji: '💃' },
    { name: 'Health', emoji: '🏥' },
    { name: 'DIY', emoji: '🔨' },
    { name: 'Beauty', emoji: '💄' },
    { name: 'Spirituality', emoji: '🙏' },
  ];

  const handleCategorySelect = (categoryName: string) => {
    let newSelectedCategories;

    if (selectedCategories.includes(categoryName)) {
      newSelectedCategories = selectedCategories.filter(cat => cat !== categoryName);
    } else {
      newSelectedCategories = [...selectedCategories, categoryName];
    }

    setSelectedCategories(newSelectedCategories);

    if (newSelectedCategories.length === 0) {
      setSessionType('All Categories' as any);
    } else if (newSelectedCategories.length === 1) {
      setSessionType(newSelectedCategories[0] as any);
    } else {
      setSessionType(newSelectedCategories[0] as any);
    }
  };

  const handleCategoryRemove = (categoryName: string) => {
    const newSelectedCategories = selectedCategories.filter(cat => cat !== categoryName);
    setSelectedCategories(newSelectedCategories);

    if (newSelectedCategories.length === 0) {
      setSessionType('All Categories' as any);
    } else {
      setSessionType(newSelectedCategories[0] as any);
    }
  };

  const getActiveFilters = (): ActiveFilter[] => {
    const active: ActiveFilter[] = [];

    selectedCategories.forEach(categoryName => {
      const category = allCategories.find(c => c.name === categoryName);
      active.push({
        type: 'category',
        label: categoryName,
        emoji: category?.emoji,
        onRemove: () => handleCategoryRemove(categoryName)
      });
    });

    // Handle date filters - support both range and multiple individual dates
    if (filterState.dateRange?.multipleDates && filterState.dateRange.multipleDates.length > 0) {
      // Handle multiple individual dates
      const multipleDates = filterState.dateRange.multipleDates;
      let dateLabel;

      if (multipleDates.length === 1) {
        dateLabel = format(multipleDates[0], 'MMM d, yyyy');
      } else {
        dateLabel = `${multipleDates.length} dates selected`;
      }

      active.push({
        type: 'date',
        label: dateLabel,
        emoji: undefined,
        onRemove: () => {
          setDateRange(null);
          setDate('All Dates' as any);
        }
      });
    } else if (filterState.dateRange?.from) {
      // Handle date range
      const fromDate = filterState.dateRange.from;
      const toDate = filterState.dateRange.to;

      let dateLabel;
      if (toDate && fromDate.getTime() !== toDate.getTime()) {
        dateLabel = `${format(fromDate, 'MMM d')} - ${format(toDate, 'MMM d')}`;
      } else {
        dateLabel = format(fromDate, 'MMM d, yyyy');
      }

      active.push({
        type: 'date',
        label: dateLabel,
        emoji: undefined,
        onRemove: () => {
          setDateRange(null);
          setDate('All Dates' as any);
        }
      });
    } else if (filterState.date && filterState.date !== 'All Dates' && filterState.date !== 'Choose dates') {
      // Show other date filters (like "Today", "Tomorrow", etc.) only if they're not custom date ranges
      active.push({
        type: 'date',
        label: filterState.date,
        emoji: undefined,
        onRemove: () => setDate('All Dates' as any)
      });
    }

    if (filterState.skillLevel && filterState.skillLevel !== 'All Skill Levels') {
      active.push({
        type: 'level',
        label: filterState.skillLevel,
        emoji: undefined,
        onRemove: () => setSkillLevel('All Skill Levels' as any)
      });
    }
    if (filterState.format && filterState.format !== 'All Formats') {
      active.push({
        type: 'format',
        label: filterState.format,
        emoji: undefined,
        onRemove: () => setFormat('All Formats' as any)
      });
    }
    if (filterState.language && filterState.language !== 'All Languages') {
      active.push({
        type: 'language',
        label: filterState.language,
        emoji: undefined,
        onRemove: () => setLanguage('All Languages' as any)
      });
    }
    if (filterState.price && filterState.price !== 'All Prices') {
      active.push({
        type: 'price',
        label: filterState.price,
        emoji: undefined,
        onRemove: () => setPrice('All Prices' as any)
      });
    }
    return active;
  };

  const clearAllFilters = () => {
    setSelectedCategories([]);
    clearAllTags();
  };

  const activeFilters = getActiveFilters();

  return (
    <div className="space-y-6 w-full">
      {/* Categories Section */}
      <div className="px-4">
        <div className="flex justify-center">
          <div className="w-full max-w-5xl">
            <div className="flex flex-wrap justify-center gap-2">
              {allCategories.map((category) => (
                <button
                  key={category.name}
                  onClick={() => handleCategorySelect(category.name)}
                  className={`px-3 py-2 flex items-center gap-1.5 text-sm rounded-lg transition-all duration-200 whitespace-nowrap
                           ${selectedCategories.includes(category.name)
                      ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                >
                  <span className="text-sm" aria-hidden="true">
                    {category.emoji}
                  </span>
                  <span className="text-sm font-medium">
                    {category.name}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Filter Controls Section - Improved container to prevent cutoff */}
      <div className="px-2 sm:px-4 overflow-visible">
        <div className="w-full max-w-6xl mx-auto overflow-visible">
          <div className="flex justify-center gap-2 sm:gap-3 items-center overflow-visible flex-nowrap">
            {/* Sort Control */}
            <div className="flex-shrink-0 relative z-10">
              <FilterSelect
                value={sortOption}
                onChange={(value) => setSortOption(value as any)}
                options={[
                  { value: 'Recommended', label: 'Recommended' },
                  { value: 'Rating', label: 'Rating' },
                  { value: 'Price: Low to High', label: 'Price: Low to High' },
                  { value: 'Price: High to Low', label: 'Price: High to Low' },
                  { value: 'Date', label: 'Date' },
                ]}
                placeholder="Sort"
                className="min-w-fit whitespace-nowrap text-sm"
              />
            </div>

            {/* Date Filter */}
            <div className="flex-shrink-0 relative z-10">
              <DateRangePicker
                onDateChange={setDateRange}
                selectedDates={filterState.dateRange}
                className="min-w-fit whitespace-nowrap text-sm"
                open={datePickerOpen}
                onOpenChange={setDatePickerOpen}
              />
            </div>

            {/* Skill Level Filter */}
            <div className="flex-shrink-0 relative z-10">
              <FilterSelect
                value={filterState.skillLevel}
                onChange={(value) => setSkillLevel(value as any)}
                options={[
                  { value: 'All Skill Levels', label: 'All Skill Levels' },
                  { value: 'Beginner', label: 'Beginner' },
                  { value: 'Intermediate', label: 'Intermediate' },
                  { value: 'Advanced', label: 'Advanced' },
                ]}
                placeholder="All Skill Levels"
                className="min-w-fit whitespace-nowrap text-sm"
              />
            </div>

            {/* Format Filter */}
            <div className="flex-shrink-0 relative z-10">
              <FilterSelect
                value={filterState.format}
                onChange={(value) => setFormat(value as any)}
                options={[
                  { value: 'All Formats', label: 'All Formats' },
                  { value: 'In-Person', label: 'In-Person' },
                  { value: 'Online', label: 'Online' },
                  { value: 'Group', label: 'Group' },
                  { value: 'One-on-One', label: 'One-on-One' },
                ]}
                placeholder="All Formats"
                className="min-w-fit whitespace-nowrap text-sm"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {getActiveFilters().length > 0 && (
        <div className="px-4">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-gray-700">Active filters:</span>
            {getActiveFilters().map((filter, index) => (
              <div
                key={index}
                className="inline-flex items-center px-3 py-1.5 rounded-md text-xs font-medium border bg-primary/10 text-primary border-primary/20 shadow-sm transition-all duration-200"
              >
                {filter.emoji && (
                  <span className="mr-1.5" aria-hidden="true">
                    {filter.emoji}
                  </span>
                )}
                <span className="mr-2">{filter.label}</span>
                <button
                  onClick={filter.onRemove}
                  className="inline-flex items-center justify-center w-4 h-4 text-primary hover:text-primary/80 hover:bg-primary/10 rounded-full transition-colors"
                  aria-label={`Remove ${filter.label} filter`}
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 h-auto"
            >
              Clear all
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
