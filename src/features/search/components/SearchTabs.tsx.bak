import { useFilter } from '@/contexts/FilterContext';
import { SearchMode } from '@/lib/types';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Sparkles, Users } from 'lucide-react';

export function SearchTabs({ className = '' }: { className?: string }) {
  const { filterState, setSearchMode } = useFilter();

  const handleTabChange = (value: string) => {
    setSearchMode(value as SearchMode);
  };

  return (
    <div className={`flex ${className}`}>
      <Tabs value={filterState.searchMode} onValueChange={handleTabChange} className="w-auto">
        <TabsList className="grid grid-cols-2 h-8 bg-sage-50/60 rounded-xl p-1 border border-sage-200/25">
          <TabsTrigger
            value="sessions"
            className="flex items-center gap-1 px-3 py-1 text-sm font-medium rounded-lg transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600"
            data-active={filterState.searchMode === 'sessions'}
          >
            <Sparkles className="h-3.5 w-3.5" />
            <span>Sessions</span>
          </TabsTrigger>
          <TabsTrigger
            value="teachers"
            className="flex items-center gap-1 px-3 py-1 text-sm font-medium rounded-lg transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600"
            data-active={filterState.searchMode === 'teachers'}
          >
            <Users className="h-3.5 w-3.5" />
            <span>Teachers</span>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
