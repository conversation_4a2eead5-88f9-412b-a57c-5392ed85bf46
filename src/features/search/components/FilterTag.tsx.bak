import { X } from 'lucide-react';
import { FilterTag as FilterTagType } from '@/lib/types';
import { Button } from '@/components/ui/button';

interface FilterTagProps {
  tag: FilterTagType;
  onRemove: () => void;
}

function FilterTagComponent({ tag, onRemove }: FilterTagProps) {
  // Define color classes based on tag type for our warm minimalist design
  // Ensuring high contrast ratios (minimum 4.5:1) for accessibility
  const getTagClasses = () => {
    switch (tag.type) {
      case 'sessionType':
        return 'bg-primary/10 text-primary-foreground border-primary/20';
      case 'timeOfDay':
        return 'bg-accent/10 text-accent-foreground border-accent/20';
      case 'date':
        return 'bg-primary/5 text-primary-foreground border-primary/15';
      case 'skillLevel':
        return 'bg-secondary/20 text-secondary-foreground border-secondary/30';
      case 'format':
        return 'bg-primary/5 text-primary-foreground border-primary/15';
      case 'language':
        return 'bg-accent/5 text-accent-foreground border-accent/15';
      case 'price':
        return 'bg-secondary/20 text-secondary-foreground border-secondary/30';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  // Get icon and aria label based on tag type for accessibility
  const getTagInfo = () => {
    switch (tag.type) {
      case 'sessionType':
        return { icon: '🏷️', label: 'Category' };
      case 'timeOfDay':
        return { icon: '🕒', label: 'Time' };
      case 'date':
        return { icon: '📅', label: 'Date' };
      case 'skillLevel':
        return { icon: '📊', label: 'Level' };
      case 'format':
        return { icon: '👥', label: 'Format' };
      case 'language':
        return { icon: '🌐', label: 'Language' };
      case 'price':
        return { icon: '💰', label: 'Price' };
      default:
        return { icon: null, label: 'Filter' };
    }
  };

  const { icon, label } = getTagInfo();
  const colorClasses = getTagClasses();
  const accessibleLabel = `${label}: ${tag.label}`;

  return (
    <div
      className={`inline-flex items-center px-3 py-1.5 rounded-md text-xs font-medium border ${colorClasses} shadow-sm transition-all duration-200 font-outfit`}
      role="status"
      aria-label={accessibleLabel}
    >
      {icon && (
        <span className="mr-1.5" aria-hidden="true">
          {icon}
        </span>
      )}
      <span>{tag.label}</span>
      <button
        onClick={onRemove}
        className="ml-2 p-0.5 rounded-full hover:bg-black/10 focus:bg-black/10 transition-colors focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-accent"
        aria-label={`Remove ${accessibleLabel} filter`}
        type="button"
      >
        <X className="h-3 w-3" aria-hidden="true" />
      </button>
    </div>
  );
}

// Export the component as FilterTag
export { FilterTagComponent as FilterTag };
