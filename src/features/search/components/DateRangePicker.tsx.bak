import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, X, ChevronDown, ToggleLeft, ToggleRight } from 'lucide-react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

interface DateRangePickerProps {
    onDateChange: (dates: { from?: Date; to?: Date; preset?: string; multipleDates?: Date[] } | null) => void;
    selectedDates?: { from?: Date; to?: Date; preset?: string; multipleDates?: Date[] } | null;
    className?: string;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export function DateRangePicker({
    onDateChange,
    selectedDates,
    className = '',
    open,
    onOpenChange
}: DateRangePickerProps) {
    const [dateRange, setDateRange] = useState<DateRange | undefined>(
        selectedDates?.from ? { from: selectedDates.from, to: selectedDates.to } : undefined
    );
    const [multipleDates, setMultipleDates] = useState<Date[]>(selectedDates?.multipleDates || []);
    const [isRangeMode, setIsRangeMode] = useState<boolean>(false);

    const handleSelect = (selected: DateRange | undefined, selectedDay?: Date) => {
        if (isRangeMode) {
            // Range selection mode
            setDateRange(selected);
            setMultipleDates([]); // Clear individual dates when switching to range
            onDateChange(selected ? { from: selected.from, to: selected.to } : null);
        } else {
            // Individual date selection mode
            if (selectedDay) {
                const newMultipleDates = [...multipleDates];
                const dateIndex = newMultipleDates.findIndex(date =>
                    date.toDateString() === selectedDay.toDateString()
                );

                if (dateIndex > -1) {
                    // Remove date if already selected
                    newMultipleDates.splice(dateIndex, 1);
                } else {
                    // Add date if not selected
                    newMultipleDates.push(selectedDay);
                }

                setMultipleDates(newMultipleDates);
                setDateRange(undefined); // Clear range when selecting individual dates
                onDateChange(newMultipleDates.length > 0 ? { multipleDates: newMultipleDates } : null);
            }
        }
    };

    const handleClear = () => {
        setDateRange(undefined);
        setMultipleDates([]);
        onDateChange(null);
        // Don't close the calendar - let user click outside to close
    };

    const toggleRangeMode = () => {
        const newRangeMode = !isRangeMode;
        setIsRangeMode(newRangeMode);

        // Clear selections when switching modes
        setDateRange(undefined);
        setMultipleDates([]);
        onDateChange(null);
    };

    const getDisplayText = () => {
        // Always show "Choose dates" regardless of selection
        return 'Choose dates';
    };

    const hasSelection = (isRangeMode && dateRange?.from) || (!isRangeMode && multipleDates.length > 0);

    return (
        <Popover open={open} onOpenChange={onOpenChange}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className="w-fit justify-start text-left font-normal bg-white border-none hover:bg-gray-50 transition-colors shadow-sm px-4 py-2.5 text-sm rounded-lg"
                >
                    <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                    <span className="text-gray-700">
                        {getDisplayText()}
                    </span>
                    <ChevronDown className="ml-2 h-4 w-4 text-gray-400" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 bg-white border border-gray-200 shadow-lg rounded-lg" align="start">
                <div className="p-4">
                    {/* Calendar with sage green styling - no toggle at top */}
                    {isRangeMode ? (
                        <Calendar
                            mode="range"
                            defaultMonth={dateRange?.from}
                            selected={dateRange}
                            onSelect={handleSelect}
                            numberOfMonths={1}
                            className="p-0"
                            classNames={{
                                months: "flex flex-col space-y-2",
                                month: "space-y-2",
                                caption: "flex justify-center pt-1 relative items-center mb-2",
                                caption_label: "text-sm font-semibold",
                                nav: "space-x-1 flex items-center",
                                nav_button: "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-[#84A59D]/10 hover:text-gray-900 h-7 w-7",
                                table: "w-full border-collapse space-y-1",
                                head_row: "flex",
                                head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                                row: "flex w-full mt-2",
                                cell: "h-8 w-8 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-[#84A59D]/20 [&:has([aria-selected])]:bg-[#84A59D]/20 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                                day: "h-8 w-8 p-0 font-normal hover:bg-[#84A59D]/10 rounded-md transition-colors aria-selected:opacity-100 text-gray-900",
                                day_range_end: "day-range-end",
                                day_range_start: "day-range-start",
                                day_selected: "bg-[#84A59D]/20 text-gray-900 hover:bg-[#84A59D]/30 hover:text-gray-900 focus:bg-[#84A59D]/20 focus:text-gray-900 rounded-md",
                                day_today: "bg-[#84A59D]/10 text-gray-900 font-semibold hover:bg-[#84A59D]/20",
                                day_outside: "day-outside text-muted-foreground opacity-50 aria-selected:bg-[#84A59D]/20 aria-selected:text-muted-foreground aria-selected:opacity-30",
                                day_disabled: "text-muted-foreground opacity-50",
                                day_range_middle: "aria-selected:bg-[#84A59D]/10 aria-selected:text-gray-900 hover:bg-[#84A59D]/20",
                                day_hidden: "invisible",
                            }}
                        />
                    ) : (
                        <Calendar
                            mode="multiple"
                            defaultMonth={multipleDates[0]}
                            selected={multipleDates}
                            onSelect={(dates) => {
                                // Handle multiple date selection
                                if (dates) {
                                    setMultipleDates(dates as Date[]);
                                    onDateChange({ multipleDates: dates as Date[] });
                                } else {
                                    setMultipleDates([]);
                                    onDateChange(null);
                                }
                            }}
                            numberOfMonths={1}
                            className="p-0"
                            classNames={{
                                months: "flex flex-col space-y-2",
                                month: "space-y-2",
                                caption: "flex justify-center pt-1 relative items-center mb-2",
                                caption_label: "text-sm font-semibold",
                                nav: "space-x-1 flex items-center",
                                nav_button: "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-[#84A59D]/10 hover:text-gray-900 h-7 w-7",
                                table: "w-full border-collapse space-y-1",
                                head_row: "flex",
                                head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                                row: "flex w-full mt-2",
                                cell: "h-8 w-8 text-center text-sm p-0 relative",
                                day: "h-8 w-8 p-0 font-normal hover:bg-[#84A59D]/10 rounded-md transition-colors aria-selected:opacity-100 text-gray-900",
                                day_selected: "bg-[#84A59D]/20 text-gray-900 hover:bg-[#84A59D]/30 hover:text-gray-900 focus:bg-[#84A59D]/20 focus:text-gray-900 rounded-md",
                                day_today: "bg-[#84A59D]/10 text-gray-900 font-semibold hover:bg-[#84A59D]/20",
                                day_outside: "day-outside text-muted-foreground opacity-50",
                                day_disabled: "text-muted-foreground opacity-50",
                                day_hidden: "invisible",
                            }}
                        />
                    )}

                    {/* Bottom section with Clear and Range Mode Toggle */}
                    <div className="flex items-center justify-between pt-3 mt-3 border-t border-gray-100">
                        <button
                            onClick={handleClear}
                            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                        >
                            Clear
                        </button>

                        {/* Range Mode Toggle - moved to bottom right */}
                        <button
                            onClick={toggleRangeMode}
                            className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <span className="text-xs">Range mode</span>
                            {isRangeMode ? (
                                <ToggleRight className="h-5 w-5 text-[#84A59D]" />
                            ) : (
                                <ToggleLeft className="h-5 w-5 text-gray-400" />
                            )}
                        </button>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    );
} 