import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
  useRef,
} from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ConversationWithMessages } from '@shared/schema';
import { AppError, ErrorCategory, ErrorSeverity, handleError } from '@/lib/error-handler';
import { supabase } from '@/lib/supabase-singleton';

// Caching constants
const CONVERSATION_CACHE_KEY = 'cached_conversations';
const FIVE_MINUTES = 5 * 60 * 1000;

interface ConversationContextType {
  conversations: ConversationWithMessages[];
  activeConversation: ConversationWithMessages | null;
  isLoading: boolean;
  error: AppError | null;
  setActiveConversation: (conversation: ConversationWithMessages | null) => void;
  setActiveConversationId: (conversationId: number | null) => void;
  fetchConversations: () => Promise<void>;
  createConversation: (participantIds: number[]) => Promise<ConversationWithMessages | null>;
  startNewConversation: (
    participantIds: number[],
    initialMessage?: string
  ) => Promise<ConversationWithMessages | null>;
  markConversationAsRead: (conversationId: number) => void;
  openConversationInPopup: (conversationId: number) => void;
  setConversations: (
    conversations:
      | ConversationWithMessages[]
      | ((prev: ConversationWithMessages[]) => ConversationWithMessages[])
  ) => void;
}

export const ConversationContext = createContext<ConversationContextType | null>(null);

export function ConversationProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeConversation, setActiveConversation] = useState<ConversationWithMessages | null>(
    null
  );
  const lastFetchRef = useRef<number>(0);

  // Initialize conversations from cache if available
  const [conversations, setConversations] = useState<ConversationWithMessages[]>(() => {
    if (typeof window !== 'undefined') {
      try {
        const cached = localStorage.getItem(CONVERSATION_CACHE_KEY);
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          // Use cache if less than 5 minutes old
          if (Date.now() - timestamp < FIVE_MINUTES) {
            return data;
          }
        }
      } catch (e) {
        console.error('Error reading from cache:', e);
      }
    }
    return [];
  });

  // Debounce fetch operations to prevent excessive API calls
  const shouldFetch = useCallback(() => {
    const now = Date.now();
    // Only fetch if last fetch was more than 30 seconds ago
    if (now - lastFetchRef.current > 30000) {
      lastFetchRef.current = now;
      return true;
    }
    return false;
  }, []);

  // Fetch user's conversations
  const {
    data: conversationsData = [],
    isLoading,
    error,
    refetch: refetchConversations,
  } = useQuery({
    queryKey: user ? ['/api/users', user.id, 'conversations'] : ['/api/empty-conversations'],
    queryFn: async () => {
      if (!user) return [];
      try {
        console.log(`[ConversationContext] Fetching conversations for user ${user.id}`);

        // Try to use cached data first if available
        if (typeof window !== 'undefined') {
          try {
            const cached = localStorage.getItem(CONVERSATION_CACHE_KEY);
            if (cached) {
              const { data, timestamp } = JSON.parse(cached);
              // Use cache if less than 5 minutes old
              if (Date.now() - timestamp < FIVE_MINUTES) {
                console.log('[ConversationContext] Using cached conversations data');
                return data;
              }
            }
          } catch (e) {
            console.error('[ConversationContext] Error reading from cache:', e);
          }
        }

        // Get Supabase session for authentication
        const { data: { session } } = await supabase.auth.getSession();

        // Fetch conversations from API
        const { data, response } = await apiRequest(`/api/users/${user.id}/conversations`, {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            ...(session?.access_token ? { 'Authorization': `Bearer ${session.access_token}` } : {})
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch conversations: ${response.status}`);
        }

        // Update cache
        if (typeof window !== 'undefined') {
          localStorage.setItem(
            CONVERSATION_CACHE_KEY,
            JSON.stringify({
              data,
              timestamp: Date.now(),
            })
          );
        }

        return data;
      } catch (error) {
        console.error('[ConversationContext] Error fetching conversations:', error);
        throw error;
      }
    },
    enabled: !!user,
    staleTime: FIVE_MINUTES, // Only refetch after 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: true,
  });

  // Update conversations when data changes
  useEffect(() => {
    if (conversationsData && conversationsData.length > 0) {
      // Only update if the data has actually changed to prevent unnecessary re-renders
      setConversations(prev => {
        // Compare the data to see if it's actually different
        if (prev.length !== conversationsData.length) {
          return conversationsData;
        }

        // Check if any conversation IDs have changed
        const prevIds = prev.map((c: ConversationWithMessages) => String(c.id)).sort();
        const newIds = conversationsData.map((c: ConversationWithMessages) => String(c.id)).sort();
        const hasChanged = prevIds.some((id, index) => id !== newIds[index]);

        if (hasChanged) {
          return conversationsData;
        }

        // No changes, return previous state
        return prev;
      });
    }
  }, [conversationsData]);

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async (participantIds: number[]) => {
      if (!user) throw new Error('User not authenticated');

      // Get Supabase session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      const { data, response } = await apiRequest('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(session?.access_token ? { 'Authorization': `Bearer ${session.access_token}` } : {})
        },
        body: JSON.stringify({
          participantIds: [...participantIds, user.id],
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create conversation');
      }

      return data;
    },
    onSuccess: data => {
      // Add the new conversation to the list only if data is not null
      if (data) {
        setConversations(prev => [data, ...prev]);
      }

      // Invalidate conversations query
      queryClient.invalidateQueries({ queryKey: ['/api/users', user?.id, 'conversations'] });

      return data;
    },
    onError: error => {
      console.error('[ConversationContext] Error creating conversation:', error);

      // Use standardized error handling
      handleError(error, 'Failed to create conversation', undefined, {
        action: 'createConversation',
        participantIds: createConversationMutation.variables,
      });
    },
  });

  // Start new conversation mutation (create + send initial message)
  const startNewConversationMutation = useMutation({
    mutationFn: async ({
      participantIds,
      initialMessage,
    }: {
      participantIds: number[];
      initialMessage?: string;
    }) => {
      if (!user) throw new Error('User not authenticated');

      // First create the conversation
      const conversation = await createConversationMutation.mutateAsync(participantIds);

      // If there's an initial message, send it
      if (initialMessage && conversation) {
        // Get Supabase session for authentication
        const { data: { session } } = await supabase.auth.getSession();

        await apiRequest(`/api/conversations/${(conversation as ConversationWithMessages).id}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(session?.access_token ? { 'Authorization': `Bearer ${session.access_token}` } : {})
          },
          body: JSON.stringify({
            content: initialMessage,
            senderId: user.id,
          }),
        });
      }

      return conversation;
    },
    onSuccess: data => {
      // Set as active conversation
      setActiveConversation(data);

      return data;
    },
    onError: error => {
      console.error('[ConversationContext] Error starting conversation:', error);

      // Use standardized error handling
      handleError(error, 'Failed to start conversation', undefined, {
        action: 'startNewConversation',
        participantIds: startNewConversationMutation.variables?.participantIds,
      });
    },
  });

  // Mark conversation as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (conversationId: number) => {
      if (!user) throw new Error('User not authenticated');

      // Get Supabase session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      const { response } = await apiRequest(`/api/conversations/${conversationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(session?.access_token ? { 'Authorization': `Bearer ${session.access_token}` } : {})
        },
        body: JSON.stringify({
          userId: user.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to mark conversation as read');
      }

      return true;
    },
    onSuccess: () => {
      // Invalidate unread count query
      queryClient.invalidateQueries({ queryKey: ['/api/users', user?.id, 'unread-count'] });
    },
    onError: error => {
      console.error('[ConversationContext] Error marking conversation as read:', error);

      // Use standardized error handling with silent option
      handleError(error, 'Failed to mark conversation as read', undefined, {
        action: 'markConversationAsRead',
        conversationId: markAsReadMutation.variables,
        silent: true, // Don't show toast for this background operation
      });
    },
  });

  // Set active conversation by ID
  const setActiveConversationId = useCallback(
    (conversationId: number | null) => {
      if (conversationId === null) {
        setActiveConversation(null);
        return;
      }

      const conversation = conversations.find(c => Number(c.id) === Number(conversationId));
      if (conversation) {
        setActiveConversation(conversation);

        // Mark as read when setting as active
        markAsReadMutation.mutate(conversationId);
      } else {
        console.error(`[ConversationContext] Conversation with ID ${conversationId} not found`);
      }
    },
    [conversations, markAsReadMutation]
  );

  // Fetch conversations manually
  const fetchConversations = useCallback(async () => {
    if (!user || !shouldFetch()) return;

    try {
      await refetchConversations();
    } catch (error) {
      console.error('[ConversationContext] Error fetching conversations:', error);

      // Use standardized error handling with silent option
      handleError(error, 'Failed to load conversations', undefined, {
        action: 'fetchConversations',
        userId: user?.id,
        silent: true, // Don't show toast for this background operation
      });
    }
  }, [user, shouldFetch, refetchConversations]);

  // Create a new conversation
  const createConversation = useCallback(
    async (participantIds: number[]) => {
      if (!user) return null;

      try {
        return await createConversationMutation.mutateAsync(participantIds);
      } catch (error) {
        console.error('[ConversationContext] Error creating conversation:', error);

        // Use standardized error handling
        handleError(error, 'Failed to create conversation', undefined, {
          action: 'createConversation',
          participantIds,
        });

        return null;
      }
    },
    [user, createConversationMutation]
  );

  // Start a new conversation with an initial message
  const startNewConversation = useCallback(
    async (participantIds: number[], initialMessage?: string) => {
      if (!user) return null;

      try {
        return await startNewConversationMutation.mutateAsync({ participantIds, initialMessage });
      } catch (error) {
        console.error('[ConversationContext] Error starting conversation:', error);

        // Use standardized error handling
        handleError(error, 'Failed to start conversation', undefined, {
          action: 'startNewConversation',
          participantIds,
          hasInitialMessage: !!initialMessage,
        });

        return null;
      }
    },
    [user, startNewConversationMutation]
  );

  // Mark a conversation as read
  const markConversationAsRead = useCallback(
    (conversationId: number) => {
      if (!user) return;

      markAsReadMutation.mutate(conversationId);
    },
    [user, markAsReadMutation]
  );

  // Open conversation in popup
  const openConversationInPopup = useCallback((conversationId: number) => {
    if (typeof window === 'undefined') return;

    const width = 400;
    const height = 600;
    const left = window.innerWidth / 2 - width / 2;
    const top = window.innerHeight / 2 - height / 2;

    window.open(
      `/messages/${conversationId}?popup=true`,
      `conversation_${conversationId}`,
      `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    );
  }, []);

  // Create context value
  const contextValue: ConversationContextType = {
    conversations,
    activeConversation,
    isLoading,
    error: error
      ? handleError(error as Error, 'Error loading conversations', undefined, { silent: true })
      : null,
    setActiveConversation,
    setActiveConversationId,
    fetchConversations,
    createConversation,
    startNewConversation,
    markConversationAsRead,
    openConversationInPopup,
    setConversations,
  };

  return (
    <ConversationContext.Provider value={contextValue}>{children}</ConversationContext.Provider>
  );
}

// Custom hook to use the conversation context
export function useConversation() {
  const context = useContext(ConversationContext);
  if (context === null) {
    throw new Error('useConversation must be used within a ConversationProvider');
  }
  return context;
}
