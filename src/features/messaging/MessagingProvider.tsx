import { ReactNode } from 'react';
import { ConversationProvider } from './ConversationContext';
import { MessageProvider } from './MessageContext';

interface MessagingProviderProps {
  children: ReactNode;
}

export function MessagingProvider({ children }: MessagingProviderProps) {
  return (
    <ConversationProvider>
      <MessageProvider>{children}</MessageProvider>
    </ConversationProvider>
  );
}

// Helper to clear conversation caches
export const clearConversationCache = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('cached_conversations');
    localStorage.removeItem('cached_unread_count');
    console.log('Cleared conversation cache');
  }
};
