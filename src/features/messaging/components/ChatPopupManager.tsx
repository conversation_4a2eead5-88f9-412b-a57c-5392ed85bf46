import React, { useCallback, useEffect, useState } from 'react';
import { useMessages } from '@/features/messaging';
import { useAuth } from '@/features/auth';
import { ChatPopup } from './ChatPopup';

// Polyfills for the custom hooks to prevent build errors
// In a production environment, these would be separate files
// useLocalStorage hook implementation
function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      // Save state
      setStoredValue(valueToStore);

      // Save to local storage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

// Event emitter implementation
type EventHandler<T = any> = (data: T) => void;
type EventMap = Record<string, EventHandler[]>;

const eventBus: EventMap = {};

const eventEmitter = {
  subscribe<T = any>(eventName: string, handler: EventHandler<T>) {
    // Initialize the event array if it doesn't exist
    if (!eventBus[eventName]) {
      eventBus[eventName] = [];
    }

    // Add the handler to the array
    eventBus[eventName].push(handler as EventHandler);

    // Return a function to unsubscribe
    return () => {
      eventBus[eventName] = eventBus[eventName].filter(h => h !== handler);
    };
  },

  emit<T = any>(eventName: string, data?: T) {
    if (eventBus[eventName]) {
      eventBus[eventName].forEach(handler => {
        handler(data);
      });
    }
  },
};

// Constants
const LOCAL_STORAGE_KEY = 'session-hub-chat-states';
const MAX_VISIBLE_CHATS = 3;

interface ChatPopupPosition {
  right: number;
}

interface ChatPopupState {
  conversationId: number;
  isMinimized: boolean;
  position: ChatPopupPosition;
  initialMessage?: string;
  popupKey?: string;
  showCancellationReasons?: boolean;
  bookingId?: number;
}

interface ConversationStates {
  [key: number]: ChatPopupState;
}

type StateUpdater = (prev: ConversationStates) => ConversationStates;

// Helper to start a chat with a specific user
export const startChatWithUser = (
  userId: number,
  initialMessage?: string,
  options?: { showCancellationReasons?: boolean; bookingId?: number }
): void => {
  // Dispatch a custom event to start a chat with a user, optionally with a pre-filled message and options
  eventEmitter.emit('startChatWithUser', { userId, initialMessage, ...options });
};

// Helper to open an existing chat
export const openChat = (conversationId: number): void => {
  // Dispatch a custom event to open the chat popup
  eventEmitter.emit('openChat', conversationId);
};

export function ChatPopupManager(): React.ReactElement | null {
  const { conversations } = useMessages();
  const { user } = useAuth();
  const [openChats, setOpenChats] = useLocalStorage<ConversationStates>(LOCAL_STORAGE_KEY, {});

  // Calculate new popup position
  const calculatePosition = useCallback((existingChats: ConversationStates): ChatPopupPosition => {
    const chatCount = Object.keys(existingChats).length;
    const basePosition = 20; // base right position in pixels
    const spacing = 340; // width of chat + margin

    return {
      right: basePosition + chatCount * spacing,
    };
  }, []);

  // Open a chat popup
  const openChatPopup = useCallback(
    (
      conversationId: number,
      initialMessage?: string,
      options?: { showCancellationReasons?: boolean; bookingId?: number }
    ) => {
      setOpenChats((prev: ConversationStates): ConversationStates => {
        const forceShowCancellation = options?.showCancellationReasons === true;
        const popupKey = initialMessage
          ? `${conversationId}-${btoa(encodeURIComponent(initialMessage)).slice(0, 8)}`
          : `${conversationId}`;
        const nextState = prev[conversationId]
          ? {
              ...prev,
              [conversationId]: {
                ...prev[conversationId],
                isMinimized: false,
                ...(initialMessage ? { initialMessage, popupKey } : {}),
                ...(forceShowCancellation ? { showCancellationReasons: true } : {}),
                ...(options?.bookingId ? { bookingId: options.bookingId } : {}),
              },
            }
          : {
              ...prev,
              [conversationId]: {
                conversationId,
                isMinimized: false,
                position: calculatePosition(prev),
                ...(initialMessage ? { initialMessage, popupKey } : {}),
                ...(forceShowCancellation ? { showCancellationReasons: true } : {}),
                ...(options?.bookingId ? { bookingId: options.bookingId } : {}),
              },
            };
        console.log('[openChatPopup] popup state for', conversationId, nextState[conversationId]);
        return nextState;
      });
    },
    [calculatePosition, setOpenChats]
  );

  // Handle starting a chat with a user, optionally sending an initial message
  const handleStartChatWithUser = useCallback(
    async (payload: {
      userId: number;
      initialMessage?: string;
      showCancellationReasons?: boolean;
      bookingId?: number;
    }): Promise<void> => {
      const { userId, initialMessage, showCancellationReasons, bookingId } = payload;
      console.log('[ChatPopupManager] handleStartChatWithUser', {
        userId,
        initialMessage,
        showCancellationReasons,
        bookingId,
      });
      if (!user?.id) return;
      // Find if there's an existing conversation with this user
      const existingConversation = conversations.find(conv =>
        conv.participants.some(p => p.id === userId)
      );
      if (existingConversation) {
        openChatPopup(existingConversation.id, initialMessage, {
          showCancellationReasons,
          bookingId,
        });
      } else {
        try {
          // Create a new conversation
          const response = await fetch('/api/conversations/find-or-create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              participantIds: [userId, user.id],
            }),
          });
          if (!response.ok) {
            throw new Error('Failed to create conversation');
          }
          const newConversation = await response.json();
          if (newConversation?.id) {
            openChatPopup(newConversation.id, initialMessage, {
              showCancellationReasons,
              bookingId,
            });
          }
        } catch (error) {
          console.error('Error creating conversation:', error);
        }
      }
    },
    [conversations, openChatPopup, user?.id]
  );

  // Close a chat popup
  const closeChatPopup = useCallback(
    (conversationId: number): void => {
      setOpenChats((prev: ConversationStates): ConversationStates => {
        const { [conversationId]: removed, ...rest } = prev;
        return rest;
      });
    },
    [setOpenChats]
  );

  // Toggle minimize state
  const toggleMinimize = useCallback(
    (conversationId: number): void => {
      setOpenChats((prev: ConversationStates): ConversationStates => {
        if (!prev[conversationId]) return prev;

        return {
          ...prev,
          [conversationId]: {
            ...prev[conversationId],
            isMinimized: !prev[conversationId].isMinimized,
          },
        };
      });
    },
    [setOpenChats]
  );

  // Handle deleting a conversation
  const deleteConversation = useCallback(
    (conversationId: number): void => {
      // Close the chat popup
      closeChatPopup(conversationId);

      // Emit an event to delete the conversation from the MessageContext
      eventEmitter.emit('deleteConversation', conversationId);
    },
    [closeChatPopup]
  );

  // Subscribe to events
  useEffect(() => {
    if (!user) return;

    // Handle openChat events
    const unsubscribeOpenChat = eventEmitter.subscribe<number>(
      'openChat',
      (conversationId: number) => {
        openChatPopup(conversationId);
      }
    );

    // Handle startChatWithUser events (now expects an object payload)
    const unsubscribeStartChat = eventEmitter.subscribe<{
      userId: number;
      initialMessage?: string;
      showCancellationReasons?: boolean;
      bookingId?: number;
    }>('startChatWithUser', payload => {
      handleStartChatWithUser(payload);
    });

    // Cleanup function
    return () => {
      unsubscribeOpenChat();
      unsubscribeStartChat();
    };
  }, [openChatPopup, handleStartChatWithUser, user]);

  // Don't render anything if user is not authenticated
  if (!user) return null;

  return (
    <div className="fixed bottom-0 right-0 z-50 flex items-end space-x-2 p-4">
      {Object.values(openChats).map((chatState: unknown) => {
        const state = chatState as ChatPopupState & {
          showCancellationReasons?: boolean;
          bookingId?: number;
        };
        const conversation = conversations.find(c => c.id === state.conversationId);
        if (!conversation) return null;

        return (
          <ChatPopup
            key={state.popupKey || state.conversationId}
            conversationId={state.conversationId}
            isMinimized={state.isMinimized}
            initialMessage={state.initialMessage}
            showCancellationReasons={state.showCancellationReasons}
            bookingId={state.bookingId}
            onToggleMinimize={() => toggleMinimize(state.conversationId)}
            onClose={() => closeChatPopup(state.conversationId)}
            onDelete={deleteConversation}
            data-conversation-id={state.conversationId}
            style={{
              position: 'fixed',
              bottom: '0',
              right: `${state.position.right}px`,
              transition: 'all 0.3s ease',
            }}
          />
        );
      })}
    </div>
  );
}
