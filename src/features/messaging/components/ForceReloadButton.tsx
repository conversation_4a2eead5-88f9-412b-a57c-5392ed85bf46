import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';
import { clearConversationCache } from '@/features/messaging/MessagingProvider';
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';
import { queryClient } from '@/lib/queryClient';

export function ForceReloadButton() {
  const { toast } = useToast();
  const [isReloading, setIsReloading] = useState(false);

  const handleForceReload = () => {
    setIsReloading(true);

    // Clear localStorage cache
    clearConversationCache();

    // Invalidate React Query cache
    queryClient.invalidateQueries();

    // Show loading toast
    toast({
      title: 'Reloading conversations',
      description: 'Please wait while we refresh your conversations...',
    });

    // Slight delay to ensure cache clearing happens
    setTimeout(() => {
      // Reload the page
      window.location.reload();
    }, 500);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="gap-2"
      onClick={handleForceReload}
      disabled={isReloading}
    >
      {isReloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="h-4 w-4" />
      )}
      Force Reload
    </Button>
  );
}
