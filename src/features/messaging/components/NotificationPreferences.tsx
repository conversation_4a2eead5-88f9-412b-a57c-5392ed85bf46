import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Bell, BellOff, AlertTriangle, Loader2 } from 'lucide-react';

interface NotificationPreference {
  new_message: boolean;
  new_booking: boolean;
  booking_reminder: boolean;
  booking_changes: boolean;
}

export const NotificationPreferencesComponent = () => {
  const [preferences, setPreferences] = useState<NotificationPreference>({
    new_message: true,
    new_booking: true,
    booking_reminder: true,
    booking_changes: true,
  });

  const [webPushEnabled, setWebPushEnabled] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { toast } = useToast();

  // Check if web push is available in this browser
  const isPushSupported = 'serviceWorker' in navigator && 'PushManager' in window;

  // Initial loading of preferences
  useEffect(() => {
    const fetchPreferences = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/notification-preferences', {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch notification preferences');
        }

        const data = await response.json();
        setPreferences(data);

        // Also check if the user has registered for web push
        const deviceResponse = await fetch('/api/devices', {
          credentials: 'include',
        });

        if (deviceResponse.ok) {
          const devices = await deviceResponse.json();
          setWebPushEnabled(devices.some((device: any) => device.platform === 'web'));
        }

        setError(null);
      } catch (error) {
        console.error('Error fetching notification preferences:', error);
        setError('Failed to load notification preferences');
      } finally {
        setLoading(false);
      }
    };

    fetchPreferences();
  }, []);

  // Save preferences to server
  const savePreferences = async () => {
    try {
      setSaving(true);

      const response = await fetch('/api/notification-preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to save notification preferences');
      }

      toast({
        title: 'Preferences saved',
        description: 'Your notification preferences have been updated.',
      });

      setError(null);
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      setError('Failed to save notification preferences');

      toast({
        title: 'Error',
        description: 'Failed to save your notification preferences.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Enable web push notifications
  const enableWebPush = async () => {
    try {
      setSaving(true);

      if (!isPushSupported) {
        throw new Error('Push notifications are not supported in this browser');
      }

      // Request permission
      const permission = await Notification.requestPermission();

      if (permission !== 'granted') {
        throw new Error('Permission denied for notifications');
      }

      // Register service worker (in a real app this would be implemented)
      // const registration = await navigator.serviceWorker.register('/service-worker.js');

      // Get push subscription (this is a mock for demonstration)
      // const subscription = await registration.pushManager.subscribe({
      //   userVisibleOnly: true,
      //   applicationServerKey: 'your-public-key'
      // });

      // For demo purposes we'll use a mock token
      const mockToken = 'web-push-token-' + Math.random().toString(36).substring(7);

      // Register with backend
      const response = await fetch('/api/devices/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: mockToken,
          platform: 'web',
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to register for web push notifications');
      }

      setWebPushEnabled(true);

      toast({
        title: 'Web notifications enabled',
        description: 'You will now receive notifications in this browser.',
      });

      setError(null);
    } catch (error) {
      console.error('Error enabling web push:', error);
      setError(error instanceof Error ? error.message : 'Failed to enable web push notifications');

      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to enable web push notifications',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Disable web push notifications
  const disableWebPush = async () => {
    try {
      setSaving(true);

      // In a real app we would unsubscribe from the push service
      // and remove the subscription from the server

      // For demo purposes we'll use a mock token
      const mockToken = 'web-push-token-mock';

      // Unregister with backend
      const response = await fetch(`/api/devices/${mockToken}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to unregister web push notifications');
      }

      setWebPushEnabled(false);

      toast({
        title: 'Web notifications disabled',
        description: 'You will no longer receive notifications in this browser.',
      });

      setError(null);
    } catch (error) {
      console.error('Error disabling web push:', error);
      setError('Failed to disable web push notifications');

      toast({
        title: 'Error',
        description: 'Failed to disable web push notifications',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle preference toggle changes
  const handleToggle = (key: keyof NotificationPreference) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  if (loading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl flex items-center">
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading Preferences
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl">Notification Preferences</CardTitle>
        <CardDescription>
          Choose how you want to be notified about activity in your account
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <div className="bg-destructive/15 p-3 rounded-md flex items-center text-sm mb-4">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <span>{error}</span>
          </div>
        )}

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="new_message" className="flex-1">
              New message notifications
            </Label>
            <Switch
              id="new_message"
              checked={preferences.new_message}
              onCheckedChange={() => handleToggle('new_message')}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="new_booking" className="flex-1">
              New booking notifications
            </Label>
            <Switch
              id="new_booking"
              checked={preferences.new_booking}
              onCheckedChange={() => handleToggle('new_booking')}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="booking_reminder" className="flex-1">
              Session reminder notifications
            </Label>
            <Switch
              id="booking_reminder"
              checked={preferences.booking_reminder}
              onCheckedChange={() => handleToggle('booking_reminder')}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="booking_changes" className="flex-1">
              Session changes notifications
            </Label>
            <Switch
              id="booking_changes"
              checked={preferences.booking_changes}
              onCheckedChange={() => handleToggle('booking_changes')}
            />
          </div>
        </div>

        <div className="mt-6 pt-4 border-t">
          <h3 className="font-medium mb-3">Browser Notifications</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {isPushSupported
              ? 'Enable browser notifications to get alerts even when the app is closed.'
              : 'Your browser does not support push notifications.'}
          </p>

          {isPushSupported && (
            <Button
              variant={webPushEnabled ? 'destructive' : 'secondary'}
              onClick={webPushEnabled ? disableWebPush : enableWebPush}
              disabled={saving}
              className="w-full"
            >
              {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {webPushEnabled ? (
                <>
                  <BellOff className="h-4 w-4 mr-2" />
                  Disable Browser Notifications
                </>
              ) : (
                <>
                  <Bell className="h-4 w-4 mr-2" />
                  Enable Browser Notifications
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <Button onClick={savePreferences} disabled={saving || loading} className="w-full">
          {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Preferences
        </Button>
      </CardFooter>
    </Card>
  );
};

export const NotificationPreferences = NotificationPreferencesComponent;
