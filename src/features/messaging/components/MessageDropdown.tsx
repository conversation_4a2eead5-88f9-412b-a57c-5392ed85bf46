import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useMessages } from '@/features/messaging';
import { useConversation } from '@/features/messaging/ConversationContext';
import { useAuth } from '@/hooks/use-auth';
import { formatTimeAgo } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MessageSquare, X, ChevronRight, Search } from 'lucide-react';
import { getInitials } from '@/lib/utils';
import { ConversationWithMessages } from '@shared/schema';

export function MessageDropdown() {
  const { unreadCount } = useMessages();
  const { conversations, setActiveConversation, startNewConversation } = useConversation();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize recent conversations to avoid unnecessary processing on every render
  const recentConversations = useMemo(() => {
    return conversations
      .sort((a, b) => {
        // Handle both possible property names for last message timestamp
        const aTime = (a as any).lastMessageAt || (a as any).last_message_at || (a as any).updated_at || 0;
        const bTime = (b as any).lastMessageAt || (b as any).last_message_at || (b as any).updated_at || 0;
        return new Date(bTime).getTime() - new Date(aTime).getTime();
      })
      .slice(0, 5);
  }, [conversations]);

  // Function to get other participants from a conversation - memoize to avoid recalculation
  const getOtherParticipants = useCallback(
    (conversation: ConversationWithMessages) => {
      if (!user) return [];

      // Handle both old and new conversation structures
      if ('participants' in conversation && Array.isArray(conversation.participants)) {
        return conversation.participants.filter(participant => participant.id !== user.id);
      }

      // Handle participant_1/participant_2 structure
      const participants: any[] = [];
      if (conversation.participant_1 && conversation.participant_1.id !== user.id) {
        participants.push(conversation.participant_1);
      }
      if (conversation.participant_2 && conversation.participant_2.id !== user.id) {
        participants.push(conversation.participant_2);
      }
      return participants;
    },
    [user]
  );

  // Function to get conversation name - memoize to avoid recalculation
  const getConversationName = useCallback(
    (conversation: ConversationWithMessages) => {
      const others = getOtherParticipants(conversation);
      if (others.length === 0) return 'No participants';
      if (others.length === 1) return others[0].name;
      return `${others[0].name} and ${others.length - 1} more`;
    },
    [getOtherParticipants]
  );

  // Function to get conversation preview text - memoize to avoid recalculation
  const getConversationPreview = useCallback((conversation: ConversationWithMessages) => {
    if (conversation.messages.length === 0) return 'No messages yet';
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    return lastMessage.content.length > 30
      ? lastMessage.content.substring(0, 30) + '...'
      : lastMessage.content;
  }, []);

  // Function to handle opening a conversation - now navigates to the messages page
  const handleOpenConversation = useCallback(
    (conversation: ConversationWithMessages) => {
      // Update the active conversation in context (useful for when we land on the messages page)
      setActiveConversation(conversation);
      // Navigate to the message page with this conversation
      window.location.href = `/messages/${conversation.id}`;
    },
    [setActiveConversation]
  );

  // Function to redirect to full messages page
  const handleViewAll = useCallback(() => {
    window.location.href = '/messages';
  }, []);

  // Search for users with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    searchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await fetch(
          `/api/users/search/by-username?username=${encodeURIComponent(searchQuery)}`
        );

        if (response.ok) {
          const users = await response.json();
          setSearchResults(users);
        } else {
          setSearchResults([]);
        }
      } catch (err) {
        console.error('Error searching for users:', err);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300); // 300ms debounce for faster feedback

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Handle starting a conversation with a user from search results
  const handleStartConversationWithUser = async (userId: number) => {
    try {
      const newConversation = await startNewConversation([userId]);
      if (newConversation) {
        setSearchQuery('');
        setSearchResults([]);
        // Navigate to the messages page with the new conversation
        window.location.href = `/messages/${newConversation.id}`;
      }
    } catch (err) {
      console.error('Error starting conversation:', err);
    }
  };

  return (
    <div className="w-96 max-h-96 overflow-hidden flex flex-col">
      <div className="p-3 font-medium text-sm flex justify-between items-center">
        <span>Recent Messages</span>
        <Button
          variant="ghost"
          size="sm"
          className="text-[#E07A5F] text-xs hover:text-[#C86A52]"
          onClick={handleViewAll}
        >
          View All
        </Button>
      </div>
      <Separator />

      {/* Search input */}
      <div className="p-3 border-b">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search for users..."
            className="pl-8 pr-8 h-8 text-sm"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear search</span>
            </button>
          )}
        </div>
        {searchQuery && (
          <div className="mt-2 bg-background border rounded-md divide-y max-h-48 overflow-y-auto">
            {isSearching ? (
              <div className="p-3 text-center text-sm text-muted-foreground">Searching...</div>
            ) : searchResults.length > 0 ? (
              searchResults.map(user => (
                <div
                  key={user.id}
                  className="p-3 flex items-center gap-3 hover:bg-accent/50 cursor-pointer"
                  onClick={() => handleStartConversationWithUser(user.id)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar || undefined} alt={user.username} />
                    <AvatarFallback>{getInitials(user.username)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-sm">{user.name}</p>
                    <p className="text-xs text-muted-foreground">@{user.username}</p>
                  </div>
                </div>
              ))
            ) : searchQuery.trim() ? (
              <div className="p-3 text-center text-sm text-muted-foreground">No users found</div>
            ) : null}
          </div>
        )}
      </div>

      {recentConversations.length === 0 && !searchQuery ? (
        <div className="p-6 text-center text-sm text-gray-500">
          <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p>No messages yet</p>
          <p className="text-xs mt-1">Your conversations will appear here</p>
        </div>
      ) : !searchQuery ? (
        <div className="overflow-y-auto">
          {recentConversations.map(conversation => {
            const otherParticipant = getOtherParticipants(conversation)[0] || {};
            const lastMessage = conversation.messages[conversation.messages.length - 1];
            const hasUnread = conversation.messages.some(
              msg => {
                // Handle both possible property names for read status
                const isRead = (msg as any).read || (msg as any).is_read || false;
                const senderId = (msg as any).sender?.id || (msg as any).senderId || (msg as any).sender_id;
                return !isRead && senderId !== user?.id;
              }
            );

            return (
              <a
                key={conversation.id}
                href={`/messages/${conversation.id}`}
                className={`p-3 flex items-start space-x-3 hover:bg-gray-50 cursor-pointer transition-colors duration-200 ${hasUnread ? 'bg-[#E07A5F]/10' : ''}`}
                onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                  e.preventDefault();
                  handleOpenConversation(conversation);
                }}
              >
                <Avatar className="h-10 w-10 flex-shrink-0">
                  <AvatarImage
                    src={otherParticipant.avatar || undefined}
                    alt={otherParticipant.name || 'User'}
                  />
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {getInitials(otherParticipant.name || 'Unknown User')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <p
                      className={`text-sm font-medium truncate ${hasUnread ? 'text-[#E07A5F]' : 'text-gray-800'}`}
                    >
                      {getConversationName(conversation)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {lastMessage && formatTimeAgo(new Date(lastMessage.createdAt))}
                    </p>
                  </div>
                  <p
                    className={`text-xs truncate mt-1 ${hasUnread ? 'font-medium text-gray-800' : 'text-gray-500'}`}
                  >
                    {getConversationPreview(conversation)}
                  </p>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0 mt-1" />
              </a>
            );
          })}
        </div>
      ) : null}
    </div>
  );
}
