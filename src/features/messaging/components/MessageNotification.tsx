import React, { useEffect, useState } from 'react';
import { useWebSocket } from '@/hooks/useWebSocket';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';
import { AnimatePresence, motion } from 'framer-motion';
import { useAuth } from '@/features/auth';
import { MessageCircle } from 'lucide-react';

interface Message {
  id: number;
  conversationId: number;
  senderId: number;
  content: string;
  createdAt: string;
  read: boolean;
  sender: {
    id: number;
    name: string;
    avatar?: string;
  };
}

const MessageNotificationComponent: React.FC = () => {
  const { lastMessage } = useWebSocket();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Message[]>([]);

  // Process new messages
  useEffect(() => {
    // Only show notifications for new messages
    if (lastMessage?.type === 'new_message' && lastMessage.data) {
      const message = lastMessage.data as Message;

      // Don't notify about our own messages
      if (message.senderId === user?.id) return;

      // Add the message to our notifications
      setNotifications(prev => [message, ...prev]);

      // Play notification sound
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(err => console.log('Error playing notification sound:', err));

      // Remove notification after 5 seconds
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== message.id));
      }, 5000);
    }
  }, [lastMessage, user?.id]);

  // Handle notification click - navigate to conversation
  const handleNotificationClick = (conversationId: number) => {
    // In a real app, you'd navigate to the conversation
    window.location.href = `/messages?conversation=${conversationId}`;
  };

  return (
    <ToastProvider>
      <AnimatePresence>
        {notifications.map(message => (
          <Toast
            key={message.id}
            className="cursor-pointer"
            onClick={() => handleNotificationClick(message.conversationId)}
            asChild
          >
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-start gap-2">
                {message.sender.avatar ? (
                  <img
                    src={message.sender.avatar}
                    alt={message.sender.name}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                    <MessageCircle className="h-4 w-4 text-primary" />
                  </div>
                )}
                <div className="flex-1">
                  <ToastTitle className="text-sm font-medium">
                    New message from {message.sender.name}
                  </ToastTitle>
                  <ToastDescription className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {message.content}
                  </ToastDescription>
                </div>
              </div>
              <ToastClose />
            </motion.div>
          </Toast>
        ))}
      </AnimatePresence>
      <ToastViewport />
    </ToastProvider>
  );
};

export const MessageNotification = MessageNotificationComponent;
export { MessageNotificationComponent };
