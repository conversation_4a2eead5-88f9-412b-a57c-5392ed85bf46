import { useState, useEffect, useRef } from 'react';
import { useMessages } from '@/features/messaging';
import { useConversation } from '@/features/messaging/ConversationContext';
import { ConversationWithMessages } from '@shared/schema';
import { formatTimeAgo } from '@/lib/utils';
import { useAuth } from '@/hooks/use-auth';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { getInitials } from '@/lib/utils';
import { MessageSquare, Plus, Search, X, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ConversationCard } from './ConversationCard';

interface ConversationListProps {
  onSelectConversation?: (conversation: ConversationWithMessages) => void;
}

export function ConversationList({ onSelectConversation }: ConversationListProps) {
  const { unreadCount } = useMessages();
  const {
    conversations,
    activeConversation,
    setActiveConversation,
    startNewConversation,
  } = useConversation();
  const { user } = useAuth();
  const [isNewChatOpen, setIsNewChatOpen] = useState(false);
  const [recipientUsername, setRecipientUsername] = useState('');
  const [initialMessage, setInitialMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // New state for user search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Helper to get other participants in a conversation
  const getOtherParticipants = (conversation: ConversationWithMessages) => {
    // Handle both old and new conversation structures
    if ('participants' in conversation && Array.isArray(conversation.participants)) {
      return conversation.participants.filter(p => p.id !== user?.id);
    }

    // Handle participant_1/participant_2 structure
    const participants: any[] = [];
    if (conversation.participant_1 && conversation.participant_1.id !== user?.id) {
      participants.push(conversation.participant_1);
    }
    if (conversation.participant_2 && conversation.participant_2.id !== user?.id) {
      participants.push(conversation.participant_2);
    }
    return participants;
  };

  // Helper to get conversation name (using the names of other participants)
  const getConversationName = (conversation: ConversationWithMessages) => {
    const others = getOtherParticipants(conversation);

    if (others.length === 0) return 'Just you';
    if (others.length === 1) return others[0].username;

    // Check if conversation has a custom name (handle both possible property names)
    const conversationName = (conversation as any).name || (conversation as any).conversation_name;
    if (conversationName) {
      return conversationName;
    } else {
      // Otherwise create a name from participants
      return `${others[0].username}${others.length > 1 ? ` + ${others.length - 1} ${others.length === 2 ? 'other' : 'others'}` : ''}`;
    }
  };

  // Handler for creating a new conversation
  const handleStartNewConversation = async () => {
    if (!recipientUsername.trim()) {
      setError('Please enter a username');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Search for the user by username
      const response = await fetch(
        `/api/users/search/by-username?username=${encodeURIComponent(recipientUsername)}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          setError('User not found. Please check the username and try again.');
        } else {
          setError('Failed to find user. Please try again later.');
        }
        return;
      }

      const userFound = await response.json();

      // Start a conversation with the found user
      const newConversation = await startNewConversation(
        [userFound.id],
        initialMessage.trim() || undefined
      );

      if (newConversation) {
        setIsNewChatOpen(false);
        setRecipientUsername('');
        setInitialMessage('');
      }
    } catch (err) {
      setError('Failed to start conversation. User may not exist.');
    } finally {
      setIsLoading(false);
    }
  };

  // Search for users with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    searchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await fetch(
          `/api/users/search/by-username?username=${encodeURIComponent(searchQuery)}`
        );

        if (response.ok) {
          const users = await response.json();
          setSearchResults(users);
        } else {
          setSearchResults([]);
        }
      } catch (err) {
        console.error('Error searching for users:', err);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300); // 300ms debounce for faster feedback

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Handle starting a conversation with a user from search results
  const handleStartConversationWithUser = async (userId: number) => {
    try {
      const newConversation = await startNewConversation([userId]);
      if (newConversation) {
        setSearchQuery('');
        setSearchResults([]);
      }
    } catch (err) {
      console.error('Error starting conversation:', err);
    }
  };

  // Handle conversation selection with the new prop
  const handleSelectConversation = (conversation: ConversationWithMessages) => {
    setActiveConversation(conversation);
    if (onSelectConversation) {
      onSelectConversation(conversation);
    }
  };

  return (
    <div className="flex flex-col h-full border-r">
      <div className="p-4 border-b flex justify-between items-center bg-gradient-to-r from-white to-gray-50">
        <h2 className="text-lg font-semibold flex items-center gap-2 text-gray-800">
          Messages
          {unreadCount > 0 && (
            <Badge variant="destructive" className="ml-2 animate-pulse">
              {unreadCount}
            </Badge>
          )}
        </h2>
        <Dialog open={isNewChatOpen} onOpenChange={setIsNewChatOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="icon" className="hover:bg-gray-100 rounded-full h-9 w-9">
              <Plus className="h-5 w-5 text-primary" />
              <span className="sr-only">New message</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-sm">
            <DialogHeader>
              <DialogTitle>New Conversation</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="recipient">Recipient Username</Label>
                <Input
                  id="recipient"
                  placeholder="Enter username"
                  value={recipientUsername}
                  onChange={e => setRecipientUsername(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Initial Message (optional)</Label>
                <Input
                  id="message"
                  placeholder="Type your first message..."
                  value={initialMessage}
                  onChange={e => setInitialMessage(e.target.value)}
                />
              </div>
              {error && <p className="text-sm text-red-500">{error}</p>}
              <Button
                className="w-full bg-primary hover:bg-primary/90"
                onClick={handleStartNewConversation}
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Start Conversation'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search input with improved styling */}
      <div className="p-4 border-b bg-white">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search for users..."
            className="pl-10 pr-8 border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary rounded-full"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Clear search</span>
            </button>
          )}
        </div>
        {searchQuery && (
          <div className="mt-2 bg-white border rounded-lg shadow-sm divide-y max-h-48 overflow-y-auto">
            {isSearching ? (
              <div className="p-3 text-center text-sm text-gray-500 flex items-center justify-center">
                <Loader2 className="h-4 w-4 mr-2 animate-spin text-primary" />
                Searching...
              </div>
            ) : searchResults.length > 0 ? (
              searchResults.map(user => (
                <div
                  key={user.id}
                  className="p-3 flex items-center gap-3 hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => handleStartConversationWithUser(user.id)}
                >
                  <Avatar className="h-9 w-9 border border-gray-200">
                    <AvatarImage src={user.avatar || undefined} alt={user.username} />
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getInitials(user.username)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-sm text-gray-800">{user.name}</p>
                    <p className="text-xs text-gray-500">@{user.username}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-3 text-center text-sm text-gray-500">
                No users found. Try a different search.
              </div>
            )}
          </div>
        )}
      </div>

      {/* Conversation list with improved styling */}
      <div className="flex-1 overflow-y-auto divide-y">
        {conversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
              <MessageSquare className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">No conversations yet</h3>
            <p className="text-xs text-gray-500 max-w-[200px]">
              Start chatting with teachers or students from your sessions
            </p>
          </div>
        ) : (
          conversations.map(conversation => (
            <ConversationCard
              key={conversation.id}
              conversation={conversation}
              isActive={activeConversation?.id === conversation.id}
              onClick={() => handleSelectConversation(conversation)}
            />
          ))
        )}
      </div>
    </div>
  );
}
