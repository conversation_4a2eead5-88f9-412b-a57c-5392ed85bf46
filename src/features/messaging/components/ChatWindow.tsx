import { useState, useRef, useEffect } from 'react';
import { useMessages } from '@/contexts/MessageContext';
import { useAuth } from '@/hooks/use-auth';
import { formatTimeAgo } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2, ExternalLink, MessageSquare } from 'lucide-react';
import { getInitials } from '@/lib/utils';

interface ChatWindowProps {
  isFullPage?: boolean;
}

export function ChatWindow({ isFullPage = false }: ChatWindowProps) {
  const { activeConversation, userTypingStatus, sendMessage, setTypingStatus } = useMessages();
  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevMessageCountRef = useRef<number>(0);

  // Scroll to bottom of messages only when new messages arrive
  useEffect(() => {
    if (!activeConversation) {
      return;
    }

    const currentMessageCount = activeConversation.messages.length;

    // Scroll when new messages arrive or when switching conversations
    if (currentMessageCount > prevMessageCountRef.current || prevMessageCountRef.current === 0) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }, 100);
    }

    // Update the previous message count
    prevMessageCountRef.current = currentMessageCount;
  }, [activeConversation?.id, activeConversation?.messages.length]);

  // Reset message count when changing conversations and scroll to bottom
  useEffect(() => {
    if (activeConversation) {
      // Set to 0 initially when conversation changes
      prevMessageCountRef.current = 0;

      // Ensure dialog content stays in view
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });

        // Ensure the dialog is positioned correctly relative to viewport
        const dialogContent = messagesEndRef.current?.closest('[role="dialog"]');
        if (dialogContent) {
          dialogContent.scrollTop = 0;
        }
      }, 150);
    }
  }, [activeConversation?.id]);

  // Get other participant info for display
  const getOtherParticipant = () => {
    if (!activeConversation || !user) return null;

    // Find the first participant that isn't the current user
    return activeConversation.participants.find(p => p.id !== user.id) || null;
  };

  const otherParticipant = getOtherParticipant();

  // Handle sending a message
  const handleSendMessage = () => {
    if (!activeConversation || !message.trim()) return;

    setIsSending(true);

    // Clear typing indicator
    setTypingStatus(activeConversation.id, false);

    // Send the message
    sendMessage(activeConversation.id, message.trim());

    // Clear the input
    setMessage('');

    // Reset sending state after a short delay
    setTimeout(() => {
      setIsSending(false);
    }, 500);
  };

  // Handle typing indicator
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);

    if (!activeConversation) return;

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send typing indicator
    setTypingStatus(activeConversation.id, true);

    // Clear typing indicator after 2 seconds of no typing
    typingTimeoutRef.current = setTimeout(() => {
      setTypingStatus(activeConversation.id, false);
    }, 2000);
  };

  // Check if the other user is typing in this conversation
  const isOtherUserTyping =
    userTypingStatus &&
    userTypingStatus.conversationId === activeConversation?.id &&
    userTypingStatus.isTyping;

  // Handle enter key to send message
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!activeConversation) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-6">
        <div className="max-w-md">
          <h3 className="text-xl font-medium mb-2">Select a conversation</h3>
          <p>Choose an existing conversation from the sidebar or start a new one.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100">
      {/* Chat header with improved styling */}
      <div className="flex items-center justify-between px-4 py-3 md:px-6 md:py-4 border-b bg-gradient-to-r from-gray-50 to-white">
        <div className="flex items-center">
          {otherParticipant ? (
            <>
              <Avatar className="h-10 w-10 border-2 border-white shadow-sm">
                <AvatarImage
                  src={otherParticipant.avatar || undefined}
                  alt={otherParticipant.username}
                />
                <AvatarFallback className="bg-[#E07A5F]/10 text-[#E07A5F]">
                  {getInitials(otherParticipant.username)}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="font-medium text-gray-800">{otherParticipant.username}</p>
                {isOtherUserTyping && (
                  <p className="text-xs text-[#E07A5F] animate-pulse">typing...</p>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center">
              <div className="flex -space-x-2 mr-3">
                {activeConversation.participants.slice(0, 3).map(participant => (
                  <Avatar key={participant.id} className="h-8 w-8 border-2 border-white">
                    <AvatarImage src={participant.avatar || undefined} alt={participant.username} />
                    <AvatarFallback className="bg-[#E07A5F]/10 text-[#E07A5F]">
                      {getInitials(participant.username)}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {activeConversation.participants.length > 3 && (
                  <div className="h-8 w-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs text-gray-500">
                    +{activeConversation.participants.length - 3}
                  </div>
                )}
              </div>
              <div>
                <p className="font-medium text-gray-800">Group Chat</p>
                <p className="text-xs text-gray-500">
                  {activeConversation.participants.length} participants
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Show View Full button only when not in full page mode */}
        {!isFullPage && activeConversation && (
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="ml-auto text-xs flex items-center gap-1 text-[#E07A5F] hover:text-[#E07A5F]/80 hover:bg-[#E07A5F]/5"
          >
            <a href={`/messages/${activeConversation.id}`}>
              View Full
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </Button>
        )}
      </div>

      {/* Messages area with improved styling and mobile optimization */}
      <div
        className={`flex-1 overflow-y-auto p-3 md:p-6 space-y-3 md:space-y-4
          ${isFullPage ? 'min-h-[60vh] md:min-h-[65vh]' : 'h-[calc(80vh-200px)] max-h-96'}
          pointer-events-auto bg-gray-50`}
      >
        {activeConversation.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center text-gray-500">
            <div className="w-16 h-16 bg-[#E07A5F]/10 rounded-full flex items-center justify-center mb-4">
              <MessageSquare className="h-8 w-8 text-[#E07A5F]" />
            </div>
            <p className="font-medium mb-2">No messages yet</p>
            <p className="text-sm">Start the conversation by sending a message</p>
          </div>
        ) : (
          activeConversation.messages.map((msg, index) => {
            const isCurrentUser = msg.senderId === user?.id;
            const sender = activeConversation.participants.find(p => p.id === msg.senderId);
            const isFirstMessageOfGroup =
              index === 0 || activeConversation.messages[index - 1].senderId !== msg.senderId;
            const isLastMessageOfGroup =
              index === activeConversation.messages.length - 1 ||
              activeConversation.messages[index + 1].senderId !== msg.senderId;

            // Check if this is a system message or an automated message
            const isSystemMessage = msg.senderId === 0;
            const isAutomatedMessage = msg.metadata && (msg.metadata as any).automated === true;

            // For system messages and automated messages, use the MessageBubble component
            if (isSystemMessage || isAutomatedMessage) {
              return (
                <MessageBubble
                  key={msg.id}
                  message={msg}
                  isMine={isCurrentUser}
                  otherParticipant={sender}
                />
              );
            }

            // For regular messages, use the existing message display
            return (
              <div
                key={msg.id}
                className={`flex items-end gap-2 ${isCurrentUser ? 'justify-end' : 'justify-start'}
                  ${!isLastMessageOfGroup ? 'mb-1' : 'mb-3'}`}
              >
                {!isCurrentUser && isFirstMessageOfGroup && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarImage
                      src={sender?.avatar || undefined}
                      alt={sender?.username || 'User'}
                    />
                    <AvatarFallback className="bg-[#E07A5F]/10 text-[#E07A5F]">
                      {sender ? getInitials(sender.username) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                )}

                {!isCurrentUser && !isFirstMessageOfGroup && (
                  <div className="w-8 flex-shrink-0"></div>
                )}

                <div
                  className={`max-w-[75%] md:max-w-[80%] flex flex-col ${
                    isCurrentUser ? 'items-end' : 'items-start'
                  }`}
                >
                  {isFirstMessageOfGroup && !isCurrentUser && (
                    <span className="text-xs text-gray-500 ml-1 mb-1">{sender?.username}</span>
                  )}

                  <div
                    className={`px-3 py-2 md:px-4 md:py-2 rounded-2xl ${
                      isCurrentUser
                        ? 'bg-[#E07A5F] text-white rounded-tr-none'
                        : 'bg-white shadow-sm border border-gray-100 rounded-tl-none'
                    }`}
                  >
                    <p className="text-sm break-words">{msg.content}</p>
                  </div>

                  {isLastMessageOfGroup && (
                    <span className="text-xs text-gray-400 mt-1 px-1">
                      {formatTimeAgo(new Date(msg.createdAt))}
                    </span>
                  )}
                </div>

                {isCurrentUser && isFirstMessageOfGroup && (
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarImage src={user?.avatar || undefined} alt={user?.username || 'You'} />
                    <AvatarFallback className="bg-[#84a59d]/10 text-[#84a59d]">
                      {user ? getInitials(user.username) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                )}

                {isCurrentUser && !isFirstMessageOfGroup && (
                  <div className="w-8 flex-shrink-0"></div>
                )}
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />

        {/* Typing indicator with improved styling */}
        {isOtherUserTyping && (
          <div className="flex items-center gap-2 pl-2 animate-pulse">
            <div className="flex space-x-1">
              <div
                className="w-2 h-2 rounded-full bg-[#E07A5F] animate-bounce"
                style={{ animationDelay: '0ms' }}
              ></div>
              <div
                className="w-2 h-2 rounded-full bg-[#E07A5F] animate-bounce"
                style={{ animationDelay: '150ms' }}
              ></div>
              <div
                className="w-2 h-2 rounded-full bg-[#E07A5F] animate-bounce"
                style={{ animationDelay: '300ms' }}
              ></div>
            </div>
            <span className="text-xs text-gray-500">Someone is typing...</span>
          </div>
        )}
      </div>

      {/* Message input area with improved styling */}
      <div className="p-3 md:p-4 border-t bg-white">
        <div className="flex items-end gap-2">
          <Textarea
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            className="min-h-20 md:min-h-24 resize-none border-gray-200 focus:border-[#E07A5F] focus:ring-1 focus:ring-[#E07A5F] rounded-lg"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || isSending}
            className={`p-3 h-10 w-10 rounded-full flex items-center justify-center shrink-0 ${
              message.trim() && !isSending
                ? 'bg-[#E07A5F] hover:bg-[#E07A5F]/90'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            {isSending ? (
              <Loader2 className="h-4 w-4 animate-spin text-white" />
            ) : (
              <Send className="h-4 w-4" />
            )}
            <span className="sr-only">Send</span>
          </Button>
        </div>
      </div>
    </div>
  );
}

// Message bubble component with activity message support
function MessageBubble({
  message,
  isMine,
  otherParticipant,
}: {
  message: any;
  isMine: boolean;
  otherParticipant: any;
}) {
  const isSystemMessage = message.senderId === 0;
  const isAutomatedMessage = message.metadata && (message.metadata as any).automated === true;
  const isCancellationActivity = message.content.includes('**CANCELLATION NOTIFICATION**');
  const isBookingActivity = message.content.includes('**BOOKING NOTIFICATION**');
  const isPaymentActivity = message.content.includes('**PAYMENT NOTIFICATION**');
  const isSessionUpdateActivity = message.content.includes('**SESSION UPDATE**');
  const isNoteActivity = message.content.includes('**NOTE**');

  const isActivity =
    isSystemMessage &&
    (isCancellationActivity ||
      isBookingActivity ||
      isPaymentActivity ||
      isSessionUpdateActivity ||
      isNoteActivity);

  let icon = null;
  let activityColor = '';

  if (isActivity) {
    if (isCancellationActivity) {
      icon = <div className="bg-red-100 p-1 rounded-full text-red-500 mr-2">❌</div>;
      activityColor = 'border-red-200 bg-red-50 text-red-800';
    } else if (isBookingActivity) {
      icon = <div className="bg-green-100 p-1 rounded-full text-green-500 mr-2">📅</div>;
      activityColor = 'border-green-200 bg-green-50 text-green-800';
    } else if (isPaymentActivity) {
      icon = <div className="bg-blue-100 p-1 rounded-full text-blue-500 mr-2">💰</div>;
      activityColor = 'border-blue-200 bg-blue-50 text-blue-800';
    } else if (isSessionUpdateActivity) {
      icon = <div className="bg-amber-100 p-1 rounded-full text-amber-500 mr-2">📝</div>;
      activityColor = 'border-amber-200 bg-amber-50 text-amber-800';
    } else if (isNoteActivity) {
      icon = <div className="bg-purple-100 p-1 rounded-full text-purple-500 mr-2">📌</div>;
      activityColor = 'border-purple-200 bg-purple-50 text-purple-800';
    }
  } else if (isAutomatedMessage) {
    icon = <div className="bg-blue-100 p-1 rounded-full text-blue-500 mr-2">🤖</div>;
    activityColor = 'border-blue-100 bg-blue-50 text-blue-700';
  }

  // Format activity messages for better display
  let formattedContent = message.content;
  if (isActivity) {
    // Replace markdown-style formatting with actual HTML
    formattedContent = formattedContent
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n- /g, '<br/>• ');
  } else if (isAutomatedMessage) {
    // Add a label for automated messages
    formattedContent = `<strong>Automated Message</strong><br/>${message.content}`;
  }

  return (
    <div className={`flex items-start ${isMine ? 'justify-end' : 'justify-start'} mb-4`}>
      {!isMine && !isActivity && (
        <Avatar className="h-8 w-8 mr-2 mt-1">
          <AvatarImage
            src={otherParticipant?.avatar || undefined}
            alt={otherParticipant?.username || 'User'}
          />
          <AvatarFallback className="bg-[#E07A5F]/10 text-[#E07A5F]">
            {getInitials(otherParticipant?.username || 'User')}
          </AvatarFallback>
        </Avatar>
      )}

      {isActivity || isAutomatedMessage ? (
        <div
          className={`rounded-lg px-4 py-2 max-w-[80%] md:max-w-[70%] text-sm border ${activityColor} w-full mx-4 my-2`}
        >
          <div className="flex items-center">
            {icon}
            <div dangerouslySetInnerHTML={{ __html: formattedContent }} />
          </div>
          <p className="text-xs mt-1 text-gray-500">{formatTimeAgo(message.createdAt)}</p>
        </div>
      ) : (
        <div
          className={`
          px-4 py-2 rounded-lg max-w-[80%] md:max-w-[70%] text-sm
          ${
            isMine
              ? 'bg-[#E07A5F] text-white rounded-tr-none'
              : 'bg-gray-100 text-gray-800 rounded-tl-none'
          }
        `}
        >
          <p>{message.content}</p>
          <p className={`text-xs mt-1 ${isMine ? 'text-[#E07A5F]/80' : 'text-gray-500'}`}>
            {formatTimeAgo(message.createdAt)}
          </p>
        </div>
      )}
    </div>
  );
}
