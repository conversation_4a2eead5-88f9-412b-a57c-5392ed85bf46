import { useState } from 'react';
import { Link } from '../../../components/ui/NextLink';
import { useMessages } from '@/contexts/MessageContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { formatTimeAgo } from '@/lib/utils';
import { getInitials } from '@/lib/utils';
import { MessageSquareMore } from 'lucide-react';

interface ConversationCardProps {
  conversation: any;
  isActive: boolean;
  onClick: () => void;
}

export function ConversationCard({ conversation, isActive, onClick }: ConversationCardProps) {
  const { openConversationInPopup } = useMessages();
  const [showPopupButton, setShowPopupButton] = useState(false);

  // Get the other user in the conversation (assuming 1:1 chat)
  const otherUser = conversation.participants.find((p: any) => p.id !== conversation.userId);

  // Get the latest message
  const latestMessage =
    conversation.messages && conversation.messages.length > 0
      ? conversation.messages[conversation.messages.length - 1]
      : null;

  // Check if there are unread messages
  const hasUnread = conversation.unreadCount > 0;

  const handlePopupClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    openConversationInPopup(conversation.id);
  };

  return (
    <div
      className={`relative group flex items-start p-3 gap-3 cursor-pointer rounded-md transition-colors
        ${isActive ? 'bg-primary/10 text-primary' : 'hover:bg-gray-100'}`}
      onClick={onClick}
      onMouseEnter={() => setShowPopupButton(true)}
      onMouseLeave={() => setShowPopupButton(false)}
    >
      <Avatar className="h-10 w-10 shrink-0">
        <AvatarImage src={otherUser?.avatar || undefined} alt={otherUser?.username} />
        <AvatarFallback
          className={`bg-primary/10 text-primary ${hasUnread ? 'ring-2 ring-primary' : ''}`}
        >
          {getInitials(otherUser?.username || 'User')}
        </AvatarFallback>
      </Avatar>

      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-start mb-1">
          <h3 className={`text-sm font-medium truncate ${hasUnread ? 'font-semibold' : ''}`}>
            {otherUser?.username || 'Unknown User'}
          </h3>
          {latestMessage && (
            <span className="text-xs text-gray-500 shrink-0">
              {formatTimeAgo(new Date(latestMessage.createdAt))}
            </span>
          )}
        </div>

        <p
          className={`text-xs ${hasUnread ? 'text-gray-900 font-medium' : 'text-gray-500'
            } line-clamp-1`}
        >
          {latestMessage ? latestMessage.content : 'No messages yet'}
        </p>

        {hasUnread && (
          <div className="absolute top-1/2 transform -translate-y-1/2 right-3 w-2 h-2 bg-primary rounded-full" />
        )}
      </div>

      {showPopupButton && !isActive && (
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={handlePopupClick}
          title="Open in popup"
        >
          <MessageSquareMore className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
