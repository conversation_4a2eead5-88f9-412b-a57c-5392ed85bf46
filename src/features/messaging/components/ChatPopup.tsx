import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useMessages } from '@/features/messaging';
import { useConversation } from '@/features/messaging/ConversationContext';
import { useAuth } from '@/features/auth';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { Send, X, Minus, MoreVertical, Maximize, Trash } from 'lucide-react';
import { useLocation } from '../../../lib/next-router-utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSession } from '@/hooks/use-session';

interface ChatPopupProps {
  conversationId: string;
  isMinimized: boolean;
  onToggleMinimize: () => void;
  onClose: () => void;
  onDelete: (conversationId: string) => void;
  initialMessage?: string;
  showCancellationReasons?: boolean;
  'data-conversation-id'?: number;
  style?: React.CSSProperties;
  bookingId?: number;
}

const cancellationReasons = [
  { id: 'schedule-conflict', label: 'Schedule conflict' },
  { id: 'personal-reasons', label: 'Personal reasons' },
  { id: 'found-alternative', label: 'Found an alternative session' },
  { id: 'need-to-reschedule', label: 'Need to reschedule' },
  { id: 'no-longer-interested', label: 'No longer interested' },
];

const ChatPopupComponent: React.FC<ChatPopupProps> = ({
  conversationId,
  isMinimized,
  onClose,
  onToggleMinimize,
  onDelete,
  initialMessage,
  showCancellationReasons,
  ...rest
}) => {
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();
  const { sendMessage } = useMessages();
  const { conversations, markConversationAsRead } = useConversation();
  const [isTyping, setIsTyping] = useState(false);
  const [, setLocation] = useLocation();
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [confirmState, setConfirmState] = useState<'idle' | 'submitting' | 'submitted' | 'error'>(
    'idle'
  );
  const { cancelBookingAsync } = useSession();
  const [selectedReason, setSelectedReason] = useState<string | null>(null);
  const [messageSent, setMessageSent] = useState(false);
  const [localMessages, setLocalMessages] = useState<any[]>([]);

  const conversation = conversations.find(conv => conv.id === conversationId || String(conv.id) === conversationId);

  // This effect handles cleanup when the component unmounts unexpectedly
  useEffect(() => {
    return () => {
      // Remove any animations or classes that might persist
      const chatElement = document.querySelector(`[data-conversation-id="${conversationId}"]`);
      if (chatElement) {
        chatElement.classList.remove('chat-popup-exit');
      }
    };
  }, [conversationId]);

  // Function to get other participants in the conversation
  const getOtherParticipants = useCallback(() => {
    if (!conversation || !user) return [];
    // Handle both old and new conversation structures
    if ('participants' in conversation && Array.isArray(conversation.participants)) {
      return conversation.participants.filter(p => p.id !== user.id);
    }
    // Handle participant_1/participant_2 structure
    const participants: any[] = [];
    if (conversation.participant_1 && conversation.participant_1.id !== user.id) {
      participants.push(conversation.participant_1);
    }
    if (conversation.participant_2 && conversation.participant_2.id !== user.id) {
      participants.push(conversation.participant_2);
    }
    return participants;
  }, [conversation, user]);

  // Function to handle the header click - toggles minimize
  const handleHeaderClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onToggleMinimize();
    },
    [onToggleMinimize]
  );

  // Handle sending a message
  const handleSendMessage = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!message.trim() || isLoading || !conversation) return;

      try {
        setIsLoading(true);
        await sendMessage(Number(conversationId), message);
        setMessage('');

        // Focus the input again after sending
        setTimeout(() => {
          inputRef.current?.focus();
        }, 0);

        // After sendMessage resolves, remove the optimistic message (if present)
        setLocalMessages(msgs => msgs.filter(m => !m.optimistic));
      } catch (error) {
        console.error('Failed to send message:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [message, isLoading, conversation, conversationId, sendMessage]
  );

  // Handle closing the popup
  const handleClose = useCallback(
    (e: React.MouseEvent) => {
      if (e) {
        e.stopPropagation();
      }

      // Add animation class
      const element = document.querySelector(`[data-conversation-id="${conversationId}"]`);
      if (element) {
        element.classList.add('chat-popup-exit');

        // Wait for animation to complete
        setTimeout(() => {
          onClose();
        }, 200);
      } else {
        onClose();
      }
    },
    [conversationId, onClose]
  );

  // Handle opening the full chat view
  const handleOpenFullChat = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setLocation(`/messages/${conversationId}`);
    },
    [conversationId, setLocation]
  );

  // Handle deleting a conversation
  const handleDeleteConversation = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onDelete(conversationId);
    },
    [conversationId, onDelete]
  );

  // Scroll to bottom of messages when new ones arrive or chat is expanded
  useEffect(() => {
    if (!isMinimized && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }

    // Mark as read when chat is opened
    if (!isMinimized && conversation?.id) {
      markConversationAsRead(Number(conversation.id));
    }
  }, [conversation?.messages, isMinimized, conversationId, markConversationAsRead]);

  // Focus input when chat is maximized
  useEffect(() => {
    if (!isMinimized && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isMinimized]);

  useEffect(() => {
    if (initialMessage) {
      console.log('[ChatPopup] received initialMessage:', initialMessage);
    }
    if (conversation) {
      console.log('[ChatPopup] conversation participants:', getOtherParticipants());
    }
    if (initialMessage) setMessage(initialMessage);
  }, [initialMessage, conversation]);

  if (!conversation) return null;

  const otherUser = getOtherParticipants()[0];

  return (
    <Card
      className={cn(
        'chat-popup-wrapper rounded-t-lg shadow-md overflow-hidden',
        isMinimized ? 'h-[41px]' : 'h-[450px]',
        'w-[328px] bg-white transition-all duration-200'
      )}
      style={{
        transition: 'height 0.2s ease-out',
        zIndex: 9999,
        boxShadow: '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      }}
      {...rest}
    >
      {/* Chat Header */}
      <div
        className="bg-primary text-primary-foreground p-2 cursor-pointer flex items-center justify-between"
        onClick={handleHeaderClick}
      >
        <div className="flex items-center gap-2 overflow-hidden">
          <Avatar className="h-7 w-7">
            <AvatarImage src={otherUser?.avatar || ''} alt={otherUser?.username || 'User'} />
            <AvatarFallback>{otherUser?.username?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
          </Avatar>
          <div className="overflow-hidden">
            <p className="text-sm font-medium leading-none truncate">
              {otherUser?.username || 'User'}
            </p>
            {isTyping && <p className="text-xs opacity-90">typing...</p>}
          </div>
        </div>

        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={e => e.stopPropagation()}>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 rounded-full hover:bg-primary-foreground/20"
              >
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleOpenFullChat}>
                <Maximize className="h-4 w-4 mr-2" />
                Open in full view
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDeleteConversation} className="text-destructive">
                <Trash className="h-4 w-4 mr-2" />
                Delete conversation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 rounded-full hover:bg-primary-foreground/20"
            onClick={e => {
              e.stopPropagation();
              onToggleMinimize();
            }}
          >
            <Minus className="h-4 w-4" />
            <span className="sr-only">Minimize</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 rounded-full hover:bg-primary-foreground/20"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        </div>
      </div>

      {/* Chat Body - Hidden when minimized */}
      <div
        className={cn('chat-body flex flex-col', isMinimized ? 'hidden' : 'h-[calc(100%-41px)]')}
      >
        {/* Messages Area */}
        <ScrollArea className="flex-1 p-3 bg-gray-50">
          <div className="flex flex-col gap-2">
            {[...(conversation.messages || []), ...localMessages].map(msg => {
              const isCurrentUser = msg.senderId === user?.id;
              return (
                <div
                  key={msg.id}
                  className={cn('flex', isCurrentUser ? 'justify-end' : 'justify-start')}
                >
                  <div
                    className={cn(
                      'rounded-lg p-3 max-w-[75%]',
                      isCurrentUser
                        ? 'bg-primary text-primary-foreground rounded-br-none'
                        : 'bg-gray-200 text-gray-900 rounded-bl-none'
                    )}
                  >
                    <p className="text-sm">{msg.content}</p>
                    <p className="text-[10px] opacity-80 mt-1">
                      {formatDistanceToNow(new Date(msg.createdAt), { addSuffix: true })}
                      {msg.optimistic && <span className="ml-1 text-yellow-500">(sending...)</span>}
                    </p>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Quick-reply cancellation reasons */}
        {showCancellationReasons && (
          <div className="flex flex-wrap gap-2 p-2 border-t bg-gray-50">
            {cancellationReasons.map(reason => (
              <Button
                key={reason.id}
                size="sm"
                variant="outline"
                className="text-xs"
                onClick={async () => {
                  setIsLoading(true);
                  // Optimistically add a local message
                  const tempMsg = {
                    id: `temp-${Date.now()}`,
                    senderId: user?.id,
                    content: reason.label,
                    createdAt: new Date().toISOString(),
                    optimistic: true,
                  };
                  setLocalMessages(msgs => [...msgs, tempMsg]);
                  try {
                    await sendMessage(Number(conversationId), reason.label);
                    setSelectedReason(reason.label);
                    setMessageSent(true);
                  } catch (err) {
                    // Remove the temp message if send fails
                    setLocalMessages(msgs => msgs.filter(m => m.id !== tempMsg.id));
                    console.error('Failed to send cancellation reason:', err);
                  } finally {
                    setIsLoading(false);
                  }
                }}
                type="button"
                disabled={isLoading || messageSent}
              >
                {reason.label}
              </Button>
            ))}
          </div>
        )}
        {/* Message input and confirm cancellation button */}
        <form
          onSubmit={async e => {
            e.preventDefault();
            if (!message.trim() || isLoading || !conversation) return;
            setIsLoading(true);
            try {
              await sendMessage(Number(conversationId), message);
              setSelectedReason(message);
              setMessageSent(true);
              setMessage('');

              // After sendMessage resolves, remove the optimistic message (if present)
              setLocalMessages(msgs => msgs.filter(m => !m.optimistic));
            } catch (error) {
              console.error('Failed to send message:', error);
            } finally {
              setIsLoading(false);
            }
          }}
          className="flex flex-col gap-2 p-2 border-t bg-white"
        >
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={message}
              onChange={e => setMessage(e.target.value)}
              placeholder="Type your message or reason..."
              disabled={isLoading || confirmState === 'submitted' || messageSent}
              className="flex-1"
              onKeyDown={e => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  if (!messageSent) {
                    // Only allow sending if not already sent
                    e.currentTarget.form?.requestSubmit();
                  }
                }
              }}
            />
            <Button
              type="submit"
              variant="default"
              disabled={!message.trim() || isLoading || confirmState === 'submitted' || messageSent}
              className="px-4"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          {/* Confirm Cancellation Button */}
          {showCancellationReasons && messageSent && selectedReason && (
            <Button
              className="w-full mt-2 py-3 text-lg font-bold"
              disabled={isLoading || confirmState === 'submitted'}
              onClick={async e => {
                e.preventDefault();
                setIsLoading(true);
                setConfirmState('submitting');
                try {
                  if (typeof rest.bookingId === 'number') {
                    await cancelBookingAsync(rest.bookingId);
                    if (window.location && window.location.reload) {
                      window.location.reload();
                    } else if (window.scrollTo) {
                      window.scrollTo({ top: 0, behavior: 'smooth' });
                    }
                  }
                  setConfirmState('submitted');
                  setMessageSent(false);
                  setSelectedReason(null);
                } catch (err) {
                  setConfirmState('error');
                  console.error('Failed to confirm cancellation:', err);
                } finally {
                  setIsLoading(false);
                }
              }}
            >
              {confirmState === 'submitting'
                ? 'Submitting...'
                : confirmState === 'submitted'
                  ? 'Cancellation Submitted'
                  : 'Confirm Cancellation'}
            </Button>
          )}
          {confirmState === 'error' && (
            <div className="text-red-500 text-sm mt-1">
              Failed to confirm cancellation. Please try again.
            </div>
          )}
        </form>
      </div>
    </Card>
  );
};

export const ChatPopup = ChatPopupComponent;
export { ChatPopupComponent };
