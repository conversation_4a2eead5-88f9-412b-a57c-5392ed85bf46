import { ConversationWithMessages } from '@shared/schema';

export interface MessageContextType {
  conversations: ConversationWithMessages[];
  activeConversation: ConversationWithMessages | null;
  unreadCount: number;
  unreadCounts: Record<number, number>;
  isLoading: boolean;
  error: Error | null;
  sendMessage: (conversationId: number, content: string) => void;
  startNewConversation: (
    participantIds: number[],
    initialMessage?: string
  ) => Promise<ConversationWithMessages | null>;
  setActiveConversation: (conversation: ConversationWithMessages | null) => void;
  setActiveConversationId: (conversationId: number | null) => void;
  setTypingStatus: (conversationId: number, isTyping: boolean) => void;
  userTypingStatus: {
    conversationId: number;
    userId: number;
    isTyping: boolean;
  } | null;
  fetchConversations: () => Promise<void>;
  setError?: (error: string | null) => void;
  isSocketConnected: boolean;
  connectionFailed: boolean;
  reconnect: () => void;
  setConversations: (
    conversations:
      | ConversationWithMessages[]
      | ((prev: ConversationWithMessages[]) => ConversationWithMessages[])
  ) => void;
  openConversationInPopup: (conversationId: number) => void;
  isAuthenticated: boolean;
  markConversationAsRead: (conversationId: number) => void;
  createConversation: (participantIds: number[]) => Promise<ConversationWithMessages | null>;
}
