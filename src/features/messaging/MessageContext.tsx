import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
  useRef,
} from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { MessageWithSender, ConversationWithMessages } from '@shared/schema';
import { useConversation } from './ConversationContext';
import { AppError, ErrorCategory, ErrorSeverity, handleError } from '@/lib/error-handler';
import { supabase } from '@/lib/supabase-singleton';
import { User } from '@supabase/supabase-js';

// Caching constants
const UNREAD_CACHE_KEY = 'cached_unread_count';
const FIVE_MINUTES = 5 * 60 * 1000;

interface MessageContextType {
  unreadCount: number;
  unreadCounts: Record<number, number>;
  isLoading: boolean;
  error: AppError | null;
  sendMessage: (conversationId: number, content: string) => void;
  setTypingStatus: (conversationId: number, isTyping: boolean) => void;
  userTypingStatus: {
    conversationId: number;
    userId: number;
    isTyping: boolean;
  } | null;
  isSocketConnected: boolean;
  connectionFailed: boolean;
  reconnect: () => void;
  isAuthenticated: boolean;
}

export const MessageContext = createContext<MessageContextType | null>(null);

export function MessageProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { conversations, activeConversation, setConversations, markConversationAsRead } =
    useConversation();

  const [pathname, setPathname] = useState<string>('');
  const [userTypingStatus, setUserTypingStatus] = useState<{
    conversationId: number;
    userId: number;
    isTyping: boolean;
  } | null>(null);

  const [unreadCount, setUnreadCount] = useState<number>(() => {
    // Initialize from cache if available
    if (typeof window !== 'undefined') {
      try {
        const cached = localStorage.getItem(UNREAD_CACHE_KEY);
        if (cached) {
          const { data, timestamp } = JSON.parse(cached);
          // Use cache if less than 5 minutes old
          if (Date.now() - timestamp < FIVE_MINUTES) {
            return data;
          }
        }
      } catch (e) {
        // handleError will determine category and severity internally
        handleError(e as any, 'Error reading unread count cache');
      }
    }
    return 0;
  });

  const [unreadCounts, setUnreadCounts] = useState<Record<number, number>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const authFlagRef = useRef(false);
  const lastFetchRef = useRef<number>(0);

  // Update pathname when it changes
  useEffect(() => {
    const updatePathname = () => {
      setPathname(window.location.pathname);
    };

    // Set initial pathname
    updatePathname();

    // Listen for route changes
    window.addEventListener('popstate', updatePathname);

    return () => {
      window.removeEventListener('popstate', updatePathname);
    };
  }, []);

  // Check if current path is related to messages
  const isMessagesPage = pathname.startsWith('/messages');

  // Convert Supabase User to the format expected by useWebSocket
  const adaptedUser = user ? {
    id: user.id,
    user_id: user.id,
    full_name: user.user_metadata?.full_name || user.email || 'User',
    email: user.email || '',
    created_at: user.created_at || new Date().toISOString(),
    updated_at: user.updated_at || new Date().toISOString(),
  } : null;

  // Connect to Socket.IO for real-time messaging
  const {
    isConnected: socketConnected,
    lastMessage,
    sendMessage: sendSocketMessage,
    connectionFailed,
    reconnect,
  } = useWebSocket(isMessagesPage ? adaptedUser : null);

  // Process incoming messages
  const handleMessage = useCallback(
    (message: any) => {
      if (!message || !message.type) return;

      console.log(`[MessageContext] Handling message of type: ${message.type}`);

      switch (message.type) {
        case 'conversations':
          if (Array.isArray(message.conversations)) {
            console.log(`[MessageContext] Updated conversations: ${message.conversations.length}`);
            setConversations(message.conversations);

            // Update cache
            if (typeof window !== 'undefined') {
              localStorage.setItem(
                'cached_conversations',
                JSON.stringify({
                  data: message.conversations,
                  timestamp: Date.now(),
                })
              );
            }
          }
          break;

        case 'unread_count':
          setUnreadCount(message.count || 0);

          // Update cache
          if (typeof window !== 'undefined') {
            localStorage.setItem(
              UNREAD_CACHE_KEY,
              JSON.stringify({
                data: message.count || 0,
                timestamp: Date.now(),
              })
            );
          }
          break;

        case 'message':
          // Only handle messages for active conversation to reduce computations
          if (activeConversation && message.conversationId === activeConversation.id) {
            // Mark as read
            if (sendSocketMessage) {
              sendSocketMessage({
                type: 'mark_read',
                conversationId: message.conversationId,
              });
            }

            // Mark as read in the database
            markConversationAsRead(message.conversationId);
          }

          // Update conversations list with the new message
          setConversations(prevConversations => {
            return prevConversations.map((convo): ConversationWithMessages => {
              if (convo.id === message.conversationId) {
                // Explicitly construct the updated conversation to help with type inference
                const updatedMessages = [...(convo.messages || []), message];
                const updatedLastMessageAt = message.timestamp ? new Date(message.timestamp) : convo.last_message_at;

                return {
                  id: convo.id,
                  created_at: convo.created_at || new Date().toISOString(),
                  updated_at: convo.updated_at || new Date().toISOString(),
                  participant_1_id: convo.participant_1_id || '',
                  participant_2_id: convo.participant_2_id || '',
                  last_message_id: convo.last_message_id,
                  last_message_at: typeof updatedLastMessageAt === 'string' ? updatedLastMessageAt : updatedLastMessageAt?.toISOString(),
                  messages: updatedMessages,
                } as ConversationWithMessages;
              }
              return convo;
            });
          });

          // Update unread count if not looking at conversation
          if (!activeConversation || activeConversation.id !== message.conversationId) {
            setUnreadCount(prev => prev + 1);
            if (message.senderId !== user?.id) {
              toast({
                title: 'New Message',
                description: `${message.sender?.name || 'Someone'} sent you a message`,
              });
            }
          }
          break;

        case 'typing':
          if (message.userId !== user?.id) {
            setUserTypingStatus({
              conversationId: message.conversationId,
              userId: message.userId,
              isTyping: message.isTyping,
            });

            // Auto-clear typing status after 3 seconds
            setTimeout(() => {
              setUserTypingStatus(prev => {
                if (
                  prev &&
                  prev.userId === message.userId &&
                  prev.conversationId === message.conversationId
                ) {
                  return null;
                }
                return prev;
              });
            }, 3000);
          }
          break;

        default:
          // Ignore other message types
          break;
      }
    },
    [activeConversation, setConversations, toast, user, sendSocketMessage, markConversationAsRead]
  );

  // Handle WebSocket messages
  useEffect(() => {
    // Only set up WebSocket handling on messages pages or when there's an active conversation
    if (!user || (!isMessagesPage && !activeConversation)) return;

    // Handle authentication on successful WebSocket connection
    if (socketConnected && user && !authFlagRef.current) {
      console.log(`[MessageContext] Authenticated user ${user.id} with WebSocket`);
      if (sendSocketMessage) {
        sendSocketMessage({
          type: 'authenticate',
          userId: user.id,
        });
        authFlagRef.current = true;
      }
    }

    // Listen for messages
    if (lastMessage) {
      handleMessage(lastMessage);
    }

    // Set up polling with a reduced frequency
    let pollTimer: NodeJS.Timeout | null = null;

    if (socketConnected && sendSocketMessage) {
      // Use a longer polling interval (5 minutes) to minimize updates
      pollTimer = setInterval(() => {
        // Only poll if visible and actively using messages
        if (
          socketConnected &&
          document.visibilityState === 'visible' &&
          (isMessagesPage || activeConversation)
        ) {
          sendSocketMessage({
            type: 'get_conversations',
          });
          sendSocketMessage({
            type: 'get_unread_count',
          });
        }
      }, 300000); // 5 minutes

      // Initial request
      if (isMessagesPage || activeConversation) {
        sendSocketMessage({
          type: 'get_conversations',
        });
        sendSocketMessage({
          type: 'get_unread_count',
        });
      }
    }

    // Cleanup on unmount
    return () => {
      if (pollTimer) clearInterval(pollTimer);
    };
  }, [
    socketConnected,
    user,
    handleMessage,
    sendSocketMessage,
    isMessagesPage,
    activeConversation,
    lastMessage,
  ]);

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    if (!user) return;

    // Prevent excessive fetching
    if (Date.now() - lastFetchRef.current < 1000 && lastFetchRef.current !== 0) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(`[MessageContext] Fetching unread count for user ${user.id}`);
      const { data, response } = await apiRequest(`/api/users/${user.id}/unread-messages-count`);
      if (!response.ok) {
        throw new Error(`Failed to fetch unread count: ${response.status}`);
      }
      // Type assertion to fix TypeScript 'never' type inference
      const responseData = data as { unreadCount?: number } | null;
      if (responseData && typeof responseData.unreadCount !== 'undefined') {
        setUnreadCount(Number(responseData.unreadCount) || 0);

        // Update cache
        if (typeof window !== 'undefined') {
          localStorage.setItem(
            UNREAD_CACHE_KEY,
            JSON.stringify({
              data: responseData.unreadCount,
              timestamp: Date.now(),
            })
          );
        }
      }

      lastFetchRef.current = Date.now(); // Update last fetch time
    } catch (error) {
      // handleError will determine category and severity internally
      const appError = handleError(error as any, 'Error fetching unread count');
      setError(appError);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch initial unread count and conversations
  useEffect(() => {
    fetchUnreadCount();
  }, [fetchUnreadCount]);

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({
      conversationId,
      content,
    }: {
      conversationId: number;
      content: string;
    }) => {
      if (!user) throw new Error('User not authenticated');

      // Get Supabase session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      const { data, response } = await apiRequest(`/api/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(session?.access_token ? { 'Authorization': `Bearer ${session.access_token}` } : {})
        },
        body: JSON.stringify({
          content,
          senderId: user.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      return data as MessageWithSender;
    },
    onSuccess: (message, { conversationId }) => {
      // Update conversations with the new message
      setConversations(prevConversations => {
        return prevConversations.map(convo => {
          if (convo.id === conversationId) {
            return {
              ...convo,
              messages: [...(convo.messages || []), message],
              lastMessageAt: new Date().toISOString(),
            };
          }
          return convo;
        });
      });

      // Notify via WebSocket if connected
      if (socketConnected && sendSocketMessage) {
        sendSocketMessage({
          type: 'message',
          conversationId,
          message,
        });
      }

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['/api/conversations', conversationId] });
    },
    onError: error => {
      console.error('[MessageContext] Error sending message:', error);

      // Determine error category based on error message
      let category = ErrorCategory.UNKNOWN;
      let customMessage = '';

      if (error.message?.includes('Network Error') || error.message?.includes('Failed to fetch')) {
        category = ErrorCategory.NETWORK;
        customMessage =
          "Unable to connect to the server. Your message will be sent when you're back online.";
      } else if (
        error.message?.includes('not authenticated') ||
        error.message?.includes('unauthorized')
      ) {
        category = ErrorCategory.AUTHENTICATION;
        customMessage = 'You need to be logged in to send messages.';
      }

      // Use standardized error handling with the determined category
      handleError(error, customMessage || 'Failed to send message', undefined, {
        category,
        action: 'sendMessage',
        conversationId: sendMessageMutation.variables?.conversationId,
      });
    },
  });

  // Send a message
  const sendMessage = useCallback(
    (conversationId: number, content: string) => {
      if (!user || !content.trim()) return;

      sendMessageMutation.mutate({ conversationId, content });
    },
    [user, sendMessageMutation]
  );

  // Set typing status
  const setTypingStatus = useCallback(
    (conversationId: number, isTyping: boolean) => {
      if (!user || !socketConnected || !sendSocketMessage) return;

      sendSocketMessage({
        type: 'typing',
        conversationId,
        userId: user.id,
        isTyping,
      });
    },
    [user, socketConnected, sendSocketMessage]
  );

  // Create context value
  const contextValue: MessageContextType = {
    unreadCount,
    unreadCounts,
    isLoading,
    error,
    sendMessage,
    setTypingStatus,
    userTypingStatus,
    isSocketConnected: socketConnected,
    connectionFailed,
    reconnect,
    isAuthenticated: !!user,
  };

  return <MessageContext.Provider value={contextValue}>{children}</MessageContext.Provider>;
}

// Custom hook to use the message context
export function useMessages() {
  const context = useContext(MessageContext);
  if (context === null) {
    throw new Error('useMessages must be used within a MessageProvider');
  }
  return context;
}
