// Context providers and hooks
export { ConversationContext, ConversationProvider, useConversation } from './ConversationContext';
export { MessageContext, MessageProvider, useMessages } from './MessageContext';
export { MessagingProvider, clearConversationCache } from './MessagingProvider';

// Component exports
// ChatPopup, ChatPopupManager, and ChatWindow components removed temporarily
export { ConversationCard } from './components/ConversationCard';
export { ConversationList } from './components/ConversationList';
export { ForceReloadButton } from './components/ForceReloadButton';
export { MessageDropdown } from './components/MessageDropdown';
export { MessageNotification } from './components/MessageNotification';
export {
  NotificationPreferencesComponent,
  NotificationPreferences,
} from './components/NotificationPreferences';

// Types and utilities
export type { MessageContextType } from './messagingTypes';
export {
  ONE_HOUR,
  FIVE_MINUTES,
  CONVERSATION_CACHE_KEY,
  UNREAD_CACHE_KEY,
  MAX_RECONNECT_ATTEMPTS,
  getBaseApiUrl,
} from './messagingUtils';
