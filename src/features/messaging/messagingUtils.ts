// Caching and optimization constants
export const ONE_HOUR = 60 * 60 * 1000;
export const FIVE_MINUTES = 5 * 60 * 1000;
export const CONVERSATION_CACHE_KEY = 'cached_conversations';
export const UNREAD_CACHE_KEY = 'cached_unread_count';
export const MAX_RECONNECT_ATTEMPTS = 5;

// Helper to clear conversation caches
export const clearConversationCache = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(CONVERSATION_CACHE_KEY);
    localStorage.removeItem(UNREAD_CACHE_KEY);
    console.log('Cleared conversation cache');
  }
};

// Helper to determine WebSocket URL based on current environment
export function getBaseApiUrl(): string {
  // Get the current origin or use localhost in development
  const currentOrigin = typeof window !== 'undefined' ? window.location.origin : '';
  return currentOrigin;
}

// Add resilient fetch helper
export async function fetchWithFallback(
  userId: number,
  endpoint: string = 'conversations'
): Promise<any> {
  console.log(
    `[MessageContext] Attempting to fetch ${endpoint} for user ${userId} with fallback strategy`
  );

  // Try emergency direct endpoint first
  try {
    const directUrl = `/api/direct/${endpoint}/${userId}?force=json`;
    console.log(`[MessageContext] Trying direct endpoint: ${directUrl}`);

    // Use apiRequest instead of fetch to ensure we use the proxy
    const { data, response: directResponse } = await apiRequest(directUrl, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'X-Force-Json': 'true',
        'Cache-Control': 'no-cache',
      },
    });

    if (directResponse.ok) {
      try {
        const text = await directResponse.text();
        // Check if the response is HTML
        if (text.trim().startsWith('<!DOCTYPE html>') || text.trim().startsWith('<html>')) {
          console.error('[MessageContext] Direct endpoint returned HTML instead of JSON');
          throw new Error('Received HTML instead of JSON');
        }

        // Parse JSON
        const data = JSON.parse(text);
        console.log(`[MessageContext] Direct endpoint succeeded for ${endpoint}`);
        return data;
      } catch (parseError) {
        console.error(`[MessageContext] Error parsing direct endpoint response: ${parseError}`);
        throw parseError;
      }
    } else {
      console.error(
        `[MessageContext] Direct endpoint failed with status: ${directResponse.status}`
      );
      throw new Error(`Direct endpoint failed: ${directResponse.status}`);
    }
  } catch (directError) {
    console.error(`[MessageContext] Direct endpoint error: ${directError}`);

    // Fall back to regular API endpoint
    try {
      console.log(`[MessageContext] Falling back to regular API endpoint for ${endpoint}`);
      const regularUrl = `/api/users/${userId}/${endpoint}`;

      // Use apiRequest instead of fetch to ensure we use the proxy
      const { data, response: regularResponse } = await apiRequest(regularUrl, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      if (regularResponse.ok) {
        const data = await regularResponse.json();
        console.log(`[MessageContext] Regular API endpoint succeeded for ${endpoint}`);
        return data;
      } else {
        console.error(
          `[MessageContext] Regular API endpoint also failed: ${regularResponse.status}`
        );
        throw new Error(`Both endpoints failed for ${endpoint}`);
      }
    } catch (regularError) {
      console.error(`[MessageContext] All endpoints failed for ${endpoint}: ${regularError}`);
      return endpoint === 'conversations' ? [] : { unreadCount: 0 };
    }
  }
}

export async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }

  throw new Error('Invalid response format: Expected JSON');
}

// Import at the end to avoid circular dependencies
import { apiRequest } from '../../lib/queryClient';
