import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary component to catch and handle errors in React components
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Provide user-friendly error messages based on error type
      let title = 'Something went wrong';
      let description = 'An unexpected error occurred. Please try refreshing the page.';
      let actionText = 'Try again';
      let showTechnicalDetails = false;

      if (this.state.error?.message?.includes('Invalid URL')) {
        title = 'Profile Loading Issue';
        description = 'There seems to be an issue with your profile data. We\'re working to fix this.';
        actionText = 'Refresh page';
      } else if (this.state.error?.message?.includes('Failed to fetch')) {
        title = 'Connection Problem';
        description = 'Unable to connect to our servers. Please check your internet connection.';
        actionText = 'Try again';
      } else if (this.state.error?.message?.includes('auth')) {
        title = 'Authentication Issue';
        description = 'Please log in again to continue.';
        actionText = 'Go to login';
      }

      return (
        <div className="min-h-[70vh] flex flex-col items-center justify-center p-8">
          <div className="max-w-md w-full text-center">
            <div className="mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h1 className="text-xl font-semibold text-gray-900 mb-2">{title}</h1>
              <p className="text-gray-600 mb-6">{description}</p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => {
                  if (this.state.error?.message?.includes('auth')) {
                    window.location.href = '/auth';
                  } else {
                    window.location.reload();
                  }
                }}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {actionText}
              </button>

              <button
                onClick={() => window.location.href = '/'}
                className="w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Go to home page
              </button>

              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4 text-left">
                  <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
                    Technical details (development only)
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 rounded text-xs text-gray-700 font-mono">
                    {this.state.error?.message}
                    {this.state.error?.stack && (
                      <pre className="mt-2 whitespace-pre-wrap">
                        {this.state.error.stack}
                      </pre>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap a component with an error boundary
 * @param Component - The component to wrap
 * @returns The wrapped component
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  const displayName = Component.displayName || Component.name || 'Component';

  const WrappedComponent: React.FC<P> = props => (
    <ErrorBoundary>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${displayName})`;

  return WrappedComponent;
}
