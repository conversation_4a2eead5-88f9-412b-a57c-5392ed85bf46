import { createContext, ReactNode, useContext } from 'react';
import { UseMutationResult } from '@tanstack/react-query';
import { useAuth } from '@/features/auth/AuthContext';
import { useProfile } from '@/features/profile/ProfileContext';
import { useBooking } from '@/features/booking/BookingContext';
import { User, BookingWithSession } from '@shared/schema';

// Add this to declare the global function for external updates
declare global {
  interface Window {
    __updateUserContext?: (user: User) => void;
  }
}

interface UserContextType {
  currentUser: User | null;
  userBookings: BookingWithSession[];
  isLoading: boolean;
  isCheckingAuth: boolean;
  error: Error | null;
  registerMutation: UseMutationResult<User, Error, any>;
  loginMutation: UseMutationResult<User, Error, LoginCredentials>;
  logoutMutation: UseMutationResult<any, Error, void>;
  updateUserMutation: UseMutationResult<User, Error, { id: number; userData: Partial<User> }>;
  updateTeacherStatusMutation: UseMutationResult<
    User,
    Error,
    {
      userId: number;
      teacherData: Partial<User>;
    }
  >;
  updateInstructorProfileMutation: UseMutationResult<
    any,
    Error,
    {
      userId: number;
      profileData: any;
    }
  >;
  topTeachers: User[]; // Renamed from topInstructors
  getTeacher: (id: string) => Promise<User | null>; // Renamed from getInstructor
  refetchUser: () => Promise<User>;
}

// Define credential types
interface LoginCredentials {
  username: string;
  password: string;
  provider?: string; // For social login
}

interface RegisterCredentials extends InsertUser {
  // Any additional registration fields not in InsertUser
}

// Auth check constants - used for localStorage keys
// These constants help maintain consistency in localStorage key names

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  // Get data from other contexts
  const {
    user: authUser,
    isCheckingAuth,
    loginMutation,
    registerMutation,
    logoutMutation,
    refetchUser,
  } = useAuth();
  const {
    profile,
    updateProfileMutation,
    updateTeacherStatusMutation,
    updateTeacherProfileMutation,
    topTeachers,
    getTeacher,
    isLoading: profileLoading,
  } = useProfile();

  // Use a try-catch block to handle the case when BookingContext isn't available yet
  let userBookings = [];
  let isLoadingBookings = false;
  let refreshBookings = async () => { };

  try {
    const bookingContext = useBooking();
    userBookings = bookingContext.userBookings;
    isLoadingBookings = bookingContext.isLoadingBookings;
    refreshBookings = bookingContext.refreshBookings;
  } catch (error) {
    console.warn('BookingContext not available yet in UserProvider');
  }

  // Log that we're using the compatibility layer
  console.log('[UserContext] Using compatibility layer with ProfileContext and BookingContext');
  console.log('[UserContext] Current user from AuthContext:', authUser);
  console.log('[UserContext] Profile from ProfileContext:', profile);
  console.log('[UserContext] Bookings from BookingContext:', userBookings?.length || 0, 'bookings');

  const value = {
    currentUser: authUser,
    userBookings,
    isLoading: isLoadingBookings || profileLoading,
    isCheckingAuth,
    error: null,
    registerMutation,
    loginMutation,
    logoutMutation,
    updateUserMutation: updateProfileMutation,
    updateTeacherStatusMutation,
    updateInstructorProfileMutation: updateTeacherProfileMutation,
    topTeachers,
    getTeacher,
    refetchUser,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
