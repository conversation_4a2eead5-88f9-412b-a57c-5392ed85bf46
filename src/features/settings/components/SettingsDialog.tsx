import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogTrigger,
  DialogDescription,
} from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '@/hooks/use-auth';

import {
  Bell,
  Lock,
  Eye,
  Users,
  Smartphone,
  Megaphone,
  Globe,
  Mail as MailIcon,
  Calendar as CalendarIcon,
  MessageSquare as MessageSquareIcon,
  Clock,
  Star as StarIcon,
  UserCog,
  X,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Account settings schema
const accountSchema = z
  .object({
    username: z.string().min(3, 'Username must be at least 3 characters'),
    currentPassword: z.string().optional(),
    newPassword: z.string().optional(),
    confirmPassword: z.string().optional(),
  })
  .refine(
    data => {
      if (data.newPassword && !data.currentPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'Current password is required to set a new password',
      path: ['currentPassword'],
    }
  )
  .refine(
    data => {
      if (data.newPassword && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    }
  );

// Notification settings schema
const notificationSchema = z.object({
  emailNotifications: z.boolean().default(true),
  sessionReminders: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
  siteUpdates: z.boolean().default(true),
});

type AccountFormValues = z.infer<typeof accountSchema>;
type NotificationFormValues = z.infer<typeof notificationSchema>;

interface SettingsDialogProps {
  trigger?: React.ReactNode;
}

export function SettingsDialog({ trigger }: SettingsDialogProps) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('account');
  const { toast } = useToast();
  const { user } = useAuth();

  // Prevent dialog from unmounting when state changes
  const [mounted, setMounted] = useState(false);

  // Only update open state if component is mounted
  const updateOpenState = (newState: boolean) => {
    if (mounted) {
      setOpen(newState);
    }
  };

  // Set mounted on first render
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const [preferences, setPreferences] = useState({
    theme: 'light',
    language: 'english',
    sessionReminders: true,
    notificationsEnabled: true,
    emailNotifications: true,
    pushNotifications: true,
    messageNotifications: true,
    bookingNotifications: true,
    emailDigest: 'daily',
    timezone: 'auto',
    fontScale: [100],
  });

  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    showOnlineStatus: true,
    showLastSeen: true,
    showUpcomingSessions: true,
    allowMessagesFrom: 'everyone',
    showReviewsOnProfile: true,
  });

  // Account form
  const accountForm = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      username: '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Notification form
  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      emailNotifications: true,
      sessionReminders: true,
      marketingEmails: false,
      siteUpdates: true,
    },
  });

  // Load user data when available
  useEffect(() => {
    if (user) {
      accountForm.setValue('username', (user as any).username || '');
    }
  }, [user]);

  const handleSavePreferences = () => {
    toast({
      title: 'Preferences saved',
      description: 'Your preferences have been updated successfully.',
      duration: 3000,
    });
  };

  const handleSavePrivacy = () => {
    toast({
      title: 'Privacy settings saved',
      description: 'Your privacy settings have been updated successfully.',
      duration: 3000,
    });
  };

  const onAccountSubmit = (data: AccountFormValues) => {
    console.log('Saving account settings:', data);
    // In a real app, you would save this to the backend
    toast({
      title: 'Account updated',
      description: data.newPassword
        ? 'Your account details and password have been updated.'
        : 'Your account details have been updated.',
      duration: 3000,
    });

    // Reset password fields
    accountForm.setValue('currentPassword', '');
    accountForm.setValue('newPassword', '');
    accountForm.setValue('confirmPassword', '');
  };

  const onNotificationSubmit = (data: NotificationFormValues) => {
    console.log('Saving notification preferences:', data);
    // In a real app, you would save this to the backend
    toast({
      title: 'Notification preferences updated',
      description: 'Your notification preferences have been saved successfully.',
      duration: 3000,
    });
  };

  // Handle dialog open/close state
  const handleDialogOpenChange = (newOpen: boolean) => {
    updateOpenState(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild onClick={() => updateOpenState(true)}>
        {trigger || <Button variant="outline">Settings</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
            <DialogClose asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <X className="h-4 w-4" />
              </Button>
            </DialogClose>
          </div>
          <DialogDescription>
            Manage your account settings, preferences, and privacy options
          </DialogDescription>
        </DialogHeader>

        <div className="mt-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 mb-6 bg-sage-50/60 rounded-2xl p-1.5 border border-sage-200/25">
              <TabsTrigger value="account" className="flex items-center gap-2 rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">
                <UserCog className="h-4 w-4" />
                <span>Account</span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2 rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">
                <Bell className="h-4 w-4" />
                <span>Notifications</span>
              </TabsTrigger>
              <TabsTrigger value="preferences" className="flex items-center gap-2 rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">
                <Globe className="h-4 w-4" />
                <span>Preferences</span>
              </TabsTrigger>
              <TabsTrigger value="privacy" className="flex items-center gap-2 rounded-xl font-medium transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:shadow-sage-200/20 data-[state=active]:text-sage-700 text-sage-500 hover:text-sage-600">
                <Lock className="h-4 w-4" />
                <span>Privacy</span>
              </TabsTrigger>
            </TabsList>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-5">
              <Form {...accountForm}>
                <form onSubmit={accountForm.handleSubmit(onAccountSubmit)} className="space-y-5">
                  <FormField
                    control={accountForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-5">
                    <h3 className="text-base font-medium">Change Password</h3>

                    <FormField
                      control={accountForm.control}
                      name="currentPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={accountForm.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={accountForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" className="w-full mt-4">
                    Update Account
                  </Button>
                </form>
              </Form>
            </TabsContent>

            {/* Notifications Tab */}
            <TabsContent value="notifications" className="space-y-5">
              <Form {...notificationForm}>
                <form
                  onSubmit={notificationForm.handleSubmit(onNotificationSubmit)}
                  className="space-y-5"
                >
                  <FormField
                    control={notificationForm.control}
                    name="emailNotifications"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Email Notifications</FormLabel>
                          <FormDescription>Receive notifications via email</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="sessionReminders"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Session Reminders</FormLabel>
                          <FormDescription>Get reminders about upcoming sessions</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="marketingEmails"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Marketing Emails</FormLabel>
                          <FormDescription>Receive promotional offers and updates</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="siteUpdates"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Platform Updates</FormLabel>
                          <FormDescription>
                            Get notified about new features and improvements
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Button type="submit" className="w-full mt-4">
                    Save Notification Settings
                  </Button>
                </form>
              </Form>
            </TabsContent>

            {/* Preferences Tab */}
            <TabsContent value="preferences" className="space-y-5">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="language">Display Language</Label>
                    <Select
                      value={preferences.language}
                      onValueChange={value => setPreferences({ ...preferences, language: value })}
                    >
                      <SelectTrigger id="language">
                        <SelectValue placeholder="Select Language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="english">English</SelectItem>
                        <SelectItem value="spanish">Spanish</SelectItem>
                        <SelectItem value="french">French</SelectItem>
                        <SelectItem value="german">German</SelectItem>
                        <SelectItem value="chinese">Chinese</SelectItem>
                        <SelectItem value="japanese">Japanese</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="theme">Theme</Label>
                    <Select
                      value={preferences.theme}
                      onValueChange={value => setPreferences({ ...preferences, theme: value })}
                    >
                      <SelectTrigger id="theme">
                        <SelectValue placeholder="Select Theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System Default</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="font-scale">Font Size</Label>
                    <span className="text-sm text-muted-foreground">
                      {preferences.fontScale[0]}%
                    </span>
                  </div>
                  <Slider
                    id="font-scale"
                    value={preferences.fontScale}
                    onValueChange={value => setPreferences({ ...preferences, fontScale: value })}
                    max={150}
                    min={75}
                    step={5}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select
                      value={preferences.timezone}
                      onValueChange={value => setPreferences({ ...preferences, timezone: value })}
                    >
                      <SelectTrigger id="timezone">
                        <SelectValue placeholder="Select Timezone" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto-detect</SelectItem>
                        <SelectItem value="est">Eastern Time (ET)</SelectItem>
                        <SelectItem value="cst">Central Time (CT)</SelectItem>
                        <SelectItem value="mst">Mountain Time (MT)</SelectItem>
                        <SelectItem value="pst">Pacific Time (PT)</SelectItem>
                        <SelectItem value="utc">UTC</SelectItem>
                        <SelectItem value="gmt">GMT</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button onClick={handleSavePreferences} className="w-full mt-4">
                  Save Preferences
                </Button>
              </div>
            </TabsContent>

            {/* Privacy Tab */}
            <TabsContent value="privacy" className="space-y-5">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-visibility">Profile Visibility</Label>
                  <Select
                    value={privacy.profileVisibility}
                    onValueChange={value => setPrivacy({ ...privacy, profileVisibility: value })}
                  >
                    <SelectTrigger id="profile-visibility">
                      <SelectValue placeholder="Select Visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">Public - Everyone can see</SelectItem>
                      <SelectItem value="registered">Registered Users Only</SelectItem>
                      <SelectItem value="private">Private - Only When Booked</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message-permission">Who Can Message You</Label>
                  <Select
                    value={privacy.allowMessagesFrom}
                    onValueChange={value => setPrivacy({ ...privacy, allowMessagesFrom: value })}
                  >
                    <SelectTrigger id="message-permission">
                      <SelectValue placeholder="Select Who Can Message You" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="everyone">Everyone</SelectItem>
                      <SelectItem value="booked">Users Who've Booked Sessions</SelectItem>
                      <SelectItem value="approved">Approved Users Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="online-status">Show When I'm Online</Label>
                  </div>
                  <Switch
                    id="online-status"
                    checked={privacy.showOnlineStatus}
                    onCheckedChange={checked =>
                      setPrivacy({ ...privacy, showOnlineStatus: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="last-seen">Show Last Seen</Label>
                  </div>
                  <Switch
                    id="last-seen"
                    checked={privacy.showLastSeen}
                    onCheckedChange={checked => setPrivacy({ ...privacy, showLastSeen: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="upcoming-sessions">Show Upcoming Sessions</Label>
                  </div>
                  <Switch
                    id="upcoming-sessions"
                    checked={privacy.showUpcomingSessions}
                    onCheckedChange={checked =>
                      setPrivacy({ ...privacy, showUpcomingSessions: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <StarIcon className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor="show-reviews">Show Reviews on Profile</Label>
                  </div>
                  <Switch
                    id="show-reviews"
                    checked={privacy.showReviewsOnProfile}
                    onCheckedChange={checked =>
                      setPrivacy({ ...privacy, showReviewsOnProfile: checked })
                    }
                  />
                </div>

                <Button onClick={handleSavePrivacy} className="w-full mt-4">
                  Save Privacy Settings
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
