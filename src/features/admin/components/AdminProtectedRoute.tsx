import { useEffect, useState } from 'react';
import { useAuth } from '@/features/auth/AuthContext';
import { Loader2 } from 'lucide-react';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

export function AdminProtectedRoute({ children }: AdminProtectedRouteProps) {
  const { user, isCheckingAuth } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function checkAdminStatus() {
      try {
        // First check if we're still loading auth state
        if (isCheckingAuth) {
          return;
        }

        // If no user is logged in, they're definitely not an admin
        if (!user) {
          setIsAdmin(false);
          setIsLoading(false);
          return;
        }

        // Check if user has admin role in our system
        if (user.role === 'admin') {
          setIsAdmin(true);
          setIsLoading(false);
          return;
        }

        // Check for admin privileges in Supabase user metadata
        const cognitoTokensStr = localStorage.getItem('cognito_tokens');
        if (cognitoTokensStr) {
          try {
            // Make a request to verify admin status
            const response = await fetch('/api/auth/verify-admin', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${JSON.parse(cognitoTokensStr).accessToken}`
              },
              credentials: 'include'
            });

            if (response.ok) {
              setIsAdmin(true);
            } else {
              setIsAdmin(false);
            }
          } catch (error) {
            console.error('Error verifying admin status:', error);
            setIsAdmin(false);
          }
        } else {
          setIsAdmin(false);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
        setIsLoading(false);
      }
    }

    checkAdminStatus();
  }, [user, isCheckingAuth]);

  // Show loading state
  if (isLoading || isCheckingAuth) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-gray-600">Verifying admin access...</p>
      </div>
    );
  }

  // Show access denied if not admin
  if (!isAdmin) {
    return (
      <div className="min-h-[70vh] flex flex-col items-center justify-center p-4">
        <h1 className="text-xl font-medium text-gray-800 mb-2">Access Denied</h1>
        <p className="text-gray-600 text-center max-w-md">
          You don't have permission to access this page. This area is restricted to administrators
          only.
        </p>
        <div className="mt-4">
          <a
            href="/"
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
          >
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  // If admin, render children
  return <>{children}</>;
}
