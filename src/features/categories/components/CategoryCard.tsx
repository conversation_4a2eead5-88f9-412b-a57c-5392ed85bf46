import { Link } from '../../../components/ui/NextLink';
import { CategoryItem } from '@/lib/types';

interface CategoryCardProps {
  category: CategoryItem;
}

export function CategoryCard({ category }: CategoryCardProps) {
  const { id, name, icon, sessionCount, color } = category;

  // Map color to Tailwind classes for more subtle, minimal colors
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'bg-primary/10 text-primary';
      case 'secondary':
        return 'bg-secondary/10 text-secondary';
      case 'blue':
        return 'bg-blue-500/10 text-blue-500';
      case 'red':
        return 'bg-red-500/10 text-red-500';
      case 'green':
        return 'bg-green-500/10 text-green-500';
      case 'yellow':
        return 'bg-amber-500/10 text-amber-500';
      case 'purple':
        return 'bg-purple-500/10 text-purple-500';
      case 'pink':
        return 'bg-pink-500/10 text-pink-500';
      case 'indigo':
        return 'bg-indigo-500/10 text-indigo-500';
      default:
        return 'bg-gray-500/10 text-gray-500';
    }
  };

  return (
    <Link href={`/?sessionType=${name}`}>
      <div
        className="bg-white/90 backdrop-blur-sm rounded-xl border border-gray-100 
                      hover:border-primary/20 overflow-hidden hover:shadow-sm 
                      hover:translate-y-[-2px] transition-all duration-300 
                      text-center p-3.5 cursor-pointer group"
      >
        <div
          className={`w-9 h-9 ${getColorClasses()} rounded-full mx-auto flex items-center justify-center shadow-sm`}
        >
          <i className={`${icon} text-xs`}></i>
        </div>
        <h3 className="mt-2.5 text-xs font-medium text-gray-700 group-hover:text-primary transition-colors">
          {name}
        </h3>
        <p className="mt-0.5 text-[10px] text-gray-400">{sessionCount} sessions</p>
      </div>
    </Link>
  );
}
