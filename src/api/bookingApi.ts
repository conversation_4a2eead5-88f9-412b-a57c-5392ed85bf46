import { apiClient } from '../lib/apiClient';

/**
 * Booking API client
 */
export const bookingApi = {
  /**
   * Get all bookings
   * @returns The bookings
   */
  async getBookings() {
    try {
      const { data } = await apiClient.get('/api/bookings');
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get a booking by ID
   * @param id - The booking ID
   * @returns The booking
   */
  async getBookingById(id: number) {
    try {
      const { data } = await apiClient.get(`/api/bookings/${id}`);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Create a new booking
   * @param booking - The booking data
   * @returns The created booking
   */
  async createBooking(booking: any) {
    try {
      const { data } = await apiClient.post('/api/bookings', booking);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update a booking
   * @param id - The booking ID
   * @param booking - The booking data to update
   * @returns The updated booking
   */
  async updateBooking(id: number, booking: any) {
    try {
      const { data } = await apiClient.put(`/api/bookings/${id}`, booking);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Cancel a booking
   * @param id - The booking ID
   * @returns The cancelled booking
   */
  async cancelBooking(id: number) {
    try {
      const { data } = await apiClient.put(`/api/bookings/${id}`, { status: 'cancelled' });
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get bookings for a user
   * @param userId - The user ID
   * @returns The user's bookings
   */
  async getUserBookings(userId: number) {
    try {
      const { data } = await apiClient.get(`/api/users/${userId}/bookings`);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get bookings for a session
   * @param sessionId - The session ID
   * @returns The session's bookings
   */
  async getSessionBookings(sessionId: number) {
    try {
      const { data } = await apiClient.get(`/api/sessions/${sessionId}/bookings`);
      return data;
    } catch (error) {
      throw error;
    }
  },
};
