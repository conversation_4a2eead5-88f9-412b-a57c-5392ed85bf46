/**
 * Profile API
 *
 * This file contains functions for interacting with the profile API
 * directly from Supabase.
 */

import { supabase } from '../lib/supabase';

/**
 * Check if a user profile exists in Supabase
 * @param userId The user ID to check
 * @returns True if the profile exists, false otherwise
 */
export async function checkUserProfileExists(userId: string): Promise<boolean> {
  try {
    console.log('[ProfileAPI] Checking if user profile exists for user:', userId);

    const { data, error } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // PGRST116 means no rows returned
        console.log('[ProfileAPI] No profile found for user:', userId);
        return false;
      }

      console.error('[ProfileAPI] Error checking if profile exists:', error);
      throw error;
    }

    console.log('[ProfileAPI] Profile exists for user:', userId);
    return !!data;
  } catch (error) {
    console.error('[ProfileAPI] Error checking if profile exists:', error);
    return false;
  }
}

/**
 * Create a user profile in Supabase
 * @param userId The user ID
 * @param profileData Optional profile data
 * @returns The created profile
 */
export async function createUserProfile(userId: string, profileData?: any) {
  try {
    console.log('[ProfileAPI] Creating user profile for user:', userId);

    // Get user data from auth
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('[ProfileAPI] Error getting user data:', userError);
      throw userError;
    }

    if (!user) {
      console.error('[ProfileAPI] No user found in auth');
      throw new Error('No user found in auth');
    }

    // Prepare profile data
    const data = {
      user_id: userId,
      name: profileData?.name || user.user_metadata?.name || user.email?.split('@')[0] || 'User',
      avatar: profileData?.avatar || user.user_metadata?.avatar_url || 'https://placehold.co/400x400?text=Profile',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('[ProfileAPI] Creating profile with data:', data);

    // Create the profile
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .insert([data])
      .select()
      .single();

    if (error) {
      // If the error is a duplicate key violation (unique constraint violation)
      if (error.code === '23505' || error.message?.includes('duplicate key value')) {
        console.log('[ProfileAPI] Profile already exists (concurrent creation), fetching existing profile');
        // If it's a duplicate key error, just fetch the existing profile
        const { data: existingProfile, error: fetchError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (fetchError) {
          console.error('[ProfileAPI] Error fetching existing profile:', fetchError);
          throw fetchError;
        }

        return existingProfile;
      } else {
        console.error('[ProfileAPI] Error creating profile:', error);
        throw error;
      }
    }

    console.log('[ProfileAPI] Profile created successfully:', profile);
    return profile;
  } catch (error) {
    console.error('[ProfileAPI] Error creating profile:', error);
    throw error;
  }
}

/**
 * Get a user profile from Supabase
 * @param userId The user ID
 * @returns The user profile
 */
export async function getUserProfile(userId: string) {
  try {
    console.log('[ProfileAPI] Getting user profile for user:', userId);

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('[ProfileAPI] Error getting profile:', error);
      throw error;
    }

    console.log('[ProfileAPI] Got profile:', data);
    return data;
  } catch (error) {
    console.error('[ProfileAPI] Error getting profile:', error);
    throw error;
  }
}

/**
 * Update a user profile in Supabase
 * @param userId The user ID
 * @param profileData The profile data to update
 * @returns The updated profile
 */
export async function updateUserProfile(userId: string, profileData: any) {
  try {
    console.log('[ProfileAPI] Updating user profile for user:', userId);
    console.log('[ProfileAPI] Update data:', profileData);

    // Add updated_at timestamp
    const data = {
      ...profileData,
      updated_at: new Date().toISOString()
    };

    const { data: profile, error } = await supabase
      .from('user_profiles')
      .update(data)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('[ProfileAPI] Error updating profile:', error);
      throw error;
    }

    console.log('[ProfileAPI] Profile updated successfully:', profile);
    return profile;
  } catch (error) {
    console.error('[ProfileAPI] Error updating profile:', error);
    throw error;
  }
}

/**
 * Ensure a user profile exists in Supabase
 * @param userId The user ID
 * @param profileData Optional profile data
 * @returns The user profile
 */
export async function ensureUserProfile(userId: string, profileData?: any) {
  try {
    console.log('[ProfileAPI] Ensuring user profile exists for user:', userId);

    // Check if profile exists
    const exists = await checkUserProfileExists(userId);

    if (exists) {
      console.log('[ProfileAPI] Profile already exists, getting it');
      return await getUserProfile(userId);
    }

    console.log('[ProfileAPI] Profile does not exist, creating it');
    return await createUserProfile(userId, profileData);
  } catch (error) {
    console.error('[ProfileAPI] Error ensuring user profile exists:', error);
    throw error;
  }
}
