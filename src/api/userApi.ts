import { apiClient } from '../lib/apiClient';

/**
 * User API client
 */
export const userApi = {
  /**
   * Get the current user's profile
   * @returns The current user's profile
   */
  async getCurrentUser() {
    try {
      const { data } = await apiClient.get('/api/users/profile');
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get a user by ID
   * @param id - The user ID
   * @returns The user profile
   */
  async getUserById(id: number) {
    try {
      const { data } = await apiClient.get(`/api/users/${id}`);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get a user by username
   * @param username - The username
   * @returns The user profile
   */
  async getUserByUsername(username: string) {
    try {
      const { data } = await apiClient.get(`/api/users/username/${username}`);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update the current user's profile
   * @param profile - The profile data to update
   * @returns The updated profile
   */
  async updateUserProfile(profile: any) {
    try {
      const { data } = await apiClient.put('/api/users/profile', profile);
      return data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Search for users by username
   * @param query - The search query
   * @param limit - The maximum number of results to return
   * @returns The search results
   */
  async searchUsers(query: string, limit: number = 20) {
    try {
      const { data } = await apiClient.get('/api/users/search', {
        params: { q: query, limit },
      });
      return data;
    } catch (error) {
      throw error;
    }
  },
};
