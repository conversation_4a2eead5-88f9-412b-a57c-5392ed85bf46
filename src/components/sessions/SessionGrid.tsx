import { useFilter } from '@/features/filter';
import { useSession } from '@/contexts/SessionContext';
import { SessionCard } from './SessionCard';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEffect, useState } from 'react';
import { ArrowRight } from 'lucide-react';

interface SessionGridProps {
  title?: string;
  subtitle?: string;
  limit?: number;
  showLoadMore?: boolean;
  featured?: boolean;
}

export function SessionGrid({
  title = 'Featured Sessions',
  subtitle,
  limit,
  showLoadMore = false,
  featured = false,
}: SessionGridProps) {
  const { isLoading } = useSession();
  const { filteredSessions } = useFilter();
  const [visibleSessions, setVisibleSessions] = useState<number[]>([]);
  const [displayLimit, setDisplayLimit] = useState<number>(7); // Show 7 items initially per user request

  // Get all sessions
  const allSessions = filteredSessions;

  // Apply display limit
  const sessions = limit ? filteredSessions.slice(0, limit) : filteredSessions.slice(0, displayLimit);

  const hasMoreSessions = allSessions.length > sessions.length;

  const loadMoreSessions = () => {
    setDisplayLimit(prevLimit => prevLimit + 7); // Load 7 more sessions per user request
  };

  // Reset and animate cards when sessions change - simplified for better performance
  useEffect(() => {
    if (sessions.length > 0 && !isLoading) {
      // Show all sessions immediately without staggered animation
      setVisibleSessions(sessions.map((_, index) => index));
    } else {
      setVisibleSessions([]);
    }
  }, [sessions, isLoading]);

  return (
    <div className={`${featured ? 'py-8' : 'py-4'}`}>
      {(title || subtitle) && (
        <div className="mb-8">
          {title && (
            <h2 className={`${featured ? 'text-3xl' : 'text-2xl'} font-bold text-[#333333] mb-2`}>
              {title}
            </h2>
          )}
          {subtitle && <p className="text-[#666666] max-w-3xl">{subtitle}</p>}
        </div>
      )}

      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
          {Array.from({ length: limit || 8 }).map((_, index) => (
            <div
              key={index}
              className="bg-white border-b border-gray-200 overflow-hidden h-full py-4"
            >
              {/* Image area */}
              <div className="aspect-[16/9] relative bg-gray-100 rounded-lg">
                <Skeleton className="absolute top-2 right-2 h-6 w-6 rounded-full" />
              </div>
              {/* Content area */}
              <div className="px-1 mt-3">
                <div className="flex items-start justify-between mb-1 md:mb-2">
                  <div className="flex">
                    <Skeleton className="h-10 w-10 rounded-full mr-3 flex-shrink-0" />
                    <div>
                      <Skeleton className="h-4 md:h-5 w-32 md:w-40 mb-1 md:mb-2 rounded-md" />
                      <Skeleton className="h-3 w-24 rounded-md" />
                    </div>
                  </div>
                  <Skeleton className="h-4 w-12 rounded-md" />
                </div>

                <div className="mt-3">
                  <Skeleton className="h-3 w-32 rounded-md" />
                </div>

                <Skeleton className="h-3 md:h-4 w-full mb-2 rounded-md mt-2 hidden xs:block" />
                <Skeleton className="h-3 md:h-4 w-full mb-2 rounded-md hidden xs:block" />

                <div className="flex gap-2 mt-3">
                  <Skeleton className="h-5 w-16 rounded-full" />
                  <Skeleton className="h-5 w-16 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-5 md:gap-6 lg:gap-7">
            {sessions.length > 0 ? (
              sessions.map((session, index) => (
                <div
                  key={session.id}
                  className={`h-full ${visibleSessions.includes(index) ? 'opacity-100' : 'opacity-0'}`}
                  style={{
                    transition: 'opacity 200ms ease-out',
                  }}
                >
                  <SessionCard session={session} />
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-16 bg-[#F0EEE9]/40 rounded-xl">
                <h3 className="text-xl font-semibold text-[#666666]">No sessions found</h3>
                <p className="text-[#808080] mt-2 max-w-md mx-auto">
                  Try adjusting your filters or search criteria to discover more learning
                  opportunities.
                </p>
              </div>
            )}
          </div>

          {hasMoreSessions && (
            <div className="mt-12 flex justify-center">
              <Button
                variant="outline"
                className="group px-6 py-2 border-[#E07A5F] text-[#E07A5F] hover:bg-[#E07A5F] hover:text-white transition-all duration-300"
                onClick={loadMoreSessions}
              >
                <span>Load More Sessions</span>
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
