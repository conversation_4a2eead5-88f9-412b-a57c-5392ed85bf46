import { Link } from '../ui/NextLink';
import { SessionWithTeacher } from '../../../shared/schema';
import { Clock, Calendar } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { StarRating } from '@/components/ui/star-rating';
import {
  formatPrice,
  formatDuration,
  getInitials,
  getSessionCoverImage,
  getAvatarUrl,
  handleImageError
} from '@/lib/utils';

interface SessionListItemProps {
  session: SessionWithTeacher;
}

// Format date without year and in more user-friendly format
function formatDateShort(date: Date | string): string {
  const d = new Date(date);
  // Get month name and day
  const month = d.toLocaleString('default', { month: 'short' });
  const day = d.getDate();

  // Format time in 12-hour format
  let hours = d.getHours();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // The hour '0' should be '12'
  const minutes = d.getMinutes().toString().padStart(2, '0');

  return `${month} ${day}, ${hours}:${minutes} ${ampm}`;
}

export function SessionListItem({ session }: SessionListItemProps) {
  return (
    <div className="border-b hover:bg-gray-50/50 transition-all duration-200">
      <Link href={`/session/${session.id}`} className="flex w-full">
        <div className="flex py-4 w-full">
          {/* Image container with teacher avatar overlaid at top-right */}
          <div className="relative w-24 sm:w-32 h-24 sm:h-32 flex-shrink-0 rounded-lg overflow-hidden">
            <img
              src={getSessionCoverImage(session.type, session.id, session.image_url)}
              onError={e => {
                console.log(`Image error for session: ${session.title} (${session.type})`);
                console.log(`Original image URL: ${session.image_url}`);
                handleImageError(e, `session ${session.id} (${session.type})`);
              }}
              alt={session.title}
              className="w-full h-full object-cover"
              loading="lazy"
              decoding="async"
            />
            {/* Overlay rating and price for better visibility */}
            <div className="absolute top-0 right-0 bg-white/90 px-1.5 py-0.5 rounded-bl-lg text-sm font-medium text-gray-900">
              {formatPrice(session.price)}
            </div>

            {/* Position teacher avatar overlapping the top-right corner of cover image */}
            {(() => {
              // Use either teacher or teacher property
              const teacher = session.teacher || session.teacher;
              console.log('SessionListItem - Teacher/Teacher data:', teacher);

              if (!teacher) return null;

              return (
                <div
                  className="absolute -top-2 -right-2 cursor-pointer z-10"
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.href = `/teachers/${teacher.id}`;
                  }}
                >
                  <Avatar className="h-11 w-11 rounded-full shadow-md border-2 border-white">
                    <AvatarImage
                      src={teacher.avatar_url ? getAvatarUrl(teacher.avatar_url) : undefined}
                      className="object-cover"
                      onError={(e) => {
                        console.log(`Avatar error for teacher: ${teacher.full_name}`);
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite loop
                        target.src = 'https://placehold.co/100x100?text=Profile';
                      }}
                    />
                    <AvatarFallback className="bg-gray-100 text-gray-600 text-xs">
                      {getInitials(teacher.full_name || 'Unknown')}
                    </AvatarFallback>
                  </Avatar>
                </div>
              );
            })()}
          </div>

          {/* Content container */}
          <div className="flex-1 ml-4 flex flex-col justify-between">
            <div>
              <div className="flex-1">
                <div className="flex items-center mb-1">
                  {(() => {
                    // Use either teacher or teacher property
                    const teacher = session.teacher || session.teacher;

                    if (!teacher) return null;

                    return (
                      <p
                        className="text-xs font-medium text-gray-700 hover:text-[#E07A5F] transition-colors cursor-pointer truncate max-w-[150px]"
                        onClick={e => {
                          e.preventDefault();
                          e.stopPropagation();
                          window.location.href = `/teachers/${teacher.id}`;
                        }}
                      >
                        {(teacher.full_name || '').includes('[SAMPLE]')
                          ? teacher.full_name || 'Unknown'
                          : (teacher.full_name || '').includes('SAMPLE')
                            ? teacher.full_name || 'Unknown'
                            : `[SAMPLE] ${teacher.full_name || 'Unknown'}`}
                      </p>
                    );
                  })()}
                </div>
                <h3 className="text-base font-medium text-gray-900 line-clamp-1">
                  {session.title}
                </h3>
                <p className="text-sm text-gray-600 mt-0.5 line-clamp-2">{session.description}</p>
              </div>
            </div>

            {/* Bottom info section with improved spacing */}
            <div className="flex flex-wrap justify-between items-center mt-auto">
              {/* Session details */}
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <div className="flex items-center gap-1">
                  <Clock className="w-3.5 h-3.5 text-primary/70" />
                  <span>{formatDuration(session.duration)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-3.5 h-3.5 text-primary/70" />
                  <span>{session.date ? formatDateShort(session.date) : 'TBD'}</span>
                </div>
              </div>

              {/* Rating displayed here instead */}
              <div className="flex items-center gap-1 ml-auto">
                <StarRating rating={session.teacher.rating ?? 0} size="sm" />
                <span className="text-[0.65rem] text-gray-500">({session.teacher.reviewCount ?? 0})</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}