import { useState, useEffect } from 'react';
import { useFilter } from '@/features/filter';
import { useSession } from '@/contexts/SessionContext';
import { SessionCard } from './SessionCard';
import { SessionListItem } from './SessionListItem';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LayoutGrid, List } from 'lucide-react';
import { SORT_OPTIONS } from '@/lib/constants';
import { useIsMobile } from '@/hooks/use-mobile';
import { SortOption } from '@/lib/types';

interface SessionListProps {
  title?: string;
  limit?: number;
  showControls?: boolean;
}

export function SessionList({ title, limit, showControls = false }: SessionListProps) {
  // Always use grid view as default
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [displayCount, setDisplayCount] = useState<number>(7);
  const isMobile = useIsMobile();
  const { isLoading } = useSession();
  const { filteredSessions, sortOption, setSortOption, filterState, setSearchMode } = useFilter();

  // Apply limit if provided, otherwise use display count
  const sessions = limit ? filteredSessions.slice(0, limit) : filteredSessions.slice(0, displayCount);

  const loadMore = () => {
    setDisplayCount(prev => prev + 7);
  };

  const handleSortChange = (value: string) => {
    setSortOption(value as SortOption);
  };

  return (
    <section className="py-4">
      <div className="container">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant={filterState.searchMode === 'sessions' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('sessions')}
            >
              <i className="fas fa-chalkboard-teacher mr-2"></i>
              Sessions
            </Button>
            <Button
              variant={filterState.searchMode === 'teachers' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('teachers')}
            >
              <i className="fas fa-users mr-2"></i>
              Teachers
            </Button>
          </div>
          <div className="flex items-center gap-4">
            <div className="w-auto relative">
              <Select value={sortOption} onValueChange={handleSortChange}>
                <SelectTrigger className="bg-white border-gray-200 text-sm">
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent className="bg-white border-gray-200 z-50" align="end">
                  {SORT_OPTIONS.map(option => (
                    <SelectItem key={option} value={option} className="text-sm cursor-pointer">
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        <div
          className={
            viewMode === 'grid'
              ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'flex flex-col gap-4'
          }
        >
          {sessions.map(session =>
            viewMode === 'grid' ? (
              <SessionCard key={session.id} session={session} />
            ) : (
              <SessionListItem key={session.id} session={session} />
            )
          )}
        </div>

        {!limit && filteredSessions.length > displayCount && (
          <div className="mt-8 flex justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={loadMore}
              className="hover:bg-[#E07A5F]/10"
            >
              Load More Sessions
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
