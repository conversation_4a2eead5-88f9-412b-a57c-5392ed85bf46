# Session Form Shared Components

This directory contains shared components used in both the Create Session and Edit Session forms.

## Overview

These components are designed to be reusable between the Create Session and Edit Session forms, ensuring consistent behavior and appearance while reducing code duplication.

## Components

### SessionDetailsTab

A shared component for the Session Details tab that handles:
- Session title, type, and description
- Image upload
- Price and duration
- Language and time of day preferences
- Skill level and format
- Maximum participants

### SchedulingTab

A shared component for the Scheduling tab that handles:
- Scheduling mode selection (fixed, recurring, availability)
- Date and time selection
- Recurring pattern configuration
- Custom availability management

### CancellationTab

A shared component for the Cancellation tab that handles:
- Cancellation timeframe settings
- Late cancellation fee percentage
- No-show fee percentage
- Cancellation policy text generation

### LegalAgreementTab

A shared component for the Legal Agreement tab that handles:
- Agreement template selection
- Custom agreement toggle
- Legal agreement text editing
- Legal notices

### MessagesTab

A shared component for the Messages tab that handles:
- Automated message management
- Message scheduling
- Message content editing

## Usage

Each component accepts a common set of props:

```typescript
interface SharedTabProps {
  mode: 'create' | 'edit';  // Determines context-specific behavior
  form: UseFormReturn<any>; // React Hook Form instance
  formSubmitted: boolean;   // Whether the form has been submitted (for validation)
  onCancel: () => void;     // Handler for cancel button
  onDelete?: () => void;    // Handler for delete button (edit mode only)
  onSubmit: () => void;     // Handler for submit button
  isSubmitting: boolean;    // Whether the form is currently submitting
  submitButtonText: string; // Text for the submit button
  loadingButtonText: string; // Text for the submit button when loading
}
```

### Example

```tsx
// In create-session.tsx or edit-session.tsx
<TabsContent value="details">
  <SessionDetailsTab
    mode="create" // or "edit"
    form={form}
    formSubmitted={formSubmitted}
    onCancel={() => navigate('/profile')}
    onSubmit={form.handleSubmit(onSubmit)}
    isSubmitting={createSessionMutation.isPending}
    submitButtonText="Create Session"
    loadingButtonText="Creating..."
    onImageChange={handleImageChange}
    imageUrl={imageUrl}
  />
</TabsContent>
```

## Performance Optimizations

All components are memoized using React.memo to prevent unnecessary re-renders. Event handlers are optimized with useCallback to maintain referential equality between renders.

## Maintenance

When making changes to these components:

1. Ensure changes work correctly in both create and edit contexts
2. Use the `mode` prop to conditionally render context-specific UI elements
3. Keep shared logic in the components and context-specific logic in the parent forms
4. Maintain consistent styling and behavior across all components
