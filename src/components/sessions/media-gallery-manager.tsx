import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
    Upload,
    X,
    Image as ImageIcon,
    Video,
    FileText,
    Edit2,
    Trash2,
    ExternalLink,
    AlertCircle,
    CheckCircle2,
    Plus,
    ChevronLeft,
    ChevronRight,
    GripVertical,
    Camera
} from 'lucide-react';
import { storageService } from '@/services/storage-service';

interface MediaItem {
    id: string;
    type: 'image' | 'video' | 'document';
    url: string;
    title: string;
    description?: string;
    upload_date: string;
    file_size?: number;
    storage_path?: string;
    metadata?: {
        width?: number;
        height?: number;
        format?: string;
        platform?: string;
        duration?: string;
        pages?: number;
    };
}

interface StorageUsage {
    totalBytes: number;
    totalMB: number;
    fileCount: number;
}

interface MediaGalleryManagerProps {
    mediaItems: MediaItem[];
    onMediaItemsChange: (items: MediaItem[]) => void;
    sessionId: string;
    userId: string;
    className?: string;
}

export const MediaGalleryManager: React.FC<MediaGalleryManagerProps> = ({
    mediaItems,
    onMediaItemsChange,
    sessionId,
    userId,
    className
}) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [storageUsage, setStorageUsage] = useState<StorageUsage | null>(null);
    const [showAddForm, setShowAddForm] = useState(false);
    const [newItemType, setNewItemType] = useState<'image' | 'video' | 'document'>('image');
    const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

    useEffect(() => {
        loadStorageUsage();
    }, [userId]);

    const loadStorageUsage = async () => {
        try {
            const usage = await storageService.getUserStorageUsage(userId);
            setStorageUsage(usage);
        } catch (error) {
            console.error('Failed to load storage usage:', error);
        }
    };

    const showToast = (message: string, type: 'success' | 'error' = 'success') => {
        import('sonner').then(({ toast }) => {
            if (type === 'success') {
                toast.success(message, {
                    style: {
                        background: '#C2A584',
                        color: 'white',
                        border: '1px solid #B8956F'
                    }
                });
            } else {
                toast.error(message, {
                    style: {
                        background: '#ef4444',
                        color: 'white',
                        border: '1px solid #dc2626'
                    }
                });
            }
        });
    };

    const handleFileUpload = async (file: File, type: 'image' | 'document', title: string, description?: string) => {
        setIsLoading(true);
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);
            formData.append('title', title);
            if (description) formData.append('description', description);

            const response = await fetch(`/api/sessions/${sessionId}/media`, {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                throw new Error('Failed to upload file');
            }

            const newItem = await response.json();
            onMediaItemsChange([...mediaItems, newItem]);
            setCurrentIndex(mediaItems.length);

            showToast(`${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully`);
            setShowAddForm(false);
        } catch (error) {
            console.error('Upload error:', error);
            showToast('Failed to upload file. Please try again.', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    const handleVideoAdd = (url: string, title: string, description?: string) => {
        const platform = getVideoPlatform(url);

        const newItem: MediaItem = {
            id: `temp-${Date.now()}`,
            type: 'video',
            url: url,
            title: title,
            description: description,
            upload_date: new Date().toISOString(),
            metadata: {
                platform: platform
            }
        };

        onMediaItemsChange([...mediaItems, newItem]);
        setCurrentIndex(mediaItems.length);

        showToast('Video added successfully');
        setShowAddForm(false);
    };

    const handleDeleteItem = async (item: MediaItem) => {
        try {
            if (item.storage_path) {
                await storageService.deleteFile(item.storage_path);
            }

            const newItems = mediaItems.filter(i => i.id !== item.id);
            onMediaItemsChange(newItems);

            if (currentIndex >= newItems.length) {
                setCurrentIndex(Math.max(0, newItems.length - 1));
            }

            showToast('Media item deleted successfully');
            loadStorageUsage();
        } catch (error) {
            console.error('Delete error:', error);
            showToast('Failed to delete item. Please try again.', 'error');
        }
    };

    const handleUpdateItem = (updatedItem: MediaItem) => {
        const newItems = mediaItems.map(item =>
            item.id === updatedItem.id ? updatedItem : item
        );
        onMediaItemsChange(newItems);
        showToast('Media item updated successfully');
    };

    const handleReorderThumbnails = (fromIndex: number, toIndex: number) => {
        const newItems = [...mediaItems];
        const [removed] = newItems.splice(fromIndex, 1);
        newItems.splice(toIndex, 0, removed);
        onMediaItemsChange(newItems);

        // Update current index if needed
        if (currentIndex === fromIndex) {
            setCurrentIndex(toIndex);
        } else if (currentIndex > fromIndex && currentIndex <= toIndex) {
            setCurrentIndex(currentIndex - 1);
        } else if (currentIndex < fromIndex && currentIndex >= toIndex) {
            setCurrentIndex(currentIndex + 1);
        }

        // Only show toast when actually reordering (not when just clicking thumbnails)
        // The toast will only show when this function is called from drag and drop operations
        if (fromIndex !== toIndex) {
            showToast('Media reordered successfully');
        }
    };

    const getVideoPlatform = (url: string): string => {
        if (url.includes('youtube.com') || url.includes('youtu.be')) return 'YouTube';
        if (url.includes('vimeo.com')) return 'Vimeo';
        if (url.includes('twitch.tv')) return 'Twitch';
        return 'Unknown';
    };

    const getMediaIcon = (type: MediaItem['type']) => {
        switch (type) {
            case 'image': return <ImageIcon className="h-4 w-4" />;
            case 'video': return <Video className="h-4 w-4" />;
            case 'document': return <FileText className="h-4 w-4" />;
            default: return <FileText className="h-4 w-4" />;
        }
    };

    const getTypeColor = (type: MediaItem['type']) => {
        switch (type) {
            case 'image': return 'text-sage-600';
            case 'video': return 'text-sage-600';
            case 'document': return 'text-sage-600';
            default: return 'text-sage-600';
        }
    };

    const ThumbnailIcon = ({
        item,
        index,
        isActive,
        onClick
    }: {
        item: MediaItem;
        index: number;
        isActive: boolean;
        onClick: () => void;
    }) => {
        const handleDragStart = (e: React.DragEvent) => {
            setDraggedIndex(index);
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/plain', index.toString());
        };

        const handleDragEnd = () => {
            setDraggedIndex(null);
        };

        const handleDragOver = (e: React.DragEvent) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        };

        const handleDrop = (e: React.DragEvent) => {
            e.preventDefault();
            const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
            if (fromIndex !== index && draggedIndex !== null) {
                handleReorderThumbnails(fromIndex, index);
            }
            setDraggedIndex(null);
        };

        return (
            <button
                onClick={(e) => {
                    e.preventDefault();
                    onClick();
                }}
                draggable
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                className={`relative w-12 h-12 rounded-lg transition-all cursor-pointer group ${isActive
                    ? 'bg-accent/20 shadow-sm ring-2 ring-accent/40'
                    : 'bg-sage-50/60 hover:bg-sage-100/60'
                    } ${draggedIndex === index ? 'opacity-50 scale-95' : ''}`}
                title={`${item.type}: ${item.title}`}
            >
                <div className={`w-full h-full rounded-md flex items-center justify-center ${getTypeColor(item.type)}`}>
                    {getMediaIcon(item.type)}
                </div>
                {/* Drag indicator */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-sage-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                    <GripVertical className="h-2 w-2 text-sage-600 m-0.5" />
                </div>
            </button>
        );
    };

    const AddItemForm = () => {
        const [title, setTitle] = useState('');
        const [description, setDescription] = useState('');
        const [videoUrl, setVideoUrl] = useState('');

        const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
            const file = e.target.files?.[0];
            if (!file || !title.trim()) return;

            await handleFileUpload(file, newItemType as 'image' | 'document', title.trim(), description.trim() || undefined);

            // Clear form
            setTitle('');
            setDescription('');
            e.target.value = '';
        };

        const handleVideoSubmit = () => {
            if (!videoUrl.trim() || !title.trim()) return;

            handleVideoAdd(videoUrl.trim(), title.trim(), description.trim() || undefined);

            // Clear form
            setTitle('');
            setDescription('');
            setVideoUrl('');
        };

        return (
            <div className="bg-sage-50/30 rounded-2xl p-6 space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-sage-700">Add New Media</h3>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowAddForm(false)}
                        className="text-sage-500 hover:text-sage-700"
                    >
                        <X className="h-4 w-4" />
                    </Button>
                </div>

                {/* Type Selection */}
                <div className="flex gap-2">
                    {(['image', 'video', 'document'] as const).map((type) => (
                        <button
                            key={type}
                            type="button"
                            onClick={() => setNewItemType(type)}
                            className={`flex items-center gap-2 px-3 py-2 rounded-xl text-sm font-medium transition-all ${newItemType === type
                                ? 'bg-accent/90 text-white'
                                : 'bg-sage-100/60 text-sage-600 hover:bg-sage-200/60'
                                }`}
                        >
                            {getMediaIcon(type)}
                            {type.charAt(0).toUpperCase() + type.slice(1)}
                        </button>
                    ))}
                </div>

                {/* Form Fields */}
                <div className="space-y-3">
                    <div>
                        <Label htmlFor="new-title">Title *</Label>
                        <Input
                            id="new-title"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder={`Enter ${newItemType} title`}
                            className="mt-1 border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                        />
                    </div>

                    <div>
                        <Label htmlFor="new-description">Description</Label>
                        <Textarea
                            id="new-description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter description (optional)"
                            className="mt-1 border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                            rows={2}
                        />
                    </div>

                    {newItemType === 'video' ? (
                        <div className="space-y-3">
                            <div>
                                <Label htmlFor="video-url">Video URL *</Label>
                                <Input
                                    id="video-url"
                                    value={videoUrl}
                                    onChange={(e) => setVideoUrl(e.target.value)}
                                    placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
                                    className="mt-1 border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                                />
                            </div>
                            <Button
                                onClick={handleVideoSubmit}
                                disabled={!videoUrl.trim() || !title.trim() || isLoading}
                                className="w-full bg-accent/90 hover:bg-accent text-white"
                            >
                                <Video className="h-4 w-4 mr-2" />
                                Add Video
                            </Button>
                        </div>
                    ) : (
                        <div>
                            <Label htmlFor="file-input">
                                {newItemType === 'image' ? 'Image File' : 'Document File'} *
                            </Label>
                            <Input
                                id="file-input"
                                type="file"
                                onChange={handleFileSelect}
                                accept={newItemType === 'image' ? 'image/*' : '.pdf,.doc,.docx,.txt'}
                                disabled={!title.trim() || isLoading}
                                className="mt-1 border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                            />
                        </div>
                    )}
                </div>
            </div>
        );
    };

    const CarouselItem = ({ item, index }: { item: MediaItem; index: number }) => {
        const [isEditing, setIsEditing] = useState(false);
        const [editTitle, setEditTitle] = useState(item.title);
        const [editDescription, setEditDescription] = useState(item.description || '');

        const handleSaveEdit = () => {
            handleUpdateItem({
                ...item,
                title: editTitle,
                description: editDescription
            });
            setIsEditing(false);
        };

        return (
            <div
                className="relative bg-sage-50/30 rounded-2xl p-6 min-h-[300px] group"
                draggable
                onDragStart={(e) => {
                    setDraggedIndex(index);
                    e.dataTransfer.effectAllowed = 'move';
                    e.dataTransfer.setData('text/plain', index.toString());
                }}
                onDragEnd={() => setDraggedIndex(null)}
                onDragOver={(e) => {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'move';
                }}
                onDrop={(e) => {
                    e.preventDefault();
                    const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
                    if (fromIndex !== index && draggedIndex !== null) {
                        handleReorderThumbnails(fromIndex, index);
                    }
                    setDraggedIndex(null);
                }}
            >
                {/* Media Preview */}
                <div className="relative mb-4">
                    {item.type === 'image' ? (
                        <div className="aspect-video bg-sage-100 rounded-xl overflow-hidden">
                            <img
                                src={item.url}
                                alt={item.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    const parent = target.parentElement;
                                    if (parent) {
                                        parent.innerHTML = `
                                            <div class="w-full h-full flex items-center justify-center">
                                                <div class="text-center">
                                                    <svg class="h-12 w-12 text-sage-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                    </svg>
                                                    <p class="text-sm text-sage-600">Image preview unavailable</p>
                                                </div>
                                            </div>
                                        `;
                                    }
                                }}
                            />
                        </div>
                    ) : item.type === 'video' ? (
                        <div className="aspect-video bg-sage-100 rounded-xl flex items-center justify-center">
                            <div className="text-center">
                                <Video className="h-12 w-12 text-sage-400 mx-auto mb-2" />
                                <p className="text-sm text-sage-600">Video: {item.metadata?.platform || 'External'}</p>
                            </div>
                        </div>
                    ) : (
                        <div className="aspect-video bg-sage-100 rounded-xl flex items-center justify-center">
                            <div className="text-center">
                                <FileText className="h-12 w-12 text-sage-400 mx-auto mb-2" />
                                <p className="text-sm text-sage-600">Document</p>
                            </div>
                        </div>
                    )}

                    {/* Overlay for adding new media */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm p-2 flex gap-1">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowAddForm(true)}
                                className="text-accent hover:text-accent/80 hover:bg-accent/10 h-8 w-8 p-0"
                                title="Add Media"
                            >
                                <Plus className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="space-y-3">
                    {isEditing ? (
                        <div className="space-y-3">
                            <Input
                                value={editTitle}
                                onChange={(e) => setEditTitle(e.target.value)}
                                className="font-medium border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                                placeholder="Title"
                            />
                            <Textarea
                                value={editDescription}
                                onChange={(e) => setEditDescription(e.target.value)}
                                className="border-sage-200/40 focus:border-accent/60 focus:ring-accent/20"
                                placeholder="Description"
                                rows={2}
                            />
                            <div className="flex gap-2">
                                <Button
                                    size="sm"
                                    onClick={handleSaveEdit}
                                    className="bg-accent/90 hover:bg-accent text-white"
                                >
                                    <CheckCircle2 className="h-3 w-3 mr-1" />
                                    Save
                                </Button>
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                        setIsEditing(false);
                                        setEditTitle(item.title);
                                        setEditDescription(item.description || '');
                                    }}
                                    className="border-sage-200/40 text-sage-600 hover:bg-sage-50"
                                >
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            <h3 className="font-medium text-sage-700 line-clamp-2">{item.title}</h3>
                            {item.description && (
                                <p className="text-sm text-sage-500 line-clamp-3">{item.description}</p>
                            )}
                        </div>
                    )}
                </div>

                {/* Actions */}
                {!isEditing && (
                    <div className="flex justify-between items-center mt-4 pt-3 border-t border-sage-200/30">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(item.url, '_blank')}
                            className="text-sage-500 hover:text-sage-700"
                        >
                            <ExternalLink className="h-4 w-4" />
                        </Button>
                        <div className="flex gap-1">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setIsEditing(true)}
                                className="text-sage-500 hover:text-sage-700"
                            >
                                <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteItem(item)}
                                className="text-red-500 hover:text-red-700"
                            >
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className={className}>
            {/* Storage Usage */}
            {storageUsage && (
                <div className="mb-6 p-4 bg-sage-50/30 rounded-2xl">
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-sage-700">Storage Usage</span>
                        <span className="text-xs text-sage-500">
                            {Math.round((storageUsage.totalBytes / (50 * 1024 * 1024)) * 100)}% used
                        </span>
                    </div>
                    <Progress
                        value={(storageUsage.totalBytes / (50 * 1024 * 1024)) * 100}
                        className="h-2"
                    />
                    <div className="flex justify-between text-xs text-sage-500 mt-1">
                        <span>{storageUsage.totalMB} MB used</span>
                        <span>50 MB total</span>
                    </div>
                </div>
            )}

            {/* Carousel Container */}
            <div className="space-y-6">
                {/* Add Item Form */}
                {showAddForm && <AddItemForm />}

                {/* Carousel */}
                {mediaItems.length > 0 ? (
                    <div className="space-y-4">
                        {/* Navigation */}
                        <div className="flex items-center justify-between">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentIndex(Math.max(0, currentIndex - 1))}
                                disabled={currentIndex === 0}
                                className="border-sage-200/40 text-sage-600 hover:bg-sage-50"
                            >
                                <ChevronLeft className="h-4 w-4" />
                            </Button>

                            <div className="text-sm text-sage-600">
                                {currentIndex + 1} of {mediaItems.length}
                            </div>

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentIndex(Math.min(mediaItems.length - 1, currentIndex + 1))}
                                disabled={currentIndex === mediaItems.length - 1}
                                className="border-sage-200/40 text-sage-600 hover:bg-sage-50"
                            >
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        </div>

                        {/* Current Item */}
                        <CarouselItem item={mediaItems[currentIndex]} index={currentIndex} />

                        {/* Thumbnail Navigation - Enhanced with drag and drop */}
                        {mediaItems.length > 1 && (
                            <div className="space-y-2">
                                <p className="text-xs text-sage-500 text-center">
                                    Click to navigate • Drag to reorder
                                </p>
                                <div className="flex gap-2 justify-center flex-wrap">
                                    {mediaItems.map((item, index) => (
                                        <ThumbnailIcon
                                            key={item.id}
                                            item={item}
                                            index={index}
                                            isActive={index === currentIndex}
                                            onClick={() => setCurrentIndex(index)}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="relative">
                        {/* Empty state with overlay add button */}
                        <div className="text-center py-12 text-sage-500 bg-sage-50/30 rounded-2xl group cursor-pointer relative" onClick={() => setShowAddForm(true)}>
                            <div className="mb-4">
                                <Upload className="h-12 w-12 mx-auto text-sage-300" />
                            </div>
                            <p className="text-lg font-medium mb-2">No media added yet</p>
                            <p className="text-sm">Click here to add images, videos, and documents</p>

                            {/* Overlay add button */}
                            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity bg-accent/5 rounded-2xl flex items-center justify-center">
                                <Button className="bg-accent/90 hover:bg-accent text-white shadow-lg">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Your First Media
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}; 