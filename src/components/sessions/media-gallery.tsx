import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
    ChevronLeft,
    ChevronRight,
    Play,
    ExternalLink,
    X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MediaItem {
    id: string;
    type: 'image' | 'video' | 'document';
    url: string;
    title: string;
    description?: string;
    upload_date?: string;
    file_size?: number;
    metadata?: {
        width?: number;
        height?: number;
        format?: string;
        platform?: string;
        duration?: string;
        pages?: number;
    };
}

interface MediaGalleryProps {
    mediaItems: MediaItem[];
    className?: string;
}

export const MediaGallery: React.FC<MediaGalleryProps> = ({
    mediaItems,
    className
}) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);

    // Don't render if no media items
    if (!mediaItems || mediaItems.length === 0) {
        return null;
    }

    const nextSlide = () => {
        setCurrentIndex((prev) => (prev + 1) % mediaItems.length);
    };

    const prevSlide = () => {
        setCurrentIndex((prev) => (prev - 1 + mediaItems.length) % mediaItems.length);
    };

    const handleMediaClick = (item: MediaItem) => {
        if (item.type === 'video' && item.metadata?.platform) {
            window.open(item.url, '_blank');
        } else if (item.type === 'document') {
            window.open(item.url, '_blank');
        } else {
            setSelectedMedia(item);
        }
    };

    const currentItem = mediaItems[currentIndex];

    return (
        <div className={cn("space-y-4", className)}>
            {/* Main Carousel */}
            <div className="relative">
                <Card className="overflow-hidden bg-white border-0 shadow-md shadow-black/5">
                    <CardContent className="p-0">
                        <div className="relative aspect-video bg-gradient-to-br from-gray-50 to-gray-100">
                            {currentItem.type === 'image' && (
                                <img
                                    src={currentItem.url}
                                    alt={currentItem.title}
                                    className="w-full h-full object-cover cursor-pointer"
                                    onClick={() => handleMediaClick(currentItem)}
                                />
                            )}

                            {currentItem.type === 'video' && (
                                <div
                                    className="w-full h-full flex items-center justify-center cursor-pointer bg-gradient-to-br from-blue-50 to-indigo-50"
                                    onClick={() => handleMediaClick(currentItem)}
                                >
                                    <div className="text-center">
                                        <Play className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                                        <h3 className="text-lg font-semibold text-gray-800">{currentItem.title}</h3>
                                    </div>
                                </div>
                            )}

                            {currentItem.type === 'document' && (
                                <div
                                    className="w-full h-full flex items-center justify-center cursor-pointer bg-gradient-to-br from-gray-50 to-slate-50"
                                    onClick={() => handleMediaClick(currentItem)}
                                >
                                    <div className="text-center">
                                        <ExternalLink className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                                        <h3 className="text-lg font-semibold text-gray-800">{currentItem.title}</h3>
                                    </div>
                                </div>
                            )}

                            {/* Navigation Arrows */}
                            {mediaItems.length > 1 && (
                                <>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white shadow-md"
                                        onClick={prevSlide}
                                    >
                                        <ChevronLeft className="w-5 h-5" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white shadow-md"
                                        onClick={nextSlide}
                                    >
                                        <ChevronRight className="w-5 h-5" />
                                    </Button>
                                </>
                            )}
                        </div>

                        {/* Media Title */}
                        <div className="p-4">
                            <h3 className="font-semibold text-foreground">{currentItem.title}</h3>
                            {currentItem.description && (
                                <p className="text-sm text-muted-foreground mt-1">{currentItem.description}</p>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Dots Indicator */}
                {mediaItems.length > 1 && (
                    <div className="flex justify-center space-x-2 mt-4">
                        {mediaItems.map((_, index) => (
                            <button
                                key={index}
                                className={cn(
                                    "w-2 h-2 rounded-full transition-all duration-200",
                                    index === currentIndex
                                        ? "bg-primary w-6"
                                        : "bg-gray-300 hover:bg-gray-400"
                                )}
                                onClick={() => setCurrentIndex(index)}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* Image Modal */}
            {selectedMedia && selectedMedia.type === 'image' && (
                <div
                    className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
                    onClick={() => setSelectedMedia(null)}
                >
                    <div className="relative max-w-4xl max-h-full">
                        <img
                            src={selectedMedia.url}
                            alt={selectedMedia.title}
                            className="max-w-full max-h-full object-contain"
                        />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white"
                            onClick={() => setSelectedMedia(null)}
                        >
                            <X className="w-5 h-5" />
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}; 