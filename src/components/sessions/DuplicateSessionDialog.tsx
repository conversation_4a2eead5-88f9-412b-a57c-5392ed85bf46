import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useSession } from '@/contexts/SessionContext';
import { Session } from '@shared/schema';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { apiRequest } from '@/lib/apiClient';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/features/auth/AuthContext';
import { formatDate } from '@/lib/utils';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Copy, Loader2 } from 'lucide-react';
import { format, addDays } from 'date-fns';

interface DuplicateSessionDialogProps {
  session: Session;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm';
  className?: string;
  asIcon?: boolean;
}

// Schema for the form
const formSchema = z.object({
  dates: z.array(z.date()).min(1, 'Please select at least one date'),
  adjustedPrice: z.coerce.number().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function DuplicateSessionDialog({
  session,
  variant = 'outline',
  size = 'default',
  className = '',
  asIcon = false,
}: DuplicateSessionDialogProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Create a form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dates: [addDays(new Date(), 7)], // Default to a week from now
      adjustedPrice: session.price,
    },
  });

  // Function to create a duplicate session
  const createDuplicates = async (data: FormValues) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to duplicate sessions.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Create promises for each date to duplicate the session
      const duplicatePromises = data.dates.map(async date => {
        const sessionData = {
          title: session.title,
          description: session.description,
          type: session.session_type,
          price: data.adjustedPrice !== undefined ? data.adjustedPrice : session.price,
          date: date.toISOString(),
          duration: session.duration,
          maxParticipants: session.max_participants,
          skillLevel: session.skill_level,
          format: session.format,
        };

        const { data: result, response } = await apiRequest('/api/sessions', {
          method: 'POST',
          body: JSON.stringify(sessionData),
          headers: {
            'Content-Type': 'application/json',
          },
        });

        return result;
      });

      // Wait for all duplications to complete
      const results = await Promise.all(duplicatePromises);

      // Invalidate sessions cache to refresh the list
      queryClient.invalidateQueries({ queryKey: ['/api/sessions'] });

      setOpen(false);
      toast({
        title: 'Sessions duplicated',
        description: `Successfully created ${data.dates.length} duplicate sessions`,
        variant: 'default',
      });

      // Clear form and reset
      form.reset();
    } catch (error) {
      toast({
        title: 'Error duplicating sessions',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {asIcon ? (
          <Button variant="ghost" size="icon" className={className} title="Duplicate session">
            <Copy className="h-4 w-4" />
          </Button>
        ) : (
          <Button variant={variant} size={size} className={className}>
            <Copy className="h-4 w-4 mr-2" />
            Duplicate Session
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Duplicate Session</DialogTitle>
          <DialogDescription>
            Create copies of "{session.title}" on multiple dates.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(createDuplicates)} className="space-y-6 py-4">
            <FormField
              control={form.control}
              name="dates"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Select Dates</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className="min-h-10 w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value.length > 0 ? (
                            field.value.length === 1 ? (
                              format(field.value[0], 'PPP')
                            ) : (
                              `${field.value.length} dates selected`
                            )
                          ) : (
                            <span>Pick dates</span>
                          )}
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="multiple"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={date => date < new Date() || date < new Date('1900-01-01')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    Select all dates you want to create this session on.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="adjustedPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Session Price</FormLabel>
                  <FormControl>
                    <Input type="number" min={0} step={1} placeholder="Price" {...field} />
                  </FormControl>
                  <FormDescription>
                    Price for each duplicated session (original: ${session.price})
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Duplicates'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}