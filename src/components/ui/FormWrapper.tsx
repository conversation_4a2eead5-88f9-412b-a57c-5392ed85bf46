import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingOverlay } from './LoadingSpinner';
import { ErrorDisplay } from './ErrorDisplay';

interface FormWrapperProps {
  children: React.ReactNode;
  isLoading?: boolean;
  isSubmitting?: boolean;
  error?: Error | null;
  onRetry?: () => void;
  className?: string;
  loadingText?: string;
  showError?: boolean;
}

/**
 * Form wrapper component with loading and error handling
 */
export const FormWrapper: React.FC<FormWrapperProps> = ({
  children,
  isLoading = false,
  isSubmitting = false,
  error = null,
  onRetry,
  className,
  loadingText = 'Loading...',
  showError = true,
}) => {
  return (
    <div className={cn('relative', className)}>
      <LoadingOverlay
        isLoading={isLoading || isSubmitting}
        text={isSubmitting ? 'Submitting...' : loadingText}
      >
        {error && showError ? (
          <div className="mb-4">
            <ErrorDisplay error={error} resetError={onRetry} variant="card" />
          </div>
        ) : null}
        {children}
      </LoadingOverlay>
    </div>
  );
};

/**
 * Form section component
 */
export const FormSection: React.FC<{
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}> = ({ title, description, children, className }) => {
  return (
    <div className={cn('space-y-4', className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
      )}
      <div className="space-y-4">{children}</div>
    </div>
  );
};

/**
 * Form divider component
 */
export const FormDivider: React.FC<{ className?: string }> = ({ className }) => {
  return <div className={cn('h-px bg-border my-6', className)} />;
};