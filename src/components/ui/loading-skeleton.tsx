import React from 'react';

interface LoadingSkeletonProps {
  count?: number;
}

export function LoadingSkeleton({ count = 3 }: LoadingSkeletonProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array(count)
        .fill(0)
        .map((_, i) => (
          <div
            key={i}
            className="bg-white border border-gray-100 rounded-lg overflow-hidden shadow-sm"
          >
            <div className="h-40 bg-gray-200 animate-pulse"></div>
            <div className="p-4 space-y-3">
              <div className="h-5 bg-gray-200 rounded animate-pulse w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
              <div className="flex justify-between pt-2">
                <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4"></div>
                <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4"></div>
              </div>
            </div>
          </div>
        ))}
    </div>
  );
}
