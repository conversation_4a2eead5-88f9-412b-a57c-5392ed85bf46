import React, { useState } from 'react';
import { Check, ChevronDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
} from '@/components/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { CURRENCY_OPTIONS, getCurrencyInfo } from '@/lib/constants';

interface CurrencySelectorProps {
    value?: string;
    onValueChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
}

export function CurrencySelector({
    value,
    onValueChange,
    placeholder = "Select currency...",
    className,
    disabled = false,
}: CurrencySelectorProps) {
    const [open, setOpen] = useState(false);
    const selectedCurrency = value ? getCurrencyInfo(value) : null;

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(
                        "w-full justify-between",
                        !value && "text-muted-foreground",
                        className
                    )}
                    disabled={disabled}
                >
                    {selectedCurrency ? (
                        <div className="flex items-center gap-2">
                            <span className="font-medium">{selectedCurrency.symbol}</span>
                            <span>{selectedCurrency.code}</span>
                            <span className="text-muted-foreground">- {selectedCurrency.name}</span>
                        </div>
                    ) : (
                        placeholder
                    )}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[400px] p-0">
                <Command>
                    <CommandInput placeholder="Search currencies..." className="h-9" />
                    <CommandEmpty>No currency found.</CommandEmpty>
                    <CommandGroup className="max-h-[300px] overflow-auto">
                        {CURRENCY_OPTIONS.map((currency) => (
                            <CommandItem
                                key={currency.code}
                                value={`${currency.code} ${currency.name}`}
                                onSelect={() => {
                                    onValueChange(currency.code);
                                    setOpen(false);
                                }}
                                className="flex items-center gap-3 py-2"
                            >
                                <div className="flex items-center gap-3 flex-1">
                                    <span className="font-medium text-lg w-8">{currency.symbol}</span>
                                    <div className="flex flex-col">
                                        <span className="font-medium">{currency.code}</span>
                                        <span className="text-sm text-muted-foreground">{currency.name}</span>
                                    </div>
                                </div>
                                <Check
                                    className={cn(
                                        "h-4 w-4",
                                        value === currency.code ? "opacity-100" : "opacity-0"
                                    )}
                                />
                            </CommandItem>
                        ))}
                    </CommandGroup>
                </Command>
            </PopoverContent>
        </Popover>
    );
} 