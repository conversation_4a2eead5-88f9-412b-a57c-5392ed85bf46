/**
 * Direct Supabase Uploader
 *
 * A minimal, direct approach to uploading files to Supabase storage
 * with no dependencies on other components or utilities.
 */
import { useState, useRef, ChangeEvent } from 'react';
import { supabase } from '@/lib/supabase-singleton';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

// Define types for Supabase response
type SupabaseUploadResponse = {
  data: {
    path: string;
  } | null;
  error: Error | null;
};

interface DirectSupabaseUploaderProps {
  type: 'avatar' | 'cover';
  onSuccess: (url: string) => void;
  onError?: (error: Error) => void;
  className?: string;
  buttonText?: string;
}

export function DirectSupabaseUploader({
  type,
  onSuccess,
  onError,
  className = '',
  buttonText = 'Upload Image'
}: DirectSupabaseUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle button click to trigger file input
  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Helper function to update user profile with new image URL
  const updateUserProfile = async (userId: string, type: 'avatar' | 'cover', url: string) => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert(
          { id: userId, [type === 'avatar' ? 'avatar_url' : 'cover_url']: url },
          { onConflict: 'id' }
        );

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error(`[DirectSupabaseUploader] Error updating ${type} URL:`, error);
      throw error;
    }
  };

  // Helper function to handle upload attempts
  const attemptUpload = async (
    userId: string,
    file: File,
    path: string,
    type: 'avatar' | 'cover',
    onSuccess: (url: string) => void,
    onError?: (error: Error) => void
  ) => {
    const uploadStartTime = Date.now();

    // Create a promise that rejects after a timeout
    const timeoutPromise = new Promise<{ data: { path: string } | null; error: Error | null }>((_, reject) => {
      setTimeout(() => reject(new Error('Upload timed out after 30 seconds')), 30000);
    });

    try {
      // Create the upload promise
      const uploadPromise = supabase.storage
        .from('profiles')
        .upload(path, file, {
          cacheControl: '3600',
          upsert: true
        })
        .then(({ data, error }) => ({
          data: data ? { path: data.path } : null,
          error
        }));

      // Race the upload against the timeout
      const { data: uploadData, error: uploadError } = await Promise.race([
        uploadPromise,
        timeoutPromise
      ]) as { data: { path: string } | null; error: Error | null };

      const uploadDuration = Date.now() - uploadStartTime;
      console.log(`[DirectSupabaseUploader] Upload to ${path} took ${uploadDuration}ms`);

      if (uploadError) {
        throw uploadError;
      }

      if (!uploadData?.path) {
        throw new Error('No data returned from upload');
      }

      // Get the public URL for the uploaded file
      const { data: publicUrlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(path) as unknown as { data: { publicUrl: string } };

      if (!publicUrlData?.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      // Update the user profile with the new URL
      await updateUserProfile(userId, type, publicUrlData.publicUrl);
      onSuccess(publicUrlData.publicUrl);

    } catch (error) {
      console.error(`[DirectSupabaseUploader] Upload to ${path} failed:`, error);
      throw error; // Re-throw to allow for fallback attempts
    }
  };

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setProgress(0);
      setError(null);

      console.log(`[DirectSupabaseUploader] Starting ${type} upload for file:`, file.name);

      // Get the current authenticated user
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError) {
        throw new Error(`Authentication error: ${userError.message}`);
      }

      if (!userData?.user?.id) {
        throw new Error('No authenticated user found');
      }

      const userId = userData.user.id;
      console.log(`[DirectSupabaseUploader] Authenticated user ID: ${userId}`);

      // Create a unique filename
      const timestamp = Date.now();
      const fileExt = file.name.split('.').pop() || 'jpg';
      const fileName = `${type}-${timestamp}.${fileExt}`;

      // Create a path structure that matches the RLS policy requirements
      const filePath = `${userId}/${type}s/${fileName}`;
      console.log(`[DirectSupabaseUploader] Upload path: ${filePath}`);

      // Upload the file with detailed logging
      console.log(`[DirectSupabaseUploader] Starting upload to Supabase storage: ${filePath}`);
      console.log(`[DirectSupabaseUploader] File details: size=${file.size}, type=${file.type}`);

      try {
        // Try the first upload attempt
        await attemptUpload(userId, file, filePath, type, onSuccess, onError);
      } catch (error) {
        console.error('[DirectSupabaseUploader] First upload attempt failed, trying fallback:', error);

        // Try a simpler path that still complies with RLS policy
        const simplePath = `${userId}/${type}-${timestamp}.${fileExt}`;
        console.log(`[DirectSupabaseUploader] Trying simpler path: ${simplePath}`);

        try {
          await attemptUpload(userId, file, simplePath, type, onSuccess, onError);
        } catch (fallbackError) {
          console.error('[DirectSupabaseUploader] Fallback upload failed, trying root path:', fallbackError);

          // As a last resort, try a direct root-level upload
          const rootPath = `direct-${type}-${timestamp}.${fileExt}`;
          console.log(`[DirectSupabaseUploader] Attempting last-resort direct upload to: ${rootPath}`);

          try {
            await attemptUpload(userId, file, rootPath, type, onSuccess, onError);
          } catch (rootError) {
            console.error('[DirectSupabaseUploader] All upload attempts failed:', rootError);
            setError('Failed to upload file. Please try again.');
            if (onError) {
              onError(rootError instanceof Error ? rootError : new Error(String(rootError)));
            }
          }
        }
      }
    } catch (error) {
      console.error('[DirectSupabaseUploader] Error during file upload:', error);
      setError('An error occurred while uploading the file.');
      if (onError) {
        onError(error instanceof Error ? error : new Error(String(error)));
      }
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        disabled={isUploading}
      />
      <Button
        type="button"
        onClick={handleButtonClick}
        disabled={isUploading}
        className="relative overflow-hidden"
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Uploading...
          </>
        ) : (
          buttonText
        )}
      </Button>
      {error && (
        <p className="mt-2 text-sm text-red-600">
          {error}
        </p>
      )}
      {progress > 0 && progress < 100 && (
        <div className="mt-2 w-full max-w-xs">
          <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
            <div
              className="h-full bg-blue-600 transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="mt-1 text-center text-xs text-gray-600">
            Uploading... {Math.round(progress)}%
          </p>
        </div>
      )}
    </div>
  );
}

/**
 * Update the user profile with the new image URL
 */
async function updateUserProfile(userId: string, type: 'avatar' | 'cover', url: string): Promise<void> {
  try {
    console.log(`[DirectSupabaseUploader] Updating user profile for ${type}:`, url);

    // Create the update data
    const updateData: Record<string, any> = {
      updated_at: new Date().toISOString()
    };

    if (type === 'avatar') {
      updateData.avatar = url;
    } else {
      updateData.cover_photo = url;
    }

    // Update the user profile
    const { data: publicData, error: publicError } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: userId,
        ...updateData
      }, { onConflict: 'user_id' });

    if (publicError) {
      throw publicError;
    }

    // Update localStorage cache regardless of which table was updated
    try {
      const cacheKey = `user_profile_${userId}`;
      const cachedData = localStorage.getItem(cacheKey);

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        if (type === 'avatar') {
          parsedData.avatar = url;
        } else {
          parsedData.coverPhoto = url;
        }
        localStorage.setItem(cacheKey, JSON.stringify(parsedData));
      }

      // Also update generic cache keys
      if (type === 'avatar') {
        localStorage.setItem('lastUploadedProfileImage', url);
      } else {
        localStorage.setItem('lastUploadedCoverPhoto', url);
      }
    } catch (cacheError) {
      console.error('[DirectSupabaseUploader] Error updating localStorage:', cacheError);
    }
  } catch (error) {
    console.error('[DirectSupabaseUploader] Error in updateUserProfile:', error);
  }
}
