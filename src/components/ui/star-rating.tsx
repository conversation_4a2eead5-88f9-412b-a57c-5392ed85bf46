import { useState } from 'react';
import { Star, StarHalf } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  rating?: number;
  value?: number;
  onChange?: (value: number) => void;
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  className?: string;
}

export function StarRating({
  rating,
  value,
  onChange,
  size = 'sm',
  interactive = false,
  className = '',
}: StarRatingProps) {
  const [hoverValue, setHoverValue] = useState<number>(0);

  // Use either value (for controlled component) or rating (for display only)
  const displayValue = hoverValue > 0 ? hoverValue : value !== undefined ? value : rating || 0;

  // Calculate full stars and half stars
  const fullStars = Math.floor(displayValue);
  const hasHalfStar = displayValue % 1 >= 0.5;

  // Get the appropriate size
  const getStarSize = () => {
    switch (size) {
      case 'sm':
        return 'h-2.5 w-2.5';
      case 'md':
        return 'h-4 w-4';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-3 w-3';
    }
  };

  const starSize = getStarSize();

  const handleClick = (index: number) => {
    if (interactive && onChange) {
      onChange(index + 1);
    }
  };

  const handleMouseEnter = (index: number) => {
    if (interactive) {
      setHoverValue(index + 1);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverValue(0);
    }
  };

  return (
    <div className={cn('flex items-center text-yellow-400', className)}>
      {[...Array(5)].map((_, i) => {
        const starProps = {
          className: cn(
            starSize,
            i < fullStars
              ? 'fill-current'
              : i === fullStars && hasHalfStar
                ? 'fill-current'
                : 'text-gray-300'
          ),
          onClick: () => handleClick(i),
          onMouseEnter: () => handleMouseEnter(i),
          onMouseLeave: handleMouseLeave,
          style: interactive ? { cursor: 'pointer' } : undefined,
        };

        if (i < fullStars) {
          // Full star
          return <Star key={i} {...starProps} />;
        } else if (i === fullStars && hasHalfStar) {
          // Half star
          return <StarHalf key={i} {...starProps} />;
        } else {
          // Empty star
          return <Star key={i} {...starProps} />;
        }
      })}
    </div>
  );
}

// Default export removed in favor of named export
