import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Check, Loader2, X, MoveVertical } from 'lucide-react';

interface DraggableCoverPhotoProps {
  imageUrl: string;
  initialPosition: string;
  onSave: (position: string) => void;
  onCancel: () => void;
}

export function DraggableCoverPhoto({
  imageUrl,
  initialPosition = '50%',
  onSave,
  onCancel,
}: DraggableCoverPhotoProps) {
  const [position, setPosition] = useState(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef<number>(0);
  const startPositionRef = useRef<number>(0);

  // Parse initial position to a number (remove % if present)
  useEffect(() => {
    setPosition(initialPosition);
  }, [initialPosition]);

  // Start dragging
  const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    setIsDragging(true);
    startYRef.current = e.clientY;

    // Parse the current position as a percentage
    const currentPos = parseFloat(position);
    startPositionRef.current = isNaN(currentPos) ? 50 : currentPos;
  };

  // Handle touch start for mobile devices
  const handleTouchStart = (e: React.TouchEvent<HTMLImageElement>) => {
    setIsDragging(true);
    startYRef.current = e.touches[0].clientY;

    // Parse the current position as a percentage
    const currentPos = parseFloat(position);
    startPositionRef.current = isNaN(currentPos) ? 50 : currentPos;
  };

  // Handle dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerHeight = containerRef.current.clientHeight;
    const deltaY = startYRef.current - e.clientY;

    // Convert delta to percentage of container height
    const deltaPercent = (deltaY / containerHeight) * 100;

    // Calculate new position (constrain between 0% and 100%)
    let newPosition = startPositionRef.current + deltaPercent;
    newPosition = Math.max(0, Math.min(100, newPosition));

    setPosition(`${newPosition}%`);
  };

  // Handle touch move for mobile devices
  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerHeight = containerRef.current.clientHeight;
    const deltaY = startYRef.current - e.touches[0].clientY;

    // Convert delta to percentage of container height
    const deltaPercent = (deltaY / containerHeight) * 100;

    // Calculate new position (constrain between 0% and 100%)
    let newPosition = startPositionRef.current + deltaPercent;
    newPosition = Math.max(0, Math.min(100, newPosition));

    setPosition(`${newPosition}%`);
  };

  // Stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle save
  const handleSave = () => {
    setIsSaving(true);
    onSave(position);
  };

  // Add and remove event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div
      ref={containerRef}
      className="relative h-full w-full overflow-hidden"
      style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
    >
      {/* Image with dragable behavior */}
      <img
        src={imageUrl}
        alt="Cover photo"
        className="w-full h-auto min-h-full object-cover"
        style={{
          objectPosition: `center ${position}`,
          transform: isDragging ? 'scale(1.02)' : 'scale(1)',
          transition: isDragging ? 'none' : 'transform 0.2s ease-in-out',
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        draggable="false"
        onError={e => {
          const target = e.target as HTMLImageElement;
          target.onerror = null; // Prevent infinite error loop

          // Use a fallback image
          target.src = 'https://placehold.co/1200x400?text=Cover+Photo';

          console.log('Using fallback cover image in DraggableCoverPhoto');
        }}
      />

      {/* Instructions overlay */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="bg-black/70 px-4 py-3 rounded-lg text-white flex items-center">
          <MoveVertical className="h-5 w-5 mr-2" />
          <span>Drag to reposition cover photo</span>
        </div>
      </div>

      {/* Control buttons */}
      <div className="absolute bottom-4 right-4 flex gap-2">
        <Button
          variant="secondary"
          size="sm"
          onClick={onCancel}
          className="bg-white/90 hover:bg-white text-gray-800"
        >
          <X className="h-4 w-4 mr-1" />
          Cancel
        </Button>
        <Button
          variant="default"
          size="sm"
          onClick={handleSave}
          disabled={isSaving}
          className="bg-primary/90 hover:bg-primary text-white"
        >
          {isSaving ? (
            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
          ) : (
            <Check className="h-4 w-4 mr-1" />
          )}
          Save Position
        </Button>
      </div>
    </div>
  );
}
