import React from 'react';
import TimezoneSelect, { type ITimezone } from 'react-timezone-select';
import { cn, normalizeTimezone, formatTimezoneForDisplay } from '@/lib/utils';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

interface TimezoneSelectProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const CustomTimezoneSelect: React.FC<TimezoneSelectProps> = ({ value, onChange, className }) => {
  // Handle the timezone object from the library
  const handleChange = (selectedTimezone: ITimezone) => {
    // The library returns an object, but we want to store just the string value
    if (typeof selectedTimezone === 'object' && selectedTimezone.value) {
      console.log('[TimezoneSelect] Selected timezone object:', selectedTimezone);
      console.log('[TimezoneSelect] Selected timezone value:', selectedTimezone.value);
      console.log('[TimezoneSelect] Selected timezone label:', selectedTimezone.label);

      // Normalize the timezone to ensure we always store IANA format
      let timezoneValue = selectedTimezone.value;

      // Special case for Dawson, Yukon
      if (
        selectedTimezone.label &&
        (selectedTimezone.label.includes('Dawson') || selectedTimezone.label.includes('Yukon'))
      ) {
        console.log('[TimezoneSelect] Found Dawson/Yukon in label, mapping to America/Dawson');
        // Return the IANA timezone directly and log it for debugging
        timezoneValue = 'America/Dawson';
        console.log('[TimezoneSelect] Set timezone value to America/Dawson');
      }
      // If it's not in IANA format (doesn't contain a slash), normalize it
      else if (!timezoneValue.includes('/')) {
        // For GMT format like "(GMT+12:00) Kamchatka, Marshall Islands"
        if (
          selectedTimezone.label &&
          (selectedTimezone.label.includes('GMT+') || selectedTimezone.label.includes('GMT-'))
        ) {
          timezoneValue = normalizeTimezone(selectedTimezone.label);
        } else {
          timezoneValue = normalizeTimezone(timezoneValue);
        }
      }

      console.log('[TimezoneSelect] Normalized timezone value:', timezoneValue);
      console.log('[TimezoneSelect] Final timezone value being passed to onChange:', timezoneValue);

      onChange(timezoneValue);
    }
  };

  // Convert our string value to the format expected by the library
  // For IANA timezones like "America/Dawson", we need to create a proper object
  // that the timezone select component can understand
  let selectedTimezone: ITimezone | null = null;

  if (value) {
    // Special case for America/Dawson to ensure it displays correctly
    if (value === 'America/Dawson') {
      console.log('[TimezoneSelect] Found America/Dawson, creating custom timezone object');
      selectedTimezone = {
        value: 'America/Dawson',
        label: '(GMT-7:00) Dawson, Yukon',
      };
    } else {
      // For other timezones, use the formatted display
      selectedTimezone = {
        value,
        label: formatTimezoneForDisplay(value),
      };
    }
  }

  return (
    <div className={cn('relative bg-white', className)}>
      {/* Removed tooltip explanation */}
      <TimezoneSelect
        value={selectedTimezone as any}
        onChange={handleChange}
        styles={{
          container: provided => ({
            ...provided,
            width: '100%',
          }),
          control: provided => ({
            ...provided,
            backgroundColor: 'white',
            borderColor: 'hsl(var(--input))',
            borderRadius: 'var(--radius)',
            minHeight: '40px',
            boxShadow: 'none',
            '&:hover': {
              borderColor: 'hsl(var(--input))',
            },
          }),
          valueContainer: provided => ({
            ...provided,
            padding: '0 12px',
          }),
          input: provided => ({
            ...provided,
            margin: '0',
            padding: '0',
          }),
          indicatorSeparator: () => ({
            display: 'none',
          }),
          dropdownIndicator: provided => ({
            ...provided,
            color: 'hsl(var(--foreground))',
          }),
          menu: provided => ({
            ...provided,
            backgroundColor: 'white',
            borderRadius: 'var(--radius)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            zIndex: 50,
          }),
          option: (provided, state) => ({
            ...provided,
            backgroundColor: state.isSelected
              ? 'hsl(var(--primary))'
              : state.isFocused
                ? 'hsl(var(--accent))'
                : 'white',
            color: state.isSelected ? 'white' : 'hsl(var(--foreground))',
            cursor: 'pointer',
            '&:active': {
              backgroundColor: state.isSelected ? 'hsl(var(--primary))' : 'hsl(var(--accent))',
            },
          }),
        }}
      />
    </div>
  );
};

export { CustomTimezoneSelect };
