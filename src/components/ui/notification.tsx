import { useState, useEffect } from 'react';
import { Bell, X, MessageCircle, User, Calendar } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getInitials } from '@/lib/utils';

export type NotificationType = 'message' | 'booking' | 'reminder' | 'system';

export interface NotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  sender?: {
    id: number;
    name: string;
    avatar?: string;
  };
  actionUrl?: string;
}

interface NotificationProps {
  notifications?: NotificationItem[];
  maxItems?: number;
}

export function NotificationBell({ notifications = [], maxItems = 5 }: NotificationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState<NotificationItem[]>(notifications);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    setItems(notifications);
    setUnreadCount(notifications.filter(n => !n.read).length);
  }, [notifications]);

  const handleMarkAllAsRead = () => {
    setItems(prev => prev.map(item => ({ ...item, read: true })));
    setUnreadCount(0);
  };

  const handleMarkAsRead = (id: string) => {
    setItems(prev => prev.map(item => (item.id === id ? { ...item, read: true } : item)));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleDismiss = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setItems(prev => prev.filter(item => item.id !== id));
    setUnreadCount(prev => {
      const item = items.find(n => n.id === id);
      return item && !item.read ? prev - 1 : prev;
    });
  };

  const handleClear = () => {
    setItems([]);
    setUnreadCount(0);
  };

  // Notification icon based on type
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'message':
        return <MessageCircle className="h-4 w-4 text-[#E07A5F]" />;
      case 'booking':
        return <Calendar className="h-4 w-4 text-green-600" />;
      case 'reminder':
        return <Bell className="h-4 w-4 text-amber-600" />;
      case 'system':
        return <User className="h-4 w-4 text-blue-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  // If no sample notifications provided, create some demos
  useEffect(() => {
    if (items.length === 0) {
      const demoNotifications: NotificationItem[] = [
        {
          id: '1',
          type: 'message',
          title: 'New message from Emily',
          message: 'Hi! I had a question about the yoga session tomorrow...',
          timestamp: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
          read: false,
          sender: {
            id: 1,
            name: 'Emily Johnson',
            avatar:
              'https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=200&h=200&auto=format&fit=crop',
          },
          actionUrl: '/messages/1',
        },
        {
          id: '2',
          type: 'booking',
          title: 'Session Booked',
          message: 'Your session "Music Theory Workshop" has been booked by Alex',
          timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
          read: false,
          actionUrl: '/my-sessions',
        },
        {
          id: '3',
          type: 'reminder',
          title: 'Session Starting Soon',
          message: 'Your session "Morning Yoga Flow" starts in 30 minutes',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
          read: true,
          actionUrl: '/session/1',
        },
      ];

      setItems(demoNotifications);
      setUnreadCount(demoNotifications.filter(n => !n.read).length);
    }
  }, []);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-[1.2rem] w-[1.2rem]" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-[#E07A5F] text-white text-xs font-semibold rounded-full"
              variant="default"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <div className="p-3 border-b border-gray-100 flex items-center justify-between">
            <h3 className="font-medium">Notifications</h3>
            <div className="flex gap-1">
              {unreadCount > 0 && (
                <Button variant="ghost" size="sm" onClick={handleMarkAllAsRead}>
                  Mark all as read
                </Button>
              )}
              {items.length > 0 && (
                <Button variant="ghost" size="sm" onClick={handleClear}>
                  Clear all
                </Button>
              )}
            </div>
          </div>

          <CardContent className="p-0 max-h-[400px] overflow-y-auto">
            {items.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {items.slice(0, maxItems).map(notification => (
                  <div
                    key={notification.id}
                    className={`p-3 hover:bg-gray-50 transition-colors flex gap-3 ${
                      !notification.read ? 'bg-gray-50/80' : ''
                    }`}
                  >
                    <div className="flex-shrink-0">
                      {notification.sender ? (
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={notification.sender.avatar}
                            alt={notification.sender.name}
                          />
                          <AvatarFallback>{getInitials(notification.sender.name)}</AvatarFallback>
                        </Avatar>
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-primary-50 flex items-center justify-center">
                          {getNotificationIcon(notification.type)}
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <p className="text-sm font-medium text-gray-900">{notification.title}</p>
                        <div className="flex gap-1">
                          <div className="flex items-center gap-1">
                            {!notification.read && (
                              <Button
                                size="icon"
                                variant="ghost"
                                className="h-5 w-5"
                                onClick={() => handleMarkAsRead(notification.id)}
                                title="Mark as read"
                              >
                                <div className="w-2 h-2 rounded-full bg-[#E07A5F]" />
                              </Button>
                            )}
                            <Button
                              size="icon"
                              variant="ghost"
                              className="h-5 w-5 text-gray-400 hover:text-[#E07A5F]"
                              onClick={e => handleDismiss(notification.id, e)}
                              title="Dismiss notification"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                          </div>
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                        {notification.message}
                      </p>
                      {notification.actionUrl && (
                        <Button
                          variant="link"
                          size="sm"
                          className="h-auto p-0 text-xs mt-1"
                          onClick={() => {
                            window.location.href = notification.actionUrl!;
                            setIsOpen(false);
                          }}
                        >
                          View details
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-8 text-center">
                <p className="text-gray-500 text-sm">No notifications</p>
              </div>
            )}
          </CardContent>

          {items.length > maxItems && (
            <CardFooter className="border-t border-gray-100 p-2">
              <Button variant="link" className="w-full">
                View all notifications
              </Button>
            </CardFooter>
          )}
        </Card>
      </PopoverContent>
    </Popover>
  );
}

// Helper function to format timestamps
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffDay > 0) {
    return diffDay === 1 ? '1 day ago' : `${diffDay} days ago`;
  }
  if (diffHour > 0) {
    return diffHour === 1 ? '1 hour ago' : `${diffHour} hours ago`;
  }
  if (diffMin > 0) {
    return diffMin === 1 ? '1 minute ago' : `${diffMin} minutes ago`;
  }
  return 'Just now';
}
