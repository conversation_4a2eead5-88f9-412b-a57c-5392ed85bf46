import { useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Camera, ImagePlus, Upload, XCircle } from 'lucide-react';

interface SocialMediaImagePickerProps {
  onSelect: (url: string) => void;
}

function SocialMediaImagePicker({ onSelect }: SocialMediaImagePickerProps) {
  const socialMediaImages = [
    {
      name: 'Professional 1',
      url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Professional 2',
      url: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Nature 1',
      url: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Nature 2',
      url: 'https://images.unsplash.com/photo-1501854140801-50d01698950b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Urban 1',
      url: 'https://images.unsplash.com/photo-1477959858617-67f85cf4f1df?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Urban 2',
      url: 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Abstract 1',
      url: 'https://images.unsplash.com/photo-1541701494587-cb58502866ab?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
    {
      name: 'Abstract 2',
      url: 'https://images.unsplash.com/photo-1550859492-d5da9d8e45f3?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
    },
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
      {socialMediaImages.map((image, index) => (
        <div
          key={index}
          className="relative overflow-hidden rounded-md cursor-pointer group"
          onClick={() => onSelect(image.url)}
        >
          <img src={image.url} alt={image.name} className="w-full h-20 object-cover" />
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Button variant="ghost" size="sm" className="text-white p-1">
              Select
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}

interface CameraUploaderProps {
  onCapture: (imageDataUrl: string) => void;
}

function CameraUploader({ onCapture }: CameraUploaderProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreamActive, setIsStreamActive] = useState(false);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreamActive(true);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsStreamActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const context = canvasRef.current.getContext('2d');
      if (!context) return;

      // Set canvas dimensions to match video
      const videoWidth = videoRef.current.videoWidth;
      const videoHeight = videoRef.current.videoHeight;

      // Calculate new dimensions (resize if too large)
      let width = videoWidth;
      let height = videoHeight;
      const maxWidth = 1200;

      if (width > maxWidth) {
        height = Math.round((height * maxWidth) / width);
        width = maxWidth;
      }

      canvasRef.current.width = width;
      canvasRef.current.height = height;

      // Draw the video frame to the canvas with resizing
      context.drawImage(videoRef.current, 0, 0, width, height);

      // Convert canvas to data URL with quality reduction
      const imageDataUrl = canvasRef.current.toDataURL('image/jpeg', 0.8);

      // Pass the image data to the parent component
      onCapture(imageDataUrl);

      // Stop the camera
      stopCamera();
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative aspect-video rounded-lg overflow-hidden bg-gray-100 border">
        <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />
        <canvas ref={canvasRef} className="hidden" />

        {!isStreamActive && (
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <Camera className="w-12 h-12 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">Camera feed will appear here</p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        {!isStreamActive ? (
          <Button type="button" onClick={startCamera} variant="outline">
            <Camera className="w-4 h-4 mr-2" />
            Start Camera
          </Button>
        ) : (
          <>
            <Button type="button" onClick={stopCamera} variant="outline">
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="button" onClick={captureImage}>
              <Camera className="w-4 h-4 mr-2" />
              Take Photo
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

interface ImageUploaderProps {
  initialImage?: string;
  onImageSelected: (imageUrl: string) => void;
  aspectRatio?: 'square' | 'cover';
  title?: string;
}

export function ImageUploader({
  initialImage,
  onImageSelected,
  aspectRatio = 'square',
  title = 'Upload Image',
}: ImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>(initialImage);
  const [urlInput, setUrlInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Resize the image before setting it
      resizeImage(file, 1200, 0.7)
        .then(resizedDataUrl => {
          setPreviewImage(resizedDataUrl);
          onImageSelected(resizedDataUrl);
          setIsOpen(false);
        })
        .catch(error => {
          console.error('Error resizing image:', error);
          // Fallback to original file if resizing fails
          const reader = new FileReader();
          reader.onload = event => {
            const result = event.target?.result as string;
            setPreviewImage(result);
            onImageSelected(result);
            setIsOpen(false);
          };
          reader.readAsDataURL(file);
        });
    }
  };

  // Function to resize image before upload
  const resizeImage = (file: File, maxWidth: number, quality: number): Promise<string> => {
    return new Promise((resolve, reject) => {
      // Create a FileReader to read the file
      const reader = new FileReader();

      // Set up the FileReader onload event
      reader.onload = readerEvent => {
        // Create an image element
        const img = new Image();

        // Set up the image onload event
        img.onload = () => {
          // Get the image dimensions
          let width = img.width;
          let height = img.height;

          // Calculate new dimensions if the image is larger than maxWidth
          if (width > maxWidth) {
            height = Math.round((height * maxWidth) / width);
            width = maxWidth;
          }

          // Create a canvas element
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          // Draw the image on the canvas
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // Get the data URL from the canvas
          const dataUrl = canvas.toDataURL('image/jpeg', quality);

          resolve(dataUrl);
        };

        // Set the image source
        img.src = readerEvent.target?.result as string;
      };

      // Read the file as a data URL
      reader.readAsDataURL(file);
    });
  };

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      // Try to fetch and resize the image from the URL
      fetch(urlInput)
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.blob();
        })
        .then(blob => {
          const file = new File([blob], 'image-from-url.jpg', { type: blob.type });
          return resizeImage(file, 1200, 0.7);
        })
        .then(resizedDataUrl => {
          setPreviewImage(resizedDataUrl);
          onImageSelected(resizedDataUrl);
          setIsOpen(false);
          setUrlInput('');
        })
        .catch(error => {
          console.error('Error processing URL image:', error);
          // Fallback to using the URL directly
          setPreviewImage(urlInput);
          onImageSelected(urlInput);
          setIsOpen(false);
          setUrlInput('');
        });
    }
  };

  const handleSocialMediaSelect = (url: string) => {
    // Try to fetch and resize the image from the URL
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.blob();
      })
      .then(blob => {
        const file = new File([blob], 'social-media-image.jpg', { type: blob.type });
        return resizeImage(file, 1200, 0.7);
      })
      .then(resizedDataUrl => {
        setPreviewImage(resizedDataUrl);
        onImageSelected(resizedDataUrl);
        setIsOpen(false);
      })
      .catch(error => {
        console.error('Error processing social media image:', error);
        // Fallback to using the URL directly
        setPreviewImage(url);
        onImageSelected(url);
        setIsOpen(false);
      });
  };

  const handleCameraCapture = (imageDataUrl: string) => {
    setPreviewImage(imageDataUrl);
    onImageSelected(imageDataUrl);
    setIsOpen(false);
  };

  const imagePlaceholderClasses =
    aspectRatio === 'square' ? 'w-full aspect-square rounded-lg' : 'w-full h-48 rounded-lg';

  return (
    <div>
      {/* Image Preview or Placeholder */}
      <div
        className="group relative cursor-pointer overflow-hidden"
        onClick={() => setIsOpen(true)}
      >
        {previewImage ? (
          <div className={imagePlaceholderClasses}>
            <img
              src={previewImage}
              alt="Preview"
              className="w-full h-full object-cover rounded-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
              <ImagePlus className="h-8 w-8 text-white" />
            </div>
          </div>
        ) : (
          <div
            className={`${imagePlaceholderClasses} bg-gray-100 border-2 border-dashed border-gray-300 flex flex-col items-center justify-center`}
          >
            <ImagePlus className="h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500 text-center">
              Click to upload <br /> or select from social media
            </p>
          </div>
        )}
      </div>

      {/* Upload Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>
              Choose an image from your device, URL, social media, or take a photo.
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="upload">
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="social">Social</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-10 w-10 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 text-center mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-400">PNG, JPG or GIF (max. 2MB)</p>
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
              <Button onClick={handleUrlSubmit} className="w-full">
                Use Image URL
              </Button>
            </TabsContent>

            <TabsContent value="social">
              <SocialMediaImagePicker onSelect={handleSocialMediaSelect} />
            </TabsContent>

            <TabsContent value="camera">
              <CameraUploader onCapture={handleCameraCapture} />
            </TabsContent>
          </Tabs>

          <DialogFooter className="flex justify-between sm:justify-end">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
