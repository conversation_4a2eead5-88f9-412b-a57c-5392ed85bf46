import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Check, Loader2, X, MoveVertical } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';

interface ImprovedCoverPhotoProps {
  imageUrl: string;
  initialPosition?: string;
  onSave: (position: string) => void;
  onCancel: () => void;
}

export function ImprovedCoverPhoto({
  imageUrl,
  initialPosition = '50%',
  onSave,
  onCancel,
}: ImprovedCoverPhotoProps) {
  // Convert initial position from string (e.g., "50%") to number (e.g., 50)
  const initialPositionNumber = parseInt(initialPosition.replace('%', '')) || 50;

  const [position, setPosition] = useState(initialPositionNumber);
  const [isSaving, setIsSaving] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Handle slider change
  const handlePositionChange = (newPosition: number[]) => {
    setPosition(newPosition[0]);
  };

  // Handle save
  const handleSave = () => {
    setIsSaving(true);

    // Set a timeout to automatically clear the saving state after 10 seconds
    // This prevents the UI from getting stuck in a saving state
    const saveTimeoutId = setTimeout(() => {
      console.log('[ImprovedCoverPhoto] Save timeout reached, clearing saving state');
      setIsSaving(false);
      onSave(`${position}%`);
    }, 10000); // 10 seconds timeout

    // Call the onSave callback
    onSave(`${position}%`);

    // Clear the timeout after a short delay to ensure the UI updates
    setTimeout(() => {
      clearTimeout(saveTimeoutId);
      setIsSaving(false);

      // Show success toast
      toast({
        title: 'Cover Photo Position Updated',
        description: 'Your cover photo position has been updated successfully.',
        variant: 'default',
      });
    }, 2000);
  };

  return (
    <div ref={containerRef} className="relative h-full w-full overflow-hidden flex flex-col">
      {/* Image container with fixed height */}
      <div className="relative flex-grow overflow-hidden">
        <img
          src={imageUrl}
          alt="Cover photo"
          className="w-full h-auto min-h-full object-cover"
          style={{
            objectPosition: `center ${position}%`,
          }}
          draggable="false"
          onError={e => {
            const target = e.target as HTMLImageElement;
            target.onerror = null; // Prevent infinite error loop

            // Use a fallback image
            target.src = 'https://placehold.co/1200x400?text=Cover+Photo';

            console.log('Using fallback cover image in ImprovedCoverPhoto');
          }}
        />
      </div>

      {/* Controls overlay at the bottom */}
      <div className="absolute bottom-0 left-0 right-0 bg-black/70 p-4">
        <div className="flex flex-col gap-4">
          {/* Slider control */}
          <div className="flex items-center gap-3">
            <MoveVertical className="h-5 w-5 text-white" />
            <div className="flex-grow">
              <Slider
                value={[position]}
                min={0}
                max={100}
                step={1}
                onValueChange={handlePositionChange}
                className="w-full"
              />
            </div>
            <span className="text-white text-sm">{position}%</span>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="bg-white/10 hover:bg-white/20 text-white border-white/20"
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              disabled={isSaving}
              className="bg-primary/90 hover:bg-primary text-white"
            >
              {isSaving ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <Check className="h-4 w-4 mr-1" />
              )}
              Save Position
            </Button>
          </div>
        </div>
      </div>

      {/* Instructions overlay */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="bg-black/70 px-4 py-3 rounded-lg text-white flex items-center">
          <MoveVertical className="h-5 w-5 mr-2" />
          <span>Use the slider to reposition cover photo</span>
        </div>
      </div>
    </div>
  );
}
