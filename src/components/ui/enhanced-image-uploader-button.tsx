import { useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Camera, ImagePlus, Upload, XCircle, Loader2, PlusCircle } from 'lucide-react';
import { uploadImage, uploadBase64ImageToS3 } from '@/utils/s3-upload';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Sample avatars for quick selection
const defaultAvatars = [
  {
    id: 1,
    label: 'Professional',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/professional-1.jpg',
  },
  {
    id: 2,
    label: 'Creative',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/creative-1.jpg',
  },
  {
    id: 3,
    label: 'Casual',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/casual-1.jpg',
  },
  {
    id: 4,
    label: 'Minimal',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/minimal-1.jpg',
  },
];

interface AvatarPickerProps {
  onSelect: (url: string) => void;
}

function AvatarPicker({ onSelect }: AvatarPickerProps) {
  return (
    <div className="grid grid-cols-2 gap-3">
      {defaultAvatars.map(avatar => (
        <div
          key={avatar.id}
          className="border rounded-lg overflow-hidden cursor-pointer hover:border-primary transition-colors"
          onClick={() => onSelect(avatar.url)}
        >
          <div className="relative">
            <img
              src={avatar.url}
              alt={`${avatar.label} Avatar`}
              className="w-full h-32 object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 px-2 py-1">
              <span className="text-xs text-white">{avatar.label}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

interface CameraUploaderProps {
  onCapture: (imageDataUrl: string) => void;
  onError: (error: string) => void;
}

function CameraUploader({ onCapture, onError }: CameraUploaderProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreamActive, setIsStreamActive] = useState(false);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreamActive(true);
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      onError('Could not access camera. Please check your permissions.');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsStreamActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the video frame to the canvas
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert canvas to data URL
        const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);
        onCapture(imageDataUrl);

        // Stop the camera
        stopCamera();
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          className={`w-full h-full object-cover ${isStreamActive ? 'block' : 'hidden'}`}
        />
        <canvas ref={canvasRef} className="hidden" />

        {!isStreamActive && (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-white text-sm">Click "Start Camera" to enable your webcam</p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        {!isStreamActive ? (
          <Button type="button" onClick={startCamera} variant="outline">
            <Camera className="w-4 h-4 mr-2" />
            Start Camera
          </Button>
        ) : (
          <>
            <Button type="button" onClick={stopCamera} variant="outline">
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="button" onClick={captureImage}>
              <Camera className="w-4 h-4 mr-2" />
              Take Photo
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

interface EnhancedImageUploaderButtonProps {
  onFileSelect: (file: File) => void;
  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  buttonText?: string;
  buttonIcon?: React.ReactNode;
  acceptedFileTypes?: string;
  maxFileSizeMB?: number;
  isLoading?: boolean;
  className?: string;
}

export function EnhancedImageUploaderButton({
  onFileSelect,
  buttonVariant = 'default',
  buttonSize = 'default',
  buttonText = 'Upload Image',
  buttonIcon,
  acceptedFileTypes = 'image/*',
  maxFileSizeMB = 5,
  isLoading = false,
  className,
}: EnhancedImageUploaderButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [urlInput, setUrlInput] = useState('');
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (acceptedFileTypes && !file.type.match(new RegExp(acceptedFileTypes.replace('*', '.*')))) {
      setError(`Invalid file type. Please upload ${acceptedFileTypes.replace('/*', '')}.`);
      return;
    }

    // Validate file size
    if (maxFileSizeMB && file.size > maxFileSizeMB * 1024 * 1024) {
      setError(`File too large. Maximum size is ${maxFileSizeMB}MB.`);
      return;
    }

    // Clear any previous errors
    setError(null);

    // Pass the file to the parent component
    onFileSelect(file);

    // Close the dialog
    setIsOpen(false);
  };

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) return;

    try {
      setError(null);

      // Try to fetch the image from the URL
      const response = await fetch(urlInput);
      if (!response.ok) {
        throw new Error('Failed to fetch image from URL');
      }

      const blob = await response.blob();

      // Validate file type
      if (acceptedFileTypes && !blob.type.match(new RegExp(acceptedFileTypes.replace('*', '.*')))) {
        setError(
          `Invalid file type. Please provide a URL to ${acceptedFileTypes.replace('/*', '')}.`
        );
        return;
      }

      // Validate file size
      if (maxFileSizeMB && blob.size > maxFileSizeMB * 1024 * 1024) {
        setError(`File too large. Maximum size is ${maxFileSizeMB}MB.`);
        return;
      }

      const file = new File([blob], 'image-from-url.jpg', { type: blob.type });

      // Pass the file to the parent component
      onFileSelect(file);

      // Close the dialog and reset the input
      setIsOpen(false);
      setUrlInput('');
    } catch (error) {
      console.error('Error processing URL image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to process image from URL: ${errorMessage}`);

      toast({
        title: 'URL processing failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    try {
      setError(null);

      // Convert base64 to blob
      const response = await fetch(imageDataUrl);
      const blob = await response.blob();

      // Create a file from the blob
      const file = new File([blob], 'camera-image.jpg', { type: 'image/jpeg' });

      // Validate file size
      if (maxFileSizeMB && file.size > maxFileSizeMB * 1024 * 1024) {
        setError(`Image too large. Maximum size is ${maxFileSizeMB}MB.`);
        return;
      }

      // Pass the file to the parent component
      onFileSelect(file);

      // Close the dialog
      setIsOpen(false);
    } catch (error) {
      console.error('Error processing camera image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to process camera image: ${errorMessage}`);

      toast({
        title: 'Camera capture failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <>
      <Button
        variant={buttonVariant}
        size={buttonSize}
        onClick={() => setIsOpen(true)}
        disabled={isLoading}
        className={cn(className)}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          buttonIcon || <PlusCircle className="h-4 w-4 mr-2" />
        )}
        {buttonSize !== 'icon' && (isLoading ? 'Uploading...' : buttonText)}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload Image</DialogTitle>
            <DialogDescription>Choose an image to upload</DialogDescription>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
              {error}
            </div>
          )}

          <Tabs defaultValue="upload">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-10 w-10 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 text-center mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-400">
                  {acceptedFileTypes.replace('/*', '')} (max. {maxFileSizeMB}MB)
                </p>
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept={acceptedFileTypes}
                onChange={handleFileChange}
              />
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
              <Button onClick={handleUrlSubmit} className="w-full" disabled={!urlInput.trim()}>
                Use Image URL
              </Button>
            </TabsContent>

            <TabsContent value="camera">
              <CameraUploader onCapture={handleCameraCapture} onError={handleError} />
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
