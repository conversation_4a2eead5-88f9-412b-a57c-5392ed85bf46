import React, { useRef, useState } from 'react';
import { Button } from './button';
import { Input } from './input';
import { XCircle, Upload } from 'lucide-react';

export interface Attachment {
  name: string;
  size: number;
  url: string;
  type: string;
}

interface FileUploaderProps {
  onFilesUploaded: (attachments: Attachment[]) => void;
  initialFiles?: Attachment[];
  multiple?: boolean;
  accept?: string;
  maxSizeMB?: number;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onFilesUploaded,
  initialFiles = [],
  multiple = true,
  accept = '*',
  maxSizeMB = 10,
}) => {
  const [attachments, setAttachments] = useState<Attachment[]>(initialFiles);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const files = Array.from(e.target.files || []);
    if (!files.length) return;
    setUploading(true);
    try {
      const newAttachments: Attachment[] = [];
      for (const file of files) {
        if (file.size > maxSizeMB * 1024 * 1024) {
          setError(`File ${file.name} exceeds ${maxSizeMB}MB limit.`);
          continue;
        }
        // Upload to backend endpoint
        const formData = new FormData();
        formData.append('file', file);
        const res = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });
        if (!res.ok) {
          setError(`Failed to upload ${file.name}`);
          continue;
        }
        const data = await res.json();
        newAttachments.push({
          name: file.name,
          size: file.size,
          url: data.url, // backend should return { url }
          type: file.type,
        });
      }
      const updated = [...attachments, ...newAttachments];
      setAttachments(updated);
      onFilesUploaded(updated);
    } catch (err) {
      setError('Upload failed.');
    } finally {
      setUploading(false);
      if (fileInputRef.current) fileInputRef.current.value = '';
    }
  };

  const handleRemove = (idx: number) => {
    const updated = attachments.filter((_, i) => i !== idx);
    setAttachments(updated);
    onFilesUploaded(updated);
  };

  return (
    <div>
      <div
        className="border-2 border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-primary mb-2"
        onClick={() => fileInputRef.current?.click()}
        style={{ opacity: uploading ? 0.6 : 1 }}
      >
        <Upload className="h-8 w-8 text-gray-400 mb-2" />
        <p className="text-sm text-gray-500 text-center mb-1">
          Click to upload or drag and drop files
        </p>
        <p className="text-xs text-gray-400">Max {maxSizeMB}MB each</p>
        <Input
          type="file"
          ref={fileInputRef}
          className="hidden"
          multiple={multiple}
          accept={accept}
          onChange={handleFileChange}
        />
      </div>
      {error && <div className="text-sm text-red-500 mb-2">{error}</div>}
      <ul className="space-y-2">
        {attachments.map((file, idx) => (
          <li
            key={file.url}
            className="flex items-center justify-between bg-gray-50 rounded px-3 py-2"
          >
            <div className="flex-1 truncate">
              <span className="font-medium text-gray-700">{file.name}</span>
              <span className="ml-2 text-xs text-gray-500">
                ({(file.size / 1024).toFixed(1)} KB)
              </span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={e => {
                e.stopPropagation();
                handleRemove(idx);
              }}
              disabled={uploading}
            >
              <XCircle className="h-4 w-4 text-gray-400" />
            </Button>
          </li>
        ))}
      </ul>
    </div>
  );
};
