import { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Camera, ImagePlus, Upload, XCircle, Loader2, Edit } from 'lucide-react';
import { uploadImage, uploadBase64ImageToS3 } from '@/utils/s3-upload';

import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface CircularImageUploaderProps {
  initialImage?: string;
  onImageSelected: (imageUrl: string) => void;
  title?: string;
  folder?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallbackText?: string;
}

export function CircularImageUploader({
  initialImage,
  onImageSelected,
  title = 'Upload Profile Photo',
  folder = 'profiles',
  size = 'lg',
  fallbackText = '?',
}: CircularImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>(initialImage);
  const [urlInput, setUrlInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine the S3 folder
  const s3Folder = `${folder}/avatars`;

  // Update preview image when initialImage changes
  useEffect(() => {
    setPreviewImage(initialImage);
  }, [initialImage]);

  // Size mapping for avatar
  const sizeClasses = {
    sm: 'h-16 w-16',
    md: 'h-24 w-24',
    lg: 'h-32 w-32',
    xl: 'h-40 w-40',
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setError(null);

      // Upload the file to S3
      const imageUrl = await uploadImage(file, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);
      setIsOpen(false);
    } catch (error) {
      console.error('Error uploading file:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) return;

    try {
      setIsUploading(true);
      setError(null);

      // Upload the image from URL to S3
      const imageUrl = await uploadImage(urlInput, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);
      setIsOpen(false);
      setUrlInput('');
    } catch (error) {
      console.error('Error uploading image from URL:', error);
      setError('Failed to upload image from URL. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleAvatarSelect = (url: string) => {
    setPreviewImage(url);
    onImageSelected(url);
    setIsOpen(false);
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    try {
      setIsUploading(true);
      setError(null);

      // Upload the base64 image to S3
      const imageUrl = await uploadBase64ImageToS3(imageDataUrl, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);
      setIsOpen(false);
    } catch (error) {
      console.error('Error uploading camera image:', error);
      setError('Failed to upload image from camera. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  return (
    <div className="flex flex-col items-center">
      <div className="relative group cursor-pointer" onClick={() => setIsOpen(true)}>
        <Avatar
          className={`${sizeClasses[size]} border-2 border-gray-200 ${isUploading ? 'opacity-50' : ''}`}
        >
          <AvatarImage src={previewImage} alt="Profile" className="object-cover" />
          <AvatarFallback className="bg-gray-100 text-gray-400 text-xl">
            {fallbackText}
          </AvatarFallback>
        </Avatar>

        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        )}

        <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
          <Edit className="h-6 w-6 text-white" />
        </div>
      </div>

      <p className="mt-2 text-xs text-gray-500 text-center">Click to change</p>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
              {error}
            </div>
          )}

          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
              <TabsTrigger value="avatars">Gallery</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-upload">Upload Image</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    disabled={isUploading}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
              <Button
                onClick={handleUrlSubmit}
                className="w-full"
                disabled={isUploading || !urlInput.trim()}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Use Image URL</>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="camera">
              <div className="text-center p-4">
                <Camera className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Camera feature coming soon</p>
              </div>
            </TabsContent>

            <TabsContent value="avatars">
              <div className="text-center p-4">
                <ImagePlus className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Avatar gallery coming soon</p>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
