import { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Camera, ImagePlus, Upload, XCircle, Loader2, MoveVertical } from 'lucide-react';
import { uploadImage, uploadBase64ImageToS3 } from '@/utils/s3-upload';


interface AdjustableImageUploaderProps {
  initialImage?: string;
  initialPosition?: number; // 0-100 value for vertical position (0 = top, 100 = bottom)
  onImageSelected: (imageUrl: string, position: number) => void;
  aspectRatio?: 'square' | 'cover';
  title?: string;
  folder?: string;
}

export function AdjustableImageUploader({
  initialImage,
  initialPosition = 50, // Default to center
  onImageSelected,
  aspectRatio = 'cover',
  title = 'Upload Image',
  folder = 'uploads',
}: AdjustableImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>(initialImage);
  const [position, setPosition] = useState(initialPosition);
  const [urlInput, setUrlInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  // Determine the appropriate S3 folder based on the aspect ratio and provided folder
  const s3Folder = aspectRatio === 'square' ? `${folder}/avatars` : `${folder}/covers`;

  // Update preview image when initialImage changes
  useEffect(() => {
    setPreviewImage(initialImage);
  }, [initialImage]);

  // Update position when initialPosition changes
  useEffect(() => {
    setPosition(initialPosition);
  }, [initialPosition]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setError(null);

      // Upload the file to S3
      const imageUrl = await uploadImage(file, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl, position);
      setIsOpen(false);
    } catch (error) {
      console.error('Error uploading file:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) return;

    try {
      setIsUploading(true);
      setError(null);

      // Upload the image from URL to S3
      const imageUrl = await uploadImage(urlInput, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl, position);
      setIsOpen(false);
      setUrlInput('');
    } catch (error) {
      console.error('Error uploading image from URL:', error);
      setError('Failed to upload image from URL. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleAvatarSelect = (url: string) => {
    setPreviewImage(url);
    onImageSelected(url, position);
    setIsOpen(false);
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    try {
      setIsUploading(true);
      setError(null);

      // Upload the base64 image to S3
      const imageUrl = await uploadBase64ImageToS3(imageDataUrl, s3Folder);

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl, position);
      setIsOpen(false);
    } catch (error) {
      console.error('Error uploading camera image:', error);
      setError('Failed to upload image from camera. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handlePositionChange = (newPosition: number[]) => {
    const pos = newPosition[0];
    setPosition(pos);
    if (previewImage) {
      onImageSelected(previewImage, pos);
    }
  };

  // Handle mouse/touch drag for position adjustment
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!previewImage || !imageContainerRef.current) return;
    setIsDragging(true);

    const containerHeight = imageContainerRef.current.clientHeight;
    const startY = e.clientY;
    const startPosition = position;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaY = moveEvent.clientY - startY;
      const deltaPercent = (deltaY / containerHeight) * 100;
      const newPosition = Math.max(0, Math.min(100, startPosition - deltaPercent));
      setPosition(newPosition);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (previewImage) {
        onImageSelected(previewImage, position);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div className="space-y-4">
      <div
        ref={imageContainerRef}
        className={`relative border-2 border-dashed border-gray-300 rounded-lg overflow-hidden 
          hover:border-primary transition-colors cursor-pointer
          bg-gray-50 ${aspectRatio === 'square' ? 'aspect-square' : 'aspect-[3/1]'}`}
        onClick={() => !previewImage && setIsOpen(true)}
      >
        {previewImage ? (
          <div className="relative w-full h-full overflow-hidden">
            <img
              src={previewImage}
              alt="Uploaded"
              className="absolute w-full object-cover cursor-move"
              style={{
                height: 'auto',
                minHeight: '100%',
                objectPosition: `center ${position}%`,
                transform: isDragging ? 'scale(1.02)' : 'scale(1)',
                transition: isDragging ? 'none' : 'transform 0.2s ease-out',
              }}
              onMouseDown={handleMouseDown}
              draggable={false}
            />
            <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black/20">
              <div className="bg-white p-3 rounded-lg shadow-lg flex flex-col items-center gap-2">
                <Button size="sm" variant="outline" onClick={() => setIsOpen(true)}>
                  Change Image
                </Button>
                <div className="flex items-center gap-2 w-full max-w-40">
                  <MoveVertical className="h-4 w-4 text-gray-500" />
                  <Slider
                    value={[position]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={handlePositionChange}
                    className="w-full"
                  />
                </div>
                <p className="text-xs text-gray-500">Drag image or use slider to adjust position</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center p-4 h-full flex flex-col items-center justify-center">
            <ImagePlus className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-500">Click to upload</p>
          </div>
        )}
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
              {error}
            </div>
          )}

          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
              <TabsTrigger value="avatars">Gallery</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-upload">Upload Image</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    disabled={isUploading}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
              <Button
                onClick={handleUrlSubmit}
                className="w-full"
                disabled={isUploading || !urlInput.trim()}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Use Image URL</>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="camera">
              <div className="text-center p-4">
                <Camera className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Camera feature coming soon</p>
              </div>
            </TabsContent>

            <TabsContent value="avatars">
              <div className="text-center p-4">
                <ImagePlus className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">Avatar gallery coming soon</p>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
