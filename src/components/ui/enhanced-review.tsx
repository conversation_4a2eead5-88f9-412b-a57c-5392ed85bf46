import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { getInitials } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  Star,
  MessageSquare,
  Video,
  Mic,
  Image,
  ThumbsUp,
  Calendar,
  Clock,
} from 'lucide-react';
import ReactStars from 'react-rating-stars-component';

// Types for reviews
export type ReviewType = 'text' | 'audio' | 'video';

export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  content: string;
  type: ReviewType;
  mediaUrl?: string;
  createdAt: string;
  sessionTitle?: string;
  sessionDate?: string;
  helpful?: number;
}

interface ReviewCardProps {
  review: Review;
  onHelpfulClick?: (reviewId: string) => void;
}

interface ReviewsListProps {
  reviews: Review[];
  isLoading?: boolean;
  onHelpfulClick?: (reviewId: string) => void;
}

interface ReviewFormProps {
  onSubmit: (review: Partial<Review>) => void;
  isSubmitting?: boolean;
  allowedTypes?: ReviewType[];
}

interface ReviewsContainerProps {
  reviews: Review[];
  isLoading?: boolean;
  onSubmitReview: (review: Partial<Review>) => void;
  isSubmitting?: boolean;
  allowedTypes?: ReviewType[];
  userId?: string;
  teacherId?: string;
}

// Individual Review Card Component
export const ReviewCard: React.FC<ReviewCardProps> = ({ review, onHelpfulClick }) => {
  const formattedDate = new Date(review.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  const reviewTypeIcon = {
    text: <MessageSquare className="h-3.5 w-3.5 text-gray-400" />,
    audio: <Mic className="h-3.5 w-3.5 text-gray-400" />,
    video: <Video className="h-3.5 w-3.5 text-gray-400" />,
  };

  return (
    <div className="bg-white rounded-lg border border-gray-100 shadow-sm p-4 mb-4">
      <div className="flex items-start gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={review.userAvatar} alt={review.userName} />
          <AvatarFallback>{getInitials(review.userName)}</AvatarFallback>
        </Avatar>

        <div className="flex-1">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
            <div>
              <h4 className="font-medium text-gray-900">{review.userName}</h4>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <span>{formattedDate}</span>
                <span className="mx-1">•</span>
                <Badge
                  variant="outline"
                  className="text-[10px] py-0 px-1.5 rounded-full flex items-center gap-1"
                >
                  {reviewTypeIcon[review.type]}
                  <span>{review.type.charAt(0).toUpperCase() + review.type.slice(1)}</span>
                </Badge>
              </div>
            </div>

            <div className="mt-1 sm:mt-0">
              <ReactStars
                count={5}
                value={review.rating}
                edit={false}
                size={16}
                activeColor="#ffd700"
              />
            </div>
          </div>

          {review.sessionTitle && (
            <div className="mb-2 flex items-center text-xs text-gray-500">
              <Calendar className="h-3 w-3 mr-1" />
              <span className="mr-2">{review.sessionTitle}</span>
              {review.sessionDate && (
                <>
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{new Date(review.sessionDate).toLocaleDateString()}</span>
                </>
              )}
            </div>
          )}

          {review.type === 'text' && <p className="text-sm text-gray-700 mb-3">{review.content}</p>}

          {review.type === 'audio' && review.mediaUrl && (
            <div className="mb-3">
              <audio controls className="w-full h-10 mt-2">
                <source src={review.mediaUrl} type="audio/mpeg" />
                Your browser does not support the audio element.
              </audio>
            </div>
          )}

          {review.type === 'video' && review.mediaUrl && (
            <div className="mb-3">
              <video controls className="w-full rounded-md mt-2 max-h-60">
                <source src={review.mediaUrl} type="video/mp4" />
                Your browser does not support the video element.
              </video>
            </div>
          )}

          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-gray-500 hover:text-gray-700"
              onClick={() => onHelpfulClick && onHelpfulClick(review.id)}
            >
              <ThumbsUp className="h-3.5 w-3.5 mr-1" />
              Helpful {review.helpful && review.helpful > 0 ? `(${review.helpful})` : ''}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Reviews List Component
export const ReviewsList: React.FC<ReviewsListProps> = ({
  reviews = [],
  isLoading,
  onHelpfulClick,
}) => {
  // Ensure reviews is always an array
  const safeReviews = Array.isArray(reviews) ? reviews : [];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (safeReviews.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <MessageSquare className="h-12 w-12 mx-auto mb-3 text-gray-300" />
        <p>No reviews yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {safeReviews.map(review => (
        <ReviewCard key={review.id} review={review} onHelpfulClick={onHelpfulClick} />
      ))}
    </div>
  );
};

// Review Form Component
export const ReviewForm: React.FC<ReviewFormProps> = ({
  onSubmit,
  isSubmitting = false,
  allowedTypes = ['text', 'audio', 'video'],
}) => {
  const [rating, setRating] = useState<number>(0);
  const [content, setContent] = useState<string>('');
  const [reviewType, setReviewType] = useState<ReviewType>('text');
  const [mediaFile, setMediaFile] = useState<File | null>(null);
  const [mediaPreview, setMediaPreview] = useState<string | null>(null);

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setMediaFile(file);

      // Create preview for image files
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = e => {
          setMediaPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setMediaPreview(null);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      alert('Please select a rating');
      return;
    }

    if (reviewType === 'text' && !content.trim()) {
      alert('Please enter your review');
      return;
    }

    if ((reviewType === 'audio' || reviewType === 'video') && !mediaFile) {
      alert(`Please upload a ${reviewType} file`);
      return;
    }

    let mediaUrl;

    // Upload media file if present
    if (mediaFile && (reviewType === 'audio' || reviewType === 'video')) {
      try {
        const formData = new FormData();
        formData.append('media', mediaFile);

        const response = await fetch('http://localhost:4002/api/reviews/upload-media', {
          method: 'POST',
          body: formData,
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to upload media');
        }

        const data = await response.json();
        mediaUrl = data.mediaUrl;
      } catch (error) {
        console.error('Error uploading media:', error);
        alert('Failed to upload media. Please try again.');
        return;
      }
    }

    onSubmit({
      rating,
      content,
      type: reviewType,
      mediaUrl,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex flex-col items-center mb-4">
        <p className="text-sm text-gray-500 mb-2">Rate your experience</p>
        <ReactStars count={5} onChange={setRating} size={32} activeColor="#ffd700" />
      </div>

      <Tabs defaultValue="text" onValueChange={value => setReviewType(value as ReviewType)}>
        <TabsList className="grid grid-cols-3 mb-4">
          {allowedTypes.includes('text') && (
            <TabsTrigger value="text" className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span>Text</span>
            </TabsTrigger>
          )}
          {allowedTypes.includes('audio') && (
            <TabsTrigger value="audio" className="flex items-center gap-1">
              <Mic className="h-4 w-4" />
              <span>Audio</span>
            </TabsTrigger>
          )}
          {allowedTypes.includes('video') && (
            <TabsTrigger value="video" className="flex items-center gap-1">
              <Video className="h-4 w-4" />
              <span>Video</span>
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="text">
          <Textarea
            placeholder="Share your experience..."
            value={content}
            onChange={e => setContent(e.target.value)}
            className="min-h-[120px]"
          />
        </TabsContent>

        <TabsContent value="audio">
          <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 text-center">
            <Mic className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-500 mb-2">Upload an audio recording</p>
            <input
              type="file"
              accept="audio/*"
              onChange={handleMediaChange}
              className="hidden"
              id="audio-upload"
            />
            <label htmlFor="audio-upload">
              <Button type="button" variant="outline" size="sm">
                Select Audio File
              </Button>
            </label>
            {mediaFile && reviewType === 'audio' && (
              <p className="mt-2 text-xs text-gray-500">{mediaFile.name}</p>
            )}
          </div>
        </TabsContent>

        <TabsContent value="video">
          <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 text-center">
            <Video className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-500 mb-2">Upload a video review</p>
            <input
              type="file"
              accept="video/*"
              onChange={handleMediaChange}
              className="hidden"
              id="video-upload"
            />
            <label htmlFor="video-upload">
              <Button type="button" variant="outline" size="sm">
                Select Video File
              </Button>
            </label>
            {mediaFile && reviewType === 'video' && (
              <p className="mt-2 text-xs text-gray-500">{mediaFile.name}</p>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Submit Review
        </Button>
      </div>
    </form>
  );
};

// Main Reviews Container Component
export const ReviewsContainer: React.FC<ReviewsContainerProps> = ({
  reviews = [],
  isLoading,
  onSubmitReview,
  isSubmitting,
  allowedTypes,
  userId,
  teacherId,
}) => {
  // Ensure reviews is always an array
  const safeReviews = Array.isArray(reviews) ? reviews : [];
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [localIsSubmitting, setLocalIsSubmitting] = useState(false);

  const handleSubmitReview = async (review: Partial<Review>) => {
    if (!userId || !teacherId) {
      alert('You must be logged in to submit a review');
      return;
    }

    setLocalIsSubmitting(true);

    try {
      // Submit the review to the API
      const response = await fetch('http://localhost:4002/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...review,
          userId,
          teacherId,
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to submit review');
      }

      const data = await response.json();
      onSubmitReview(data);
      setIsReviewDialogOpen(false);
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setLocalIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Reviews ({safeReviews.length})</h3>

        <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Star className="h-4 w-4 mr-1" />
              Write a Review
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Write a Review</DialogTitle>
              <DialogDescription>Share your experience with this teacher</DialogDescription>
            </DialogHeader>
            <ReviewForm
              onSubmit={handleSubmitReview}
              isSubmitting={isSubmitting || localIsSubmitting}
              allowedTypes={allowedTypes}
            />
          </DialogContent>
        </Dialog>
      </div>

      <ReviewsList
        reviews={safeReviews}
        isLoading={isLoading}
        onHelpfulClick={async reviewId => {
          try {
            const response = await fetch(`http://localhost:4002/api/reviews/${reviewId}/helpful`, {
              method: 'POST',
              credentials: 'include',
            });

            if (!response.ok) {
              throw new Error('Failed to mark review as helpful');
            }

            // In a real app, you would update the review in the UI
            console.log('Marked as helpful:', reviewId);
          } catch (error) {
            console.error('Error marking review as helpful:', error);
          }
        }}
      />
    </div>
  );
};

// ReviewsContainer is already exported above
