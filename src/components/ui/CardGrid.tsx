import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorDisplay } from './ErrorDisplay';
import { PaginationWithResults } from './PaginationWithResults';

interface CardGridProps<T> {
  data: T[];
  renderCard: (item: T, index: number) => React.ReactNode;
  isLoading?: boolean;
  error?: Error | null;
  onRetry?: () => void;
  pagination?: {
    pageSize: number;
    currentPage: number;
    totalItems: number;
    onPageChange: (page: number) => void;
  };
  emptyState?: React.ReactNode;
  className?: string;
  gridClassName?: string;
  columns?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

/**
 * Reusable card grid component
 */
export function CardGrid<T>({
  data,
  renderCard,
  isLoading = false,
  error = null,
  onRetry,
  pagination,
  emptyState,
  className,
  gridClassName,
  columns = {
    default: 1,
    sm: 2,
    md: 2,
    lg: 3,
    xl: 4,
  },
}: CardGridProps<T>) {
  // Generate grid classes based on columns
  const gridClasses = cn(
    'grid gap-4',
    columns.default === 1 && 'grid-cols-1',
    columns.default === 2 && 'grid-cols-2',
    columns.default === 3 && 'grid-cols-3',
    columns.default === 4 && 'grid-cols-4',
    columns.sm === 1 && 'sm:grid-cols-1',
    columns.sm === 2 && 'sm:grid-cols-2',
    columns.sm === 3 && 'sm:grid-cols-3',
    columns.sm === 4 && 'sm:grid-cols-4',
    columns.md === 1 && 'md:grid-cols-1',
    columns.md === 2 && 'md:grid-cols-2',
    columns.md === 3 && 'md:grid-cols-3',
    columns.md === 4 && 'md:grid-cols-4',
    columns.lg === 1 && 'lg:grid-cols-1',
    columns.lg === 2 && 'lg:grid-cols-2',
    columns.lg === 3 && 'lg:grid-cols-3',
    columns.lg === 4 && 'lg:grid-cols-4',
    columns.xl === 1 && 'xl:grid-cols-1',
    columns.xl === 2 && 'xl:grid-cols-2',
    columns.xl === 3 && 'xl:grid-cols-3',
    columns.xl === 4 && 'xl:grid-cols-4',
    gridClassName
  );

  // Render loading state
  if (isLoading) {
    return (
      <div className="min-h-[200px] flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-[200px] flex items-center justify-center">
        <ErrorDisplay error={error} resetError={onRetry} />
      </div>
    );
  }

  // Render empty state
  if (data.length === 0) {
    return (
      <div className="min-h-[200px] flex items-center justify-center">
        {emptyState || (
          <div className="text-center p-6">
            <p className="text-muted-foreground">No items found</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Grid */}
      <div className={gridClasses}>
        {data.map((item, index) => (
          <React.Fragment key={index}>{renderCard(item, index)}</React.Fragment>
        ))}
      </div>

      {/* Pagination */}
      {pagination && (
        <PaginationWithResults
          currentPage={pagination.currentPage}
          totalPages={Math.ceil(pagination.totalItems / pagination.pageSize)}
          totalItems={pagination.totalItems}
          pageSize={pagination.pageSize}
          onPageChange={pagination.onPageChange}
        />
      )}
    </div>
  );
}
