/**
 * Image Cropper Component
 */
import React, { useState, useRef, useEffect } from 'react';
import ReactCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { RotateCw, ZoomIn, ZoomOut, Crop as CropIcon, Save } from 'lucide-react';

interface ImageCropperProps {
  src: string;
  onCropComplete: (croppedImageBlob: Blob) => void;
  aspectRatio?: number;
  circularCrop?: boolean;
  minWidth?: number;
  minHeight?: number;
  className?: string;
}

/**
 * Convert a crop percentage to pixels
 */
function percentToPixels(crop: Crop, imageWidth: number, imageHeight: number): PixelCrop {
  return {
    unit: 'px',
    x: Math.round((crop.x / 100) * imageWidth),
    y: Math.round((crop.y / 100) * imageHeight),
    width: Math.round((crop.width / 100) * imageWidth),
    height: Math.round((crop.height / 100) * imageHeight),
  };
}

/**
 * Create a centered crop with the given aspect ratio
 */
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number
): Crop {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight
    ),
    mediaWidth,
    mediaHeight
  );
}

/**
 * ImageCropper component
 * A component for cropping images with zoom and rotation
 */
export function ImageCropper({
  src,
  onCropComplete,
  aspectRatio,
  circularCrop = false,
  minWidth = 50,
  minHeight = 50,
  className,
}: ImageCropperProps) {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const imageRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Set initial crop when image loads
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    // If aspect ratio is provided, create a centered crop with that aspect ratio
    if (aspectRatio) {
      setCrop(centerAspectCrop(width, height, aspectRatio));
    } else {
      // Otherwise, create a default crop
      setCrop({
        unit: '%',
        x: 10,
        y: 10,
        width: 80,
        height: 80,
      });
    }
  };

  // Generate the cropped image when crop changes
  useEffect(() => {
    if (
      completedCrop?.width &&
      completedCrop?.height &&
      imageRef.current &&
      canvasRef.current
    ) {
      // Get the image element
      const image = imageRef.current;
      const canvas = canvasRef.current;
      
      // Set canvas dimensions
      canvas.width = completedCrop.width;
      canvas.height = completedCrop.height;
      
      // Get canvas context
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        return;
      }
      
      // Set canvas properties
      ctx.imageSmoothingQuality = 'high';
      
      // Calculate the center point for rotation
      const centerX = image.naturalWidth / 2;
      const centerY = image.naturalHeight / 2;
      
      // Save the canvas state
      ctx.save();
      
      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Move to the center of the image
      ctx.translate(canvas.width / 2, canvas.height / 2);
      
      // Rotate the canvas
      ctx.rotate((rotate * Math.PI) / 180);
      
      // Scale the image
      ctx.scale(scale, scale);
      
      // Move back to the top left corner
      ctx.translate(-canvas.width / 2, -canvas.height / 2);
      
      // Draw the image
      ctx.drawImage(
        image,
        completedCrop.x / scale,
        completedCrop.y / scale,
        completedCrop.width / scale,
        completedCrop.height / scale,
        0,
        0,
        completedCrop.width,
        completedCrop.height
      );
      
      // Restore the canvas state
      ctx.restore();
      
      // If circular crop is enabled, apply a circular mask
      if (circularCrop) {
        ctx.globalCompositeOperation = 'destination-in';
        ctx.beginPath();
        ctx.arc(
          canvas.width / 2,
          canvas.height / 2,
          Math.min(canvas.width, canvas.height) / 2,
          0,
          2 * Math.PI
        );
        ctx.fill();
      }
    }
  }, [completedCrop, scale, rotate, circularCrop]);

  // Handle zoom in
  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.1, 3));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.1, 0.5));
  };

  // Handle rotation
  const handleRotate = () => {
    setRotate((prevRotate) => (prevRotate + 90) % 360);
  };

  // Handle save
  const handleSave = () => {
    if (canvasRef.current) {
      canvasRef.current.toBlob((blob) => {
        if (blob) {
          onCropComplete(blob);
        }
      }, 'image/jpeg', 0.95);
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="relative">
        <ReactCrop
          crop={crop}
          onChange={(_, percentCrop) => setCrop(percentCrop)}
          onComplete={(c) => setCompletedCrop(c)}
          aspect={aspectRatio}
          minWidth={minWidth}
          minHeight={minHeight}
          circularCrop={circularCrop}
          className="max-h-[500px] mx-auto"
        >
          <img
            ref={imageRef}
            src={src}
            alt="Crop me"
            style={{ transform: `scale(${scale}) rotate(${rotate}deg)` }}
            onLoad={onImageLoad}
            className="max-h-[500px] max-w-full transition-transform duration-200"
          />
        </ReactCrop>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <ZoomOut className="w-4 h-4 text-muted-foreground" />
          <Slider
            value={[scale * 100]}
            min={50}
            max={300}
            step={5}
            onValueChange={(value) => setScale(value[0] / 100)}
            className="flex-1"
          />
          <ZoomIn className="w-4 h-4 text-muted-foreground" />
        </div>

        <div className="flex justify-between">
          <div className="space-x-2">
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4 mr-1" />
              Zoom In
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4 mr-1" />
              Zoom Out
            </Button>
            <Button variant="outline" size="sm" onClick={handleRotate}>
              <RotateCw className="w-4 h-4 mr-1" />
              Rotate
            </Button>
          </div>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4 mr-1" />
            Save Crop
          </Button>
        </div>
      </div>

      {/* Hidden canvas for generating the cropped image */}
      <canvas
        ref={canvasRef}
        style={{
          display: 'none',
          position: 'absolute',
          top: 0,
          left: 0,
        }}
      />
    </div>
  );
}
