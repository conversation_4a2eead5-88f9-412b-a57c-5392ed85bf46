'use client'

import NextLink from 'next/link'
import { ReactNode, forwardRef } from 'react'

interface LinkProps {
    href?: string
    to?: string  // Wouter-style prop
    children: ReactNode
    className?: string
    onClick?: () => void
    target?: string
    rel?: string
    replace?: boolean
    scroll?: boolean
    prefetch?: boolean
}

/**
 * Next.js Link wrapper that provides a similar API to W<PERSON><PERSON>'s Link
 * This makes migration easier by maintaining the same component interface
 * Supports both 'href' (Next.js style) and 'to' (Wouter style) props
 */
export const Link = forwardRef<HTMLAnchorElement, LinkProps>(
    ({ href, to, children, className, onClick, target, rel, replace, scroll, prefetch, ...props }, ref) => {
        // Use 'to' prop if provided (Wouter style), otherwise use 'href' (Next.js style)
        const linkHref = to || href || '#'

        return (
            <NextLink
                href={linkHref}
                className={className}
                onClick={onClick}
                target={target}
                rel={rel}
                replace={replace}
                scroll={scroll}
                prefetch={prefetch}
                ref={ref}
                {...props}
            >
                {children}
            </NextLink>
        )
    }
)

Link.displayName = 'Link'

export default Link 