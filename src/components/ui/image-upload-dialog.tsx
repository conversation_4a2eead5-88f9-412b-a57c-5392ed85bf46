/**
 * Image Upload Dialog
 * 
 * A simple dialog for uploading images directly to Supabase
 */
import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { DirectSupabaseUploader } from './direct-supabase-uploader';
import { Button } from './button';
import { X } from 'lucide-react';

interface ImageUploadDialogProps {
  type: 'avatar' | 'cover';
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: (url: string) => void;
  onError?: (error: Error) => void;
}

export function ImageUploadDialog({
  type,
  open,
  onOpenChange,
  onSuccess,
  onError
}: ImageUploadDialogProps) {
  const handleSuccess = (url: string) => {
    // Call the success callback
    onSuccess(url);
    
    // Close the dialog
    onOpenChange(false);
    
    // Update the UI immediately
    if (type === 'cover') {
      const coverElements = document.querySelectorAll('.cover-photo, [alt*="cover"]');
      if (coverElements.length > 0) {
        console.log('[ImageUploadDialog] Updating cover elements in the DOM:', coverElements.length);
        coverElements.forEach(img => {
          if (img instanceof HTMLImageElement) {
            img.src = `${url}?t=${Date.now()}`;
          } else if (img instanceof HTMLElement) {
            img.style.backgroundImage = `url(${url}?t=${Date.now()})`;
          }
        });
      }
    } else {
      const avatarElements = document.querySelectorAll('img[alt*="avatar"], img[alt*="profile"]');
      if (avatarElements.length > 0) {
        console.log('[ImageUploadDialog] Updating avatar elements in the DOM:', avatarElements.length);
        avatarElements.forEach(img => {
          if (img instanceof HTMLImageElement) {
            img.src = `${url}?t=${Date.now()}`;
          }
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {type === 'avatar' ? 'Update Profile Picture' : 'Update Cover Photo'}
          </DialogTitle>
          <DialogDescription>
            Upload a new {type === 'avatar' ? 'profile picture' : 'cover photo'} to your profile.
          </DialogDescription>
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2"
            onClick={() => onOpenChange(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>
        
        <div className="p-4 space-y-4">
          <DirectSupabaseUploader
            type={type}
            onSuccess={handleSuccess}
            onError={onError}
            buttonText={`Select ${type === 'avatar' ? 'Profile Picture' : 'Cover Photo'}`}
            className="w-full"
          />
          
          <div className="text-sm text-gray-500 mt-4">
            <p>Recommended {type === 'avatar' ? 'profile picture' : 'cover photo'} dimensions:</p>
            <ul className="list-disc list-inside mt-2">
              {type === 'avatar' ? (
                <>
                  <li>Square format (1:1 ratio)</li>
                  <li>At least 400x400 pixels</li>
                </>
              ) : (
                <>
                  <li>Landscape format (3:1 ratio)</li>
                  <li>At least 1200x400 pixels</li>
                </>
              )}
              <li>Maximum file size: 5MB</li>
              <li>Supported formats: JPG, PNG, GIF</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
