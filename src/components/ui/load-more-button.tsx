import React from 'react';
import { Button } from '@/components/ui/button';

interface LoadMoreButtonProps {
  onClick: () => void;
  isLoading: boolean;
  hasMore: boolean;
  className?: string;
}

export function LoadMoreButton({
  onClick,
  isLoading,
  hasMore,
  className = '',
}: LoadMoreButtonProps) {
  if (!hasMore) return null;

  return (
    <Button
      variant="outline"
      onClick={onClick}
      disabled={isLoading}
      className={`px-6 py-2 border-[#E07A5F] text-[#E07A5F] hover:bg-[#E07A5F] hover:text-white transition-all duration-300 ${className}`}
    >
      {isLoading ? (
        <>
          <span className="mr-2">Loading...</span>
          <span className="animate-spin">⟳</span>
        </>
      ) : (
        'Load More Sessions'
      )}
    </Button>
  );
}
