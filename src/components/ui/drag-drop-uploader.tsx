/**
 * Drag and Drop File Uploader Component
 */
import React, { useState, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Upload, X, Image, File, AlertCircle, CheckCircle2 } from 'lucide-react';

interface UploadState {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  url?: string;
  error?: string;
}

interface DragDropUploaderProps {
  onUpload: (file: File) => Promise<string>;
  onComplete?: (url: string) => void;
  accept?: string;
  maxSize?: number; // in bytes
  className?: string;
  multiple?: boolean;
  children?: React.ReactNode;
  showPreview?: boolean;
  previewMaxHeight?: number;
  label?: string;
  description?: string;
}

/**
 * DragDropUploader component
 * A drag and drop file uploader with progress indicator
 */
export function DragDropUploader({
  onUpload,
  onComplete,
  accept = 'image/*',
  maxSize = 5 * 1024 * 1024, // 5MB
  className,
  multiple = false,
  children,
  showPreview = true,
  previewMaxHeight = 200,
  label = 'Upload a file',
  description = 'Drag and drop a file here, or click to select a file',
}: DragDropUploaderProps) {
  // State for drag and drop
  const [isDragging, setIsDragging] = useState(false);
  const [uploads, setUploads] = useState<UploadState[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  // Process files for upload
  const processFiles = useCallback(
    async (files: FileList | null) => {
      if (!files || files.length === 0) return;

      // Convert FileList to array
      const fileArray = Array.from(files);
      const filesToProcess = multiple ? fileArray : [fileArray[0]];

      // Create upload states for each file
      const newUploads = filesToProcess.map((file) => ({
        file,
        progress: 0,
        status: 'pending' as const,
      }));

      // Add new uploads to state
      setUploads((prev) => [...prev, ...newUploads]);

      // Process each file
      for (const upload of newUploads) {
        // Validate file size
        if (upload.file.size > maxSize) {
          setUploads((prev) =>
            prev.map((u) =>
              u.file === upload.file
                ? {
                    ...u,
                    status: 'error' as const,
                    error: `File size exceeds the maximum allowed size (${formatFileSize(maxSize)})`,
                  }
                : u
            )
          );
          continue;
        }

        // Validate file type
        const acceptedTypes = accept.split(',').map((type) => type.trim());
        const isAccepted =
          acceptedTypes.includes('*') ||
          acceptedTypes.some((type) => {
            if (type.endsWith('/*')) {
              const mainType = type.split('/')[0];
              return upload.file.type.startsWith(`${mainType}/`);
            }
            return type === upload.file.type;
          });

        if (!isAccepted) {
          setUploads((prev) =>
            prev.map((u) =>
              u.file === upload.file
                ? {
                    ...u,
                    status: 'error' as const,
                    error: `File type not accepted. Please upload ${accept}`,
                  }
                : u
            )
          );
          continue;
        }

        // Start upload
        setUploads((prev) =>
          prev.map((u) =>
            u.file === upload.file
              ? { ...u, status: 'uploading' as const, progress: 10 }
              : u
          )
        );

        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setUploads((prev) =>
              prev.map((u) =>
                u.file === upload.file && u.status === 'uploading' && u.progress < 90
                  ? { ...u, progress: u.progress + 10 }
                  : u
              )
            );
          }, 300);

          // Upload the file
          const url = await onUpload(upload.file);

          // Clear interval and update state
          clearInterval(progressInterval);

          setUploads((prev) =>
            prev.map((u) =>
              u.file === upload.file
                ? { ...u, status: 'success' as const, progress: 100, url }
                : u
            )
          );

          // Call onComplete callback
          onComplete?.(url);
        } catch (error) {
          setUploads((prev) =>
            prev.map((u) =>
              u.file === upload.file
                ? {
                    ...u,
                    status: 'error' as const,
                    error: error instanceof Error ? error.message : 'Upload failed',
                  }
                : u
            )
          );
        }
      }
    },
    [multiple, maxSize, accept, onUpload, onComplete]
  );

  // Handle drop event
  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const { files } = e.dataTransfer;
      processFiles(files);
    },
    [processFiles]
  );

  // Handle file input change
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { files } = e.target;
      processFiles(files);
      // Reset the input value so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    },
    [processFiles]
  );

  // Handle click to open file dialog
  const handleClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Remove an upload
  const removeUpload = useCallback((upload: UploadState) => {
    setUploads((prev) => prev.filter((u) => u.file !== upload.file));
  }, []);

  // Format file size
  const formatFileSize = (size: number): string => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  // Get file icon based on type
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Drag and drop area */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 transition-colors',
          isDragging
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50',
          'cursor-pointer'
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <Upload className="w-8 h-8 text-muted-foreground" />
          <h3 className="text-lg font-medium">{label}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
          {children}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept={accept}
            multiple={multiple}
            onChange={handleFileChange}
          />
        </div>
      </div>

      {/* Upload list */}
      {uploads.length > 0 && (
        <div className="space-y-2">
          {uploads.map((upload, index) => (
            <div
              key={`${upload.file.name}-${index}`}
              className="flex items-center p-2 border rounded-md"
            >
              <div className="mr-2">{getFileIcon(upload.file)}</div>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between">
                  <p className="text-sm font-medium truncate">{upload.file.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(upload.file.size)}
                  </p>
                </div>
                {upload.status === 'uploading' && (
                  <Progress value={upload.progress} className="h-1 mt-1" />
                )}
                {upload.status === 'error' && (
                  <p className="text-xs text-destructive flex items-center mt-1">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    {upload.error}
                  </p>
                )}
              </div>
              {upload.status === 'success' && (
                <CheckCircle2 className="w-5 h-5 ml-2 text-green-500" />
              )}
              <Button
                variant="ghost"
                size="icon"
                className="ml-2 h-6 w-6"
                onClick={(e) => {
                  e.stopPropagation();
                  removeUpload(upload);
                }}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Image previews */}
      {showPreview &&
        uploads.some(
          (upload) => upload.status === 'success' && upload.file.type.startsWith('image/')
        ) && (
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
            {uploads
              .filter(
                (upload) => upload.status === 'success' && upload.file.type.startsWith('image/')
              )
              .map((upload, index) => (
                <div
                  key={`preview-${index}`}
                  className="relative aspect-square rounded-md overflow-hidden border"
                >
                  <img
                    src={upload.url}
                    alt={upload.file.name}
                    className="object-cover w-full h-full"
                    style={{ maxHeight: previewMaxHeight }}
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute top-1 right-1 h-6 w-6 rounded-full opacity-80 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeUpload(upload);
                    }}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
          </div>
        )}
    </div>
  );
}
