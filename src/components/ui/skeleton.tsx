import React from 'react';
import { cn } from '@/lib/utils';

function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <div className={cn('animate-pulse rounded-md bg-muted', className)} {...props} />;
}

/**
 * Text skeleton component
 */
function TextSkeleton({
  lines = 1,
  className,
  lastLineWidth = '100%',
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  lines?: number;
  lastLineWidth?: string | number;
}) {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            'h-4',
            i === lines - 1 && typeof lastLineWidth === 'string'
              ? { width: lastLineWidth }
              : i === lines - 1 && typeof lastLineWidth === 'number'
                ? { width: `${lastLineWidth}%` }
                : 'w-full'
          )}
        />
      ))}
    </div>
  );
}

/**
 * Avatar skeleton component
 */
function AvatarSkeleton({
  size = 'md',
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  size?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  return <Skeleton className={cn('rounded-full', sizeClasses[size], className)} {...props} />;
}

/**
 * Card skeleton component
 */
function CardSkeleton({
  className,
  imageHeight = 'h-48',
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  imageHeight?: string;
}) {
  return (
    <div className={cn('rounded-lg border bg-card overflow-hidden', className)} {...props}>
      <Skeleton className={cn('w-full', imageHeight)} />
      <div className="p-4 space-y-3">
        <TextSkeleton lines={1} className="w-3/4" />
        <TextSkeleton lines={2} lastLineWidth="60%" />
        <div className="flex justify-between items-center pt-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </div>
    </div>
  );
}

/**
 * Table row skeleton component
 */
function TableRowSkeleton({
  columns = 4,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  columns?: number;
}) {
  return (
    <div className={cn('flex items-center space-x-4 py-3', className)} {...props}>
      {Array.from({ length: columns }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn('h-4', i === 0 ? 'w-1/6' : i === columns - 1 ? 'w-1/6' : 'flex-1')}
        />
      ))}
    </div>
  );
}

/**
 * Form skeleton component
 */
function FormSkeleton({
  rows = 3,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  rows?: number;
}) {
  return (
    <div className={cn('space-y-6', className)} {...props}>
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className="h-4 w-1/4" />
          <Skeleton className="h-10 w-full" />
        </div>
      ))}
      <Skeleton className="h-10 w-24 mt-6" />
    </div>
  );
}

export { Skeleton, TextSkeleton, AvatarSkeleton, CardSkeleton, TableRowSkeleton, FormSkeleton };
