import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/radix-select';

interface Option {
  label: string;
  value: string;
}

interface FilterSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[] | string[];
  label?: string;
  placeholder?: string;
  className?: string;
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
}

export function FilterSelect({
  value,
  onChange,
  options,
  label,
  placeholder,
  className = '',
  align = 'center',
  sideOffset = 4,
}: FilterSelectProps) {
  // Format options to ensure they're in the correct format
  const formattedOptions: Option[] = options.map(option =>
    typeof option === 'string' ? { label: option, value: option } : option
  );

  // Store scroll position before opening dropdown
  const handleValueChange = (newValue: string) => {
    // Store current scroll position before applying filter
    if (typeof window !== 'undefined') {
      const currentPos = window.scrollY;
      onChange(newValue);
      // Restore position after state update
      setTimeout(() => window.scrollTo(0, currentPos), 0);
    } else {
      onChange(newValue);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="text-xs font-medium text-gray-600 block mb-1.5 font-outfit">
          {label}
        </label>
      )}
      <Select value={value} onValueChange={handleValueChange}>
        <SelectTrigger
          className="w-auto min-w-max rounded-md border-0 bg-white text-gray-800 hover:bg-gray-50 focus:ring-accent/20 shadow-sm h-10 font-outfit px-3"
          onClick={e => {
            // Prevent potential scroll jumps
            e.stopPropagation();
          }}
        >
          <SelectValue placeholder={placeholder || label || 'Select'} />
        </SelectTrigger>
        <SelectContent
          className="rounded-md border-0 bg-white text-gray-800 font-outfit shadow-lg"
          position="popper"
          align={align}
          sideOffset={sideOffset}
          avoidCollisions={true}
          onCloseAutoFocus={e => {
            // Prevent focus events that might cause scroll jumps
            e.preventDefault();
          }}
          onEscapeKeyDown={e => {
            // Ensure escape key doesn't interfere with scrolling
            e.preventDefault();
            onChange(value); // Keep current value
          }}
        >
          {formattedOptions.map(option => (
            <SelectItem
              key={option.value}
              value={option.value}
              className="focus:bg-accent/10 focus:text-gray-800"
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
