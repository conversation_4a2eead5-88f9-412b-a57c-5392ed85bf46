/**
 * CachedImage component for optimized image loading
 */
import React, { useState, useEffect } from 'react';
import { useCachedImage } from '@/lib/image-cache';
import { cn } from '@/lib/utils';

interface CachedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  fallbackSrc?: string;
  placeholderColor?: string;
  onLoad?: () => void;
  onError?: () => void;
  aspectRatio?: 'square' | '16:9' | '4:3' | '3:2' | '1:1';
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
}

/**
 * CachedImage component
 * Optimizes image loading with caching, lazy loading, and fallbacks
 */
export function CachedImage({
  src,
  fallbackSrc,
  alt,
  className,
  placeholderColor = '#f3f4f6',
  onLoad,
  onError,
  aspectRatio,
  objectFit = 'cover',
  ...props
}: CachedImageProps) {
  // Get the cached image URL
  const cachedSrc = useCachedImage(src);
  
  // State for loading and error
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(cachedSrc);
  
  // Update the source when the cached source changes
  useEffect(() => {
    setCurrentSrc(cachedSrc);
  }, [cachedSrc]);
  
  // Handle image load
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };
  
  // Handle image error
  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    
    // Try the fallback if available
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
    }
    
    onError?.();
  };
  
  // Determine aspect ratio class
  const aspectRatioClass = aspectRatio
    ? aspectRatio === 'square' || aspectRatio === '1:1'
      ? 'aspect-square'
      : aspectRatio === '16:9'
      ? 'aspect-video'
      : aspectRatio === '4:3'
      ? 'aspect-4/3'
      : aspectRatio === '3:2'
      ? 'aspect-3/2'
      : ''
    : '';
  
  // Determine object fit class
  const objectFitClass = 
    objectFit === 'cover'
      ? 'object-cover'
      : objectFit === 'contain'
      ? 'object-contain'
      : objectFit === 'fill'
      ? 'object-fill'
      : objectFit === 'none'
      ? 'object-none'
      : objectFit === 'scale-down'
      ? 'object-scale-down'
      : 'object-cover';
  
  return (
    <div
      className={cn(
        'relative overflow-hidden bg-muted',
        aspectRatioClass,
        className
      )}
      style={{ backgroundColor: placeholderColor }}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse">
          <svg
            className="w-8 h-8 text-muted-foreground opacity-25"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
            />
          </svg>
        </div>
      )}
      
      {/* The image */}
      <img
        src={currentSrc}
        alt={alt}
        className={cn(
          'w-full h-full transition-opacity duration-300',
          objectFitClass,
          isLoading ? 'opacity-0' : 'opacity-100'
        )}
        loading="lazy"
        onLoad={handleLoad}
        onError={handleError}
        {...props}
      />
      
      {/* Error fallback */}
      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-muted/50 text-muted-foreground">
          <svg
            className="w-8 h-8 mb-2 opacity-50"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
            />
          </svg>
          <span className="text-xs">Image not available</span>
        </div>
      )}
    </div>
  );
}

/**
 * CachedAvatar component
 * Specialized cached image for avatars
 */
export function CachedAvatar({
  src,
  alt,
  className,
  ...props
}: Omit<CachedImageProps, 'aspectRatio' | 'objectFit'>) {
  return (
    <CachedImage
      src={src}
      alt={alt || 'Avatar'}
      className={cn('rounded-full', className)}
      aspectRatio="square"
      objectFit="cover"
      fallbackSrc="https://placehold.co/400x400?text=Profile"
      placeholderColor="#e5e7eb"
      {...props}
    />
  );
}

/**
 * CachedCoverPhoto component
 * Specialized cached image for cover photos
 */
export function CachedCoverPhoto({
  src,
  alt,
  className,
  ...props
}: Omit<CachedImageProps, 'aspectRatio' | 'objectFit'>) {
  return (
    <CachedImage
      src={src}
      alt={alt || 'Cover Photo'}
      className={cn('w-full', className)}
      aspectRatio="16:9"
      objectFit="cover"
      fallbackSrc="https://placehold.co/1200x400?text=Cover+Photo"
      placeholderColor="#e5e7eb"
      {...props}
    />
  );
}

/**
 * CachedSessionImage component
 * Specialized cached image for session images
 */
export function CachedSessionImage({
  src,
  alt,
  className,
  ...props
}: Omit<CachedImageProps, 'aspectRatio' | 'objectFit'>) {
  return (
    <CachedImage
      src={src}
      alt={alt || 'Session'}
      className={cn('w-full', className)}
      aspectRatio="16:9"
      objectFit="cover"
      fallbackSrc="https://placehold.co/1200x400?text=Session"
      placeholderColor="#e5e7eb"
      {...props}
    />
  );
}
