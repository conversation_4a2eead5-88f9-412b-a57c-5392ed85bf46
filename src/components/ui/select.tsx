import * as React from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { createPortal } from 'react-dom';

interface SelectOption {
  label: string;
  value: string;
}

interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  options?: SelectOption[];
  placeholder?: string;
  className?: string;
  children?: React.ReactNode;
}

interface SelectContextValue {
  value?: string;
  onValueChange?: (value: string) => void;
  options?: SelectOption[];
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  triggerRef: React.RefObject<HTMLButtonElement>;
  contentRef: React.RefObject<HTMLDivElement>;
  position: { top: number; left: number; width: number } | null;
  setPosition: React.Dispatch<
    React.SetStateAction<{ top: number; left: number; width: number } | null>
  >;
}

const SelectContext = React.createContext<SelectContextValue | null>(null);

function useSelect() {
  const context = React.useContext(SelectContext);
  if (!context) {
    throw new Error('useSelect must be used within a Select provider');
  }
  return context;
}

export function Select({
  value,
  onValueChange,
  options = [],
  placeholder,
  className,
  children,
}: SelectProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const triggerRef = React.useRef<HTMLButtonElement>(null);
  const contentRef = React.useRef<HTMLDivElement>(null);
  const [position, setPosition] = React.useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);
  const closeTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Stop ongoing close timeouts when opening
  React.useEffect(() => {
    if (isOpen && closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  }, [isOpen]);

  // Calculate position exactly when the dropdown opens and on scroll/resize
  const updatePosition = React.useCallback(() => {
    if (!isOpen || !triggerRef.current) return;

    const trigger = triggerRef.current;
    if (!trigger) return;

    const rect = trigger.getBoundingClientRect();

    setPosition({
      top: rect.top + rect.height, // Use top + height instead of bottom to get position relative to viewport
      left: rect.left,
      width: rect.width,
    });
  }, [isOpen]);

  // Update position when dropdown opens
  React.useEffect(() => {
    updatePosition();
  }, [isOpen, updatePosition]);

  // Update position on scroll and resize
  React.useEffect(() => {
    if (!isOpen) return;

    window.addEventListener('scroll', updatePosition, true);
    window.addEventListener('resize', updatePosition);

    return () => {
      window.removeEventListener('scroll', updatePosition, true);
      window.removeEventListener('resize', updatePosition);
    };
  }, [isOpen, updatePosition]);

  // Handle clicks outside the dropdown
  React.useEffect(() => {
    if (!isOpen) return;

    function handleClickOutside(event: MouseEvent) {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        contentRef.current &&
        !contentRef.current.contains(event.target as Node)
      ) {
        closeTimeoutRef.current = setTimeout(() => {
          setIsOpen(false);
        }, 50);
      }
    }

    // Add listener with a slight delay to avoid immediate triggers
    const timeout = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timeout);
      document.removeEventListener('mousedown', handleClickOutside);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [isOpen]);

  const contextValue = React.useMemo(
    () => ({
      value,
      onValueChange,
      options,
      isOpen,
      setIsOpen,
      triggerRef,
      contentRef,
      position,
      setPosition,
    }),
    [value, onValueChange, options, isOpen, position]
  );

  return (
    <SelectContext.Provider value={contextValue}>
      <div className="relative">{children}</div>
    </SelectContext.Provider>
  );
}

interface SelectTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export function SelectTrigger({ className, style, children, ...props }: SelectTriggerProps) {
  const { setIsOpen, triggerRef, isOpen } = useSelect();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <button
      ref={triggerRef}
      type="button"
      className={cn('flex items-center justify-between', className)}
      style={style}
      onClick={handleClick}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 opacity-70 ml-2 flex-shrink-0" />
    </button>
  );
}

interface SelectValueProps {
  placeholder?: string;
  defaultValue?: string;
  children?: React.ReactNode;
}

export function SelectValue({ placeholder, defaultValue, children }: SelectValueProps) {
  const { value, options } = useSelect();
  const selectedOption = options?.find(opt => opt.value === value);

  if (children) {
    return <span>{children}</span>;
  }

  return (
    <span className="truncate text-left">
      {selectedOption?.label || value || placeholder || defaultValue}
    </span>
  );
}

interface SelectContentProps {
  className?: string;
  position?: 'popper';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
  children?: React.ReactNode;
}

export function SelectContent({
  className,
  position,
  align = 'start',
  sideOffset = 4,
  alignOffset = 0,
  children,
}: SelectContentProps) {
  const {
    isOpen,
    contentRef,
    position: pos,
    options = [],
    onValueChange,
    setIsOpen,
    value,
  } = useSelect();

  if (!isOpen) return null;

  // Basic positioning directly below the trigger with exact placement
  let left = pos?.left || 0;
  let top = (pos?.top || 0) + sideOffset;
  let width = pos?.width || 240;

  // Get the current scroll position
  const scrollY = window.scrollY;

  // Adjust horizontal positioning based on alignment
  if (align === 'center') {
    left = left + width / 2 - width / 2;
  } else if (align === 'end') {
    left = left + width - width;
  }

  // Prevent dropdown from going off-screen horizontally
  if (left + width > window.innerWidth - 10) {
    left = Math.max(10, window.innerWidth - width - 10);
  }
  if (left < 10) {
    left = 10;
  }

  // Ensure it stays within vertical viewport bounds
  const viewportHeight = window.innerHeight;
  const scrollTop = window.scrollY;
  const estimatedHeight = Math.min(300, (options.length || 5) * 40);

  // Check if dropdown would extend beyond bottom of viewport
  if (top + estimatedHeight > scrollTop + viewportHeight - 20) {
    // Position above trigger if there's room
    const triggerHeight = 40; // Approximate height
    const abovePosition = (pos?.top || 0) - estimatedHeight - triggerHeight;

    if (abovePosition > scrollTop) {
      top = abovePosition;
    } else {
      // Position at top of viewport with padding if can't fit above
      top = scrollTop + 10;
    }
  }

  // Use the same positioning for both mobile and desktop
  return createPortal(
    <div
      ref={contentRef}
      className={cn(
        'z-[100] overflow-hidden rounded-md border border-[#E4E2DD] shadow-md bg-white py-1',
        'fixed max-h-[300px] overflow-y-auto text-left',
        className
      )}
      style={{
        top: `${top}px`, // Use fixed position in viewport coordinates
        left: left,
        width: width,
        position: 'fixed', // Ensure it's fixed relative to the viewport
        transition: 'none', // Disable any transitions/animations
        zIndex: 9999, // Ensure it's above other elements
      }}
    >
      {children
        ? children
        : options.map(option => (
            <div
              key={option.value}
              className={cn(
                'px-3 py-2 cursor-pointer rounded-sm text-left',
                option.value === value
                  ? 'bg-[#E07A5F]/10 text-[#333333] font-medium'
                  : 'hover:bg-[#E07A5F]/5 text-[#333333]'
              )}
              onClick={() => {
                onValueChange?.(option.value);
                setIsOpen(false);
              }}
            >
              {option.label}
            </div>
          ))}
    </div>,
    document.body
  );
}

export const SelectItem = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & { value: string }
>(({ className, children, value, ...props }, ref) => {
  const context = React.useContext(SelectContext);
  if (!context) throw new Error('SelectItem must be used within Select');

  const isSelected = context.value === value;

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    context.onValueChange?.(value);
    context.setIsOpen(false);
  };

  return (
    <button
      ref={ref}
      type="button"
      className={cn(
        'flex w-full items-center px-3 py-2 text-sm cursor-pointer rounded hover:bg-gray-100 text-left',
        isSelected && 'bg-gray-100 font-medium',
        className
      )}
      onClick={handleClick}
      {...props}
    >
      <span className="flex-grow text-left">{children}</span>
      {isSelected && <Check className="h-4 w-4 ml-2" />}
    </button>
  );
});

SelectItem.displayName = 'SelectItem';
