/**
 * Reliable Image Uploader
 * 
 * A simplified, reliable component for uploading images to Supabase storage
 * with consistent path structure and robust error handling.
 */
import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Upload, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase-singleton';

interface ReliableImageUploaderProps {
  type: 'avatar' | 'cover';
  onSuccess: (url: string) => void;
  onError?: (error: Error) => void;
  className?: string;
  buttonText?: string;
  showPreview?: boolean;
}

export function ReliableImageUploader({
  type,
  onSuccess,
  onError,
  className = '',
  buttonText = 'Upload Image',
  showPreview = false
}: ReliableImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Clear previous errors
    setError(null);
    
    // Show preview if enabled
    if (showPreview) {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
    }

    try {
      setIsUploading(true);
      setUploadProgress(10);

      console.log(`[ReliableImageUploader] Starting ${type} upload for file:`, file.name);

      // Show loading toast
      toast({
        title: 'Uploading...',
        description: `Uploading your ${type === 'avatar' ? 'profile picture' : 'cover photo'}`,
      });

      // Get the current authenticated user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        throw new Error(`Authentication error: ${userError.message}`);
      }
      
      if (!userData?.user?.id) {
        throw new Error('No authenticated user found');
      }
      
      const userId = userData.user.id;
      console.log(`[ReliableImageUploader] Authenticated user ID: ${userId}`);

      // Update progress
      setUploadProgress(30);

      // Create a unique filename with timestamp to avoid conflicts
      const timestamp = Date.now();
      const fileExt = file.name.split('.').pop() || 'jpg';
      const fileName = `${timestamp}.${fileExt}`;
      
      // Use a consistent path structure
      // For avatar: avatars/{userId}/{timestamp}.{ext}
      // For cover: covers/{userId}/{timestamp}.{ext}
      const folder = type === 'avatar' ? 'avatars' : 'covers';
      const filePath = `${folder}/${userId}/${fileName}`;
      
      console.log(`[ReliableImageUploader] Upload path: ${filePath}`);

      // Update progress
      setUploadProgress(50);

      // Upload the file
      const { data, error: uploadError } = await supabase.storage
        .from('profiles')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      // Update progress
      setUploadProgress(80);

      if (uploadError) {
        console.error('[ReliableImageUploader] Upload error:', uploadError);
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(data.path);
        
      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL');
      }
      
      console.log('[ReliableImageUploader] Upload successful:', urlData.publicUrl);
      
      // Update progress
      setUploadProgress(90);

      // Update the user profile with the new URL
      await updateUserProfile(userId, type, urlData.publicUrl);
      
      // Update progress
      setUploadProgress(100);

      // Show success toast
      toast({
        title: 'Upload successful',
        description: `Your ${type === 'avatar' ? 'profile picture' : 'cover photo'} has been updated`,
      });

      // Call the success callback
      onSuccess(urlData.publicUrl);
    } catch (err) {
      console.error('[ReliableImageUploader] Error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      
      // Show error toast
      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });
      
      if (onError && err instanceof Error) {
        onError(err);
      }
    } finally {
      setIsUploading(false);
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const clearPreview = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
      
      {showPreview && previewUrl && (
        <div className="relative w-full mb-4">
          <img 
            src={previewUrl} 
            alt="Preview" 
            className={`w-full ${type === 'avatar' ? 'aspect-square rounded-full object-cover' : 'aspect-[3/1] rounded-md object-cover'}`} 
          />
          <button 
            onClick={clearPreview}
            className="absolute top-2 right-2 bg-black/50 rounded-full p-1"
          >
            <X className="h-4 w-4 text-white" />
          </button>
        </div>
      )}
      
      <Button
        onClick={handleButtonClick}
        disabled={isUploading}
        className="w-full"
      >
        {isUploading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Uploading... {uploadProgress > 0 ? `${Math.round(uploadProgress)}%` : ''}
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            {buttonText}
          </>
        )}
      </Button>
      
      {error && (
        <p className="text-red-500 text-sm mt-2">{error}</p>
      )}
    </div>
  );
}

/**
 * Update the user profile with the new image URL
 */
async function updateUserProfile(userId: string, type: 'avatar' | 'cover', url: string): Promise<void> {
  try {
    console.log(`[ReliableImageUploader] Updating user profile for ${type}:`, url);
    
    // Create the update data
    const updateData: Record<string, any> = {
      updated_at: new Date().toISOString()
    };
    
    if (type === 'avatar') {
      updateData.avatar = url;
    } else {
      updateData.cover_photo = url;
    }
    
    // Update the user profile
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: userId,
        ...updateData
      }, { onConflict: 'user_id' });
      
    if (error) {
      console.error('[ReliableImageUploader] Error updating user profile:', error);
    } else {
      console.log('[ReliableImageUploader] User profile updated successfully');
    }
  } catch (error) {
    console.error('[ReliableImageUploader] Error in updateUserProfile:', error);
  }
}
