import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X } from 'lucide-react';

interface FileUploaderProps {
  onFilesUploaded: (files: any[]) => void;
  initialFiles?: any[];
}

export function FileUploader({ onFilesUploaded, initialFiles = [] }: FileUploaderProps) {
  const [files, setFiles] = useState<any[]>(initialFiles || []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      // In a real app, you would upload the files to a server here
      // For now, we'll just simulate it by creating file objects
      const newFiles = Array.from(e.target.files).map(file => ({
        id: Math.random().toString(36).substring(2),
        name: file.name,
        size: file.size,
        type: file.type,
        url: URL.createObjectURL(file),
      }));

      const updatedFiles = [...files, ...newFiles];
      setFiles(updatedFiles);
      onFilesUploaded(updatedFiles);
    }
  };

  const removeFile = (id: string) => {
    const updatedFiles = files.filter(file => file.id !== id);
    setFiles(updatedFiles);
    onFilesUploaded(updatedFiles);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-6 bg-gray-50">
        <Upload className="h-8 w-8 text-gray-400 mb-2" />
        <p className="text-sm text-gray-500 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <Button
          type="button"
          variant="outline"
          onClick={() => document.getElementById('file-upload')?.click()}
        >
          Select Files
        </Button>
        <input
          id="file-upload"
          type="file"
          multiple
          className="hidden"
          onChange={handleFileChange}
        />
      </div>

      {files.length > 0 && (
        <ul className="space-y-2">
          {files.map(file => (
            <li
              key={file.id}
              className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
            >
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-2">
                  <span className="text-xs text-blue-600">{file.name.split('.').pop()}</span>
                </div>
                <div>
                  <p className="text-sm font-medium">{file.name}</p>
                  <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                </div>
              </div>
              <Button type="button" variant="ghost" size="sm" onClick={() => removeFile(file.id)}>
                <X className="h-4 w-4 text-gray-400" />
              </Button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
