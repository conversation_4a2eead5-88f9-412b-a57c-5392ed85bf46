import { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Camera, ImagePlus, Upload, XCircle, Loader2, PlusCircle, Edit, Image } from 'lucide-react';
import { uploadImage, uploadBase64ImageToS3 } from '@/utils/s3-upload';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';

// Sample avatars for quick selection
const defaultAvatars = [
  {
    id: 1,
    label: 'Professional',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/professional-1.jpg',
  },
  {
    id: 2,
    label: 'Creative',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/creative-1.jpg',
  },
  {
    id: 3,
    label: 'Casual',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/casual-1.jpg',
  },
  {
    id: 4,
    label: 'Minimal',
    url: 'https://sessionhub-images.s3.amazonaws.com/avatars/minimal-1.jpg',
  },
];

// Default cover templates from Unsplash
const DEFAULT_COVERS = [
  {
    id: 'cover1',
    url: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop',
    description: 'Colorful gradient background'
  },
  {
    id: 'cover2',
    url: 'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop',
    description: 'Purple gradient background'
  },
  {
    id: 'cover3',
    url: 'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop',
    description: 'Blue gradient background'
  },
  {
    id: 'cover4',
    url: 'https://images.unsplash.com/photo-1566041510639-8d95a2490bfb?w=1200&auto=format&fit=crop',
    description: 'Sunset gradient background'
  },
  {
    id: 'cover5',
    url: 'https://images.unsplash.com/photo-1559251606-c623743a6d76?w=1200&auto=format&fit=crop',
    description: 'Green gradient background'
  },
  {
    id: 'cover6',
    url: 'https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=1200&auto=format&fit=crop',
    description: 'Orange gradient background'
  },
  {
    id: 'cover7',
    url: 'https://images.unsplash.com/photo-1558470598-a5dda9640f68?w=1200&auto=format&fit=crop',
    description: 'Red gradient background'
  },
  {
    id: 'cover8',
    url: 'https://images.unsplash.com/photo-1554147090-e1221a04a025?w=1200&auto=format&fit=crop',
    description: 'Yellow gradient background'
  }
];

interface AvatarPickerProps {
  onSelect: (url: string) => void;
}

function AvatarPicker({ onSelect }: AvatarPickerProps) {
  return (
    <div className="grid grid-cols-2 gap-3">
      {defaultAvatars.map(avatar => (
        <div
          key={avatar.id}
          className="border rounded-lg overflow-hidden cursor-pointer hover:border-primary transition-colors"
          onClick={() => onSelect(avatar.url)}
        >
          <div className="relative">
            <img
              src={avatar.url}
              alt={`${avatar.label} Avatar`}
              className="w-full h-32 object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 px-2 py-1">
              <span className="text-xs text-white">{avatar.label}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

interface CoverTemplatesPickerProps {
  onSelect: (url: string) => void;
  currentCoverUrl?: string;
}

function CoverTemplatesPicker({ onSelect, currentCoverUrl }: CoverTemplatesPickerProps) {
  const [selectedCover, setSelectedCover] = useState<string | null>(null);

  // Set the current cover as selected when the component mounts
  useEffect(() => {
    if (currentCoverUrl) {
      const matchingCover = DEFAULT_COVERS.find(cover => cover.url === currentCoverUrl);
      if (matchingCover) {
        setSelectedCover(matchingCover.id);
      } else {
        setSelectedCover(null);
      }
    }
  }, [currentCoverUrl]);

  const handleSelect = (coverId: string) => {
    setSelectedCover(coverId);
    const cover = DEFAULT_COVERS.find(c => c.id === coverId);
    if (cover) {
      onSelect(cover.url);
    }
  };

  return (
    <ScrollArea className="h-[400px] mt-4">
      <div className="grid grid-cols-2 gap-4">
        {DEFAULT_COVERS.map(cover => (
          <div
            key={cover.id}
            className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${selectedCover === cover.id ? 'border-primary ring-2 ring-primary' : 'border-transparent'
              }`}
            onClick={() => handleSelect(cover.id)}
          >
            <img
              src={cover.url}
              alt={cover.description}
              className="w-full h-40 object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 text-sm">
              {cover.description}
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}

interface CameraUploaderProps {
  onCapture: (imageDataUrl: string) => void;
  onError: (error: string) => void;
}

function CameraUploader({ onCapture, onError }: CameraUploaderProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreamActive, setIsStreamActive] = useState(false);

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreamActive(true);
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      onError('Could not access camera. Please check your permissions.');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsStreamActive(false);
    }
  };

  const captureImage = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the video frame to the canvas
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert canvas to data URL
        const imageDataUrl = canvas.toDataURL('image/jpeg', 0.8);
        onCapture(imageDataUrl);

        // Stop the camera
        stopCamera();
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          className={`w-full h-full object-cover ${isStreamActive ? 'block' : 'hidden'}`}
        />
        <canvas ref={canvasRef} className="hidden" />

        {!isStreamActive && (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-white text-sm">Click "Start Camera" to enable your webcam</p>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-4">
        {!isStreamActive ? (
          <Button type="button" onClick={startCamera} variant="outline">
            <Camera className="w-4 h-4 mr-2" />
            Start Camera
          </Button>
        ) : (
          <>
            <Button type="button" onClick={stopCamera} variant="outline">
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button type="button" onClick={captureImage}>
              <Camera className="w-4 h-4 mr-2" />
              Take Photo
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

interface ElegantImageUploaderProps {
  imageUrl?: string;
  onImageSelected: (imageUrl: string) => void;
  onUploadStart?: () => void;
  onUploadComplete?: () => void;
  aspectRatio?: 'square' | 'cover';
  type: 'avatar' | 'cover';
  folder?: string;
  maxFileSizeMB?: number;
  isLoading?: boolean;
  className?: string;
}

export function ElegantImageUploader({
  imageUrl,
  onImageSelected,
  onUploadStart,
  onUploadComplete,
  aspectRatio = 'square',
  type = 'avatar',
  folder = 'uploads',
  maxFileSizeMB = 5,
  isLoading = false,
  className,
}: ElegantImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | undefined>(imageUrl);
  const [urlInput, setUrlInput] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Determine the appropriate S3 folder based on the aspect ratio and provided folder
  const s3Folder = type === 'avatar' ? `${folder}/avatars` : `${folder}/covers`;

  // Update previewImage when imageUrl changes
  useEffect(() => {
    if (imageUrl) {
      setPreviewImage(imageUrl);
    }
  }, [imageUrl]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setError(null);

      // Notify parent component that upload has started
      if (onUploadStart) {
        onUploadStart();
      }

      console.log('[ElegantImageUploader] Uploading file:', file.name);

      // Upload the file to Supabase storage
      let imageUrl;
      try {
        // Import the assetService to use Supabase storage
        const { assetService } = await import('@/services/assetService');

        // Use the appropriate upload method based on the type
        if (type === 'avatar') {
          const result = await assetService.uploadProfileAvatar(file);
          imageUrl = result.url;
        } else if (type === 'cover') {
          const result = await assetService.uploadProfileCover(file);
          imageUrl = result.url;
        } else {
          // Fallback to generic upload
          imageUrl = await uploadImage(file, s3Folder);
        }

        console.log('[ElegantImageUploader] File uploaded successfully:', imageUrl);
      } catch (uploadError) {
        console.error('[ElegantImageUploader] Error with direct upload:', uploadError);
        // Fallback to the original upload method
        imageUrl = await uploadImage(file, s3Folder);
      }

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);

      // Notify parent component that upload is complete
      if (onUploadComplete) {
        onUploadComplete();
      }

      // Show success toast
      toast({
        title: 'Image uploaded',
        description: 'Your image has been uploaded successfully.',
      });

      // Close the dialog
      setIsOpen(false);

      // Force refresh the image with a cache-busting parameter
      setTimeout(() => {
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src.includes(imageUrl.split('?')[0])) {
            img.src = `${imageUrl}?t=${Date.now()}`;
          }
        });
      }, 500);

    } catch (error) {
      console.error('Error uploading image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to upload image: ${errorMessage}`);

      // Show error toast
      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // Notify parent component that upload is complete (even with error)
      if (onUploadComplete) {
        onUploadComplete();
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) return;

    try {
      setIsUploading(true);
      setError(null);

      // Notify parent component that upload has started
      if (onUploadStart) {
        onUploadStart();
      }

      console.log('[ElegantImageUploader] Processing image from URL:', urlInput);

      // Try to fetch the image from the URL
      const response = await fetch(urlInput);
      if (!response.ok) {
        throw new Error('Failed to fetch image from URL');
      }

      const blob = await response.blob();
      const file = new File([blob], 'image-from-url.jpg', { type: blob.type });

      // Upload the file to Supabase storage
      let imageUrl;
      try {
        // Import the assetService to use Supabase storage
        const { assetService } = await import('@/services/assetService');

        // Use the appropriate upload method based on the type
        if (type === 'avatar') {
          const result = await assetService.uploadProfileAvatar(file);
          imageUrl = result.url;
        } else if (type === 'cover') {
          const result = await assetService.uploadProfileCover(file);
          imageUrl = result.url;
        } else {
          // Fallback to generic upload
          imageUrl = await uploadImage(file, s3Folder);
        }

        console.log('[ElegantImageUploader] URL image uploaded successfully:', imageUrl);
      } catch (uploadError) {
        console.error('[ElegantImageUploader] Error with direct upload from URL:', uploadError);
        // Fallback to the original upload method
        imageUrl = await uploadImage(file, s3Folder);
      }

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);

      // Notify parent component that upload is complete
      if (onUploadComplete) {
        onUploadComplete();
      }

      // Show success toast
      toast({
        title: 'Image uploaded',
        description: 'Your image URL has been processed successfully.',
      });

      setIsOpen(false);
      setUrlInput('');

      // Force refresh the image with a cache-busting parameter
      setTimeout(() => {
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src.includes(imageUrl.split('?')[0])) {
            img.src = `${imageUrl}?t=${Date.now()}`;
          }
        });
      }, 500);

    } catch (error) {
      console.error('Error processing URL image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to process image from URL: ${errorMessage}`);

      // Show error toast
      toast({
        title: 'URL upload failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // Notify parent component that upload is complete (even with error)
      if (onUploadComplete) {
        onUploadComplete();
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleAvatarSelect = (url: string) => {
    // Notify parent component that upload has started
    if (onUploadStart) {
      onUploadStart();
    }

    setPreviewImage(url);
    onImageSelected(url);

    // Notify parent component that upload is complete
    if (onUploadComplete) {
      onUploadComplete();
    }

    setIsOpen(false);
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    try {
      setIsUploading(true);
      setError(null);

      // Notify parent component that upload has started
      if (onUploadStart) {
        onUploadStart();
      }

      console.log('[ElegantImageUploader] Processing camera image');

      // Convert the data URL to a file
      const { dataUrlToFile } = await import('@/utils/supabase-storage');
      const file = dataUrlToFile(imageDataUrl, `camera-image-${Date.now()}.jpg`);

      // Upload the file to Supabase storage
      let imageUrl;
      try {
        // Import the assetService to use Supabase storage
        const { assetService } = await import('@/services/assetService');

        // Use the appropriate upload method based on the type
        if (type === 'avatar') {
          const result = await assetService.uploadProfileAvatar(file);
          imageUrl = result.url;
        } else if (type === 'cover') {
          const result = await assetService.uploadProfileCover(file);
          imageUrl = result.url;
        } else {
          // Fallback to generic upload
          imageUrl = await uploadBase64ImageToS3(imageDataUrl, s3Folder);
        }

        console.log('[ElegantImageUploader] Camera image uploaded successfully:', imageUrl);
      } catch (uploadError) {
        console.error('[ElegantImageUploader] Error with direct upload from camera:', uploadError);
        // Fallback to the original upload method
        imageUrl = await uploadBase64ImageToS3(imageDataUrl, s3Folder);
      }

      // Update the preview and notify parent
      setPreviewImage(imageUrl);
      onImageSelected(imageUrl);

      // Notify parent component that upload is complete
      if (onUploadComplete) {
        onUploadComplete();
      }

      // Show success toast
      toast({
        title: 'Image uploaded',
        description: 'Your camera image has been uploaded successfully.',
      });

      setIsOpen(false);

      // Force refresh the image with a cache-busting parameter
      setTimeout(() => {
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src.includes(imageUrl.split('?')[0])) {
            img.src = `${imageUrl}?t=${Date.now()}`;
          }
        });
      }, 500);

    } catch (error) {
      console.error('Error uploading camera image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(`Failed to upload image from camera: ${errorMessage}`);

      // Show error toast
      toast({
        title: 'Camera upload failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // Notify parent component that upload is complete (even with error)
      if (onUploadComplete) {
        onUploadComplete();
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  // Determine placeholder and styles based on type
  const placeholderText = type === 'avatar' ? 'Add Profile Photo' : 'Add Cover Photo';
  const containerClasses =
    type === 'avatar'
      ? 'w-full h-full rounded-full overflow-hidden'
      : 'w-full h-full rounded-lg overflow-hidden';

  const overlayClasses =
    type === 'avatar'
      ? 'absolute inset-0 bg-black/40 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-full'
      : 'absolute inset-0 bg-black/40 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity';

  // Edit button styles
  const editButtonClasses =
    type === 'avatar'
      ? 'absolute bottom-0 right-0 bg-white rounded-full p-1.5 shadow-md border border-gray-200 cursor-pointer z-10'
      : 'absolute bottom-4 right-4 bg-white rounded-full p-2 shadow-md border border-gray-200 cursor-pointer z-10';

  return (
    <div className={cn('relative group cursor-pointer', className)}>
      {/* Image or Placeholder */}
      {isLoading || isUploading ? (
        <div className={cn(containerClasses, 'bg-gray-100 flex items-center justify-center')}>
          <Loader2 className="h-8 w-8 text-primary animate-spin" />
        </div>
      ) : (
        <div className="relative w-full h-full" onClick={() => setIsOpen(true)}>
          {previewImage ? (
            <div className={containerClasses}>
              <img
                src={previewImage}
                alt={type === 'avatar' ? 'Profile Photo' : 'Cover Photo'}
                className="w-full h-full object-cover"
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.onerror = null;
                  target.src =
                    type === 'avatar'
                      ? 'https://sessionhub-images.s3.amazonaws.com/placeholders/default-avatar.jpg'
                      : 'https://sessionhub-images.s3.amazonaws.com/placeholders/default-cover.jpg';
                }}
              />
              {/* Hover overlay */}
              <div className={overlayClasses}>
                <Edit className="h-6 w-6 text-white mb-1" />
                <span className="text-xs text-white font-medium">
                  {type === 'avatar' ? 'Change Photo' : 'Change Cover'}
                </span>
              </div>

              {/* Persistent edit button */}
              <div
                className={editButtonClasses}
                onClick={e => {
                  e.stopPropagation(); // Prevent double dialog opening
                  setIsOpen(true);
                }}
              >
                <Edit
                  className={type === 'avatar' ? 'h-4 w-4 text-gray-600' : 'h-5 w-5 text-gray-600'}
                />
              </div>

              {/* Simple edit icon for cover photo in top-right corner */}
              {type === 'cover' && (
                <div className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full">
                  <Edit className="h-4 w-4" />
                </div>
              )}
            </div>
          ) : (
            <div
              className={cn(
                containerClasses,
                'bg-gray-100 border border-dashed border-gray-300 flex flex-col items-center justify-center'
              )}
            >
              <PlusCircle className="h-6 w-6 text-gray-400 mb-1" />
              <span className="text-xs text-gray-500 font-medium">{placeholderText}</span>

              {/* For empty cover photos, just show a camera icon */}
              {type === 'cover' && <Camera className="h-5 w-5 text-gray-400 mt-1" />}
            </div>
          )}
        </div>
      )}

      <Dialog
        open={isOpen}
        onOpenChange={open => {
          // Prevent closing the dialog during upload
          if (!isUploading) {
            setIsOpen(open);
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{type === 'avatar' ? 'Profile Photo' : 'Cover Photo'}</DialogTitle>
            <DialogDescription>Upload an image or select from available options</DialogDescription>
          </DialogHeader>

          {isUploading && (
            <div className="bg-primary/10 text-primary text-sm p-4 rounded-md mb-4 flex items-center">
              <Loader2 className="h-5 w-5 text-primary animate-spin mr-2" />
              <div>
                <p className="font-medium">Uploading image...</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Please wait while we process your image
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
              {error}
            </div>
          )}

          <Tabs defaultValue="upload">
            <TabsList className={`grid w-full ${type === 'avatar' || type === 'cover' ? 'grid-cols-4' : 'grid-cols-3'}`}>
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="url">URL</TabsTrigger>
              <TabsTrigger value="camera">Camera</TabsTrigger>
              {type === 'avatar' && <TabsTrigger value="avatars">Avatars</TabsTrigger>}
              {type === 'cover' && <TabsTrigger value="templates">Templates</TabsTrigger>}
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-10 w-10 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 text-center mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-400">PNG, JPG or GIF (max. {maxFileSizeMB}MB)</p>
              </div>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
            </TabsContent>

            <TabsContent value="url" className="space-y-4">
              <div className="grid w-full gap-2">
                <Label htmlFor="image-url">Image URL</Label>
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={urlInput}
                  onChange={e => setUrlInput(e.target.value)}
                />
              </div>
              <Button
                onClick={handleUrlSubmit}
                className="w-full"
                disabled={isUploading || !urlInput.trim()}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Use Image URL</>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="camera">
              <CameraUploader onCapture={handleCameraCapture} onError={handleError} />
            </TabsContent>

            {type === 'avatar' && (
              <TabsContent value="avatars">
                <AvatarPicker onSelect={handleAvatarSelect} />
              </TabsContent>
            )}

            {type === 'cover' && (
              <TabsContent value="templates">
                <CoverTemplatesPicker onSelect={handleAvatarSelect} currentCoverUrl={imageUrl} />
              </TabsContent>
            )}
          </Tabs>

          <DialogFooter className="flex justify-between sm:justify-end">
            <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isUploading}>
              {isUploading ? 'Please wait...' : 'Cancel'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
