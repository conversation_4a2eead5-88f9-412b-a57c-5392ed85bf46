import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface FilterSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: { label: string; value: string }[];
  label?: string;
  className?: string;
  placeholder?: string;
  side?: 'top' | 'bottom';
  align?: 'start' | 'center' | 'end';
}

export function FilterSelect({
  value,
  onChange,
  options,
  label,
  className = '',
  placeholder,
  side = 'bottom',
  align = 'start',
}: FilterSelectProps) {
  // Preserve scroll position when changing selection
  const handleValueChange = (newValue: string) => {
    if (typeof window !== 'undefined') {
      const currentPos = window.scrollY;
      onChange(newValue);
      // Restore scroll position after state update
      setTimeout(() => window.scrollTo(0, currentPos), 0);
    } else {
      onChange(newValue);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {label && <span className="text-xs text-gray-500 mb-1 block">{label}</span>}
      <Select value={value} onValueChange={handleValueChange}>
        <SelectTrigger
          className="w-full bg-white border-gray-200 text-sm h-9"
          onClick={e => {
            // Prevent propagation to avoid scroll jumps
            e.stopPropagation();
          }}
        >
          <SelectValue placeholder={placeholder || label || 'Select'} />
        </SelectTrigger>
        <SelectContent className="bg-white border-gray-200 z-50" sideOffset={4} align={align}>
          {options.map(option => (
            <SelectItem key={option.value} value={option.value} className="text-sm cursor-pointer">
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
