import React, { useState, useRef, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Smile,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Link,
} from 'lucide-react';
import { Button } from './button';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = 'Tell us about yourself...',
  className = '',
  minHeight = '200px',
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && initialLoad) {
      editorRef.current.innerHTML = value || '';
      setInitialLoad(false);

      // Make sure the editor is initialized with proper formatting
      if (!value || value.trim() === '') {
        // If empty, add a paragraph to ensure proper formatting
        editorRef.current.innerHTML = '<p><br></p>';
      }
    }
  }, [value, initialLoad]);

  // Update content when value changes from outside
  useEffect(() => {
    if (editorRef.current && !initialLoad && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value || '';
    }
  }, [value, initialLoad]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  // Format commands
  const execCommand = (command: string, value: string = '') => {
    // Save selection state
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      editorRef.current?.focus();
      return;
    }

    const range = selection.getRangeAt(0);

    // Special handling for lists
    if (command === 'insertUnorderedList' || command === 'insertOrderedList') {
      // First, ensure the editor has focus
      editorRef.current?.focus();

      // Create a paragraph if we're directly in the contentEditable div
      const parentElement = range?.startContainer.parentElement;
      if (
        parentElement &&
        parentElement.nodeName === 'DIV' &&
        parentElement === editorRef.current
      ) {
        document.execCommand('formatBlock', false, 'p');
      }

      // Create a new list with a default item if there's no selection or it's just a cursor
      if (range && range.collapsed && editorRef.current) {
        // Get the current paragraph or block element
        let currentBlock = range.startContainer;

        // Navigate up to find a block-level element
        while (
          currentBlock.nodeType !== Node.ELEMENT_NODE ||
          !['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(
            (currentBlock as Element).tagName
          )
        ) {
          if (currentBlock.parentElement) {
            currentBlock = currentBlock.parentElement;
          } else {
            break;
          }
        }

        // Create the appropriate list type
        const listType = command === 'insertUnorderedList' ? 'ul' : 'ol';

        // Check if we're already in a list of the same type
        const existingList = parentElement?.closest(listType);
        if (existingList) {
          // We're already in this type of list, so just add a new item
          const li = document.createElement('li');
          li.innerHTML = '<br>';
          existingList.appendChild(li);

          // Move cursor to the new list item
          const newRange = document.createRange();
          newRange.setStart(li, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          handleInput();
          return;
        }

        // Convert the current block to a list
        document.execCommand(command, false, '');

        // Apply proper styling
        setTimeout(() => {
          if (editorRef.current) {
            const lists = editorRef.current.querySelectorAll(listType);
            lists.forEach(list => {
              if (list.tagName === 'UL') {
                (list as HTMLElement).style.listStyleType = 'disc';
                (list as HTMLElement).style.paddingLeft = '1.5em';
              } else if (list.tagName === 'OL') {
                (list as HTMLElement).style.listStyleType = 'decimal';
                (list as HTMLElement).style.paddingLeft = '1.5em';
              }

              // Ensure all list items have content
              const items = list.querySelectorAll('li');
              items.forEach(item => {
                if (!item.textContent?.trim()) {
                  item.innerHTML = '<br>';
                }
              });
            });
          }

          handleInput();
        }, 0);

        return;
      }
    }

    // Execute command
    document.execCommand(command, false, value);

    // Restore focus and selection if possible
    if (editorRef.current) {
      editorRef.current.focus();

      // For lists, we need special handling
      if (command === 'insertUnorderedList' || command === 'insertOrderedList') {
        // Force a re-render to ensure proper list formatting
        setTimeout(() => {
          // Fix any direct text nodes that might be outside list items
          if (editorRef.current) {
            const listElements = editorRef.current.querySelectorAll('ul, ol');
            listElements.forEach(list => {
              // Check for direct text nodes in lists
              Array.from(list.childNodes).forEach(node => {
                if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
                  // Wrap text node in a list item
                  const li = document.createElement('li');
                  li.appendChild(node.cloneNode());
                  list.replaceChild(li, node);
                }
              });

              // Ensure list has proper styling
              if (list.tagName === 'UL') {
                (list as HTMLElement).style.listStyleType = 'disc';
                (list as HTMLElement).style.paddingLeft = '1.5em';
              } else if (list.tagName === 'OL') {
                (list as HTMLElement).style.listStyleType = 'decimal';
                (list as HTMLElement).style.paddingLeft = '1.5em';
              }
            });
          }

          handleInput();
        }, 0);
      } else {
        handleInput();
      }
    }
  };

  // Handle key events to fix cursor position issues
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Special handling for Enter key in lists
    if (e.key === 'Enter') {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let node: Node | null = range.startContainer;

      // Find if we're in a list item
      let listItem: Node | null = null;
      while (node && node !== editorRef.current) {
        if (node.nodeName === 'LI') {
          listItem = node;
          break;
        }
        node = node.parentNode;
      }

      // If we're in a list item
      if (listItem) {
        // If the list item is empty or cursor is at the beginning, create a new item
        if (!listItem.textContent?.trim() || (range.startOffset === 0 && range.collapsed)) {
          // Prevent default behavior
          e.preventDefault();

          // Get the parent list
          const parentList = listItem.parentNode;
          if (!parentList) return;

          // Create a new list item
          const newLi = document.createElement('li');
          newLi.innerHTML = '<br>';

          // Insert the new item after the current one
          if (listItem.nextSibling) {
            parentList.insertBefore(newLi, listItem.nextSibling);
          } else {
            parentList.appendChild(newLi);
          }

          // Move cursor to the new list item
          const newRange = document.createRange();
          newRange.setStart(newLi, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          handleInput();
          return;
        }
      }

      // For other Enter key presses, let the browser handle it
      setTimeout(() => {
        // Check if we need to fix the structure
        if (editorRef.current) {
          // If we're in a list, make sure we have proper list items
          const selection = window.getSelection();
          const range = selection?.getRangeAt(0);
          const parentElement = range?.startContainer.parentElement;

          // Check if we're directly in a list without a list item
          if (
            parentElement &&
            (parentElement.nodeName === 'UL' || parentElement.nodeName === 'OL')
          ) {
            // Create a list item and move the cursor inside it
            const li = document.createElement('li');
            li.innerHTML = '<br>';
            parentElement.appendChild(li);

            // Move cursor to the new list item
            const newRange = document.createRange();
            newRange.setStart(li, 0);
            newRange.collapse(true);
            selection?.removeAllRanges();
            selection?.addRange(newRange);
          }
        }

        handleInput();
      }, 0);
    }
  };

  // Handle emoji selection
  const handleEmojiSelect = (emoji: any) => {
    // Save current selection
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);

    // Insert emoji at current position
    if (range) {
      range.deleteContents();
      const textNode = document.createTextNode(emoji.native);
      range.insertNode(textNode);

      // Move cursor after the inserted emoji
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }

    handleInput();
    setShowEmojiPicker(false);
  };

  // Handle link insertion
  const handleLinkInsert = () => {
    const url = prompt('Enter URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  // Add CSS for placeholder
  useEffect(() => {
    // Add a style element for the placeholder if it doesn't exist
    if (!document.getElementById('rich-editor-styles')) {
      const style = document.createElement('style');
      style.id = 'rich-editor-styles';
      style.innerHTML = `
        [contenteditable=true]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          cursor: text;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  return (
    <div className={`border rounded-lg overflow-hidden bg-white ${className}`}>
      {/* Toolbar */}
      <div className="flex flex-wrap items-center gap-1 p-1.5 bg-white border-b">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('bold')}
          className="h-7 w-7 p-0 rounded-md"
          title="Bold"
        >
          <Bold className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('italic')}
          className="h-7 w-7 p-0 rounded-md"
          title="Italic"
        >
          <Italic className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('underline')}
          className="h-7 w-7 p-0 rounded-md"
          title="Underline"
        >
          <Underline className="h-3.5 w-3.5" />
        </Button>

        <div className="w-px h-5 bg-gray-200 mx-1"></div>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('insertUnorderedList')}
          className="h-7 w-7 p-0 rounded-md"
          title="Bullet List"
        >
          <List className="h-3.5 w-3.5" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => execCommand('insertOrderedList')}
          className="h-7 w-7 p-0 rounded-md"
          title="Numbered List"
        >
          <ListOrdered className="h-3.5 w-3.5" />
        </Button>

        <div className="w-px h-5 bg-gray-200 mx-1"></div>

        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleLinkInsert}
          className="h-7 w-7 p-0 rounded-md"
          title="Insert Link"
        >
          <Link className="h-3.5 w-3.5" />
        </Button>

        <Popover open={showEmojiPicker} onOpenChange={setShowEmojiPicker}>
          <PopoverTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 rounded-md"
              title="Insert Emoji"
            >
              <Smile className="h-3.5 w-3.5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 rounded-md" align="start">
            <Picker
              data={data}
              onEmojiSelect={handleEmojiSelect}
              theme="light"
              previewPosition="none"
              skinTonePosition="none"
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Editable content area */}
      <div
        ref={editorRef}
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        className="p-3 outline-none min-h-[150px] prose prose-sm sm:prose max-w-none bg-white prose-ul:pl-5 prose-ol:pl-5"
        style={{ minHeight }}
        data-placeholder={placeholder}
      />
    </div>
  );
};

export { RichTextEditor };
