import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { ApiError } from '@/lib/errorHandler';

interface ErrorDisplayProps {
  error: Error | ApiError | string;
  resetError?: () => void;
  className?: string;
  variant?: 'default' | 'card' | 'inline' | 'minimal';
  showRetry?: boolean;
}

/**
 * Error display component
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  resetError,
  className,
  variant = 'default',
  showRetry = true,
}) => {
  // Get error message
  const errorMessage = typeof error === 'string' ? error : error.message;

  // Determine if the error is a network error
  const isNetworkError =
    typeof error !== 'string' &&
    (error.message.includes('network') ||
      error.message.includes('fetch') ||
      error.message.includes('connection'));

  // Variants
  const variants = {
    default: 'p-6 rounded-lg border border-destructive/20 bg-destructive/5',
    card: 'p-4 rounded-lg border border-destructive/20 bg-destructive/5',
    inline: 'p-2 rounded-md border border-destructive/20 bg-destructive/5',
    minimal: '',
  };

  return (
    <div className={cn('flex flex-col items-center text-center', variants[variant], className)}>
      <AlertCircle className="h-8 w-8 text-destructive mb-2" />

      <h3 className="text-lg font-semibold text-destructive">
        {isNetworkError ? 'Connection Error' : 'Something went wrong'}
      </h3>

      <p className="text-sm text-muted-foreground mt-1 mb-4 max-w-md">
        {isNetworkError
          ? 'Unable to connect to the server. Please check your internet connection and try again.'
          : errorMessage || 'An unexpected error occurred. Please try again.'}
      </p>

      {showRetry && resetError && (
        <Button variant="outline" size="sm" onClick={resetError} className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  );
};

/**
 * Error state component for empty states with errors
 */
export const ErrorState: React.FC<ErrorDisplayProps & { title?: string }> = ({
  error,
  resetError,
  className,
  title,
  ...props
}) => {
  return (
    <div className={cn('flex flex-col items-center justify-center min-h-[200px] p-6', className)}>
      <ErrorDisplay error={error} resetError={resetError} variant="minimal" {...props} />
    </div>
  );
};
