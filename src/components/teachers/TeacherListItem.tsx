import { UserWithProfile } from '@shared/types';
import { TeacherCard } from './TeacherCard';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';

interface TeacherListItemProps {
  teacher: UserWithProfile;
}

/**
 * TeacherListItem - a wrapper around the unified TeacherCard component
 * Kept for backward compatibility
 */
export function TeacherListItem({ teacher }: TeacherListItemProps) {
  const isMobile = useIsMobile();

  // For mobile, use the list variant which is smaller and more compact
  if (isMobile) {
    return <TeacherCard teacher={teacher} variant="list" />;
  }

  // For desktop, use the enhanced version with more details
  return (
    <div
      className="relative group bg-white rounded-xl border border-gray-100 shadow-sm 
                    hover:shadow-md hover:border-primary/10 transition-all duration-300 p-4 md:p-5"
    >
      <TeacherCard
        teacher={teacher}
        variant="list"
        className="border-none py-0 hover:bg-transparent"
      />

      {/* Add bio for desktop view */}
      <p className="text-xs md:text-sm text-gray-600 mt-2 mb-2 line-clamp-2 ml-16">
        {teacher.profile?.bio || 'No bio available'}
      </p>

      {/* Additional tags for desktop view */}
      <div className="flex flex-wrap gap-1 mt-auto ml-16">
        {teacher.profile?.specializations?.slice(0, 3).map((spec, index) => (
          <Badge
            key={index}
            variant="outline"
            className="text-[0.65rem] bg-gray-50/80 font-normal py-0 h-5"
          >
            {spec}
          </Badge>
        ))}
        {teacher.profile?.specializations && teacher.profile.specializations.length > 3 && (
          <Badge variant="outline" className="text-[0.65rem] bg-gray-50/80 font-normal py-0 h-5">
            +{teacher.profile.specializations.length - 3}
          </Badge>
        )}
        {teacher.profile?.location && (
          <Badge variant="outline" className="text-[0.65rem] bg-gray-50/80 font-normal py-0 h-5">
            {teacher.profile.location}
          </Badge>
        )}
      </div>
    </div>
  );
}
