import { useFilter } from '@/features/filter';
import { UserWithProfile } from '@shared/types';
import { TeacherCard } from './TeacherCard';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEffect, useState } from 'react';
import { ArrowRight } from 'lucide-react';

interface TeacherGridProps {
  title?: string;
  subtitle?: string;
  initialDisplayCount?: number;
  increment?: number;
  showLoadMore?: boolean;
  featured?: boolean;
  isLoading?: boolean;
}

export function TeacherGrid({
  title = 'Featured Teachers',
  subtitle,
  initialDisplayCount = 12,
  increment = 12,
  showLoadMore = true,
  featured = false,
  isLoading = false,
}: TeacherGridProps) {
  const { filteredTeachers } = useFilter();
  const [visibleTeachers, setVisibleTeachers] = useState<number[]>([]);
  const [displayCount, setDisplayCount] = useState<number>(initialDisplayCount);

  // Apply pagination limit
  const teachers = filteredTeachers.slice(0, displayCount);

  // Load more teachers
  const loadMore = () => {
    setDisplayCount(prev => prev + increment);
  };

  // Reset and animate cards when teachers change
  useEffect(() => {
    setVisibleTeachers([]);

    if (teachers.length > 0 && !isLoading) {
      // Stagger the appearance of teacher cards
      teachers.forEach((_, index) => {
        setTimeout(() => {
          setVisibleTeachers(prevVisible => [...prevVisible, index]);
        }, 50 * index); // 50ms delay between each card
      });
    }
  }, [teachers, isLoading]);

  return (
    <div className={`${featured ? 'py-8' : 'py-4'}`}>
      {(title || subtitle) && (
        <div className="mb-8">
          {title && (
            <h2 className={`${featured ? 'text-3xl' : 'text-2xl'} font-bold text-[#333333] mb-2`}>
              {title}
            </h2>
          )}
          {subtitle && <p className="text-[#666666] max-w-3xl">{subtitle}</p>}
        </div>
      )}

      {isLoading ? (
        <div className="grid grid-cols-2 gap-3 sm:gap-5 md:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {Array.from({ length: initialDisplayCount }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full"
            >
              {/* Image area */}
              <div className="aspect-[16/9] relative bg-gray-100">
                <Skeleton className="absolute top-2 right-2 h-6 w-6 rounded-full" />
                <div className="absolute -bottom-4 md:-bottom-6 left-3 md:left-4">
                  <Skeleton className="h-10 w-10 md:h-16 md:w-16 rounded-full" />
                </div>
              </div>
              {/* Content area */}
              <div className="p-3 pt-5 md:p-4 md:pt-7">
                <div className="pl-10 md:pl-16 mb-2 md:mb-3">
                  <Skeleton className="h-4 md:h-5 w-3/4 mb-1 md:mb-2 rounded-md" />
                  <Skeleton className="h-2.5 md:h-3 w-1/2 rounded-md" />
                </div>
                <Skeleton className="h-3 md:h-4 w-full mb-2 rounded-md hidden xs:block" />
                <Skeleton className="h-3 md:h-4 w-full mb-2 rounded-md hidden xs:block" />
                <div className="flex gap-1 md:gap-2 mt-2">
                  <Skeleton className="h-2.5 md:h-3 w-16 rounded-full" />
                  <Skeleton className="h-2.5 md:h-3 w-12 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-2 gap-3 sm:gap-5 md:gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {teachers.length > 0 ? (
              teachers.map((teacher, index) => (
                <div
                  key={teacher.id}
                  className={`staggered-fade-in ${visibleTeachers.includes(index) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
                  style={{
                    transitionDelay: `${index * 50}ms`,
                    transitionProperty: 'opacity, transform',
                    transitionDuration: '500ms',
                    transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                >
                  <TeacherCard teacher={teacher} />
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-16 bg-[#F0EEE9]/40 rounded-xl">
                <h3 className="text-xl font-semibold text-[#666666]">No teachers found</h3>
                <p className="text-[#808080] mt-2 max-w-md mx-auto">
                  Try adjusting your filters or search criteria to discover more teachers.
                </p>
              </div>
            )}
          </div>

          {showLoadMore && filteredTeachers.length > displayCount && (
            <div className="mt-12 flex justify-center">
              <Button
                variant="outline"
                className="group px-6 py-2 border-[#84A59D] text-[#84A59D] hover:bg-[#84A59D] hover:text-white transition-all duration-300"
                onClick={loadMore}
              >
                <span>Load More Teachers</span>
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
