import React, { useState } from 'react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { UserCog, Bell, Globe, Lock, Eye, Clock, Users, Calendar, X } from 'lucide-react';

// Account settings form schema
const accountSchema = z
  .object({
    username: z.string().min(3).max(50),
    currentPassword: z.string().optional(),
    newPassword: z.string().min(8).max(100).optional(),
    confirmPassword: z.string().optional(),
  })
  .refine(
    data => {
      // If currentPassword is provided, newPassword is required
      if (data.currentPassword && !data.newPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'Please provide a new password',
      path: ['newPassword'],
    }
  )
  .refine(
    data => {
      // If newPassword is provided, confirmPassword must match
      if (data.newPassword && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    }
  );

// Notification settings form schema
const notificationSchema = z.object({
  emailNotifications: z.boolean(),
  sessionReminders: z.boolean(),
  marketingEmails: z.boolean(),
  siteUpdates: z.boolean(),
});

type AccountFormValues = z.infer<typeof accountSchema>;
type NotificationFormValues = z.infer<typeof notificationSchema>;

interface StandaloneSettingsDialogProps {
  trigger?: React.ReactNode;
}

export function StandaloneSettingsDialog({ trigger }: StandaloneSettingsDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('account');
  const { toast } = useToast();
  const { user } = useAuth();

  // States for settings
  const [preferences, setPreferences] = useState({
    theme: 'light',
    language: 'english',
    sessionReminders: true,
    notificationsEnabled: true,
    emailNotifications: true,
    pushNotifications: true,
    messageNotifications: true,
    bookingNotifications: true,
    emailDigest: 'daily',
    timezone: 'auto',
    fontScale: [100],
  });

  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    showOnlineStatus: true,
    showLastSeen: true,
    showUpcomingSessions: true,
    allowMessagesFrom: 'everyone',
    showReviewsOnProfile: true,
  });

  // Account form
  const accountForm = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      username: user?.email || '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Notification form
  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      emailNotifications: true,
      sessionReminders: true,
      marketingEmails: false,
      siteUpdates: true,
    },
  });

  const handleSavePreferences = () => {
    toast({
      title: 'Preferences saved',
      description: 'Your preferences have been updated successfully.',
      duration: 3000,
    });
  };

  const handleSavePrivacy = () => {
    toast({
      title: 'Privacy settings saved',
      description: 'Your privacy settings have been updated successfully.',
      duration: 3000,
    });
  };

  const onAccountSubmit = (data: AccountFormValues) => {
    console.log('Saving account settings:', data);
    // In a real app, you would save this to the backend
    toast({
      title: 'Account updated',
      description: data.newPassword
        ? 'Your account details and password have been updated.'
        : 'Your account details have been updated.',
      duration: 3000,
    });

    // Reset password fields
    accountForm.setValue('currentPassword', '');
    accountForm.setValue('newPassword', '');
    accountForm.setValue('confirmPassword', '');
  };

  const onNotificationSubmit = (data: NotificationFormValues) => {
    console.log('Saving notification preferences:', data);
    // In a real app, you would save this to the backend
    toast({
      title: 'Notification preferences updated',
      description: 'Your notification preferences have been saved successfully.',
      duration: 3000,
    });
  };

  return (
    <>
      {/* Trigger button */}
      <div onClick={() => setIsOpen(true)}>
        {trigger || <Button variant="outline">Settings</Button>}
      </div>

      {/* Using Sheet instead of Dialog for better UX */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent className="sm:max-w-[600px] h-screen overflow-y-auto">
          <SheetHeader className="mb-5">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl font-semibold">Settings</SheetTitle>
              <SheetClose asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </SheetClose>
            </div>
            <SheetDescription>
              Manage your account settings, preferences, and privacy options
            </SheetDescription>
          </SheetHeader>

          <div className="mt-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-6">
                <TabsTrigger value="account" className="flex items-center gap-2">
                  <UserCog className="h-4 w-4" />
                  <span>Account</span>
                </TabsTrigger>
                <TabsTrigger value="notifications" className="flex items-center gap-2">
                  <Bell className="h-4 w-4" />
                  <span>Notifications</span>
                </TabsTrigger>
                <TabsTrigger value="preferences" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  <span>Preferences</span>
                </TabsTrigger>
                <TabsTrigger value="privacy" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  <span>Privacy</span>
                </TabsTrigger>
              </TabsList>

              {/* Account Tab */}
              <TabsContent value="account" className="space-y-5">
                <Form {...accountForm}>
                  <form onSubmit={accountForm.handleSubmit(onAccountSubmit)} className="space-y-5">
                    <FormField
                      control={accountForm.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Username</FormLabel>
                          <FormControl>
                            <Input placeholder="username" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-5">
                      <h3 className="text-base font-medium">Change Password</h3>

                      <FormField
                        control={accountForm.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Current Password</FormLabel>
                            <FormControl>
                              <Input type="password" placeholder="••••••••" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={accountForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>New Password</FormLabel>
                            <FormControl>
                              <Input type="password" placeholder="••••••••" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={accountForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm New Password</FormLabel>
                            <FormControl>
                              <Input type="password" placeholder="••••••••" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button type="submit" className="w-full mt-4">
                      Update Account
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              {/* Notifications Tab */}
              <TabsContent value="notifications" className="space-y-5">
                <Form {...notificationForm}>
                  <form
                    onSubmit={notificationForm.handleSubmit(onNotificationSubmit)}
                    className="space-y-5"
                  >
                    <FormField
                      control={notificationForm.control}
                      name="emailNotifications"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Email Notifications</FormLabel>
                            <FormDescription>Receive notifications via email</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={notificationForm.control}
                      name="sessionReminders"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Session Reminders</FormLabel>
                            <FormDescription>Get reminders about upcoming sessions</FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={notificationForm.control}
                      name="marketingEmails"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Marketing Emails</FormLabel>
                            <FormDescription>
                              Receive promotional offers and updates
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={notificationForm.control}
                      name="siteUpdates"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>Platform Updates</FormLabel>
                            <FormDescription>
                              Get notified about new features and improvements
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <Button type="submit" className="w-full mt-4">
                      Save Notification Settings
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              {/* Preferences Tab */}
              <TabsContent value="preferences" className="space-y-5">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">Display Language</Label>
                      <Select
                        value={preferences.language}
                        onValueChange={value => setPreferences({ ...preferences, language: value })}
                      >
                        <SelectTrigger id="language">
                          <SelectValue placeholder="Select Language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="english">English</SelectItem>
                          <SelectItem value="spanish">Spanish</SelectItem>
                          <SelectItem value="french">French</SelectItem>
                          <SelectItem value="german">German</SelectItem>
                          <SelectItem value="chinese">Chinese</SelectItem>
                          <SelectItem value="japanese">Japanese</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="theme">Theme</Label>
                      <Select
                        value={preferences.theme}
                        onValueChange={value => setPreferences({ ...preferences, theme: value })}
                      >
                        <SelectTrigger id="theme">
                          <SelectValue placeholder="Select Theme" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System Default</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="font-scale">Font Size</Label>
                      <span className="text-sm text-muted-foreground">
                        {preferences.fontScale[0]}%
                      </span>
                    </div>
                    <Slider
                      id="font-scale"
                      value={preferences.fontScale}
                      onValueChange={value => setPreferences({ ...preferences, fontScale: value })}
                      max={150}
                      min={75}
                      step={5}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <Select
                        value={preferences.timezone}
                        onValueChange={value => setPreferences({ ...preferences, timezone: value })}
                      >
                        <SelectTrigger id="timezone">
                          <SelectValue placeholder="Select Timezone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="auto">Auto-detect</SelectItem>
                          <SelectItem value="est">Eastern Time (ET)</SelectItem>
                          <SelectItem value="cst">Central Time (CT)</SelectItem>
                          <SelectItem value="mst">Mountain Time (MT)</SelectItem>
                          <SelectItem value="pst">Pacific Time (PT)</SelectItem>
                          <SelectItem value="utc">UTC</SelectItem>
                          <SelectItem value="gmt">GMT</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button onClick={handleSavePreferences} className="w-full mt-4">
                    Save Preferences
                  </Button>
                </div>
              </TabsContent>

              {/* Privacy Tab */}
              <TabsContent value="privacy" className="space-y-5">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="profile-visibility">Profile Visibility</Label>
                    <Select
                      value={privacy.profileVisibility}
                      onValueChange={value => setPrivacy({ ...privacy, profileVisibility: value })}
                    >
                      <SelectTrigger id="profile-visibility">
                        <SelectValue placeholder="Select Visibility" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">Public - Everyone can see</SelectItem>
                        <SelectItem value="registered">Registered Users Only</SelectItem>
                        <SelectItem value="private">Private - Only When Booked</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message-permission">Who Can Message You</Label>
                    <Select
                      value={privacy.allowMessagesFrom}
                      onValueChange={value => setPrivacy({ ...privacy, allowMessagesFrom: value })}
                    >
                      <SelectTrigger id="message-permission">
                        <SelectValue placeholder="Select Who Can Message You" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="everyone">Everyone</SelectItem>
                        <SelectItem value="booked">Users Who've Booked Sessions</SelectItem>
                        <SelectItem value="approved">Approved Users Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="online-status">Show Online Status</Label>
                    </div>
                    <Switch
                      id="online-status"
                      checked={privacy.showOnlineStatus}
                      onCheckedChange={checked =>
                        setPrivacy({ ...privacy, showOnlineStatus: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="last-seen">Show Last Seen Status</Label>
                    </div>
                    <Switch
                      id="last-seen"
                      checked={privacy.showLastSeen}
                      onCheckedChange={checked => setPrivacy({ ...privacy, showLastSeen: checked })}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="upcoming-sessions">Show Upcoming Sessions</Label>
                    </div>
                    <Switch
                      id="upcoming-sessions"
                      checked={privacy.showUpcomingSessions}
                      onCheckedChange={checked =>
                        setPrivacy({ ...privacy, showUpcomingSessions: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="show-reviews">Show Reviews on Profile</Label>
                    </div>
                    <Switch
                      id="show-reviews"
                      checked={privacy.showReviewsOnProfile}
                      onCheckedChange={checked =>
                        setPrivacy({ ...privacy, showReviewsOnProfile: checked })
                      }
                    />
                  </div>

                  <Button onClick={handleSavePrivacy} className="w-full mt-4">
                    Save Privacy Settings
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
