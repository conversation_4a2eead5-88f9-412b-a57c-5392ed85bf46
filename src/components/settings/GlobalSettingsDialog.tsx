import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/features/auth/AuthContext';
import { useProfile } from '@/features/profile/ProfileContext';

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Separator } from '../ui/separator';
import { Label } from '../ui/label';
import { Slider } from '../ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

import { UserCog, Bell, Globe, Lock } from 'lucide-react';

// Form schemas
const accountSchema = z
  .object({
    username: z.string().min(3, {
      message: 'Username must be at least 3 characters.',
    }),
    currentPassword: z.string().optional(),
    newPassword: z
      .string()
      .min(8, {
        message: 'Password must be at least 8 characters.',
      })
      .optional(),
    confirmPassword: z.string().optional(),
  })
  .refine(
    data => {
      // If any password field is filled, all must be filled
      const { currentPassword, newPassword, confirmPassword } = data;
      const somePasswordFieldFilled = currentPassword || newPassword || confirmPassword;
      const allPasswordFieldsFilled = currentPassword && newPassword && confirmPassword;

      if (somePasswordFieldFilled && !allPasswordFieldsFilled) {
        return false;
      }
      return true;
    },
    {
      message: 'All password fields must be filled to change password',
      path: ['currentPassword'],
    }
  )
  .refine(
    data => {
      // If password fields are filled, newPassword and confirmPassword must match
      if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    }
  );

const notificationSchema = z.object({
  emailNotifications: z.boolean().default(true),
  sessionReminders: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
  siteUpdates: z.boolean().default(true),
});

// Settings Dialog Component
export function GlobalSettingsDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('account');

  const { toast } = useToast();
  const { user } = useAuth();

  // Safely try to use profile context - disable for now to prevent crashes
  const updateProfileMutation = null;

  // Watch for custom events to open the dialog
  useEffect(() => {
    const handleOpenDialog = () => {
      console.log('Global settings dialog opening');
      setIsOpen(true);
    };

    window.addEventListener('open-settings-dialog', handleOpenDialog);

    return () => {
      window.removeEventListener('open-settings-dialog', handleOpenDialog);
    };
  }, []);

  // Account form handling
  const accountForm = useForm<z.infer<typeof accountSchema>>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      username: user?.email || '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  // Notification form handling
  const notificationForm = useForm<z.infer<typeof notificationSchema>>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      emailNotifications: true,
      sessionReminders: true,
      marketingEmails: false,
      siteUpdates: true,
    },
  });

  // Preferences state
  const [preferences, setPreferences] = useState({
    language: 'english',
    theme: 'light',
    fontScale: [100],
    timezone: 'auto',
  });

  // Privacy state
  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    allowMessagesFrom: 'everyone',
    activityVisibility: true,
    showOnlineStatus: true,
    allowTagging: true,
  });

  // Form submission handlers
  const onAccountSubmit = (data: z.infer<typeof accountSchema>) => {
    // Handle account update
    if (data.newPassword) {
      // Handle password change
      console.log('Changing password');
    }

    if (data.username !== user?.email) {
      // Profile updates are currently disabled
      toast({
        title: 'Feature unavailable',
        description: 'Profile updates are currently unavailable.',
        variant: 'destructive',
      });
    }
  };

  const onNotificationSubmit = (data: z.infer<typeof notificationSchema>) => {
    // Handle notification settings update
    console.log('Notification settings:', data);
    toast({
      title: 'Notification preferences saved',
      description: 'Your notification settings have been updated successfully.',
    });
  };

  const handleSavePreferences = () => {
    // Handle preferences update
    console.log('Preferences:', preferences);
    toast({
      title: 'Preferences saved',
      description: 'Your preferences have been updated successfully.',
    });
  };

  const handleSavePrivacy = () => {
    // Handle privacy settings update
    console.log('Privacy settings:', privacy);
    toast({
      title: 'Privacy settings saved',
      description: 'Your privacy settings have been updated successfully.',
    });
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={open => {
        console.log('Global settings dialog changing to:', open);
        setIsOpen(open);
      }}
    >
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto px-6">
        <DialogHeader className="sticky top-0 bg-white pb-2 z-10">
          <DialogTitle className="text-xl font-semibold">Settings</DialogTitle>
          <DialogDescription>
            Manage your account settings, preferences, and privacy options
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <Tabs
            defaultValue={activeTab}
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="account" className="flex items-center gap-2">
                <span className="inline-flex">
                  <UserCog className="h-4 w-4" aria-hidden="false" />
                </span>
                <span>Account</span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <span className="inline-flex">
                  <Bell className="h-4 w-4" aria-hidden="false" />
                </span>
                <span>Notifications</span>
              </TabsTrigger>
              <TabsTrigger value="preferences" className="flex items-center gap-2">
                <span className="inline-flex">
                  <Globe className="h-4 w-4" aria-hidden="false" />
                </span>
                <span>Preferences</span>
              </TabsTrigger>
              <TabsTrigger value="privacy" className="flex items-center gap-2">
                <span className="inline-flex">
                  <Lock className="h-4 w-4" aria-hidden="false" />
                </span>
                <span>Privacy</span>
              </TabsTrigger>
            </TabsList>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-5">
              <Form {...accountForm}>
                <form onSubmit={accountForm.handleSubmit(onAccountSubmit)} className="space-y-5">
                  <FormField
                    control={accountForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-5">
                    <h3 className="text-base font-medium">Change Password</h3>

                    <FormField
                      control={accountForm.control}
                      name="currentPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={accountForm.control}
                      name="newPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={accountForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm New Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="••••••••" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button type="submit" className="w-full mt-4">
                    Update Account
                  </Button>
                </form>
              </Form>
            </TabsContent>

            {/* Notifications Tab */}
            <TabsContent value="notifications" className="space-y-5">
              <Form {...notificationForm}>
                <form
                  onSubmit={notificationForm.handleSubmit(onNotificationSubmit)}
                  className="space-y-5"
                >
                  <FormField
                    control={notificationForm.control}
                    name="emailNotifications"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Email Notifications</FormLabel>
                          <FormDescription>Receive notifications via email</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="sessionReminders"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Session Reminders</FormLabel>
                          <FormDescription>Get reminders about upcoming sessions</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="marketingEmails"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Marketing Emails</FormLabel>
                          <FormDescription>Receive promotional offers and updates</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={notificationForm.control}
                    name="siteUpdates"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Platform Updates</FormLabel>
                          <FormDescription>
                            Get notified about new features and improvements
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <Button type="submit" className="w-full mt-4">
                    Save Notification Settings
                  </Button>
                </form>
              </Form>
            </TabsContent>

            {/* Preferences Tab */}
            <TabsContent value="preferences" className="space-y-5">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="language">Display Language</Label>
                    <Select
                      value={preferences.language}
                      onValueChange={value => setPreferences({ ...preferences, language: value })}
                    >
                      <SelectTrigger id="language">
                        <SelectValue placeholder="Select Language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="english">English</SelectItem>
                        <SelectItem value="spanish">Spanish</SelectItem>
                        <SelectItem value="french">French</SelectItem>
                        <SelectItem value="german">German</SelectItem>
                        <SelectItem value="chinese">Chinese</SelectItem>
                        <SelectItem value="japanese">Japanese</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="theme">Theme</Label>
                    <Select
                      value={preferences.theme}
                      onValueChange={value => setPreferences({ ...preferences, theme: value })}
                    >
                      <SelectTrigger id="theme">
                        <SelectValue placeholder="Select Theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System Default</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="font-scale">Font Size</Label>
                    <span className="text-sm text-muted-foreground">
                      {preferences.fontScale[0]}%
                    </span>
                  </div>
                  <Slider
                    id="font-scale"
                    value={preferences.fontScale}
                    onValueChange={value => setPreferences({ ...preferences, fontScale: value })}
                    max={150}
                    min={75}
                    step={5}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Select
                      value={preferences.timezone}
                      onValueChange={value => setPreferences({ ...preferences, timezone: value })}
                    >
                      <SelectTrigger id="timezone">
                        <SelectValue placeholder="Select Timezone" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto-detect</SelectItem>
                        <SelectItem value="est">Eastern Time (ET)</SelectItem>
                        <SelectItem value="cst">Central Time (CT)</SelectItem>
                        <SelectItem value="mst">Mountain Time (MT)</SelectItem>
                        <SelectItem value="pst">Pacific Time (PT)</SelectItem>
                        <SelectItem value="utc">UTC</SelectItem>
                        <SelectItem value="gmt">GMT</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button onClick={handleSavePreferences} className="w-full mt-4">
                  Save Preferences
                </Button>
              </div>
            </TabsContent>

            {/* Privacy Tab */}
            <TabsContent value="privacy" className="space-y-5">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="profile-visibility">Profile Visibility</Label>
                  <Select
                    value={privacy.profileVisibility}
                    onValueChange={value => setPrivacy({ ...privacy, profileVisibility: value })}
                  >
                    <SelectTrigger id="profile-visibility">
                      <SelectValue placeholder="Select Visibility" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">Public - Everyone can see</SelectItem>
                      <SelectItem value="registered">Registered Users Only</SelectItem>
                      <SelectItem value="private">Private - Only When Booked</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message-permission">Who Can Message You</Label>
                  <Select
                    value={privacy.allowMessagesFrom}
                    onValueChange={value => setPrivacy({ ...privacy, allowMessagesFrom: value })}
                  >
                    <SelectTrigger id="message-permission">
                      <SelectValue placeholder="Select Who Can Message You" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="everyone">Everyone</SelectItem>
                      <SelectItem value="booked">Users Who've Booked Sessions</SelectItem>
                      <SelectItem value="approved">Approved Users Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex">
                      <Lock className="h-4 w-4 text-muted-foreground" aria-hidden="false" />
                    </span>
                    <Label htmlFor="activity-visibility">Show My Activity</Label>
                  </div>
                  <Switch
                    id="activity-visibility"
                    checked={privacy.activityVisibility}
                    onCheckedChange={checked =>
                      setPrivacy({ ...privacy, activityVisibility: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex">
                      <Globe className="h-4 w-4 text-muted-foreground" aria-hidden="false" />
                    </span>
                    <Label htmlFor="online-status">Show When I'm Online</Label>
                  </div>
                  <Switch
                    id="online-status"
                    checked={privacy.showOnlineStatus}
                    onCheckedChange={checked =>
                      setPrivacy({ ...privacy, showOnlineStatus: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex">
                      <UserCog className="h-4 w-4 text-muted-foreground" aria-hidden="false" />
                    </span>
                    <Label htmlFor="allow-tagging">Allow Others to Tag Me</Label>
                  </div>
                  <Switch
                    id="allow-tagging"
                    checked={privacy.allowTagging}
                    onCheckedChange={checked => setPrivacy({ ...privacy, allowTagging: checked })}
                  />
                </div>

                <Button onClick={handleSavePrivacy} className="w-full mt-4">
                  Save Privacy Settings
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
