/**
 * Storage Monitor Component for Admin Dashboard
 */
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// Temporarily commented out problematic import to fix build
// import { cleanupAllOrphanedFiles, findOrphanedFiles } from '../../services/supabase/storageCleanupService';
import { HardDrive, Trash2, RefreshCw, AlertCircle, CheckCircle2 } from 'lucide-react';

interface BucketStats {
  name: string;
  size: number;
  fileCount: number;
  lastUpdated: string;
}

interface StorageUsage {
  size: number;
  limit: number;
  percentage: number;
}

/**
 * StorageMonitor component
 * Displays storage usage statistics and provides cleanup tools
 */
export default function StorageMonitor() {
  const [buckets, setBuckets] = useState<BucketStats[]>([]);
  const [storageUsage, setStorageUsage] = useState<StorageUsage>({ size: 0, limit: 0, percentage: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [cleanupResults, setCleanupResults] = useState<string[]>([]);

  useEffect(() => {
    loadStorageStats();
  }, []);

  const loadStorageStats = async () => {
    try {
      setIsLoading(true);
      
      // Get list of buckets
      const { data: bucketList, error: bucketError } = await supabase.storage.listBuckets();
      
      if (bucketError) {
        console.error('Error fetching buckets:', bucketError);
        return;
      }

      const bucketStats: BucketStats[] = [];
      let totalSize = 0;

      // Get stats for each bucket
      for (const bucket of bucketList) {
        try {
          const { data: files, error: filesError } = await supabase.storage
            .from(bucket.name)
            .list('', { limit: 1000 });

          if (filesError) {
            console.error(`Error fetching files for bucket ${bucket.name}:`, filesError);
            continue;
          }

          const bucketSize = files?.reduce((acc, file) => acc + (file.metadata?.size || 0), 0) || 0;
          totalSize += bucketSize;

          bucketStats.push({
            name: bucket.name,
            size: bucketSize,
            fileCount: files?.length || 0,
            lastUpdated: bucket.updated_at || new Date().toISOString()
          });
        } catch (error) {
          console.error(`Error processing bucket ${bucket.name}:`, error);
        }
      }

      setBuckets(bucketStats);
      
      // Set storage usage (assuming 1GB limit for demo)
      const limit = 1024 * 1024 * 1024; // 1GB in bytes
      setStorageUsage({
        size: totalSize,
        limit,
        percentage: (totalSize / limit) * 100
      });

    } catch (error) {
      console.error('Error loading storage stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCleanup = async () => {
    try {
      setIsCleaningUp(true);
      setCleanupResults([]);

      // Temporarily disabled cleanup functionality
      // const results = await cleanupAllOrphanedFiles();
      // setCleanupResults(results);
      
      setCleanupResults(['Cleanup functionality temporarily disabled during build fix']);
      
      // Reload stats after cleanup
      await loadStorageStats();
    } catch (error) {
      console.error('Error during cleanup:', error);
      setCleanupResults(['Error during cleanup: ' + (error as Error).message]);
    } finally {
      setIsCleaningUp(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading storage statistics...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Storage Monitor</h2>
        <Button onClick={loadStorageStats} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Storage Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <HardDrive className="h-5 w-5 mr-2" />
            Storage Usage
          </CardTitle>
          <CardDescription>
            Overall storage consumption across all buckets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Used: {formatBytes(storageUsage.size)}</span>
              <span>Limit: {formatBytes(storageUsage.limit)}</span>
            </div>
            <Progress value={storageUsage.percentage} className="h-2" />
            <div className="text-center text-sm text-muted-foreground">
              {storageUsage.percentage.toFixed(1)}% of storage used
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="buckets" className="space-y-4">
        <TabsList>
          <TabsTrigger value="buckets">Bucket Statistics</TabsTrigger>
          <TabsTrigger value="cleanup">Storage Cleanup</TabsTrigger>
        </TabsList>

        <TabsContent value="buckets" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {buckets.map((bucket) => (
              <Card key={bucket.name}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{bucket.name}</CardTitle>
                  <CardDescription>
                    Last updated: {new Date(bucket.lastUpdated).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Size:</span>
                      <span className="font-medium">{formatBytes(bucket.size)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Files:</span>
                      <span className="font-medium">{bucket.fileCount}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="cleanup" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Trash2 className="h-5 w-5 mr-2" />
                Storage Cleanup
              </CardTitle>
              <CardDescription>
                Remove orphaned files and optimize storage usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
                  <div>
                    <p className="font-medium text-yellow-800">Cleanup Temporarily Disabled</p>
                    <p className="text-sm text-yellow-700">
                      Storage cleanup functionality is temporarily disabled during build optimization.
                    </p>
                  </div>
                </div>
                
                {cleanupResults.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Cleanup Results:</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {cleanupResults.map((result, index) => (
                        <div key={index} className="text-sm text-gray-700">
                          {result}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handleCleanup} 
                disabled={isCleaningUp}
                variant="destructive"
                className="w-full"
              >
                {isCleaningUp ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Cleaning up...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Start Cleanup (Disabled)
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}