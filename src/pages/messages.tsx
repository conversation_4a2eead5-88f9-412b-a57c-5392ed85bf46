import { useState, useEffect, useCallback } from 'react';
import { useRoute, useLocation } from '../lib/next-router-utils';
import { useMessages } from '@/contexts/MessageContext';
import { useAuth } from '@/hooks/use-auth';
// import { ChatWindow } from '@/components/chat/ChatWindow'; // Temporarily removed
import { ConversationList } from '@/components/chat/ConversationList';
import { Button } from '@/components/ui/button';
import { Loader2, MessageSquare } from 'lucide-react';
import type { ConversationWithMessages } from '@shared/schema';
import { ForceReloadButton } from '@/components/chat/ForceReloadButton';

import { toast } from '@/components/ui/use-toast';
import { clearConversationCache } from '@/features/messaging/MessagingProvider';

// Simple hook for media queries
function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
}

export function MessagesPage() {
  const [initialized, setInitialized] = useState(false);
  const [errorState, setErrorState] = useState<string | null>(null);
  const { user } = useAuth();
  const {
    conversations,
    activeConversation,
    setActiveConversation,
    setActiveConversationId,
    isLoading,
    error,
    connectionFailed,
    reconnect,
    fetchConversations,
    setConversations,
  } = useMessages();
  const [match, params] = useRoute('/messages/:id');
  const [, setLocation] = useLocation();

  // Use media query for responsive design
  const isDesktop = useMediaQuery('(min-width: 1024px)');
  const [showConversationList, setShowConversationList] = useState(true);

  // Initialize the page
  useEffect(() => {
    const initPage = async () => {
      try {
        // Check server connectivity
        // In development, point directly to the backend server
        const healthUrl =
          process.env.NODE_ENV === 'development'
            ? 'http://localhost:4005/api/health'
            : `${window.location.origin}/api/health`;

        console.log(`Checking server health at: ${healthUrl}`);

        // Add a small delay to ensure the health endpoint is available
        await new Promise(resolve => setTimeout(resolve, 1000));

        const response = await fetch(healthUrl);
        if (!response.ok) {
          throw new Error(`Server health check failed: ${response.status}`);
        }

        // Force refresh conversations to ensure we have latest data
        await fetchConversations();
        setInitialized(true);
      } catch (error) {
        console.error('Server connectivity error:', error);
        setErrorState('Unable to connect to the messaging service. Please try again.');
      }
    };

    initPage();
  }, [fetchConversations]);

  // Set active conversation from URL parameter
  useEffect(() => {
    if (match && params && params.id && initialized) {
      const conversationId = params.id;
      if (conversationId) {
        try {
          // First check if we already have this conversation in our state
          const existingConversation = conversations.find(c => c.id === conversationId);

          if (existingConversation) {
            // If we have it, set it directly to avoid unnecessary API calls
            setActiveConversation(existingConversation);
            if (!isDesktop) {
              setShowConversationList(false);
            }
          } else {
            // If conversation isn't loaded yet, but we have the ID from the URL,
            // fetch it directly to prevent 404 errors
            const fetchConversation = async () => {
              try {
                const response = await fetch(`/api/conversations/${conversationId}`);

                if (response.ok) {
                  const conversation = await response.json();
                  // Set the conversation and add it to our list if not already there
                  setActiveConversation(conversation);
                  if (!conversations.some(c => c.id === conversation.id)) {
                    setConversations((prev: ConversationWithMessages[]) => [...prev, conversation]);
                  }
                  if (!isDesktop) {
                    setShowConversationList(false);
                  }
                } else {
                  // Handle not found or access denied
                  console.error(
                    `Error fetching conversation ${conversationId}: ${response.status}`
                  );
                  setErrorState(`Conversation not found or you don't have access.`);
                  // Redirect to messages page after a short delay
                  setTimeout(() => {
                    setLocation('/messages');
                  }, 3000);
                }
              } catch (error) {
                console.error('Error fetching conversation:', error);
                setErrorState('Error loading conversation. Please try again.');
              }
            };

            fetchConversation();
          }
        } catch (error) {
          console.error('Error setting active conversation:', error);
          setErrorState('Error loading conversation. Please try again.');
        }
      }
    } else {
      // If no conversation ID in URL, show the conversation list on mobile
      if (!isDesktop) {
        setShowConversationList(true);
      }
      setActiveConversation(null);
    }
  }, [match, params, setActiveConversation, isDesktop, conversations, initialized]);

  // Handle cancellation flow from URL parameters
  useEffect(() => {
    if (!initialized || !user) return;

    // Parse URL search parameters
    const searchParams = new URLSearchParams(window.location.search);
    const action = searchParams.get('action');
    const teacherId = searchParams.get('to');
    const sessionId = searchParams.get('sessionId');
    const bookingId = searchParams.get('bookingId');
    const sessionTitle = searchParams.get('sessionTitle');

    // Check if this is a cancellation intent
    if (action === 'cancel' && teacherId && sessionId && bookingId) {
      console.log('Handling cancellation flow from URL parameters');

      const handleCancellation = async () => {
        try {
          // Find or create a conversation with the teacher
          if (!teacherId) {
            throw new Error('Invalid teacher ID');
          }

          // Find conversation with this teacher
          let conversation = conversations.find(c => {
            return (
              c.participants.some(p => p.id === teacherId) &&
              c.participants.some(p => p.id === user.id)
            );
          });

          // If not found, create a new conversation
          if (!conversation) {
            console.log('No existing conversation found with teacher, creating a new one');
            const response = await fetch('/api/conversations/find-or-create', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                participantIds: [user.id, teacherId],
                title: sessionTitle ? `Regarding session: ${sessionTitle}` : 'Session Cancellation',
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to create conversation');
            }

            conversation = await response.json();
            // Add to conversations list
            setConversations(prev => [...prev, conversation as ConversationWithMessages]);
          }

          // Add a cancellation message to the conversation
          if (!conversation) {
            throw new Error('Failed to find or create conversation');
          }

          const messageContent = `I'd like to cancel my booking for session "${sessionTitle || 'your session'}" (Booking #${bookingId}). Could we discuss this?`;

          // Send a message to the conversation
          await fetch(`/api/conversations/${conversation.id}/messages`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: messageContent,
              senderId: user.id,
            }),
          });

          // Send system notification about cancellation intent
          await fetch(`/api/conversations/${conversation.id}/notes`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              senderId: 0, // System user
              content: `Cancellation requested for booking #${bookingId}`,
              sessionId: sessionId,
              type: 'cancellation',
            }),
          });

          // Set the active conversation
          setActiveConversation(conversation);
          setLocation(`/messages/${conversation.id}`);
          if (!isDesktop) {
            setShowConversationList(false);
          }

          // Clear the URL parameters
          window.history.replaceState({}, document.title, `/messages/${conversation.id}`);
        } catch (error) {
          console.error('Error handling cancellation:', error);
          setErrorState('Failed to process cancellation request. Please try again.');
        }
      };

      handleCancellation();
    }
  }, [
    initialized,
    user,
    conversations,
    isDesktop,
    setConversations,
    setActiveConversation,
    setLocation,
  ]);

  // Reset conversation list visibility when screen size changes
  useEffect(() => {
    if (isDesktop) {
      setShowConversationList(true);
    } else if (activeConversation) {
      setShowConversationList(false);
    }
  }, [isDesktop, activeConversation]);

  // Debugging function to help users with connection issues
  const forceClearCacheAndReconnect = () => {
    try {
      // Clear relevant caches
      localStorage.removeItem('cached_conversations');
      localStorage.removeItem('cached_unread_count');
      localStorage.removeItem('activeConversationId');

      // Force refresh socket connection
      reconnect();

      // Set a small delay to avoid racing conditions
      setTimeout(() => {
        // Force page refresh to completely restart the application
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error in force reconnect:', error);
    }
  };

  // Handle connection or loading errors
  if (connectionFailed || errorState) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh] p-4 bg-white rounded-lg shadow-sm">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h1 className="text-2xl font-bold mb-2 text-red-700">Error</h1>
        <p className="text-center mb-6 text-gray-600">
          {errorState || 'Unable to connect to the messaging service. Please try again.'}
        </p>
        <div className="flex flex-col gap-4 w-full max-w-xs">
          <Button
            onClick={errorState ? () => setLocation('/messages') : reconnect}
            className="w-full bg-primary hover:bg-primary/90"
          >
            {errorState ? 'Go Back' : 'Retry Connection'}
          </Button>
          <Button
            variant="outline"
            onClick={forceClearCacheAndReconnect}
            className="w-full border-gray-300 text-gray-700"
          >
            Force Reconnection (Clear Cache)
          </Button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading && !initialized) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-gray-500">Loading your conversations...</p>
      </div>
    );
  }

  // No conversations found state
  if (initialized && conversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-[80vh] p-6 text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <MessageSquare className="h-8 w-8 text-primary" />
        </div>
        <h2 className="text-2xl font-semibold mb-2">No Conversations Yet</h2>
        <p className="text-gray-500 mb-6 max-w-md">
          You don't have any conversations yet. Start chatting with other users to see your
          conversations here.
        </p>
        <Button onClick={() => setLocation('/')} className="bg-primary hover:bg-primary/90">
          Find Users to Message
        </Button>

        {/* Simple reload button without all the extra debugging tools */}
        <div className="flex items-center gap-2 justify-center pt-4">
          <p className="text-sm text-gray-500">Having trouble seeing your conversations?</p>
          <Button
            variant="ghost"
            size="sm"
            className="text-primary hover:text-primary/90"
            onClick={() => {
              clearConversationCache();
              window.location.reload();
            }}
          >
            Refresh
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Single top-level header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Messages</h1>
        <Button
          variant="ghost"
          size="sm"
          className="text-primary hover:text-primary/90 flex items-center gap-1"
          onClick={() => {
            clearConversationCache();
            fetchConversations();
            toast({
              title: 'Refreshing conversations',
              description: 'Fetching the latest conversations...',
              duration: 2000,
            });
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Refresh
        </Button>
      </div>

      <div className="flex flex-col h-[calc(100vh-4rem)] md:flex-row max-w-6xl mx-auto p-2 md:p-4 bg-white rounded-lg shadow-sm">
        {/* Mobile navigation bar - only show on mobile */}
        {!isDesktop && (
          <div className="flex items-center justify-between py-2 px-4 border-b mb-2">
            {activeConversation && !showConversationList && (
              <Button
                variant="ghost"
                className="text-primary hover:text-primary/80 hover:bg-primary/5"
                onClick={() => {
                  setShowConversationList(true);
                  setLocation('/messages');
                }}
              >
                ← Back to conversations
              </Button>
            )}
          </div>
        )}

        {/* Conversation list - always visible on desktop, conditional on mobile */}
        <div
          className={`${!isDesktop && activeConversation && !showConversationList ? 'hidden' : 'block'}
            md:w-1/3 lg:w-1/4 border-r md:min-h-[80vh] overflow-hidden`}
        >
          <ConversationList
            onSelectConversation={(conversation: ConversationWithMessages) => {
              setActiveConversation(conversation);
              setLocation(`/messages/${conversation.id}`);
              if (!isDesktop) {
                setShowConversationList(false);
              }
            }}
          />
        </div>

        {/* Chat window */}
        <div
          className={`flex-1 md:ml-4 ${!isDesktop && showConversationList ? 'hidden' : 'block'}
            bg-gray-50 rounded-lg overflow-hidden flex flex-col h-full`}
        >
          {activeConversation ? (
            <div className="p-8 text-center">
              <p>Chat functionality temporarily disabled</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-8 text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <MessageSquare className="h-8 w-8 text-primary" />
              </div>
              <p className="text-lg font-medium text-gray-700 mb-2">Select a conversation</p>
              <p className="text-sm text-gray-500 max-w-md">
                Choose from your existing conversations or start a new one by clicking the plus icon
                in the sidebar
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
