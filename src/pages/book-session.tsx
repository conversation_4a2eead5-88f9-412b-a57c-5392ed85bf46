import React, { useState, useEffect, useRef } from 'react';
import { useRoute, useLocation } from '../lib/next-router-utils';
import { Link } from '../components/ui/NextLink';
import { useSession } from '../contexts/SessionContext';
import { useUser } from '../contexts/UserContext';
import { useToast } from '../hooks/use-toast';
import { TimeSlotBooking } from '../components/sessions/TimeSlotBooking';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Loader2, ArrowLeft, Calendar, Clock, CheckCircle, MessageSquare, MessageCircle } from 'lucide-react';
import { formatPrice } from '../lib/utils';
import { SessionWithTeacher } from '@shared/schema';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '../components/ui/dialog';
import { useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '../lib/queryClient';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { getInitials } from '../lib/utils';
// import { startChatWithUser } from '../components/chat/ChatPopupManager'; // Temporarily removed

// Define the TimeSlot interface locally if it's not exported from the component
interface TimeSlot {
  id: number;
  availability_id: number;
  start_time: string;
  end_time: string;
  is_booked: boolean;
  booking_id?: number;
}

// Add the Contact Teacher button that opens a chat with the teacher
const ContactTeacherButton = ({
  teacherId,
  teacherName,
}: {
  teacherId: string;
  teacherName: string;
}) => {
  const handleContactTeacher = (e: React.MouseEvent) => {
    e.preventDefault();
    // startChatWithUser(teacherId); // Temporarily disabled
    console.log('Contact teacher functionality temporarily disabled');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="w-full"
      onClick={handleContactTeacher}
    >
      <MessageCircle className="h-4 w-4 mr-2" />
      Contact Teacher
    </Button>
  );
};

export function BookSessionPage() {
  const [, params] = useRoute('/book-session/:id');
  const sessionId = params?.id;
  const { bookSessionMutation } = useSession();
  const { currentUser } = useUser();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const queryClient = useQueryClient();

  const [session, setSession] = useState<SessionWithTeacher | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [bookingSuccess, setBookingSuccess] = useState(false);
  const confettiRef = useRef<HTMLDivElement>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedPaymentOption, setSelectedPaymentOption] = useState<string | null>(null);

  // Fetch session details
  useEffect(() => {
    if (!sessionId) return;

    const fetchSession = async () => {
      try {
        setIsLoading(true);
        // Use apiRequest instead of fetch to ensure we use the proxy
        const { data, response } = await apiRequest(`/api/sessions/${sessionId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch session details');
        }
        setSession(data);
      } catch (error) {
        console.error('Error fetching session:', error);
        toast({
          title: 'Error',
          description: 'Failed to load session details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();
  }, [sessionId, toast]);

  // Create manual booking refresh function
  const refreshBookings = async () => {
    try {
      setSubmitting(true);

      // Try the regular endpoint first
      try {
        // Use apiRequest instead of fetch to ensure we use the proxy
        const { data: responseData, response } = await apiRequest(
          `/api/users/${currentUser?.id}/bookings?_force=${Date.now()}`
        );

        if (response.ok) {
          console.log(`[BookSession] Regular bookings refresh succeeded`);
          queryClient.invalidateQueries({ queryKey: [`/api/users/${currentUser?.id}/bookings`] });
          return;
        } else {
          console.warn(`[BookSession] Regular bookings refresh failed, trying direct endpoint`);

          // Try the direct endpoint as fallback
          const { data: directData, response: directResponse } = await apiRequest(
            `/api/direct/bookings/${currentUser?.id}?_force=${Date.now()}`
          );

          if (directResponse.ok) {
            console.log(`[BookSession] Direct bookings refresh succeeded`);

            // Manually update the React Query cache with the data
            queryClient.setQueryData([`/api/users/${currentUser?.id}/bookings`], directData);

            // Also store in localStorage as backup
            localStorage.setItem(`user_${currentUser?.id}_bookings`, JSON.stringify(directData));
            return;
          } else {
            console.error(`[BookSession] Both endpoints failed for refreshing bookings`);

            // Try parsing the HTML response for embedded JSON
            const htmlText = await response.text();
            if (
              htmlText.includes('"id":') &&
              htmlText.includes('"userId":') &&
              htmlText.includes('"sessionId":')
            ) {
              try {
                // Extract JSON from HTML
                const startIdx = htmlText.indexOf('[');
                const endIdx = htmlText.lastIndexOf(']') + 1;
                if (startIdx >= 0 && endIdx > startIdx) {
                  const jsonStr = htmlText.substring(startIdx, endIdx);
                  const parsedData = JSON.parse(jsonStr);
                  console.log(
                    `[BookSession] Extracted ${parsedData.length} bookings from HTML response`
                  );

                  // Update React Query cache
                  queryClient.setQueryData([`/api/users/${currentUser?.id}/bookings`], parsedData);

                  // Store in localStorage as backup
                  localStorage.setItem(
                    `user_${currentUser?.id}_bookings`,
                    JSON.stringify(parsedData)
                  );
                  return;
                }
              } catch (parseError) {
                console.error(`[BookSession] Error extracting bookings from HTML:`, parseError);
              }
            }
          }
        }
      } catch (error) {
        console.error(`[BookSession] Error refreshing bookings:`, error);
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Function to handle form submission
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!currentUser) {
      toast({
        title: 'Authentication required',
        description: 'Please log in to book sessions',
        variant: 'destructive',
      });
      navigate('/login');
      return;
    }

    if (!session) {
      toast({
        title: 'Session not found',
        description: "The session you're trying to book is not available",
        variant: 'destructive',
      });
      return;
    }

    if (!selectedPaymentOption) {
      toast({
        title: 'Payment method required',
        description: 'Please select a payment method',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSubmitting(true);

      // Create the booking
      const { data: booking, response } = await apiRequest('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: currentUser.id,
          sessionId: session.id,
          status: 'confirmed',
          paymentStatus: selectedPaymentOption === 'free' ? 'free' : 'paid',
          paymentProcessor: selectedPaymentOption === 'free' ? null : selectedPaymentOption,
        }),
      });

      if (!response.ok) {
        throw new Error(`Booking failed with status: ${response.status}`);
      }
      console.log('Booking created:', booking);

      // Refresh the user's bookings
      await refreshBookings();

      toast({
        title: 'Session booked!',
        description: 'Your session has been successfully booked',
        variant: 'default',
      });

      // Show the success notification and set redirect timer
      setShowSuccess(true);

      setTimeout(() => {
        navigate('/dashboard');
      }, 4000);
    } catch (error) {
      console.error('Error creating booking:', error);
      toast({
        title: 'Booking failed',
        description: 'There was an error booking this session. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle session booking
  const handleBookSession = async () => {
    if (!currentUser) {
      toast({
        title: 'Authentication required',
        description: 'Please log in to book this session.',
        variant: 'destructive',
      });
      navigate('/auth');
      return;
    }

    if (!selectedTimeSlot) {
      toast({
        title: 'Select a time slot',
        description: 'Please select a time slot to book this session.',
        variant: 'destructive',
      });
      return;
    }

    // If this is a paid session, create a payment intent first
    if (session?.price && session.price > 0) {
      try {
        setPaymentProcessing(true);
        const { data: paymentIntent, response: paymentResponse } = await apiRequest(
          '/api/payments/create-intent',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              sessionId: session.id,
              amount: session.price,
            }),
          }
        );

        if (!paymentResponse.ok) {
          throw new Error('Failed to create payment intent');
        }

        // Store booking details in session storage for after payment
        sessionStorage.setItem(
          'pendingBooking',
          JSON.stringify({
            sessionId: sessionId!,
            userId: currentUser.id,
            timeSlotId: selectedTimeSlot.id,
            status: 'confirmed',
            paymentId: paymentIntent.id,
            paymentStatus: 'pending',
            paymentAmount: session.price,
          })
        );

        // Show the payment dialog
        setShowPaymentDialog(true);

        // Simulate payment processing
        toast({
          title: 'Payment processing',
          description: 'Your payment is being processed. Please do not close this window.',
        });

        // Simulate redirect delay
        setTimeout(() => {
          // For demo purposes, we'll just create the booking directly
          // In a real app, this would happen after payment confirmation
          setShowPaymentDialog(false);
          setPaymentProcessing(false);
          completeBookingAfterPayment(paymentIntent.id);
        }, 3000);

        return;
      } catch (error) {
        setPaymentProcessing(false);
        console.error('Payment error:', error);
        toast({
          title: 'Payment error',
          description: 'There was an error processing your payment. Please try again.',
          variant: 'destructive',
        });
        return;
      }
    } else {
      // For free sessions, show confirmation dialog
      setShowConfirmDialog(true);
      return;
    }
  };

  // Function to complete booking after payment
  const completeBookingAfterPayment = (paymentId: string) => {
    const pendingBookingData = sessionStorage.getItem('pendingBooking');
    if (!pendingBookingData) {
      toast({
        title: 'Booking error',
        description: 'Booking data not found. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    const bookingData = JSON.parse(pendingBookingData);
    bookingData.paymentStatus = 'paid'; // Update status to paid

    bookSessionMutation.mutate(bookingData, {
      onSuccess: async () => {
        setBookingSuccess(true);
        setShowConfetti(true);

        // Clear the stored booking data
        sessionStorage.removeItem('pendingBooking');

        // Display confetti animation
        if (showConfetti && confettiRef.current) {
          import('canvas-confetti')
            .then(confettiModule => {
              const confetti = confettiModule.default;
              confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 },
              });
            })
            .catch(err => {
              console.error('Error loading confetti:', err);
            });
        }

        toast({
          title: 'Payment successful!',
          description: 'Your payment was processed and the session has been booked.',
          variant: 'default',
        });

        // Manually force a refetch of user bookings
        try {
          console.log('Manually refetching user bookings before navigation');
          // Attempt to trigger a manual reload of the data
          await apiRequest(`/api/users/${currentUser?.id}/bookings?_force=${Date.now()}`);
        } catch (error) {
          console.error('Error refetching bookings:', error);
        }

        // Navigate to the profile page after a delay
        setTimeout(() => {
          navigate('/profile');
        }, 3000);
      },
      onError: (error: Error) => {
        toast({
          title: 'Booking failed',
          description:
            error.message || 'There was an error completing your booking. Please contact support.',
          variant: 'destructive',
        });
      },
    });
  };

  // Add the confirmFreeBooking function
  const confirmFreeBooking = () => {
    if (!currentUser) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to book a session.',
        variant: 'destructive',
      });
      return;
    }

    // For free sessions, proceed directly with booking
    bookSessionMutation.mutate(
      {
        sessionId: sessionId!,
        userId: currentUser.id,
        timeSlotId: selectedTimeSlot!.id,
        status: 'confirmed',
        paymentStatus: 'paid', // Free sessions are automatically marked as paid
      },
      {
        onSuccess: async () => {
          setShowConfirmDialog(false);
          setBookingSuccess(true);
          setShowConfetti(true);

          // Display confetti animation
          if (showConfetti && confettiRef.current) {
            import('canvas-confetti')
              .then(confettiModule => {
                const confetti = confettiModule.default;
                confetti({
                  particleCount: 100,
                  spread: 70,
                  origin: { y: 0.6 },
                });
              })
              .catch(err => {
                console.error('Error loading confetti:', err);
              });
          }

          toast({
            title: 'Booking successful!',
            description: 'You have successfully booked this free session.',
            variant: 'default',
          });

          // Manually force a refetch of user bookings
          try {
            console.log('Manually refetching user bookings before navigation');
            // Attempt to trigger a manual reload of the data
            await apiRequest(`/api/users/${currentUser?.id}/bookings?_force=${Date.now()}`, {
              headers: {
                Accept: 'application/json',
                'Cache-Control': 'no-cache',
              },
            });
          } catch (error) {
            console.error('Error refetching bookings:', error);
          }

          // Navigate to the profile page after a delay
          setTimeout(() => {
            navigate('/profile');
          }, 3000);
        },
        onError: (error: Error) => {
          setShowConfirmDialog(false);
          toast({
            title: 'Booking failed',
            description:
              error.message || 'There was an error booking this session. Please try again.',
            variant: 'destructive',
          });
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-gray-600">Loading session details...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8 text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Session not found</h1>
        <p className="text-gray-600 mb-6">
          The session you're looking for doesn't exist or has been removed.
        </p>
        <Button asChild>
          <Link href="/">Back to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      {/* Back button */}
      <div className="mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.history.back()}
          className="flex items-center gap-1 px-3 py-2 shadow-sm hover:bg-slate-50"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Session details */}
        <div className="lg:col-span-2">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{session.title}</h1>

          <div className="flex items-center gap-2 mb-6">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">
                {new Date(session.date).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-gray-500 mr-1" />
              <span className="text-sm text-gray-500">{session.duration} min</span>
            </div>
          </div>

          <div className="prose max-w-none mb-8">
            <h2 className="text-xl font-semibold mb-3">About this session</h2>
            <p className="text-gray-700 whitespace-pre-line">{session.description}</p>
          </div>

          {session.learningOutcomes && (
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-100 mb-8">
              <h2 className="text-xl font-semibold mb-3">What you'll learn</h2>
              <ul className="space-y-2">
                {session.learningOutcomes.split('\n').map((outcome, index) => (
                  <li key={index} className="flex gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span>{outcome}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Time slot selector */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Select a time slot</h2>
            <TimeSlotBooking
              sessionId={session.id}
              className="mb-6"
              onSelectTimeSlot={setSelectedTimeSlot}
              onBookingComplete={() => {
                setBookingSuccess(true);
                setTimeout(() => navigate('/profile'), 3000);
              }}
            />
          </div>
        </div>

        {/* Booking card */}
        <div className="lg:col-span-1">
          <Card className="sticky top-8">
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">{formatPrice(session.price)}</h2>
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${session.price === 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-blue-100 text-blue-800'
                    }`}
                >
                  {session.price === 0 ? 'Free' : 'Paid'}
                </span>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Session details</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span className="font-medium">{session.duration} minutes</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{session.type}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-600">Format:</span>
                    <span className="font-medium">{session.format}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-gray-600">Language:</span>
                    <span className="font-medium">{session.language}</span>
                  </li>
                </ul>
              </div>

              {/* Teacher info */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Teacher</h3>
                <div className="flex items-center space-x-4 mb-4">
                  <Avatar className="h-12 w-12">
                    {session.teacher?.avatar ? (
                      <AvatarImage src={session.teacher.avatar} alt={session.teacher.name} />
                    ) : (
                      <AvatarFallback>
                        {getInitials(session.teacher?.name || 'Teacher')}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div>
                    <p className="font-medium">{session.teacher?.name}</p>
                    <p className="text-sm text-gray-600">Teacher</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <ContactTeacherButton
                    teacherId={session.teacher.id}
                    teacherName={session.teacher?.name || 'Teacher'}
                  />
                  <Link href={`/teachers/${session.teacher?.id}`}>View Profile</Link>
                </div>
              </div>

              {/* Confetti container */}
              <div
                ref={confettiRef}
                className="absolute inset-0 overflow-hidden pointer-events-none"
              />

              <div className="relative">
                {/* Success overlay */}
                {bookingSuccess && (
                  <div className="absolute inset-0 flex items-center justify-center bg-green-500 rounded-md z-10">
                    <div className="text-white text-center">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                      <p className="font-medium">Booking Successful!</p>
                    </div>
                  </div>
                )}

                <Button
                  className="w-full py-6 text-lg font-medium"
                  onClick={handleBookSession}
                  disabled={bookSessionMutation.isPending || bookingSuccess || !selectedTimeSlot}
                >
                  {bookSessionMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Processing...
                    </>
                  ) : selectedTimeSlot ? (
                    `Book Session for ${formatPrice(session.price)}`
                  ) : (
                    'Select a time slot'
                  )}
                </Button>
              </div>

              <p className="text-sm text-gray-500 mt-4 text-center">
                By booking this session, you agree to the teacher's cancellation policy.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Free Session Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Free Booking</DialogTitle>
            <DialogDescription>
              You're about to book "{session?.title}" with {session?.teacher?.name}. This is a
              free session.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-2">
              <strong>Session:</strong> {session?.title}
            </p>
            <p className="mb-2">
              <strong>Date:</strong> {new Date(session?.date || '').toLocaleDateString()}
            </p>
            <p className="mb-2">
              <strong>Time:</strong> {new Date(session?.date || '').toLocaleTimeString()}
            </p>
            <p className="mb-2">
              <strong>Duration:</strong> {session?.duration} minutes
            </p>
            <p className="mb-2">
              <strong>Selected Time Slot:</strong>{' '}
              {selectedTimeSlot
                ? `${new Date(selectedTimeSlot.start_time).toLocaleTimeString()} - ${new Date(selectedTimeSlot.end_time).toLocaleTimeString()}`
                : 'None'}
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button onClick={confirmFreeBooking}>Confirm Booking</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Payment Processing Dialog */}
      <Dialog
        open={showPaymentDialog}
        onOpenChange={open => {
          // Only allow closing if not processing
          if (!paymentProcessing) {
            setShowPaymentDialog(open);
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Processing Payment</DialogTitle>
            <DialogDescription>
              Please wait while we process your payment for {session?.title}.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col items-center justify-center py-6">
            <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
            <p className="text-center font-medium">
              Processing payment of {formatPrice(session?.price || 0)}
            </p>
            <p className="text-sm text-gray-500 mt-2 text-center">
              This is a demo payment. In a real application, you would be redirected to a payment
              gateway.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
