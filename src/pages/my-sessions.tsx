import { useState } from 'react';
import { useAuth } from '@/features/auth/AuthContext';
import { useBooking } from '@/features/booking/BookingContext';
import { BookingWithSession } from '@shared/schema';
import { Link } from '../components/ui/NextLink';
import { format } from 'date-fns';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User } from 'lucide-react';
import { formatPrice, formatDuration, getCategoryStyles, getInitials } from '@/lib/utils';
// import { startChatWithUser } from '@/components/chat/ChatPopupManager'; // Temporarily removed

export function MySessions() {
  const { user } = useAuth();
  const { userBookings, isLoadingBookings } = useBooking();
  const [activeTab, setActiveTab] = useState('upcoming');

  // Redirect to auth if not logged in
  if (!user && !isLoadingBookings) {
    window.location.href = '/auth';
    return null;
  }

  // Filter bookings based on active tab
  const getFilteredBookings = () => {
    const now = new Date();

    if (activeTab === 'upcoming') {
      return userBookings.filter(
        booking => new Date(booking.session.date) > now && booking.status !== 'canceled'
      );
    } else if (activeTab === 'past') {
      return userBookings.filter(
        booking => new Date(booking.session.date) < now || booking.status === 'completed'
      );
    } else if (activeTab === 'canceled') {
      return userBookings.filter(booking => booking.status === 'canceled');
    }

    return userBookings;
  };

  const filteredBookings = getFilteredBookings();

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Sessions</h1>
          <p className="text-gray-600">Manage your upcoming and past sessions</p>
        </div>

        <Button asChild>
          <Link href="/create-session">Create New Session</Link>
        </Button>
      </div>

      {isLoadingBookings ? (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-32 w-full rounded-lg" />
          ))}
        </div>
      ) : (
        <Tabs
          defaultValue="upcoming"
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-3 md:w-auto">
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="past">Past</TabsTrigger>
            <TabsTrigger value="canceled">Canceled</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {filteredBookings.length > 0 ? (
              filteredBookings.map(booking => <BookingCard key={booking.id} booking={booking} />)
            ) : (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <p className="text-gray-600">No {activeTab} sessions found.</p>
                {activeTab === 'upcoming' && (
                  <Button asChild className="mt-4">
                    <Link href="/">Browse Sessions</Link>
                  </Button>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

interface BookingCardProps {
  booking: BookingWithSession;
}

function BookingCard({ booking }: BookingCardProps) {
  const { session, status, createdAt } = booking;
  const { id, title, teacher, date, duration, type, price } = session;

  const sessionDate = new Date(date);
  const isPast = sessionDate < new Date();
  const isCanceled = status === 'canceled';

  const categoryStyles = getCategoryStyles(type);

  // Calculate if the session date is within 24 hours
  const isWithin24Hours = () => {
    const now = new Date();
    const timeDiff = sessionDate.getTime() - now.getTime();
    const hoursDiff = timeDiff / (1000 * 60 * 60);
    return hoursDiff <= 24;
  };

  // Open chat popup with reasons as quick replies
  const openCancellationChat = () => {
    if (teacher && teacher.id) {
      // Open chat popup with a special flag to show quick reasons
      // startChatWithUser(teacher.id, undefined, { showCancellationReasons: true }); // Temporarily disabled
      console.log('Chat functionality temporarily disabled');
    }
  };

  return (
    <>
      <Card className={`overflow-hidden ${isCanceled ? 'opacity-75' : ''}`}>
        <CardContent className="p-0">
          <div className="p-6 grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Session Info */}
            <div className="md:col-span-3">
              <div className="flex items-start justify-between">
                <div>
                  <Link href={`/session/${id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                      {title}
                    </h3>
                  </Link>

                  <div className="flex items-center mt-1">
                    <Link href={`/teacher/${teacher.id}`}>
                      <div className="flex items-center hover:text-primary-600 transition-colors">
                        <Avatar className="h-5 w-5 mr-2">
                          <AvatarImage src={teacher.avatar} alt={teacher.name} />
                          <AvatarFallback>{getInitials(teacher.name)}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm text-gray-600">{teacher.name}</span>
                      </div>
                    </Link>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge className={`${categoryStyles.bgColor} ${categoryStyles.textColor}`}>
                    {type}
                  </Badge>
                  <Badge variant={isCanceled ? 'destructive' : isPast ? 'outline' : 'default'}>
                    {isCanceled ? 'Canceled' : isPast ? 'Completed' : 'Upcoming'}
                  </Badge>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-1 sm:grid-cols-3 gap-3">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2 text-primary-500" />
                  {format(sessionDate, 'MMM d, yyyy')}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-2 text-primary-500" />
                  {format(sessionDate, 'h:mm a')} ({formatDuration(duration)})
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <User className="h-4 w-4 mr-2 text-primary-500" />
                  Booked on {format(new Date(createdAt), 'MMM d, yyyy')}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="md:col-span-1 flex flex-col justify-between items-end">
              <div className="text-lg font-bold text-gray-900">{formatPrice(price)}</div>

              <div className="flex flex-col w-full gap-2 mt-4">
                <Button asChild variant="default" disabled={isPast || isCanceled}>
                  <Link href={session.zoomLink || `#`}>
                    {session.zoomLink ? 'Join Session' : 'View Details'}
                  </Link>
                </Button>

                {!isPast && !isCanceled && (
                  <Button variant="outline" className="w-full" onClick={openCancellationChat}>
                    Cancel
                  </Button>
                )}

                {isPast && !isCanceled && (
                  <Button variant="outline" className="w-full">
                    Leave Review
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
