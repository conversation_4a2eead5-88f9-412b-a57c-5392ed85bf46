import { useEffect, useState, useLayoutEffect, useCallback, useMemo, memo } from 'react';
import { useSession } from '@/contexts/SessionContext';
import { useFilter } from '@/features/filter/FilterContext';

import { useDirectSupabase } from '@/lib/DirectSupabaseProvider';
import { Link } from '../components/ui/NextLink';
// import { SearchBar } from '@/features/search/components/SearchBar';
// import { FilterBar } from '@/features/search/components/FilterBar';
// import { MobileFilterBar } from '@/features/search/components/MobileFilterBar';
import { SessionCard } from '@/features/sessions/components/card';
import { TeacherCard } from '@/features/teacher/components';
import { TeacherList } from '@/features/teacher/components';
import { CategoryCard } from '@/features/categories/components/CategoryCard';
import { SessionListItem } from '@/features/sessions/components/list';
import { BackdropCleaner } from '@/features/common/components/BackdropCleaner';
import {
  CATEGORIES,
  TESTIMONIALS,
  FAQS,
} from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { cleanupBackdrops, groupSessionsByTitle } from '@/lib/utils';

export function HomePage() {
  const isMobile = useIsMobile();

  // Get data directly from Supabase
  const { sessions: supabaseSessions, teachers: supabaseTeachers, isLoading: supabaseLoading } = useDirectSupabase();

  // Get session loading state
  const { isLoading: sessionLoading } = useSession();

  const {
    setSearch,
    filterState,
    filteredTeachers,
    sortedSessions,
    viewMode, // Now comes from FilterContext
    setSearchMode,
    toggleViewMode,
  } = useFilter();

  // Combine loading states
  const isLoading = sessionLoading || supabaseLoading;

  // Use a smaller initial display count for faster loading
  const [displayCount, setDisplayCount] = useState<number>(8);

  // Use useMemo to avoid recalculating these on every render
  const limitedSessions = useMemo(() => {
    // Debug log to see what data we're receiving
    console.log('Home page received sessions:', {
      supabaseSessions: supabaseSessions?.length || 0,
      sortedSessions: sortedSessions?.length || 0,
      isLoading: isLoading,
      filterState: filterState.searchMode,
    });

    // IMPORTANT: We're removing the loading check to ensure we always display data
    // This ensures we show mock data even when the app thinks it's still loading
    // if (isLoading) {
    //   console.log('Home page: Still loading, returning empty array');
    //   return [];
    // }

    // Use supabaseSessions directly
    const sessionsToUse = supabaseSessions || [];

    // Decide which sessions to use - prefer sortedSessions if available, otherwise use supabaseSessions
    const sessionsSource = sortedSessions.length > 0 && filterState.searchMode === 'sessions'
      ? sortedSessions
      : sessionsToUse;

    console.log('Using sessions source:', {
      source: sortedSessions.length > 0 && filterState.searchMode === 'sessions' ? 'sortedSessions' : 'supabaseSessions',
      length: sessionsSource.length,
      firstSession: sessionsSource[0] ? {
        id: sessionsSource[0].id,
        title: sessionsSource[0].title,
        type: sessionsSource[0].type || sessionsSource[0].session_type
      } : null
    });

    // Return empty array if no sessions available
    if (sessionsSource.length === 0) {
      console.log('No sessions available from database');
      return [];
    }

    // Map the sessions to the format expected by the components
    const mappedSessions = sessionsSource.map((session: any) => {
      // Find the teacher for this session if using Supabase data
      const teacher = supabaseTeachers?.find((teacher: any) =>
        teacher.id === session.teacher_id ||
        teacher.user_id === session.teacher_id
      );

      return {
        id: session.id,
        title: session.title,
        description: session.description || '',
        date: session.date,
        duration: session.duration || 60,
        price: session.price || 0,
        currency: session.currency || 'USD',
        isPublic: session.is_published !== undefined ? session.is_published :
          (session.isPublic !== undefined ? session.isPublic : true),
        is_public: session.is_published !== undefined ? session.is_published :
          (session.is_public !== undefined ? session.is_public : true),
        is_published: session.is_published !== undefined ? session.is_published :
          (session.isPublic !== undefined ? session.isPublic : true),
        teacherId: session.teacherId || session.teacher_id,
        type: session.type || session.session_type || 'other',
        createdAt: session.createdAt || session.created_at, // Add createdAt for sorting
        updatedAt: session.updatedAt || session.updated_at, // Add updatedAt for completeness
        teacher: session.teacher || (teacher ? {
          id: teacher.id || teacher.user_id,
          name: teacher.name || 'Unknown',
          username: teacher.username || 'unknown',
          avatar: teacher.avatar || teacher.profile_image || '',
        } : {
          id: session.teacher_id || 'unknown',
          name: 'Unknown Teacher',
          username: 'unknown',
          avatar: '',
        }),
      };
    });

    // Group sessions by title to avoid duplicates
    const groupedSessions = groupSessionsByTitle(mappedSessions);

    console.log(`[HomePage] Grouped ${mappedSessions.length} sessions into ${groupedSessions.length} unique sessions`);

    return groupedSessions.slice(0, displayCount);
  }, [supabaseSessions, supabaseTeachers, displayCount, isLoading, sortedSessions, filterState]);

  const limitedTeachers = useMemo(() => {
    // Debug log to see what data we're receiving
    console.log('Home page received teachers:', {
      supabaseTeachers: supabaseTeachers?.length || 0,
      filteredTeachers: filteredTeachers?.length || 0,
      isLoading: isLoading,
      filterState: filterState.searchMode,
    });

    // IMPORTANT: We're removing the loading check to ensure we always display data
    // This ensures we show mock data even when the app thinks it's still loading
    // if (isLoading) {
    //   console.log('Home page: Still loading teachers, returning empty array');
    //   return [];
    // }

    // Decide which teachers to use - prefer filteredTeachers if available, otherwise use supabaseTeachers
    const teachersSource = filteredTeachers.length > 0 && filterState.searchMode === 'teachers'
      ? filteredTeachers
      : supabaseTeachers || [];

    console.log('Using teachers source:', {
      source: filteredTeachers.length > 0 && filterState.searchMode === 'teachers' ? 'filteredTeachers' : 'supabaseTeachers',
      length: teachersSource.length,
      firstTeacher: teachersSource[0] ? {
        id: teachersSource[0].id || teachersSource[0].user_id,
        name: teachersSource[0].name,
      } : null
    });

    // Only generate mock teachers if no data is available AND we're not still loading
    // and there are no filtered teachers being used
    if (teachersSource.length === 0 && !isLoading && filterState.searchMode !== 'teachers') {
      console.log('No teachers available after loading completed, and not in teacher search mode. Data may still be loading...');

      // Return empty array instead of mock data to allow for proper loading states
      // The real data should load once Supabase queries complete
      return [];
    }

    // Map the teachers to the format expected by the components
    const mappedTeachers = teachersSource.map((teacher: any) => {
      // Debug the teacher data to see what's available
      console.log('[HomePage] Processing teacher data:', {
        id: teacher.id || teacher.user_id,
        name: teacher.name || 'Unknown',
        hasSpecializations: !!teacher.specializations,
        specializationsIsArray: Array.isArray(teacher.specializations),
        specializationsLength: teacher.specializations?.length,
        hasProfileSpecializations: !!teacher.profile?.specializations,
        profileSpecializationsIsArray: Array.isArray(teacher.profile?.specializations),
        profileSpecializationsLength: teacher.profile?.specializations?.length,
      });

      // Get specializations from either direct property or nested profile
      const specializations = teacher.specializations ||
        teacher.profile?.specializations ||
        [];

      return {
        id: teacher.id || teacher.user_id,
        name: teacher.name || 'Unknown',
        username: teacher.username || 'unknown',
        avatar: teacher.avatar || teacher.profile_image || '',
        bio: teacher.bio || '',
        isTeacher: true,
        specializations: specializations,
        location: teacher.location || '',
        // PRESERVE ENHANCED COVER PHOTO FIELDS
        coverPhoto: teacher.coverPhoto || teacher.cover_photo,
        cover_photo: teacher.cover_photo,
        // Include the profile object if it exists
        profile: teacher.profile || null,
      };
    });

    return mappedTeachers.slice(0, displayCount);
  }, [supabaseTeachers, displayCount, isLoading, filteredTeachers, filterState]);

  const loadMore = () => {
    setDisplayCount(prev => prev + 8);
  };

  useEffect(() => {
    setSearch('');

    if (isMobile) {
      let portalRoot = document.getElementById('mobile-portal-root');
      if (!portalRoot) {
        portalRoot = document.createElement('div');
        portalRoot.id = 'mobile-portal-root';
        document.body.appendChild(portalRoot);
      }
    }

    cleanupBackdrops();

    return () => {
      const root = document.getElementById('mobile-portal-root');
      if (root && root.childNodes.length === 0) {
        document.body.removeChild(root);
      }

      cleanupBackdrops();
    };
  }, []);

  return (
    <div className="bg-gradient-to-b from-gray-50 to-white">
      <BackdropCleaner />
      <section className="pt-16 pb-10">
        <div className="max-w-5xl mx-auto px-6 md:px-10 lg:px-12">
          <div className="text-center">
            <h1 className="text-3xl tracking-tight font-medium text-gray-900 sm:text-4xl md:text-5xl">
              <span className="block mb-1">Discover and book</span>
              <span className="block text-primary">interactive sessions</span>
            </h1>
            <p className="mt-4 max-w-xl mx-auto text-base text-gray-600 md:text-lg">
              Teach What You Love, Learn What You Need. <br className="hidden sm:block" />
              Connect with Real People Today!
            </p>
          </div>

          <div className="mt-10 max-w-xl mx-auto">
            <div className="mb-5">
              {/* <SearchBar /> */}
              <div className="text-center text-gray-500">Search functionality temporarily disabled</div>
            </div>

            {/* Show FilterBar on desktop and MobileFilterBar on mobile */}
            {/* <div>{isMobile ? <MobileFilterBar /> : <FilterBar />}</div> */}
            <div className="text-center text-gray-500">Filter functionality temporarily disabled</div>
          </div>
        </div>
      </section>

      <section className="py-6 md:py-12">
        <div className="max-w-5xl mx-auto px-6 md:px-10 lg:px-12">
          {/* Session/Teacher and Grid/List Controls - positioned just above session cards */}
          <div className="flex justify-between items-center mb-6">
            {/* Search Mode Tabs - Left side */}
            <div className="flex gap-1">
              <button
                onClick={() => setSearchMode('sessions')}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 ${filterState.searchMode === 'sessions'
                  ? 'text-gray-900'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-[#84A59D]/10'
                  }`}
              >
                Sessions
              </button>
              <button
                onClick={() => setSearchMode('teachers')}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 ${filterState.searchMode === 'teachers'
                  ? 'text-gray-900'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-[#84A59D]/10'
                  }`}
              >
                Teachers
              </button>
            </div>

            {/* View Mode Tabs - Right side */}
            <div className="flex gap-1">
              <button
                onClick={() => {
                  if (viewMode !== 'grid') {
                    toggleViewMode();
                  }
                }}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 ${viewMode === 'grid'
                  ? 'text-gray-900'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-[#84A59D]/10'
                  }`}
              >
                Grid
              </button>
              <button
                onClick={() => {
                  if (viewMode !== 'list') {
                    toggleViewMode();
                  }
                }}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 ${viewMode === 'list'
                  ? 'text-gray-900'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-[#84A59D]/10'
                  }`}
              >
                List
              </button>
            </div>
          </div>

          {/* Content section - simplified since controls are now in FilterBar */}
          {filterState.searchMode === 'sessions' ? (
            <>
              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="h-64 rounded-xl bg-gray-100 animate-pulse"></div>
                  ))}
                </div>
              ) : viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 high-performance-scroll optimized-scroll">
                  {limitedSessions.length > 0 ? (
                    limitedSessions.map((session: any) => (
                      <div
                        key={session.id}
                        className="h-full card-container"
                        style={{ contain: 'content' }}
                      >
                        <SessionCard key={session.id} session={session as any} type="learning" className="" />
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 p-8 text-center bg-red-100 rounded-xl">
                      <h3 className="text-xl font-bold text-red-600">No sessions found</h3>
                      <p className="mt-2">
                        There are no sessions to display. Please check your filters or try again
                        later.
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  {limitedSessions.length > 0 ? (
                    limitedSessions.map((session: any) => (
                      <SessionListItem key={session.id} session={session as any} />
                    ))
                  ) : (
                    <div className="p-8 text-center bg-red-100 rounded-xl">
                      <h3 className="text-xl font-bold text-red-600">No sessions found</h3>
                      <p className="mt-2">
                        There are no sessions to display. Please check your filters or try again
                        later.
                      </p>
                    </div>
                  )}
                </div>
              )}

              {sortedSessions.length > displayCount && (
                <div className="mt-8 flex justify-center">
                  <Button
                    variant="outline"
                    className="group px-6 py-2 border-[#E07A5F] text-[#E07A5F] hover:bg-[#E07A5F] hover:text-white transition-all duration-300"
                    onClick={loadMore}
                  >
                    <span>Load More Sessions</span>
                  </Button>
                </div>
              )}
            </>
          ) : filteredTeachers.length > 0 ? (
            <>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 high-performance-scroll optimized-scroll">
                  {limitedTeachers.map((teacher: any) => (
                    <div
                      key={teacher.id}
                      className="h-full card-container minimal-card-hover"
                      style={{ contain: 'content', willChange: 'transform' }}
                    >
                      <TeacherCard teacher={teacher as any} className="minimal-card-hover" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  {limitedTeachers.map((teacher: any) => (
                    <div key={teacher.id} className="border rounded-lg p-4">
                      <TeacherCard teacher={teacher as any} />
                    </div>
                  ))}
                </div>
              )}

              {filteredTeachers.length > displayCount && (
                <div className="mt-8 flex justify-center">
                  <Button
                    variant="outline"
                    className="group px-6 py-2 border-[#84A59D] text-[#84A59D] hover:bg-[#84A59D] hover:text-white transition-all duration-300"
                    onClick={loadMore}
                  >
                    <span>Load More Teachers</span>
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-500 mb-6">
                  Try adjusting your search or browse our featured sessions
                </p>
                <Button variant="outline" onClick={() => setSearch('')}>
                  View all teachers
                </Button>
              </div>
            </div>
          )}
        </div>
      </section>

      <section className="py-10">
        <div className="max-w-3xl mx-auto px-6 md:px-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm p-6 text-center border border-gray-100">
            <h2 className="text-xl font-medium text-gray-800">Ready to share your expertise?</h2>
            <p className="mt-2 text-sm text-gray-600 max-w-md mx-auto">
              Become a teacher and connect with eager learners. Set your own schedule and rates.
            </p>
            <div className="mt-5 flex flex-wrap justify-center gap-3">
              <Button
                asChild
                variant="outline"
                className="rounded-full border-primary/30 text-primary bg-transparent hover:bg-primary/5 transition-all duration-300"
              >
                <Link href="/create-session">Start Teaching</Link>
              </Button>
              <Button
                variant="ghost"
                asChild
                className="rounded-full text-gray-600 hover:text-primary hover:bg-primary/5 transition-all duration-300"
              >
                <Link href="/auth">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-12">
        <div className="max-w-5xl mx-auto px-6 md:px-10 lg:px-12">
          <div className="text-center mb-8">
            <h2 className="text-xl font-medium text-gray-900">What Our Users Say</h2>
            <p className="mt-2 text-sm text-gray-500">Real experiences from our community</p>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {TESTIMONIALS.slice(0, 3).map(testimonial => (
              <div
                key={testimonial.id}
                className="bg-white/90 backdrop-blur-sm p-5 rounded-xl shadow-sm border border-gray-50
                          transition-all duration-300 hover:shadow-md hover:border-primary/10 hover:translate-y-[-2px]"
              >
                <div className="flex items-center mb-3">
                  <div className="flex text-amber-400">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3 w-3 ${i < Math.floor(testimonial.rating) ? 'fill-current' : i < testimonial.rating ? 'half-filled' : ''}`}
                      />
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">"{testimonial.text}"</p>
                <div className="flex items-center">
                  <img
                    src={testimonial.authorAvatar}
                    alt={testimonial.authorName}
                    className="w-8 h-8 rounded-full mr-2 border border-gray-100"
                  />
                  <div>
                    <h3 className="text-xs font-medium text-gray-900">{testimonial.authorName}</h3>
                    <p className="text-xs text-gray-500">{testimonial.authorTitle}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-12 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-4xl mx-auto px-6 md:px-8">
          <div className="text-center mb-8">
            <h2 className="text-xl font-medium text-gray-900">Frequently Asked Questions</h2>
            <p className="mt-2 text-sm text-gray-500">
              Find answers to common questions about Session Hub
            </p>
          </div>

          <div className="border-0 shadow-sm rounded-xl overflow-hidden bg-white">
            {FAQS.map((faq, index) => (
              <div key={index} className="border-b border-gray-100 last:border-b-0">
                <div className="text-sm font-medium text-gray-800 px-5 py-4">
                  {faq.question}
                </div>
                <div className="px-5 pb-4 pt-1 text-sm text-gray-600">
                  {faq.answer}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}

export function Star({ className }: { className: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
    </svg>
  );
}

// Default export for Next.js pages
export default HomePage;