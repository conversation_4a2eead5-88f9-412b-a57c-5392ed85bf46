import { apiClient } from '../apiClient';
import { Payment, PaymentMethod, PaymentIntent } from '@shared/schema';

/**
 * API client for payments
 */
export const paymentsApi = {
  /**
   * Get all payments for the current user
   * @returns Promise with the payments
   */
  async getPayments(): Promise<Payment[]> {
    try {
      console.log('[API] Getting user payments');
      const { data } = await apiClient.get<Payment[]>('/api/payments');
      return data;
    } catch (error) {
      console.error('[API] Error getting user payments:', error);
      return [];
    }
  },

  /**
   * Get a payment by ID
   * @param paymentId - The ID of the payment
   * @returns Promise with the payment
   */
  async getPayment(paymentId: string): Promise<Payment | null> {
    try {
      console.log(`[API] Getting payment ${paymentId}`);
      const { data } = await apiClient.get<Payment>(`/api/payments/${paymentId}`);
      return data;
    } catch (error) {
      console.error(`[API] Error getting payment ${paymentId}:`, error);
      return null;
    }
  },

  /**
   * Create a payment intent for a session
   * @param sessionId - The ID of the session
   * @param amount - The payment amount
   * @returns Promise with the payment intent
   */
  async createPaymentIntent(sessionId: number, amount: number): Promise<PaymentIntent | null> {
    try {
      console.log(`[API] Creating payment intent for session ${sessionId}`);
      const { data } = await apiClient.post<PaymentIntent>('/api/payments/intent', {
        sessionId,
        amount,
      });
      return data;
    } catch (error) {
      console.error(`[API] Error creating payment intent for session ${sessionId}:`, error);
      return null;
    }
  },

  /**
   * Confirm a payment
   * @param paymentIntentId - The ID of the payment intent
   * @param paymentMethodId - The ID of the payment method
   * @returns Promise with the confirmed payment
   */
  async confirmPayment(paymentIntentId: string, paymentMethodId: string): Promise<Payment | null> {
    try {
      console.log(`[API] Confirming payment ${paymentIntentId}`);
      const { data } = await apiClient.post<Payment>(`/api/payments/confirm`, {
        paymentIntentId,
        paymentMethodId,
      });
      return data;
    } catch (error) {
      console.error(`[API] Error confirming payment ${paymentIntentId}:`, error);
      return null;
    }
  },

  /**
   * Get payment methods for the current user
   * @returns Promise with the payment methods
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      console.log('[API] Getting user payment methods');
      const { data } = await apiClient.get<PaymentMethod[]>('/api/payments/methods');
      return data;
    } catch (error) {
      console.error('[API] Error getting user payment methods:', error);
      return [];
    }
  },

  /**
   * Add a payment method
   * @param paymentMethodId - The ID of the payment method from Stripe
   * @returns Promise with the added payment method
   */
  async addPaymentMethod(paymentMethodId: string): Promise<PaymentMethod | null> {
    try {
      console.log('[API] Adding payment method');
      const { data } = await apiClient.post<PaymentMethod>('/api/payments/methods', {
        paymentMethodId,
      });
      return data;
    } catch (error) {
      console.error('[API] Error adding payment method:', error);
      return null;
    }
  },

  /**
   * Remove a payment method
   * @param paymentMethodId - The ID of the payment method
   * @returns Promise with success status
   */
  async removePaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      console.log(`[API] Removing payment method ${paymentMethodId}`);
      await apiClient.delete(`/api/payments/methods/${paymentMethodId}`);
      return true;
    } catch (error) {
      console.error(`[API] Error removing payment method ${paymentMethodId}:`, error);
      return false;
    }
  },

  /**
   * Set default payment method
   * @param paymentMethodId - The ID of the payment method
   * @returns Promise with success status
   */
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<boolean> {
    try {
      console.log(`[API] Setting default payment method ${paymentMethodId}`);
      await apiClient.put(`/api/payments/methods/${paymentMethodId}/default`);
      return true;
    } catch (error) {
      console.error(`[API] Error setting default payment method ${paymentMethodId}:`, error);
      return false;
    }
  },
};
