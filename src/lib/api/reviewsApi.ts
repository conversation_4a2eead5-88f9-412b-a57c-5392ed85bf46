import { apiClient } from '../apiClient';
import { Review } from '@shared/schema';

/**
 * API client for reviews
 */
export const reviewsApi = {
  /**
   * Get reviews for a session
   * @param sessionId - The ID of the session (UUID string)
   * @returns Promise with the reviews
   */
  async getSessionReviews(sessionId: string): Promise<Review[]> {
    try {
      console.log(`[API] Getting reviews for session ${sessionId}`);
      const { data } = await apiClient.get<Review[]>(`/api/sessions/${sessionId}/reviews`);
      return data;
    } catch (error) {
      console.error(`[API] Error getting reviews for session ${sessionId}:`, error);
      return [];
    }
  },

  /**
   * Create a review for a session
   * @param sessionId - The ID of the session (UUID string)
   * @param review - The review data
   * @returns Promise with the created review
   */
  async createReview(
    sessionId: string,
    review: { rating: number; comment?: string }
  ): Promise<Review | null> {
    try {
      console.log(`[API] Creating review for session ${sessionId}`);
      const { data } = await apiClient.post<Review>(`/api/sessions/${sessionId}/reviews`, review);
      return data;
    } catch (error) {
      console.error(`[API] Error creating review for session ${sessionId}:`, error);
      return null;
    }
  },

  /**
   * Update a review
   * @param sessionId - The ID of the session (UUID string)
   * @param reviewId - The ID of the review
   * @param review - The updated review data
   * @returns Promise with the updated review
   */
  async updateReview(
    sessionId: string,
    reviewId: number,
    review: { rating?: number; comment?: string }
  ): Promise<Review | null> {
    try {
      console.log(`[API] Updating review ${reviewId} for session ${sessionId}`);
      const { data } = await apiClient.put<Review>(
        `/api/sessions/${sessionId}/reviews/${reviewId}`,
        review
      );
      return data;
    } catch (error) {
      console.error(`[API] Error updating review ${reviewId} for session ${sessionId}:`, error);
      return null;
    }
  },

  /**
   * Delete a review
   * @param sessionId - The ID of the session (UUID string)
   * @param reviewId - The ID of the review
   * @returns Promise with success status
   */
  async deleteReview(sessionId: string, reviewId: number): Promise<boolean> {
    try {
      console.log(`[API] Deleting review ${reviewId} for session ${sessionId}`);
      await apiClient.delete(`/api/sessions/${sessionId}/reviews/${reviewId}`);
      return true;
    } catch (error) {
      console.error(`[API] Error deleting review ${reviewId} for session ${sessionId}:`, error);
      return false;
    }
  },
};
