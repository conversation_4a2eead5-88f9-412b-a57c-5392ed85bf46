import { apiClient } from '../apiClient';
import { Message, Conversation } from '@shared/schema';

/**
 * API client for messages
 */
export const messagesApi = {
  /**
   * Get all messages for a conversation
   * @param conversationId - The ID of the conversation
   * @returns Promise with the messages
   */
  async getMessages(conversationId: number): Promise<Message[]> {
    try {
      console.log(`[API] Getting messages for conversation ${conversationId}`);
      const { data } = await apiClient.get<Message[]>(
        `/api/conversations/${conversationId}/messages`
      );
      return data;
    } catch (error) {
      console.error(`[API] Error getting messages for conversation ${conversationId}:`, error);
      return [];
    }
  },

  /**
   * Send a message
   * @param conversationId - The ID of the conversation
   * @param content - The message content
   * @returns Promise with the created message
   */
  async sendMessage(conversationId: number, content: string): Promise<Message | null> {
    try {
      console.log(`[API] Sending message to conversation ${conversationId}`);
      const { data } = await apiClient.post<Message>(
        `/api/conversations/${conversationId}/messages`,
        { content }
      );
      return data;
    } catch (error) {
      console.error(`[API] Error sending message to conversation ${conversationId}:`, error);
      return null;
    }
  },

  /**
   * Mark messages as read
   * @param conversationId - The ID of the conversation
   * @returns Promise with success status
   */
  async markAsRead(conversationId: number): Promise<boolean> {
    try {
      console.log(`[API] Marking messages as read for conversation ${conversationId}`);
      await apiClient.post(`/api/conversations/${conversationId}/read`);
      return true;
    } catch (error) {
      console.error(
        `[API] Error marking messages as read for conversation ${conversationId}:`,
        error
      );
      return false;
    }
  },

  /**
   * Delete a message
   * @param messageId - The ID of the message
   * @returns Promise with success status
   */
  async deleteMessage(messageId: number): Promise<boolean> {
    try {
      console.log(`[API] Deleting message ${messageId}`);
      await apiClient.delete(`/api/messages/${messageId}`);
      return true;
    } catch (error) {
      console.error(`[API] Error deleting message ${messageId}:`, error);
      return false;
    }
  },
};
