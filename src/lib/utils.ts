import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { SortOption } from './types';
import { SessionWithTeacher, UserWithProfile } from '@shared/schema';
import {
  format,
  isToday,
  isTomorrow,
  isThisWeek,
  addWeeks,
  isWithinInterval,
  formatDistance,
  formatDistanceToNow as formatDistanceToNowFns,
} from 'date-fns';

/**
 * Utility for merging and conditionally joining class names in components
 * Combines clsx for conditional classes with tailwind-merge for proper Tailwind CSS class merging
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format price display
export function formatPrice(price: number): string {
  if (price === 0) return 'Free';
  return `$${price.toFixed(0)}`;
}

// Format currency display with dollar sign
export function formatCurrency(amount: number): string {
  if (amount === 0) return 'Free';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

// Format duration in minutes to a readable format
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} min`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
  }

  return `${hours} ${hours === 1 ? 'hour' : 'hours'} ${remainingMinutes} min`;
}

// Format date display
export function formatDate(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(dateObj.getTime())) {
      console.error('Invalid date provided to formatDate:', date);
      return 'Invalid date';
    }

    if (isToday(dateObj)) {
      return `Today, ${format(dateObj, 'h:mm a')}`;
    }

    if (isTomorrow(dateObj)) {
      return `Tomorrow, ${format(dateObj, 'h:mm a')}`;
    }

    return format(dateObj, 'MMM d, yyyy h:mm a');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}

// Format date without year and in more user-friendly format
export function formatDateShort(date: Date | string): string {
  try {
    const d = new Date(date);

    if (isNaN(d.getTime())) {
      console.error('Invalid date provided to formatDateShort:', date);
      return 'Invalid date';
    }

    // Get month name and day
    const month = d.toLocaleString('default', { month: 'short' });
    const day = d.getDate();

    // Format time in 12-hour format
    let hours = d.getHours();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // The hour '0' should be '12'
    const minutes = d.getMinutes().toString().padStart(2, '0');

    return `${month} ${day}, ${hours}:${minutes} ${ampm}`;
  } catch (error) {
    console.error('Error formatting date short:', error);
    return 'Invalid date';
  }
}

// Process date filter
export function filterSessionsByDate(
  sessions: SessionWithTeacher[],
  dateOption: string
): SessionWithTeacher[] {
  if (dateOption === 'All Dates') return sessions;

  return sessions.filter(session => {
    const sessionDate = new Date(session.date);

    switch (dateOption) {
      case 'Today':
        return isToday(sessionDate);
      case 'Tomorrow':
        return isTomorrow(sessionDate);
      case 'This Week':
        return isThisWeek(sessionDate, { weekStartsOn: 1 });
      case 'Next Week':
        const nextWeekStart = addWeeks(new Date(), 1);
        const nextWeekEnd = addWeeks(nextWeekStart, 1);
        return isWithinInterval(sessionDate, { start: nextWeekStart, end: nextWeekEnd });
      default:
        return true;
    }
  });
}

// Process price filter
export function filterSessionsByPrice(
  sessions: SessionWithTeacher[],
  priceOption: string
): SessionWithTeacher[] {
  if (priceOption === 'All Prices') return sessions;

  return sessions.filter(session => {
    switch (priceOption) {
      case 'Free':
        return session.price === 0;
      case 'Under $25':
        return session.price > 0 && session.price < 25;
      case '$25-$50':
        return session.price >= 25 && session.price <= 50;
      case '$50-$100':
        return session.price > 50 && session.price <= 100;
      case 'Over $100':
        return session.price > 100;
      default:
        return true;
    }
  });
}

// Sort sessions based on option
export function sortSessions(
  sessions: SessionWithTeacher[],
  sortOption: SortOption
): SessionWithTeacher[] {
  const sessionsCopy = [...sessions];

  switch (sortOption) {
    case 'Price: Low to High':
      return sessionsCopy.sort((a, b) => a.price - b.price);
    case 'Price: High to Low':
      return sessionsCopy.sort((a, b) => b.price - a.price);
    case 'Top Rated':
      return sessionsCopy.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    case 'Most Popular':
      return sessionsCopy.sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
    case 'Recently Added':
      return sessionsCopy.sort((a, b) => {
        const aCreated = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const bCreated = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return bCreated - aCreated; // Most recent first
      });
    case 'Date: Soonest':
      return sessionsCopy.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    case 'Duration: Shortest':
      return sessionsCopy.sort((a, b) => a.duration - b.duration);
    case 'Duration: Longest':
      return sessionsCopy.sort((a, b) => b.duration - a.duration);
    default:
      // For "Recommended" or any other case, use a combination of rating and recency
      return sessionsCopy.sort((a, b) => {
        const aRating = a.rating || 0;
        const bRating = b.rating || 0;
        const scoreA = aRating * 0.7 + (1 / (Date.now() - new Date(a.date).getTime())) * 0.3;
        const scoreB = bRating * 0.7 + (1 / (Date.now() - new Date(b.date).getTime())) * 0.3;
        return scoreB - scoreA;
      });
  }
}

// Truncate text with ellipsis
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

// Create avatar initials from name
export function getInitials(name: string): string {
  if (!name) return '';
  const parts = name.split(' ');
  if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
}

// Get a unique list of languages from sessions
export function getUniqueLanguagesFromSessions(sessions: SessionWithTeacher[]): string[] {
  const languages = new Set<string>();
  sessions.forEach(session => languages.add(session.language));
  return Array.from(languages);
}

// Generate a random color for avatars without images
export function getRandomAvatarColor(): string {
  const colors = [
    'bg-red-500',
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-teal-500',
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

// Format category styles
export function getCategoryStyles(category: string): {
  bgColor: string;
  textColor: string;
  icon: string;
} {
  const categoryMap: Record<string, { bgColor: string; textColor: string; icon: string }> = {
    Yoga: { bgColor: 'bg-primary-100', textColor: 'text-primary-800', icon: 'fas fa-spa' },
    Pilates: { bgColor: 'bg-primary-100', textColor: 'text-primary-800', icon: 'fas fa-heartbeat' },
    Language: { bgColor: 'bg-blue-100', textColor: 'text-blue-800', icon: 'fas fa-language' },
    Music: { bgColor: 'bg-secondary-100', textColor: 'text-secondary-800', icon: 'fas fa-music' },
    Dance: { bgColor: 'bg-purple-100', textColor: 'text-purple-800', icon: 'fas fa-running' },
    Cooking: { bgColor: 'bg-red-100', textColor: 'text-red-800', icon: 'fas fa-utensils' },
    Art: { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', icon: 'fas fa-palette' },
    Academic: { bgColor: 'bg-pink-100', textColor: 'text-pink-800', icon: 'fas fa-book' },
    Professional: { bgColor: 'bg-gray-100', textColor: 'text-gray-800', icon: 'fas fa-briefcase' },
    Health: { bgColor: 'bg-green-100', textColor: 'text-green-800', icon: 'fas fa-heart' },
    Fitness: { bgColor: 'bg-green-100', textColor: 'text-green-800', icon: 'fas fa-dumbbell' },
    DIY: { bgColor: 'bg-orange-100', textColor: 'text-orange-800', icon: 'fas fa-tools' },
    Beauty: { bgColor: 'bg-pink-100', textColor: 'text-pink-800', icon: 'fas fa-gem' },
    Therapy: { bgColor: 'bg-indigo-100', textColor: 'text-indigo-800', icon: 'fas fa-brain' },
    Relationships: { bgColor: 'bg-rose-100', textColor: 'text-rose-800', icon: 'fas fa-heart' },
    Spirituality: { bgColor: 'bg-teal-100', textColor: 'text-teal-800', icon: 'fas fa-pray' },
  };

  return (
    categoryMap[category] || {
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
      icon: 'fas fa-question',
    }
  );
}

// Format relative time (e.g. "2 hours ago")
export function formatTimeAgo(date: Date): string {
  try {
    return formatDistance(date, new Date(), { addSuffix: true });
  } catch (err) {
    return 'Invalid date';
  }
}

// Sort teachers based on option
export function sortTeachers(
  teachers: UserWithProfile[],
  sortOption: string
): UserWithProfile[] {
  // Create a copy to avoid mutating the original array
  const sortedTeachers = [...teachers];

  switch (sortOption) {
    case 'Recommended':
      // For recommended, prioritize verified teachers with high ratings
      return sortedTeachers.sort((a, b) => {
        const ratingA = a.profile?.rating || 0;
        const ratingB = b.profile?.rating || 0;
        // Sort by rating first, and then by review count if ratings are equal
        return ratingB - ratingA || (b.profile?.reviewCount || 0) - (a.profile?.reviewCount || 0);
      });

    case 'Top Rated':
      return sortedTeachers.sort((a, b) => (b.profile?.rating || 0) - (a.profile?.rating || 0));

    case 'Most Popular':
      return sortedTeachers.sort(
        (a, b) => (b.profile?.reviewCount || 0) - (a.profile?.reviewCount || 0)
      );

    case 'Recently Added':
      return sortedTeachers.sort((a, b) => {
        const aCreated = a.profile?.createdAt ? new Date(a.profile.createdAt).getTime() : 0;
        const bCreated = b.profile?.createdAt ? new Date(b.profile.createdAt).getTime() : 0;
        return bCreated - aCreated; // Most recent first
      });

    case 'Most Sessions':
      // Sort by review count as a proxy for session count
      return sortedTeachers.sort(
        (a, b) => (b.profile?.reviewCount || 0) - (a.profile?.reviewCount || 0)
      );

    case 'Most Experience':
      // Sort by review count as a proxy for experience
      return sortedTeachers.sort(
        (a, b) => (b.profile?.reviewCount || 0) - (a.profile?.reviewCount || 0)
      );

    case 'Recently Active':
      // We don't have lastActive, so just sort by rating as a proxy for activity
      return sortedTeachers.sort((a, b) => (b.profile?.rating || 0) - (a.profile?.rating || 0));

    case 'Alphabetical A-Z':
      return sortedTeachers.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    case 'Alphabetical Z-A':
      return sortedTeachers.sort((a, b) => (b.name || '').localeCompare(a.name || ''));

    default:
      return sortedTeachers;
  }
}

// Function to get a default cover photo
export function getDefaultCoverPhoto(userId?: string): string {
  // Use reliable Unsplash URLs for default cover photos
  const defaultCovers = [
    'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1566041510639-8d95a2490bfb?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1559251606-c623743a6d76?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=1200&auto=format&fit=crop&q=80'
  ];

  // If userId is provided, use it to deterministically select a cover
  if (userId) {
    // Use a simple hash function to convert userId to a number
    const hash = userId.split('').reduce((acc, char) => {
      return acc + char.charCodeAt(0);
    }, 0);

    // Use the hash to select a cover
    const index = hash % defaultCovers.length;
    return defaultCovers[index];
  }

  // If no userId is provided, use the first cover as default
  return defaultCovers[0];
}

// Create a cache that we can clear when needed
const sessionCoverImageCache = new Map<string, string>();

// Cache clear function for debugging/refreshing
export function clearSessionCoverImageCache(): void {
  sessionCoverImageCache.clear();
  fallbackImageCache.clear();
  console.log('[getSessionCoverImage] All image caches cleared');
}

// Force refresh a specific session's image
export function forceRefreshSessionImage(sessionId: string | number, sessionType?: string): void {
  const normalizedSessionId = typeof sessionId === 'string' ? sessionId : String(sessionId);
  const normalizedSessionType = sessionType || 'session';

  // Clear all cache entries for this session
  const keysToDelete: string[] = [];
  sessionCoverImageCache.forEach((value, key) => {
    if (key.includes(normalizedSessionId)) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => {
    sessionCoverImageCache.delete(key);
  });

  console.log(`[forceRefreshSessionImage] Cleared ${keysToDelete.length} cache entries for session ${normalizedSessionId}`);
}

// Make cache clear function available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).clearSessionImageCache = clearSessionCoverImageCache;
}

export function getSessionCoverImage(
  sessionType: string | undefined,
  sessionId: number | string,
  customImageUrl?: string | null
): string {
  // Normalize session type and ID
  const normalizedSessionType = sessionType || 'session';
  const normalizedSessionId = typeof sessionId === 'string' ? sessionId : String(sessionId);

  console.log(`[getSessionCoverImage] Called for session ${normalizedSessionId} of type ${normalizedSessionType}`);
  console.log(`[getSessionCoverImage] Custom image URL provided: ${customImageUrl ? (customImageUrl.startsWith('data:') ? 'base64 data URL' : customImageUrl) : 'none'}`);

  // Create a cache key
  const cacheKey = `${normalizedSessionId}-${normalizedSessionType}-${customImageUrl || ''}`;

  // Check if we have this image URL cached
  if (sessionCoverImageCache.has(cacheKey)) {
    const cachedUrl = sessionCoverImageCache.get(cacheKey)!;
    console.log(`[getSessionCoverImage] Using cached URL: ${cachedUrl.startsWith('data:') ? 'base64 data URL' : cachedUrl}`);
    return cachedUrl;
  }

  let imageUrl: string;

  // First check if there's a custom image URL provided
  if (customImageUrl && customImageUrl.trim() !== '') {
    // Log the original URL for debugging
    console.log(`[getSessionCoverImage] Processing custom image URL: ${customImageUrl.startsWith('data:') ? 'base64 data URL' : customImageUrl}`);

    // If it's a base64 data URL, use it directly
    if (customImageUrl.startsWith('data:')) {
      imageUrl = customImageUrl;
      console.log('[getSessionCoverImage] Using base64 data URL directly');
    }
    // If the URL is already a full URL (starts with http), use it directly
    else if (customImageUrl.startsWith('http')) {
      imageUrl = customImageUrl;
      console.log('[getSessionCoverImage] Using direct URL:', imageUrl);

      // Check if it's an Unsplash URL and ensure it has the right parameters
      if (customImageUrl.includes('unsplash.com')) {
        // Make sure it has the right parameters for proper sizing
        if (!customImageUrl.includes('w=')) {
          imageUrl = `${customImageUrl}${customImageUrl.includes('?') ? '&' : '?'}w=800&auto=format&fit=crop`;
          console.log('[getSessionCoverImage] Enhanced Unsplash URL:', imageUrl);
        }
      }
    }
    // If the URL is a Supabase storage path (not starting with http), construct the full URL
    else {
      const supabaseUrl = 'https://frksndjujrbjhlrcjvtf.supabase.co';

      // Check various Supabase storage path formats
      if (customImageUrl.includes('storage/v1/object/public')) {
        // It's already a storage path, just make sure it has the full URL
        imageUrl = customImageUrl.startsWith('/')
          ? `${supabaseUrl}${customImageUrl}`
          : `${supabaseUrl}/${customImageUrl}`;
      } else if (customImageUrl.startsWith('session-types/')) {
        // Direct bucket path for session-types
        imageUrl = `${supabaseUrl}/storage/v1/object/public/${customImageUrl}`;
      } else if (customImageUrl.startsWith('sessions/')) {
        // Direct bucket path for sessions
        imageUrl = `${supabaseUrl}/storage/v1/object/public/${customImageUrl}`;
      } else {
        // Default path in sessions bucket
        imageUrl = `${supabaseUrl}/storage/v1/object/public/session-types/${normalizedSessionType.toLowerCase()}/cover.jpg`;
      }

      console.log('[getSessionCoverImage] Constructed Supabase URL:', imageUrl);
    }
  }
  // Handle null or undefined session type
  else if (!normalizedSessionType) {
    imageUrl = 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80'; // Real default image
    console.log('[getSessionCoverImage] Using default fallback image for null/undefined session type');
  } else {
    // Check for [SAMPLE] prefix and extract the actual session type
    const isSampleSession = normalizedSessionType.includes('[SAMPLE]');
    const sessionTypeWithoutSample = isSampleSession
      ? normalizedSessionType.replace(/\[SAMPLE\]\s*/i, '').trim()
      : normalizedSessionType;
    const normalizedType = sessionTypeWithoutSample.toLowerCase().trim();

    console.log(`[getSessionCoverImage] Normalized session type: ${normalizedType}`);

    // Try to get a type-specific image from Supabase storage
    const supabaseUrl = 'https://frksndjujrbjhlrcjvtf.supabase.co';
    // Add version parameter for cache busting when images are updated
    const imageVersion = 'v=2025-05-25'; // Update this when images change
    imageUrl = `${supabaseUrl}/storage/v1/object/public/session-types/${normalizedType}/cover.jpg?${imageVersion}`;

    console.log(`[getSessionCoverImage] Using type-specific image: ${imageUrl}`);
  }

  // Store in cache for future use
  sessionCoverImageCache.set(cacheKey, imageUrl);
  console.log(`[getSessionCoverImage] Final image URL (cached): ${imageUrl.startsWith('data:') ? 'base64 data URL' : imageUrl}`);

  return imageUrl;
}

// Cache for fallback images to avoid repeated errors
const fallbackImageCache = new Set<string>();

/**
 * Utility function to handle image loading errors consistently
 * This prevents infinite loops when an image fails to load
 */
export function handleImageError(
  e: React.SyntheticEvent<HTMLImageElement, Event>,
  context: string
): void {
  const target = e.target as HTMLImageElement;
  const originalSrc = target.src;

  // If we've already handled this error, don't do anything
  if (target.getAttribute('data-error-handled') === 'true') {
    return;
  }

  // Check if this URL is already known to fail
  if (fallbackImageCache.has(originalSrc)) {
    // Mark as handled to prevent infinite loop
    target.setAttribute('data-error-handled', 'true');
    target.onerror = null;

    // Set the fallback image from a reliable placeholder service
    const fallbackImage = 'https://placehold.co/600x400?text=Image+Not+Available';
    target.src = fallbackImage;
    return;
  }

  // Mark as handled to prevent infinite loop
  target.setAttribute('data-error-handled', 'true');
  target.onerror = null;

  // Set the fallback image from a reliable placeholder service
  const fallbackImage = 'https://placehold.co/600x400?text=Image+Not+Available';
  target.src = fallbackImage;

  // Add to cache of known failing URLs
  fallbackImageCache.add(originalSrc);

  // Only log in development to reduce console noise
  if (process.env.NODE_ENV === 'development') {
    console.warn(
      `Image error for ${context}:\n- Original src: ${originalSrc}\n- Fallback: ${fallbackImage}`
    );
  }

  // Add a data attribute to help with debugging
  target.setAttribute('data-original-src', originalSrc);
}

/**
 * Utility function to clean up backdrop elements from the DOM
 * This helps fix issues with overlapping/stale backdrop elements
 */
export function cleanupBackdrops(): void {
  try {
    // Clean blur backdrops
    const blurBackdrops = document.querySelectorAll('.backdrop-blur-\\[1px\\]');
    blurBackdrops.forEach(el => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });

    // Clean any other backdrop overlays with bg-black class
    const blackBackdrops = document.querySelectorAll('.bg-black\\/20, .bg-black\\/40');
    blackBackdrops.forEach(el => {
      if (el && el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });

    // Also clean up portal containers if they're empty
    const portalRoots = [
      document.getElementById('mobile-filter-root'),
      document.getElementById('mobile-portal-root'),
    ];

    portalRoots.forEach(root => {
      if (root && root.childNodes.length === 0 && root.parentNode) {
        root.parentNode.removeChild(root);
      }
    });
  } catch (err) {
    console.error('Error cleaning up backdrop elements:', err);
  }
}

/**
 * Formats a date as a relative time string (e.g. "5 minutes ago")
 * @param date The date to format
 * @returns A string representation of the relative time
 */
export function formatDistanceToNow(date: Date): string {
  return formatDistanceToNowFns(date, { addSuffix: true });
}

// Format date for display in long format (e.g., "Monday, January 1, 2023")
export function formatDateLong(date: Date): string {
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// Format time for display (e.g., "3:30 PM")
export function formatTime(date: Date): string {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Format error messages from various error objects for user display
 * @param error The error object to format
 * @returns A user-friendly error message string
 */
export function formatError(error: any): string {
  // If the error is a string, return it directly
  if (typeof error === 'string') return error;

  // If it's an Error object with a message
  if (error && error.message) return error.message;

  // If it's an API error response with a message or error field
  if (error && typeof error === 'object') {
    if (error.data && error.data.message) return error.data.message;
    if (error.response && error.response.data) {
      const { data } = error.response;
      if (typeof data === 'string') return data;
      if (data.message) return data.message;
      if (data.error) return data.error;
    }
  }

  // Default fallback message
  return 'An unexpected error occurred. Please try again.';
}

/**
 * Format timezone for display
 * @param timezone The timezone string (e.g., "America/New_York")
 * @returns A user-friendly timezone string (e.g., "New York (UTC-5, EST)")
 */
export function formatTimezone(timezone: string): string {
  if (!timezone) return 'UTC';

  console.log('[Utils] Formatting timezone:', timezone);

  try {
    // Handle special case for timezone strings that already include GMT offset
    if (timezone.includes('GMT+') || timezone.includes('GMT-')) {
      console.log('[Utils] Timezone already includes GMT offset');
      return timezone;
    }

    return formatTimezoneForDisplay(timezone);
  } catch (error) {
    console.error('Error formatting timezone:', error);

    // If there's an error, just return the original timezone
    // This handles cases where the timezone string might not be a valid IANA timezone
    return timezone;
  }
}

/**
 * Format timezone for display with consistent formatting and daylight saving time awareness
 * @param timezone The IANA timezone string (e.g., "America/New_York")
 * @returns A user-friendly timezone string (e.g., "New York (GMT-5)")
 */
export function formatTimezoneForDisplay(timezone: string): string {
  try {
    const date = new Date();

    // Get the UTC offset
    const utcOffset =
      new Intl.DateTimeFormat('en-GB', {
        timeZone: timezone,
        timeZoneName: 'longOffset',
      })
        .formatToParts(date)
        .find(part => part.type === 'timeZoneName')?.value || '';

    // Get the timezone abbreviation (EST, PST, etc.) which indicates DST status
    const abbreviation =
      new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        timeZoneName: 'short',
      })
        .formatToParts(date)
        .find(part => part.type === 'timeZoneName')?.value || '';

    // Format the location name from the IANA identifier
    let location = timezone;

    // Extract the city name from the IANA identifier
    if (timezone.includes('/')) {
      location = timezone.split('/').pop()?.replace(/_/g, ' ') || timezone;
    }

    // Check if we're in daylight saving time
    const isDST = abbreviation.includes('D'); // Most DST abbreviations include 'D' (EDT, PDT, etc.)

    // Show location and GMT offset with DST indicator if applicable
    const formattedTimezone = `${location} (${utcOffset})${isDST ? ' ☀️' : ''}`;
    console.log('[Utils] Formatted timezone for display:', formattedTimezone);
    console.log('[Utils] DST active:', isDST);
    return formattedTimezone;
  } catch (error) {
    console.error('Error in formatTimezoneForDisplay:', error);
    return timezone;
  }
}

/**
 * Convert a display timezone or GMT string to IANA format
 * @param timezone The timezone string (e.g., "(GMT+12:00) Kamchatka, Marshall Islands")
 * @returns The IANA timezone string (e.g., "Pacific/Majuro")
 */
export function normalizeTimezone(timezone: string): string {
  console.log('[Utils] Normalizing timezone:', timezone);

  // If it's already an IANA timezone, return it
  if (timezone.includes('/')) {
    return timezone;
  }

  try {
    // Check for specific location names in the timezone string
    if (timezone.includes('Dawson') || timezone.includes('Yukon')) {
      console.log(`[Utils] Found Dawson/Yukon in timezone string, mapping to America/Dawson`);
      return 'America/Dawson';
    }

    // Handle GMT format like "(GMT+12:00) Kamchatka, Marshall Islands"
    if (timezone.includes('GMT+') || timezone.includes('GMT-')) {
      // Extract the GMT offset
      const offsetMatch = timezone.match(/GMT([+-]\d{1,2}:?\d{0,2})/);
      if (offsetMatch) {
        const offset = offsetMatch[1];
        console.log('[Utils] Extracted GMT offset:', offset);

        // Try to find a matching IANA timezone
        // This is a simplified mapping - a complete solution would need a full timezone database
        const gmtToIana: Record<string, string> = {
          '+12:00': 'Pacific/Auckland', // New Zealand
          '+11:00': 'Pacific/Guadalcanal',
          '+10:00': 'Australia/Sydney',
          '+9:00': 'Asia/Tokyo',
          '+8:00': 'Asia/Shanghai',
          '+7:00': 'Asia/Bangkok',
          '+6:00': 'Asia/Dhaka',
          '+5:30': 'Asia/Kolkata',
          '+5:00': 'Asia/Karachi',
          '+4:00': 'Asia/Dubai',
          '+3:00': 'Europe/Moscow',
          '+2:00': 'Europe/Kiev',
          '+1:00': 'Europe/Paris',
          '+0:00': 'Europe/London',
          '-1:00': 'Atlantic/Azores',
          '-2:00': 'Atlantic/South_Georgia',
          '-3:00': 'America/Sao_Paulo',
          '-4:00': 'America/New_York',
          '-5:00': 'America/Chicago',
          '-6:00': 'America/Denver',
          '-7:00': 'America/Dawson', // Changed from Los_Angeles to Dawson for Yukon
          '-8:00': 'Pacific/Pitcairn',
          '-9:00': 'Pacific/Gambier',
          '-10:00': 'Pacific/Honolulu',
          '-11:00': 'Pacific/Niue',
          '-12:00': 'Pacific/Baker_Island',
        };

        // Normalize the offset format
        const normalizedOffset = offset
          .replace(/(\+\d{1,2})(?::00)?/, '$1:00')
          .replace(/(-\d{1,2})(?::00)?/, '$1:00');

        if (gmtToIana[normalizedOffset]) {
          console.log('[Utils] Mapped to IANA timezone:', gmtToIana[normalizedOffset]);
          return gmtToIana[normalizedOffset];
        }

        // If we can't find a mapping, use UTC
        console.log('[Utils] No mapping found for offset, using UTC');
        return 'UTC';
      }
    }

    // Handle common timezone abbreviations and specific locations
    const abbreviationToIana: Record<string, string> = {
      EST: 'America/New_York',
      EDT: 'America/New_York',
      CST: 'America/Chicago',
      CDT: 'America/Chicago',
      MST: 'America/Denver',
      MDT: 'America/Denver',
      PST: 'America/Los_Angeles',
      PDT: 'America/Los_Angeles',
      AEST: 'Australia/Sydney',
      AEDT: 'Australia/Sydney',
      NZST: 'Pacific/Auckland',
      NZDT: 'Pacific/Auckland',
      // Add specific location mappings
      Dawson: 'America/Dawson',
      Yukon: 'America/Dawson',
      'Dawson, Yukon': 'America/Dawson',
    };

    if (abbreviationToIana[timezone]) {
      console.log('[Utils] Mapped abbreviation to IANA timezone:', abbreviationToIana[timezone]);
      return abbreviationToIana[timezone];
    }

    // If all else fails, return UTC
    console.log('[Utils] Could not normalize timezone, using UTC');
    return 'UTC';
  } catch (error) {
    console.error('Error normalizing timezone:', error);
    return 'UTC';
  }
}

/**
 * Detect the user's timezone
 * @returns The detected timezone string (e.g., "America/New_York")
 */
export function detectUserTimezone(): string {
  try {
    // Get the user's timezone using Intl.DateTimeFormat
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    console.log(`[Utils] Detected user timezone: ${timezone}`);
    return timezone;
  } catch (error) {
    console.error('Error detecting timezone:', error);
    return 'UTC';
  }
}

/**
 * Check if a daylight saving time change is coming up soon
 * @param timezone The IANA timezone string
 * @param daysThreshold Number of days to look ahead (default: 14)
 * @returns Object with information about the upcoming DST change, or null if none
 */
export function checkUpcomingDSTChange(
  timezone: string,
  daysThreshold = 14
): { date: Date; type: 'start' | 'end' } | null {
  try {
    const today = new Date();
    const currentDST = isDaylightSavingTime(today, timezone);

    // Check each day for the next two weeks
    for (let i = 1; i <= daysThreshold; i++) {
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + i);

      const futureDST = isDaylightSavingTime(futureDate, timezone);

      // If DST status changes, we found a transition
      if (currentDST !== futureDST) {
        return {
          date: futureDate,
          type: futureDST ? 'start' : 'end',
        };
      }
    }

    return null; // No DST change in the next two weeks
  } catch (error) {
    console.error('Error checking for DST changes:', error);
    return null;
  }
}

/**
 * Check if a specific date is in daylight saving time for a given timezone
 * @param date The date to check
 * @param timezone The IANA timezone string
 * @returns Boolean indicating if the date is in DST
 */
export function isDaylightSavingTime(date: Date, timezone: string): boolean {
  try {
    // Get the timezone abbreviation which indicates DST status
    const abbreviation =
      new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        timeZoneName: 'short',
      })
        .formatToParts(date)
        .find(part => part.type === 'timeZoneName')?.value || '';

    // Most DST abbreviations include 'D' (EDT, PDT, etc.)
    return abbreviation.includes('D');
  } catch (error) {
    console.error('Error checking DST status:', error);
    return false;
  }
}

/**
 * Utility function to get a properly formatted Supabase storage URL
 * @param path The path to the file in Supabase storage
 * @param bucket The bucket name (default: 'profiles')
 * @returns The full Supabase storage URL
 */
export function getSupabaseStorageUrl(path: string, bucket: string = 'profiles'): string {
  if (!path) return '';

  // If it's already a full URL, return it
  if (path.startsWith('http')) {
    return path;
  }

  // Supabase project URL
  const supabaseUrl = 'https://frksndjujrbjhlrcjvtf.supabase.co';

  // If it already contains the storage path, just make sure it has the full URL
  if (path.includes('storage/v1/object/public')) {
    return path.startsWith('/') ? `${supabaseUrl}${path}` : `${supabaseUrl}/${path}`;
  }

  // If it starts with the bucket name, format it properly
  if (path.startsWith(`${bucket}/`)) {
    return `${supabaseUrl}/storage/v1/object/public/${path}`;
  }

  // Otherwise, assume it's a file path within the bucket
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`;
}

/**
 * Get a properly formatted avatar URL from Supabase storage
 * @param path The path to the avatar image
 * @returns The full Supabase storage URL for the avatar
 */
export function getAvatarUrl(path: string): string {
  if (!path) return 'https://placehold.co/400x400?text=Profile';

  // If it's already a full URL, return it
  if (path.startsWith('http')) {
    // Check if it's an Unsplash URL and ensure it has the right parameters without duplication
    if (path.includes('unsplash.com')) {
      // Check if parameters are already present to avoid duplication
      if (path.includes('w=400') && path.includes('auto=format') && path.includes('fit=crop')) {
        return path; // Parameters already present
      }

      // Add missing parameters
      let hasQuery = path.includes('?');
      let enhancedUrl = path;

      if (!path.includes('w=400')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}w=400`;
        hasQuery = true;
      }
      if (!path.includes('auto=format')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}auto=format`;
        hasQuery = true;
      }
      if (!path.includes('fit=crop')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}fit=crop`;
        hasQuery = true;
      }
      if (!path.includes('q=80')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}q=80`;
      }

      return enhancedUrl;
    }
    return path;
  }

  // Construct the proper path
  const avatarUrl = getSupabaseStorageUrl(path, 'profiles');
  return avatarUrl;
}

/**
 * Get a properly formatted cover photo URL from Supabase storage
 * @param path The path to the cover photo
 * @returns The full Supabase storage URL for the cover photo
 */
export function getCoverPhotoUrl(path: string): string {
  if (!path) return 'https://placehold.co/1200x400?text=Cover+Photo';

  // If it's already a full URL, return it
  if (path.startsWith('http')) {
    // Check if it's an Unsplash URL and ensure it has the right parameters without duplication
    if (path.includes('unsplash.com')) {
      // Check if parameters are already present to avoid duplication
      if (path.includes('w=1200') && path.includes('auto=format') && path.includes('fit=crop')) {
        return path; // Parameters already present
      }

      // Add missing parameters
      let hasQuery = path.includes('?');
      let enhancedUrl = path;

      if (!path.includes('w=1200')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}w=1200`;
        hasQuery = true;
      }
      if (!path.includes('auto=format')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}auto=format`;
        hasQuery = true;
      }
      if (!path.includes('fit=crop')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}fit=crop`;
        hasQuery = true;
      }
      if (!path.includes('q=80')) {
        enhancedUrl += `${hasQuery ? '&' : '?'}q=80`;
      }

      return enhancedUrl;
    }
    return path;
  }

  // Construct the proper path
  const coverUrl = getSupabaseStorageUrl(path, 'profiles');
  return coverUrl;
}

/**
 * Get a properly formatted session image URL from Supabase storage
 * @param path The path to the session image
 * @returns The full Supabase storage URL for the session image
 */
export function getSessionImageUrl(path: string): string {
  if (!path) return 'https://placehold.co/1200x400?text=Session';

  // Only log in development mode and not too frequently
  if (process.env.NODE_ENV === 'development' && Math.random() < 0.1) {
    console.log(`[getSessionImageUrl] Processing session image path: ${path}`);
  }

  // If it's already a full URL, return it
  if (path.startsWith('http')) {
    // Check if it's an Unsplash URL and ensure it has the right parameters
    if (path.includes('unsplash.com')) {
      // Always add parameters to Unsplash URLs to ensure proper loading and CORS handling
      const enhancedUrl = `${path}${path.includes('?') ? '&' : '?'}w=800&auto=format&fit=crop&q=80`;
      console.log(`[getSessionImageUrl] Enhanced Unsplash URL: ${enhancedUrl}`);
      return enhancedUrl;
    }
    return path;
  }

  // Construct the proper path
  const imageUrl = getSupabaseStorageUrl(path, 'sessions');
  return imageUrl;
}

/**
 * Group sessions by their title and return one representative session per unique title
 * with additional metadata about other dates available
 * @param sessions Array of sessions to group
 * @returns Array of grouped sessions with additionalDates metadata
 */
export function groupSessionsByTitle(sessions: any[]): any[] {
  const groupedSessions = new Map<string, any[]>();

  // Group sessions by title (case-insensitive)
  sessions.forEach(session => {
    const title = session.title?.toLowerCase().trim() || 'untitled';

    if (!groupedSessions.has(title)) {
      groupedSessions.set(title, []);
    }

    groupedSessions.get(title)!.push(session);
  });

  // For each group, return the session with the earliest upcoming date
  const representativeSessions: any[] = [];

  groupedSessions.forEach((sessionGroup, title) => {
    // Sort sessions by date (earliest first)
    const sortedGroup = sessionGroup.sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return dateA - dateB;
    });

    // Get the earliest upcoming session (or just the first if all are in the past)
    const now = new Date().getTime();
    const upcomingSessions = sortedGroup.filter(session => new Date(session.date).getTime() >= now);
    const representativeSession = upcomingSessions.length > 0 ? upcomingSessions[0] : sortedGroup[0];

    // Add metadata about additional dates
    const additionalDates = sortedGroup
      .filter(session => session.id !== representativeSession.id)
      .map(session => ({
        id: session.id,
        date: session.date,
        price: session.price,
        duration: session.duration
      }));

    // Create the enhanced session object
    const enhancedSession = {
      ...representativeSession,
      additionalDates,
      totalSessions: sortedGroup.length,
      hasMultipleDates: sortedGroup.length > 1,
      // Keep track of original sessions for detail view
      allSessions: sortedGroup
    };

    representativeSessions.push(enhancedSession);
  });

  // Sort representative sessions by date (earliest first)
  return representativeSessions.sort((a, b) => {
    const dateA = new Date(a.date).getTime();
    const dateB = new Date(b.date).getTime();
    return dateA - dateB;
  });
}
