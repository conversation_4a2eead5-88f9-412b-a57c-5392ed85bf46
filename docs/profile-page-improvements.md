# Profile Page UI/UX Improvements

## 1. Minimalist Header Design
- **Reduce cover photo height**: Change from h-64 to h-48 to reduce vertical space while maintaining visual impact
- **Cleaner avatar placement**: Position avatar to overlap the cover photo and card content for a modern look
- **Simplified header actions**: Move action buttons (Edit Profile, Share) to a more subtle position

## 2. Improved Information Organization
- **Card-based layout**: Use separate cards for different sections of information
- **Tabbed interface**: Organize content into clear tabs (About, Sessions, Reviews)
- **Visual hierarchy**: Use typography and spacing to create clear visual hierarchy

## 3. Enhanced Social Media Integration
- **Branded social icons**: Use colored brand icons for better recognition
- **Hover effects**: Add subtle hover animations to social links
- **Grouped display**: Group social links in a visually appealing way

## 4. Global Privacy Toggle
- **Profile visibility switch**: Add a prominent toggle at the top of the profile
- **Visual indicator**: Show current privacy state with appropriate icon
- **Quick access**: Allow changing privacy without going to settings

## 5. Business Profile Features
- **Organization mode**: Add toggle to switch between individual and business profile
- **Team members section**: Display teachers associated with the business
- **Branded elements**: Allow customization of colors and branding elements

## Code Example for Global Privacy Toggle:

```jsx
// Add this to the profile header section
<div className="absolute top-4 right-4 flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-full py-1.5 px-3 shadow-sm">
  <Label htmlFor="profile-privacy" className="text-xs font-medium text-gray-700">
    {isProfilePrivate ? "Private Profile" : "Public Profile"}
  </Label>
  <Switch
    id="profile-privacy"
    checked={!isProfilePrivate}
    onCheckedChange={(checked) => {
      setIsProfilePrivate(!checked);
      // API call to update privacy setting
      updateProfilePrivacy(userWithProfile.id, !checked);
    }}
    aria-label="Toggle profile privacy"
  />
  {isProfilePrivate ? 
    <Lock className="h-3.5 w-3.5 text-gray-500" /> : 
    <Globe className="h-3.5 w-3.5 text-green-500" />
  }
</div>
```

## Code Example for Business Profile Toggle:

```jsx
// Add this to the profile settings section
<div className="flex items-center justify-between p-4 border-t">
  <div>
    <h3 className="text-sm font-medium text-gray-900">Business Profile</h3>
    <p className="text-xs text-gray-500">Enable business features to showcase your organization</p>
  </div>
  <Switch
    id="business-profile"
    checked={isBusinessProfile}
    onCheckedChange={(checked) => {
      setIsBusinessProfile(checked);
      // API call to update business profile setting
      updateBusinessProfileSetting(userWithProfile.id, checked);
    }}
    aria-label="Toggle business profile"
  />
</div>
```

## Mockup for Improved Social Media Section:

```jsx
<div className="mt-4">
  <h3 className="text-sm font-medium text-gray-700 mb-2">Connect with me</h3>
  <div className="flex flex-wrap gap-2">
    {socialAccounts.map((account) => (
      <a
        key={account.platform}
        href={account.url}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center gap-1.5 bg-white hover:bg-gray-50 border border-gray-200 transition-colors rounded-full py-1.5 px-3 text-sm"
      >
        {account.platform === 'instagram' && <Instagram className="h-3.5 w-3.5 text-pink-500" />}
        {account.platform === 'facebook' && <Facebook className="h-3.5 w-3.5 text-blue-600" />}
        {account.platform === 'twitter' && <Twitter className="h-3.5 w-3.5 text-sky-400" />}
        {account.platform === 'linkedin' && <Linkedin className="h-3.5 w-3.5 text-blue-700" />}
        {account.platform === 'youtube' && <Youtube className="h-3.5 w-3.5 text-red-600" />}
        {account.platform === 'website' && <Globe className="h-3.5 w-3.5 text-gray-700" />}
        <span>{account.username}</span>
      </a>
    ))}
  </div>
</div>
```
