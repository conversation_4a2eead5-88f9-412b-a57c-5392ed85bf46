# Port Management Guide for Session Hub V.2

This guide covers best practices for managing development ports to avoid conflicts and ensure smooth development workflow.

## Default Ports

- **Client (Frontend)**: 3000
- **Server (Backend)**: 4005  
- **Supabase Local**: 54321

## Quick Commands

### Using npm scripts (recommended):
```bash
# Check all development ports
npm run ports:check

# Clean all development ports
npm run ports:clean

# Start development with clean ports
npm run dev:clean
```

### Using the port manager directly:
```bash
# Check all ports
./scripts/dev-utils/port-manager.sh check

# Check specific port
./scripts/dev-utils/port-manager.sh check 3000

# Kill process on specific port
./scripts/dev-utils/port-manager.sh kill 3000

# Clean all development ports
./scripts/dev-utils/port-manager.sh clean

# Find next available port
./scripts/dev-utils/port-manager.sh find 3000
```

## Development Workflow

### 1. Starting Development (Recommended)
```bash
# Clean all ports and start both client and server
npm run dev:clean
```

### 2. Manual Startup
```bash
# Terminal 1: Start server
npm run dev

# Terminal 2: Start client  
cd client && npm run dev
```

### 3. If Ports Are Occupied
```bash
# Check what's using the ports
npm run ports:check

# Clean the ports
npm run ports:clean

# Then start normally
npm run dev:all
```

## Troubleshooting

### Port Already in Use
If you see "Port 3000 is in use", you have several options:

1. **Clean the port** (recommended):
   ```bash
   npm run ports:clean
   ```

2. **Kill specific port**:
   ```bash
   ./scripts/dev-utils/port-manager.sh kill 3000
   ```

3. **Find what's using it**:
   ```bash
   lsof -i :3000
   ```

### Multiple Projects Running
If you're running multiple projects:

1. **Use different ports** by setting environment variables:
   ```bash
   VITE_PORT=3001 npm run dev --prefix client
   PORT=4006 npm run dev
   ```

2. **Or use the port finder**:
   ```bash
   ./scripts/dev-utils/port-manager.sh find 3000
   ```

### Zombie Processes
If processes don't die cleanly:

```bash
# Force kill all Node processes (use with caution)
npm run kill-all-node

# Or kill specific PID
kill -9 <PID>
```

## Configuration

### Environment Variables
The following environment variables control port behavior:

**Client (.env.local)**:
```env
VITE_PORT=3000
VITE_HOST=localhost
VITE_API_URL=http://localhost:4005
```

**Server**:
```env
PORT=4005
```

### Vite Configuration
The client automatically reads `VITE_PORT` from environment variables:

```typescript
// vite.config.ts
server: {
  port: parseInt(process.env.VITE_PORT || '3000'),
  host: process.env.VITE_HOST || 'localhost',
}
```

## Port Manager Features

The port manager script (`scripts/dev-utils/port-manager.sh`) provides:

- ✅ **Port checking**: See what's using each port
- ✅ **Graceful killing**: Try SIGTERM first, then SIGKILL
- ✅ **Batch operations**: Clean all development ports at once
- ✅ **Port discovery**: Find next available port
- ✅ **Colored output**: Easy to read status messages
- ✅ **Error handling**: Robust error handling and recovery

## Best Practices

### 1. Always Clean Before Starting
```bash
npm run ports:clean && npm run dev:all
```

### 2. Use Consistent Ports
- Stick to the default ports when possible
- Document any port changes in your team
- Use environment variables for configuration

### 3. Monitor Port Usage
```bash
# Check ports regularly
npm run ports:check

# Or set up an alias
alias ports='npm run ports:check'
```

### 4. Graceful Shutdown
- Always use `Ctrl+C` to stop development servers
- Avoid force-killing unless necessary
- Clean ports after crashes

### 5. Team Coordination
- Share port configurations in `.env.local`
- Document any non-standard port usage
- Use the port manager for consistency

## Common Issues and Solutions

### Issue: "EADDRINUSE: address already in use"
**Solution**: Clean the port and restart
```bash
npm run ports:clean
npm run dev:all
```

### Issue: Client can't connect to server
**Solution**: Check if server is running on correct port
```bash
npm run ports:check
# Ensure server is on port 4005
```

### Issue: Proxy errors in browser
**Solution**: Verify API URL configuration
```bash
# Check client/.env.local
cat client/.env.local | grep VITE_API_URL
```

### Issue: Multiple Vite instances
**Solution**: Kill all and restart
```bash
npm run kill-all-node
sleep 2
npm run dev:clean
```

## Advanced Usage

### Custom Port Ranges
To use different port ranges for multiple projects:

```bash
# Project 1 (Session Hub)
VITE_PORT=3000 PORT=4005

# Project 2 (Other project)  
VITE_PORT=3100 PORT=4105
```

### Docker Integration
When using Docker, map ports consistently:

```yaml
# docker-compose.yml
services:
  client:
    ports:
      - "3000:3000"
  server:
    ports:
      - "4005:4005"
```

### CI/CD Considerations
In CI/CD pipelines, use random ports to avoid conflicts:

```bash
# Use port finder in CI
CLIENT_PORT=$(./scripts/dev-utils/port-manager.sh find 3000)
SERVER_PORT=$(./scripts/dev-utils/port-manager.sh find 4005)
```

## Monitoring and Debugging

### Real-time Port Monitoring
```bash
# Watch port usage
watch -n 2 'npm run ports:check'
```

### Debug Mode
The port manager supports verbose output:
```bash
# Enable debug logging
DEBUG=1 ./scripts/dev-utils/port-manager.sh check
```

### Log Analysis
Check logs for port-related issues:
```bash
# Client logs
tail -f client/logs/vite.log

# Server logs  
tail -f server/logs/app.log
```

---

## Quick Reference Card

| Command | Purpose |
|---------|---------|
| `npm run ports:check` | Check all development ports |
| `npm run ports:clean` | Clean all development ports |
| `npm run dev:clean` | Clean ports and start development |
| `./scripts/dev-utils/port-manager.sh kill 3000` | Kill specific port |
| `lsof -i :3000` | See what's using port 3000 |
| `kill -9 <PID>` | Force kill process |

Remember: **Clean ports = Happy development!** 🚀 