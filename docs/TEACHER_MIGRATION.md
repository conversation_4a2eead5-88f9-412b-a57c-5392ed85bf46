# Teacher Migration Guide

This document explains the migration from "teacher" to "teacher" terminology in SessionHub.

## Background

SessionHub was originally built using the term "teacher" for users who teach sessions. To better align with user preferences and industry standards, we've updated the terminology to use "teacher" instead.

## Changes Made

1. **Database Schema**:
   - Added `is_teacher` column to the `users` table
   - Created a unified `user_profiles` table that replaces both `profiles` and `teacher_profiles`
   - Set up backward compatibility to ensure existing code continues to work

2. **API Endpoints**:
   - Added new `/api/teachers/...` endpoints that mirror the existing `/api/teachers/...` endpoints
   - Kept the old endpoints for backward compatibility
   - Updated the backend to use the new terminology

3. **Frontend**:
   - Updated references from "teacher" to "teacher"
   - Updated API calls to use the new endpoints
   - Maintained backward compatibility for existing code

## Running the Migration

To migrate your existing database:

1. Run the migration script:
   ```bash
   cd server
   node scripts/run-teacher-migration.js
   ```

2. Verify the migration:
   ```bash
   # Check that all teachers are now also teachers
   psql $DATABASE_URL -c "SELECT COUNT(*) FROM users WHERE is_teacher = TRUE AND is_teacher = TRUE;"
   
   # Check that profiles were migrated to the new table
   psql $DATABASE_URL -c "SELECT COUNT(*) FROM user_profiles;"
   ```

## Backward Compatibility

The migration maintains backward compatibility in several ways:

1. **Database**:
   - The `is_teacher` column is still used and updated alongside `is_teacher`
   - The `teacher_profiles` table is still updated when `user_profiles` is updated

2. **API**:
   - Old `/api/teachers/...` endpoints still work and return the same data
   - Methods like `getAllTeachers()` now call `getAllTeachersWithProfiles()` internally

3. **Frontend**:
   - Components that expect teacher data will continue to work

## Future Development

For new code, use the following guidelines:

1. Use "teacher" instead of "teacher" in all new code
2. Use the new `/api/teachers/...` endpoints for API calls
3. Use the `isTeacher` field instead of `isTeacher` to check if a user is a teacher
4. Use the `user_profiles` table for all profile data

## Cleanup Plan

In a future update, we plan to:

1. Remove the old `teacher_profiles` table
2. Remove the old `/api/teachers/...` endpoints
3. Remove the `isTeacher` field from the `User` type
4. Update all remaining code to use the new terminology

This cleanup will only happen after ensuring all code has been migrated to use the new terminology.
