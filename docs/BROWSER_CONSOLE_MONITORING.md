# Browser Console Monitoring

This setup allows you to monitor browser console logs directly from your terminal, making debugging much easier during development.

## How It Works

1. **Console Logger Script** (`client/public/console-logger.js`): Intercepts all browser console logs and sends them to a monitoring server
2. **Monitor Server** (`scripts/monitor-browser-console.js`): Receives and displays browser logs in your terminal
3. **HTML Integration** (`client/index.html`): Automatically loads the console logger script

## Setup

### 1. Start the Console Monitor

```bash
# From the project root
node scripts/monitor-browser-console.cjs
```

You should see:
```
🔍 Browser Console Monitor Started
📡 Listening for browser console logs...
💡 Make sure your browser has the console-logger.js script loaded
🌐 Open your app at http://localhost:3002
---
🚀 Console monitor running on http://localhost:4006
```

### 2. Open Your App

Navigate to your app at `http://localhost:3002`. The console logger will automatically initialize and you should see:

```
[timestamp] [BROWSER-LOG] / - 🔍 Console logger initialized - logs will be sent to server
```

### 3. Monitor Logs

Now any console.log, console.error, console.warn, or console.info calls in your browser will appear in your terminal:

```
[8:45:23 PM] [BROWSER-LOG] /edit-session/123 - === SUBMIT BUTTON CLICKED ===
[8:45:23 PM] [BROWSER-LOG] /edit-session/123 - Form state: {...}
[8:45:23 PM] [BROWSER-ERROR] /edit-session/123 - TypeError: Cannot read property 'id' of undefined
```

## Benefits

- **Real-time monitoring**: See browser logs instantly in your terminal
- **No copy-pasting**: Logs automatically appear in your development environment
- **Error tracking**: Captures unhandled errors and promise rejections
- **Context aware**: Shows which page/route the log came from
- **Development only**: Only works in development mode for security

## Usage for Debugging

When debugging the "Update Session" button issue:

1. Start the console monitor
2. Navigate to the edit session page
3. Click the "Update Session" button
4. Watch the terminal for logs showing exactly what happens

You'll see logs like:
- Button click events
- Form validation results
- API calls being made
- Any errors that occur

## Stopping the Monitor

Press `Ctrl+C` in the terminal running the monitor to stop it.

## Technical Details

- **Monitor Port**: 4006
- **CORS Enabled**: Allows cross-origin requests from your app
- **Error Handling**: Gracefully handles malformed log data
- **Automatic Cleanup**: Properly shuts down on process termination 