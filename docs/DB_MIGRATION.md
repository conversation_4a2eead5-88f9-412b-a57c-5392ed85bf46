# Database Migration Guide

## Database Structure

The application uses a PostgreSQL database with the following key tables:

- `users`: Main users table containing both regular users and teachers
- `sessions`: Learning sessions created by teachers
- `bookings`: Session bookings made by users
- `messages`: Messages between users
- `conversations`: Conversation threads between users

### Users Table

The users table has a unified structure that stores all user data, including teacher-specific fields:

```sql
Table "public.users"
     Column      |            Type             | Collation | Nullable |              Default              
-----------------+-----------------------------+-----------+----------+-----------------------------------
 id              | integer                     |           | not null | nextval('users_id_seq'::regclass)
 username        | text                        |           | not null | 
 password        | text                        |           | not null | 
 email           | text                        |           | not null | 
 name            | text                        |           | not null | 
 bio             | text                        |           |          | 
 avatar          | text                        |           |          | 
 timezone        | text                        |           |          | 
 is_teacher   | boolean                     |           |          | false
 created_at      | timestamp without time zone |           | not null | now()
 updated_at      | timestamp without time zone |           | not null | now()
 cover_photo     | text                        |           |          | 
 phone           | text                        |           |          | 
 enable_r_l_s    | text                        |           |          | 
 specializations | text[]                      |           |          | 
 skills          | text[]                      |           |          | 
 certifications  | text[]                      |           |          | 
 experience      | text                        |           |          | 
 rating          | double precision            |           |          | 0
 review_count    | integer                     |           |          | 0
```

## Making Database Changes

### Using Supabase Dashboard

1. Log in to the [Supabase Dashboard](https://supabase.com/dashboard)
2. Navigate to your project
3. Open the SQL Editor
4. Run your SQL commands

### Using psql

Connect using psql with your Supabase connection string:

```bash
psql "postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require"
```

## Before Making Changes

1. **Use Supabase Tools**: Before making changes, ensure you're using Supabase dashboard or CLI tools
2. **Backup Important Data**: Consider exporting critical data before major changes
3. **Test in Staging**: For major schema changes, test in a separate branch first

## Migration History

### Teacher Data Migration (April 2023)

The application originally had a separate `teacher_profiles` table, but has been migrated to a unified user model:

1. Added teacher fields to the users table:
   - specializations (text[])
   - skills (text[])
   - certifications (text[])
   - experience (text)
   - rating (double precision)
   - review_count (integer)

2. Migrated data from teacher_profiles to the users table
3. Set is_teacher = true for all users with teacher profiles

### Future Plan

In a future update, we plan to:
1. Remove the now-redundant `teacher_profiles` table
2. Update any remaining code references to use the unified model

## Troubleshooting

If you encounter errors after schema changes:
- Check application logs for SQL errors
- Look for missing columns errors in the PostgreSQL logs
- Verify that the schema matches what the application code expects 