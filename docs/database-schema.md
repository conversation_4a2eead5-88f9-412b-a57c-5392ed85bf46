# SessionHub Database Schema

This document describes the database schema organization for SessionHub.

## Schema Organization

The database is organized into the following schemas:

### 1. `auth` Schema

Contains authentication-related tables:

- `users`: Core user authentication data (username, password, email)
- `password_reset_tokens`: Tokens for password reset functionality
- `email_verification_tokens`: Tokens for email verification
- `session`: Session information for authentication

### 2. `profiles` Schema

Contains user profile information:

- `user_profiles`: User profile data (name, bio, contact info, etc.)
- `user_payment_info`: User payment information
- `social_accounts`: Social media accounts linked to users
- `user_devices`: User device information for notifications

### 3. `content` Schema

Contains content-related tables:

- `sessions`: Session/class information
- `reviews`: User reviews for sessions and teachers

### 4. `bookings` Schema

Contains booking and payment-related tables:

- `bookings`: Session bookings
- `platform_fees`: Platform fee records
- `payouts`: Payout records for teachers
- `global_availability`: Global availability settings for teachers
- `teacher_availability`: Availability for specific sessions
- `teacher_payment_methods`: Payment methods for teachers
- `time_slots`: Time slots for bookings

### 5. `messaging` Schema

Contains messaging-related tables:

- `conversations`: Conversation metadata
- `messages`: Individual messages
- `scheduled_messages`: Messages scheduled to be sent at a future time
- `notification_preferences`: User notification preferences

### 6. `backup` Schema

Contains backup tables:

- `users_backup`: Backup of users table
- `user_profiles_backup`: Backup of user profiles table

### 7. `public` Schema

Contains any remaining legacy tables that haven't been migrated yet.

## Database Access

When accessing the database, you should:

1. Always use schema-qualified table names (e.g., `auth.users`, `profiles.user_profiles`, etc.)
2. Set the search path to include all schemas:
   ```sql
   SET search_path TO public, auth, profiles, content, bookings, messaging, backup;
   ```

## Table Relationships

### User-related Tables

- `auth.users` contains core authentication data
- `profiles.user_profiles` contains profile information linked to `auth.users` via `user_id`
- `profiles.user_payment_info` contains payment information linked to `auth.users` via `user_id`
- `profiles.social_accounts` contains social media accounts linked to `auth.users` via `user_id`
- `profiles.user_devices` contains device information linked to `auth.users` via `user_id`

### Session-related Tables

- `content.sessions` contains session information
- `content.reviews` contains reviews linked to `content.sessions` via `session_id`
- `bookings.bookings` contains bookings linked to `content.sessions` via `session_id`
- `bookings.teacher_availability` contains availability linked to `content.sessions` via `session_id`
- `bookings.time_slots` contains time slots linked to `bookings.teacher_availability` via `availability_id`

### Messaging-related Tables

- `messaging.conversations` contains conversation metadata
- `messaging.messages` contains messages linked to `messaging.conversations` via `conversation_id`
- `messaging.scheduled_messages` contains scheduled messages linked to `messaging.conversations` via `conversation_id`
- `messaging.notification_preferences` contains notification preferences linked to `auth.users` via `user_id`

### Payment-related Tables

- `bookings.teacher_payment_methods` contains payment methods linked to `auth.users` via `user_id`
- `bookings.payouts` contains payouts linked to `bookings.teacher_payment_methods` via `payment_method_id`
- `bookings.platform_fees` contains platform fees linked to `bookings.bookings` via `booking_id`

## Migration Status

All tables have been moved to their appropriate schemas, and all code has been updated to use schema-qualified table names.

## Backup

Database backups are created using the `scripts/backup-database-js.js` script, which creates a JSON file containing the database schema and data.

## Scripts

The following scripts are available for database management:

- `scripts/backup-database-js.js`: Creates a backup of the database
- `scripts/test-schema-operations-comprehensive.js`: Tests database operations with schema-qualified table names
- `scripts/check-public-schema.js`: Checks for tables in the public schema
- `scripts/check-table-references.js`: Checks for code references to tables without schema qualification
- `scripts/migrate-public-tables-fixed.js`: Migrates tables from public schema to appropriate schemas
