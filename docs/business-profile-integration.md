# Business Profile Integration

## Overview
Adding business profile functionality would allow organizations to showcase all their teachers and sessions in one place, creating a more cohesive experience for users looking for services from a specific business.

## Key Features

### 1. Business Profile Type
- Add a new profile type option (Individual/Business)
- Business profiles would have additional fields:
  - Business name
  - Business description
  - Business logo
  - Business hours
  - Business location(s)
  - Business category

### 2. Team Management
- Allow business owners to invite teachers to join their organization
- Manage teacher permissions and visibility
- Display team members on the business profile

### 3. Session Organization
- Group sessions by teacher or category
- Allow filtering of sessions within the business profile
- Provide business-specific analytics on session performance

### 4. Global Privacy Controls
- Add ability to set privacy at the business level
- Override individual session privacy settings
- Provide granular control over what information is public

## Implementation Approach

### Database Schema Changes
```sql
-- New business_profiles table
CREATE TABLE business_profiles (
  id SERIAL PRIMARY KEY,
  owner_id INTEGER NOT NULL REFERENCES users(id),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  logo_url TEXT,
  website VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_public BOOLEAN DEFAULT true
);

-- Linking table for business members
CREATE TABLE business_members (
  id SERIAL PRIMARY KEY,
  business_id INTEGER NOT NULL REFERENCES business_profiles(id),
  user_id INTEGER NOT NULL REFERENCES users(id),
  role VARCHAR(50) NOT NULL, -- 'owner', 'admin', 'teacher'
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(business_id, user_id)
);

-- Add business_id to sessions table
ALTER TABLE sessions ADD COLUMN business_id INTEGER REFERENCES business_profiles(id);
```

### UI Components

#### Business Profile Selector
```jsx
<FormField
  control={profileForm.control}
  name="profileType"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Profile Type</FormLabel>
      <Select
        onValueChange={(value) => {
          field.onChange(value);
          setShowBusinessFields(value === "business");
        }}
        defaultValue={field.value}
      >
        <FormControl>
          <SelectTrigger>
            <SelectValue placeholder="Select profile type" />
          </SelectTrigger>
        </FormControl>
        <SelectContent>
          <SelectItem value="individual">Individual</SelectItem>
          <SelectItem value="business">Business/Organization</SelectItem>
        </SelectContent>
      </Select>
      <FormDescription>
        Choose "Business" if you're representing an organization with multiple teachers
      </FormDescription>
      <FormMessage />
    </FormItem>
  )}
/>
```

#### Business Profile Card
```jsx
{isBusinessProfile && (
  <Card className="mb-6">
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <CardTitle className="text-lg font-medium">Business Profile</CardTitle>
        <Button variant="ghost" size="sm" onClick={() => setIsEditingBusiness(true)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </Button>
      </div>
    </CardHeader>
    <CardContent>
      <div className="flex items-center gap-4 mb-4">
        <Avatar className="h-16 w-16">
          <AvatarImage src={businessProfile.logo} alt={businessProfile.name} />
          <AvatarFallback>{getInitials(businessProfile.name)}</AvatarFallback>
        </Avatar>
        <div>
          <h3 className="font-medium text-lg">{businessProfile.name}</h3>
          <p className="text-sm text-gray-500">{businessProfile.category}</p>
        </div>
      </div>
      <p className="text-sm text-gray-700 mb-4">{businessProfile.description}</p>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p className="text-gray-500 mb-1">Business Hours</p>
          <p>{businessProfile.hours || "Not specified"}</p>
        </div>
        <div>
          <p className="text-gray-500 mb-1">Location</p>
          <p>{businessProfile.location || "Not specified"}</p>
        </div>
      </div>
    </CardContent>
  </Card>
)}
```

#### Team Members Section
```jsx
{isBusinessProfile && (
  <Card className="mb-6">
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <CardTitle className="text-lg font-medium">Team Members</CardTitle>
        <Button variant="outline" size="sm" onClick={() => setIsInvitingMembers(true)}>
          <UserPlus className="h-4 w-4 mr-2" />
          Invite
        </Button>
      </div>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {teamMembers.length > 0 ? (
          teamMembers.map(member => (
            <div key={member.id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback>{getInitials(member.name)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{member.name}</p>
                  <p className="text-xs text-gray-500">{member.role}</p>
                </div>
              </div>
              <Badge variant={member.role === 'owner' ? 'default' : 'outline'}>
                {member.role}
              </Badge>
            </div>
          ))
        ) : (
          <div className="text-center py-6 text-gray-500">
            <UserX className="h-12 w-12 mx-auto mb-3 text-gray-400" />
            <p>No team members yet</p>
            <p className="text-sm">Invite teachers to join your business</p>
          </div>
        )}
      </div>
    </CardContent>
  </Card>
)}
```

## Global Privacy Toggle Implementation

```jsx
// Add this to the profile page component
const [isAllSessionsPrivate, setIsAllSessionsPrivate] = useState(false);

// Function to update all sessions privacy
const updateAllSessionsPrivacy = async (isPrivate) => {
  try {
    const response = await fetch(`/api/users/${userWithProfile.id}/sessions/privacy`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ isPublic: !isPrivate }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update sessions privacy');
    }
    
    // Refetch sessions to update UI
    refetchTeachingSessions();
    
    toast({
      title: isPrivate ? "All sessions set to private" : "All sessions set to public",
      description: `Successfully updated privacy settings for all your sessions.`,
    });
  } catch (error) {
    console.error('Error updating sessions privacy:', error);
    toast({
      title: "Error updating privacy",
      description: "There was a problem updating your sessions privacy settings.",
      variant: "destructive",
    });
  }
};

// UI Component
<Card className="mb-6">
  <CardHeader className="pb-3">
    <div className="flex items-center justify-between">
      <CardTitle className="text-lg font-medium">Privacy Settings</CardTitle>
    </div>
  </CardHeader>
  <CardContent>
    <div className="flex items-center justify-between">
      <div>
        <h3 className="font-medium">Profile Visibility</h3>
        <p className="text-sm text-gray-500">Control who can see your profile</p>
      </div>
      <Select 
        value={privacy.profileVisibility} 
        onValueChange={(value) => updateProfileVisibility(value)}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select visibility" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="public">Public</SelectItem>
          <SelectItem value="registered">Registered Users</SelectItem>
          <SelectItem value="private">Private</SelectItem>
        </SelectContent>
      </Select>
    </div>
    
    <Separator className="my-4" />
    
    <div className="flex items-center justify-between">
      <div>
        <h3 className="font-medium">All Sessions Privacy</h3>
        <p className="text-sm text-gray-500">Make all your sessions private or public</p>
      </div>
      <div className="flex items-center gap-2">
        <Label htmlFor="all-sessions-privacy" className={isAllSessionsPrivate ? "text-gray-500" : "text-green-600"}>
          {isAllSessionsPrivate ? "Private" : "Public"}
        </Label>
        <Switch
          id="all-sessions-privacy"
          checked={!isAllSessionsPrivate}
          onCheckedChange={(checked) => {
            setIsAllSessionsPrivate(!checked);
            updateAllSessionsPrivacy(!checked);
          }}
        />
      </div>
    </div>
  </CardContent>
</Card>
```
