# SessionHub Troubleshooting Guide

This guide provides solutions for common issues you might encounter when running SessionHub.

## Database Connection Issues

### Problem: "DATABASE_URL environment variable is not set"

**Solution:**

1. Make sure you have a `.env` or `.env.local` file in the project root
2. Ensure your DATABASE_URL is correctly formatted with quotes:
   ```
   DATABASE_URL="postgresql://username:password@hostname:port/database?sslmode=require"
   ```
3. Run `npm run verify-db` to test your database connection
4. If your connection is working with the verify script but not with the app, try:
   - Restarting your terminal
   - Running `npm run debug` for more detailed logs

### Problem: "Error: column 'X' does not exist"

This happens when the code expects a database column that doesn't exist.

**Solution:**

1. Run `npm run check-tables` to see the actual database schema
2. Check for any references to non-existent columns in the code
3. Update the code to match the actual database schema or modify the database schema

## Port Conflicts

### Problem: "Port X is already in use"

**Solution:**

1. Change the port in both `.env` and `.env.local` files
2. Update the default port in `server/index.ts` (fallback value)
3. Kill the process using the port with:
   ```
   lsof -i :3003 | grep LISTEN | awk '{print $2}' | xargs kill -9
   ```
4. Restart your terminal and try again

## WebSocket Connection Issues

### Problem: Constant "WebSocket connection failed" messages

**Solution:**

1. Make sure you're not running both development and production servers simultaneously
2. Check if your firewall is blocking WebSocket connections
3. Try disabling any browser extensions that might be interfering with WebSockets
4. Clear your browser cache and cookies

## Authentication Issues

### Problem: "Cannot login" or "User not found"

**Solution:**

1. Check the users table to verify the user exists:
   ```sql
   SELECT * FROM users WHERE username = 'your_username';
   ```
2. Verify that the password hashing algorithm is correct
3. Ensure the session configuration is correct
4. Try clearing your browser cookies

## Performance Issues

### Problem: Application is slow or unresponsive

**Solution:**

1. Check database query performance
2. Ensure connection pooling is properly configured
3. Check for memory leaks or excessive logging
4. Consider adding caching for frequently accessed data

## Installation Issues

### Problem: "Module not found" or "Cannot find module"

**Solution:**

1. Make sure you've installed all dependencies with `npm install`
2. Check that your Node.js version is compatible (use `node -v`)
3. Delete `node_modules` and run `npm install` again
4. Check for any ESM/CommonJS module compatibility issues

## Data Issues

### Problem: Missing data (sessions, teachers, etc.)

**Solution:**

1. Check the database to verify the data exists
2. Verify SQL queries in the code are correct
3. Look for any filters that might be excluding data
4. Check for permission issues or row-level security settings

## General Debugging Tips

1. **Enable Debug Mode**: Run with `npm run debug` for more detailed logs
2. **Check Logs**: Look for error messages in the console
3. **Verify Database Connection**: Use `npm run verify-db`
4. **Check Table Schemas**: Use `npm run check-tables`
5. **Test API Endpoints**: Use a tool like Postman to test API endpoints directly
6. **Review Environment Variables**: Ensure all required environment variables are set
7. **Client-Side Issues**: Check browser console for errors

## Still Having Issues?

If you're still experiencing problems after trying these solutions, please file an issue on the project repository with:

1. A detailed description of the problem
2. Steps to reproduce the issue
3. Relevant error messages
4. Your environment (OS, Node.js version, npm version, etc.) 