import { createClient } from '@supabase/supabase-js';

// Create a direct instance of the Supabase client
const SUPABASE_URL = 'https://frksndjujrbjhlrcjvtf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZya3NuZGp1anJiamhscmNqdnRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY5MDc1OTAsImV4cCI6MjA2MjQ4MzU5MH0.fR_qqa4rJvyaqJOL5aSWP2aM6O0MT0dLXVV5xxAmTrs';

// Create a client instance directly in this file
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  }
});

// Helper functions for common operations

// User authentication
export const signUp = async (email, password) => {
  return await supabase.auth.signUp({ email, password });
};

export const signIn = async (email, password) => {
  return await supabase.auth.signInWithPassword({ email, password });
};

export const signOut = async () => {
  return await supabase.auth.signOut();
};

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

// Profile operations
export const getProfiles = async () => {
  return await supabase.from('user_profiles').select('*');
};

export const getProfile = async (userId) => {
  return await supabase.from('user_profiles').select('*').eq('user_id', userId).single();
};

// Update a user profile
export const updateProfile = async (userId, updates) => {
  return await supabase
    .from('user_profiles')
    .update(updates)
    .eq('user_id', userId);
};

// File operations
export const uploadFile = async (bucket, path, file) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      upsert: true
    });

  return { data, error };
};

export const getFileUrl = (bucket, path) => {
  return supabase.storage.from(bucket).getPublicUrl(path).data.publicUrl;
};

export const deleteFile = async (bucket, path) => {
  return await supabase.storage
    .from(bucket)
    .remove([path]);
};
