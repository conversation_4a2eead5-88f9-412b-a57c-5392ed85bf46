export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-5xl mx-auto px-6 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Session Hub
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Discover and book interactive sessions with expert teachers
          </p>
          <div className="bg-white rounded-lg shadow-sm p-8 border border-gray-100">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              🎉 Deployment Successful!
            </h2>
            <p className="text-gray-600">
              Your Next.js app is now running successfully on Vercel.
              The complex import issues have been resolved by simplifying the page structure.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}