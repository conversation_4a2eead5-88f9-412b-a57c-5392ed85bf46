'use client'

import React, { useEffect } from 'react'
import { QueryClientProvider } from '@tanstack/react-query'

import { BackdropCleaner } from '../src/components/common/BackdropCleaner'
import { ChatPopupManager } from '../src/components/chat/ChatPopupManager'
import { ErrorBoundary } from '../src/features/error/components/ErrorBoundary'
import { Footer } from '../src/components/layout/Footer'
import { GlobalScrollToTop } from '../src/features/common/components/ScrollToTop'
import { GlobalSettingsDialog } from '../src/components/settings/GlobalSettingsDialog'
import { Header } from '../src/components/layout/Header'
import { MessageNotification } from '../src/components/chat/MessageNotification'
import { Toaster } from '../src/components/ui/toaster'

import { AuthProvider } from '../src/features/auth'
import { BookingProvider } from '../src/features/booking'
import { ProfileProvider } from '../src/features/profile'
import { SessionProvider } from '../src/features/sessions'
import { MessagingProvider } from '../src/features/messaging'
import { UserProvider } from '../src/features/user'

import { FilterProvider } from '../src/contexts/FilterContext'
import { FavoritesProvider } from '../src/contexts/FavoritesContext'
import { ToastContextProvider } from '../src/contexts/ToastContext'

import { StripeProvider } from '../src/providers/StripeProvider'
import { queryClient } from '../src/lib/queryClient'
import { initSupabaseAuth } from '../src/lib/supabase-auth'

export function Providers({ children }: { children: React.ReactNode }) {
    // Initialize Supabase auth
    useEffect(() => {
        initSupabaseAuth().then(() => {
            console.log('[App] Supabase auth initialized')
        }).catch(error => {
            console.error('[App] Error initializing Supabase auth:', error)
        })
    }, [])

    return (
        <ErrorBoundary>
            <QueryClientProvider client={queryClient}>
                <ToastContextProvider>
                    <StripeProvider>
                        <AuthProvider>
                            <ProfileProvider>
                                <BookingProvider>
                                    <UserProvider>
                                        <SessionProvider>
                                            <MessagingProvider>
                                                <FavoritesProvider>
                                                    <FilterProvider>
                                                        <GlobalScrollToTop />
                                                        <div className="flex flex-col min-h-screen bg-gradient-to-b from-gray-50 to-white">
                                                            <Header />
                                                            <main className="flex-grow pt-4 mt-16">
                                                                {children}
                                                            </main>
                                                            <Footer />
                                                        </div>
                                                        <Toaster />
                                                        <MessageNotification />
                                                        <GlobalSettingsDialog />
                                                        <BackdropCleaner />
                                                        <ChatPopupManager />
                                                    </FilterProvider>
                                                </FavoritesProvider>
                                            </MessagingProvider>
                                        </SessionProvider>
                                    </UserProvider>
                                </BookingProvider>
                            </ProfileProvider>
                        </AuthProvider>
                    </StripeProvider>
                </ToastContextProvider>
            </QueryClientProvider>
        </ErrorBoundary>
    )
} 