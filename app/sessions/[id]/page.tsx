export default async function SessionDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-4xl mx-auto px-6 py-16">
        <div className="bg-white rounded-lg shadow-sm p-8 border border-gray-100">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Session Details
          </h1>
          <div className="mb-4">
            <p className="text-gray-600">
              Session ID: <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">{id}</span>
            </p>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-700 text-sm">
              ✅ Session detail page is now working! Import issues resolved.
            </p>
            <p className="text-green-600 text-sm mt-2">
              🎉 Next.js 15 dynamic routes with async params working perfectly!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}