import Stripe from 'stripe';
// Get environment variables
function getEnv(name: string): string {
  return process.env[name] || '';
}

// Initialize Stripe with the secret key from environment variables
const stripeSecretKey = getEnv('STRIPE_SECRET_KEY');
if (!stripeSecretKey) {
  console.error('STRIPE_SECRET_KEY is not set in environment variables');
}

// Only require STRIPE_SECRET_KEY in production
if (!stripeSecretKey && process.env.NODE_ENV === 'production') {
  throw new Error('STRIPE_SECRET_KEY environment variable is required for payment processing in production');
}

if (!stripeSecretKey) {
  console.warn('⚠️ STRIPE_SECRET_KEY not set - payment features will be disabled');
}

// Create a dummy key for development if none provided
const effectiveStripeKey = stripeSecretKey || 'sk_test_dummy_key_for_development';

const stripe = new Stripe(effectiveStripeKey, {
  apiVersion: '2025-04-30.basil', // Use the latest API version
});

/**
 * Create a payment intent for a session booking
 *
 * @param amount - The amount to charge in the smallest currency unit (e.g., cents for USD)
 * @param currency - The currency to charge in (default: 'usd')
 * @param metadata - Additional metadata to attach to the payment intent
 * @returns The created payment intent
 */
export async function createPaymentIntent(
  amount: number,
  currency: string = 'usd',
  metadata: Record<string, string> = {}
): Promise<Stripe.PaymentIntent> {
  try {
    // Convert amount to cents if it's in dollars
    const amountInCents = Math.round(amount * 100);

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency,
      metadata,
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return paymentIntent;
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
}

/**
 * Retrieve a payment intent by ID
 *
 * @param paymentIntentId - The ID of the payment intent to retrieve
 * @returns The payment intent
 */
export async function retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
  try {
    return await stripe.paymentIntents.retrieve(paymentIntentId);
  } catch (error) {
    console.error(`Error retrieving payment intent ${paymentIntentId}:`, error);
    throw error;
  }
}

/**
 * Cancel a payment intent
 *
 * @param paymentIntentId - The ID of the payment intent to cancel
 * @returns The canceled payment intent
 */
export async function cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
  try {
    return await stripe.paymentIntents.cancel(paymentIntentId);
  } catch (error) {
    console.error(`Error canceling payment intent ${paymentIntentId}:`, error);
    throw error;
  }
}

/**
 * Verify a webhook signature from Stripe
 *
 * @param body - The raw request body as a string
 * @param signature - The Stripe signature from the request headers
 * @returns The verified event
 */
export function verifyWebhookSignature(body: string, signature: string): Stripe.Event {
  const webhookSecret = getEnv('STRIPE_WEBHOOK_SECRET');

  if (!webhookSecret) {
    throw new Error('STRIPE_WEBHOOK_SECRET is not set in environment variables');
  }

  try {
    return stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    throw error;
  }
}

/**
 * Create a Stripe Checkout session for a booking
 *
 * @param sessionId - The ID of the session being booked
 * @param userId - The ID of the user making the booking
 * @param amount - The amount to charge
 * @param successUrl - The URL to redirect to after successful payment
 * @param cancelUrl - The URL to redirect to if payment is canceled
 * @returns The created Checkout session
 */
export async function createCheckoutSession(
  sessionId: number,
  userId: number,
  amount: number,
  successUrl: string,
  cancelUrl: string,
  teacherId?: number
): Promise<Stripe.Checkout.Session> {
  try {
    console.log('[STRIPE-SERVICE] Creating checkout session with params:', {
      sessionId,
      userId,
      amount,
      successUrl: successUrl.substring(0, 50) + '...',
      cancelUrl: cancelUrl.substring(0, 50) + '...'
    });

    // Convert amount to cents
    const amountInCents = Math.round(amount * 100);
    console.log('[STRIPE-SERVICE] Amount in cents:', amountInCents);

    // Calculate platform fee (10%)
    const platformFeePercent = 0.1;
    const platformFeeAmount = Math.round(amountInCents * platformFeePercent);
    console.log('[STRIPE-SERVICE] Platform fee amount:', platformFeeAmount);

    // Get teacher's Stripe Connect account if available
    let stripeConnectId = null;
    if (teacherId) {
      try {
        const { storage } = await import('../storage');
        const teacher = await storage.getUser(teacherId);
        if (teacher && teacher.stripeConnectId && teacher.stripeConnectOnboardingComplete) {
          stripeConnectId = teacher.stripeConnectId;
          console.log(`[STRIPE-SERVICE] Using teacher's Stripe Connect account: ${stripeConnectId}`);
        } else {
          console.log(`[STRIPE-SERVICE] Teacher ${teacherId} does not have a valid Connect account`);
        }
      } catch (err) {
        console.error(`[STRIPE-SERVICE] Error fetching teacher ${teacherId}:`, err);
        // Continue without Connect account
      }
    }

    // Create a Checkout session
    console.log('[STRIPE-SERVICE] Calling Stripe API with key:', stripeSecretKey.substring(0, 10) + '...');

    const sessionParams: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: 'Session Booking',
              description: `Booking for session #${sessionId}`,
            },
            unit_amount: amountInCents,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        sessionId: sessionId.toString(),
        userId: userId.toString(),
        ...(teacherId ? { teacherId: teacherId.toString() } : {}),
      },
    };

    // If teacher has a Connect account, set up direct payment
    if (stripeConnectId) {
      console.log(`[STRIPE-SERVICE] Setting up direct payment to Connect account ${stripeConnectId}`);
      sessionParams.payment_intent_data = {
        application_fee_amount: platformFeeAmount,
        transfer_data: {
          destination: stripeConnectId,
        },
        metadata: {
          sessionId: sessionId.toString(),
          userId: userId.toString(),
          teacherId: teacherId.toString(),
          platformFee: platformFeeAmount.toString(),
        },
      };
    }

    const session = await stripe.checkout.sessions.create(sessionParams);

    console.log('[STRIPE-SERVICE] Checkout session created successfully:', {
      id: session.id,
      url: session.url ? session.url.substring(0, 50) + '...' : 'No URL'
    });

    return session;
  } catch (error) {
    console.error('[STRIPE-SERVICE] Error creating Checkout session:', error);

    // Add more context to the error
    if (error instanceof Error) {
      throw new Error(`Stripe checkout session creation failed: ${error.message}`);
    }

    throw error;
  }
}

/**
 * Create a Stripe Connect account for an teacher
 *
 * @param userId - The ID of the user in our database
 * @param email - The email of the user
 * @returns The created Connect account
 */
export async function createConnectAccount(userId: number, email: string): Promise<Stripe.Account> {
  try {
    console.log(`[STRIPE-SERVICE] Creating Connect account for user ${userId} with email ${email}`);

    const account = await stripe.accounts.create({
      type: 'express',
      email,
      metadata: {
        userId: userId.toString(),
      },
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });

    console.log(`[STRIPE-SERVICE] Created Connect account: ${account.id}`);
    return account;
  } catch (error) {
    console.error('[STRIPE-SERVICE] Error creating Connect account:', error);
    throw error;
  }
}

/**
 * Create an onboarding link for a Connect account
 *
 * @param accountId - The Stripe Connect account ID
 * @param refreshUrl - The URL to redirect to if the user wants to refresh the onboarding process
 * @param returnUrl - The URL to redirect to after the onboarding process is complete
 * @returns The onboarding link
 */
export async function createAccountLink(
  accountId: string,
  refreshUrl: string,
  returnUrl: string
): Promise<Stripe.AccountLink> {
  try {
    console.log(`[STRIPE-SERVICE] Creating account link for Connect account ${accountId}`);

    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    console.log(`[STRIPE-SERVICE] Created account link: ${accountLink.url}`);
    return accountLink;
  } catch (error) {
    console.error('[STRIPE-SERVICE] Error creating account link:', error);
    throw error;
  }
}

/**
 * Retrieve a Connect account
 *
 * @param accountId - The Stripe Connect account ID
 * @returns The Connect account
 */
export async function retrieveConnectAccount(accountId: string): Promise<Stripe.Account> {
  try {
    return await stripe.accounts.retrieve(accountId);
  } catch (error) {
    console.error(`[STRIPE-SERVICE] Error retrieving Connect account ${accountId}:`, error);
    throw error;
  }
}

/**
 * Create a transfer to a Connect account
 *
 * @param amount - The amount to transfer in the smallest currency unit (e.g., cents for USD)
 * @param destinationAccountId - The Stripe Connect account ID to transfer to
 * @param description - A description of the transfer
 * @param metadata - Additional metadata to attach to the transfer
 * @returns The created transfer
 */
export async function createTransfer(
  amount: number,
  destinationAccountId: string,
  description: string = '',
  metadata: Record<string, string> = {}
): Promise<Stripe.Transfer> {
  try {
    console.log(`[STRIPE-SERVICE] Creating transfer of ${amount} to Connect account ${destinationAccountId}`);

    const transfer = await stripe.transfers.create({
      amount,
      currency: 'usd',
      destination: destinationAccountId,
      description,
      metadata,
    });

    console.log(`[STRIPE-SERVICE] Created transfer: ${transfer.id}`);
    return transfer;
  } catch (error) {
    console.error('[STRIPE-SERVICE] Error creating transfer:', error);
    throw error;
  }
}

/**
 * Create a dashboard login link for a Connect account
 *
 * @param accountId - The Stripe Connect account ID
 * @returns The login link
 */
export async function createDashboardLoginLink(accountId: string): Promise<Stripe.LoginLink> {
  try {
    console.log(`[STRIPE-SERVICE] Creating dashboard login link for Connect account ${accountId}`);

    const loginLink = await stripe.accounts.createLoginLink(accountId);

    console.log(`[STRIPE-SERVICE] Created dashboard login link: ${loginLink.url}`);
    return loginLink;
  } catch (error) {
    console.error(`[STRIPE-SERVICE] Error creating dashboard login link for account ${accountId}:`, error);
    throw error;
  }
}

export default {
  createPaymentIntent,
  retrievePaymentIntent,
  cancelPaymentIntent,
  verifyWebhookSignature,
  createCheckoutSession,
  createConnectAccount,
  createAccountLink,
  retrieveConnectAccount,
  createTransfer,
  createDashboardLoginLink,
  stripe, // Export the Stripe instance for advanced usage
};
