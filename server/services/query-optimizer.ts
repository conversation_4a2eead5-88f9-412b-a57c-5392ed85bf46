import { db } from '../db';
import { sql } from 'drizzle-orm';

/**
 * Query Optimizer Service
 * Provides optimized versions of common database queries
 */
export class QueryOptimizerService {
  private static instance: QueryOptimizerService;
  private queryCache: Map<string, any> = new Map();
  private cacheExpiration: Map<string, number> = new Map();
  private readonly CACHE_TTL = 60000; // 1 minute in milliseconds
  private readonly SLOW_QUERY_THRESHOLD = 200; // ms

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance
   * @returns The QueryOptimizerService instance
   */
  public static getInstance(): QueryOptimizerService {
    if (!QueryOptimizerService.instance) {
      QueryOptimizerService.instance = new QueryOptimizerService();
    }
    return QueryOptimizerService.instance;
  }

  /**
   * Execute a query with performance monitoring and caching
   * @param queryKey - A unique key for the query
   * @param queryFn - The function that executes the query
   * @param ttl - Optional custom TTL for this query
   * @returns The query result
   */
  public async executeQuery<T>(
    queryKey: string,
    queryFn: () => Promise<T>,
    ttl: number = this.CACHE_TTL
  ): Promise<T> {
    // Check if we have a valid cache entry
    if (this.queryCache.has(queryKey)) {
      const expiration = this.cacheExpiration.get(queryKey) || 0;
      if (expiration > Date.now()) {
        return this.queryCache.get(queryKey) as T;
      }
    }

    // Cache miss or expired, execute the query
    const startTime = Date.now();
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;

      // Log slow queries
      if (duration > this.SLOW_QUERY_THRESHOLD) {
        console.log(`[DB] Slow query detected (${duration}ms): ${queryKey}`);
      }

      // Cache the result
      this.queryCache.set(queryKey, result);
      this.cacheExpiration.set(queryKey, Date.now() + ttl);

      // *** Added for debugging: Clear cache for this specific profile after fetching ***
      if (queryKey.startsWith('userProfile:')) {
        console.log(`[DB] Debugging: Clearing cache for ${queryKey} after fetch.`);
        this.queryCache.delete(queryKey);
        this.cacheExpiration.delete(queryKey);
      }

      return result;
    } catch (error) {
      console.error(`[DB] Error executing query ${queryKey}:`, error);
      throw error;
    }
  }

  /**
   * Clear the query cache
   * @param keyPattern - Optional pattern to match keys to clear
   */
  public clearCache(keyPattern?: string): void {
    if (keyPattern) {
      const regex = new RegExp(keyPattern);
      for (const key of Array.from(this.queryCache.keys())) {
        if (regex.test(key)) {
          this.queryCache.delete(key);
          this.cacheExpiration.delete(key);
        }
      }
    } else {
      this.queryCache.clear();
      this.cacheExpiration.clear();
    }
  }

  /**
   * Get sessions with optimized query
   * @param filters - Optional filters
   * @returns The sessions
   */
  public async getSessions(filters?: Record<string, any>): Promise<any[]> {
    const queryKey = `sessions:${JSON.stringify(filters || {})}`;

    return this.executeQuery(queryKey, async () => {
      // Build the query as a string
      let queryStr = `
        SELECT s.*,
               u.email as teacher_username,
               p.name as teacher_name,
               p.avatar as teacher_avatar
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON s.teacher_id = p.user_id
      `;

      const conditions: string[] = [];
      const params: any[] = [];

      if (filters) {
        if (filters.isPublic === 'true') {
          conditions.push('s.is_public = true');
        }

        if (filters.type) {
          conditions.push('s.type = $1');
          params.push(filters.type);
        }

        if (filters.skillLevel) {
          conditions.push('s.skill_level = $' + (params.length + 1));
          params.push(filters.skillLevel);
        }

        if (filters.teacherId) {
          conditions.push('s.teacher_id = $' + (params.length + 1));
          params.push(Number(filters.teacherId));
        }
      }

      if (conditions.length > 0) {
        queryStr += ` WHERE ${conditions.join(' AND ')}`;
      }

      queryStr += ` ORDER BY s.date ASC`;

      const result = await db.query(queryStr, params);
      return result.rows;
    });
  }

  /**
   * Get a session by ID with optimized query
   * @param sessionId - The session ID
   * @returns The session
   */
  public async getSession(sessionId: number): Promise<any> {
    const queryKey = `session:${sessionId}`;

    return this.executeQuery(queryKey, async () => {
      const query = `
        SELECT s.*,
               u.email as teacher_username,
               p.name as teacher_name,
               p.avatar as teacher_avatar
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON s.teacher_id = p.user_id
        WHERE s.id = ${sessionId}
      `;

      const result = await db.query(query, []);
      return result.rows[0];
    });
  }

  /**
   * Get user profile with optimized query
   * @param userId - The user ID (UUID from Supabase auth)
   * @returns The user profile
   */
  public async getUserProfile(userId: string): Promise<any> {
    const queryKey = `userProfile:${userId}`;

    console.log(`[QueryOptimizer] getUserProfile called with userId: ${userId}`);

    return this.executeQuery(queryKey, async () => {
      const query = `
        SELECT u.id, u.email, u.created_at,
               p.*
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE u.id = $1
      `;

      // Pass userId as a parameter to the query
      const result = await db.query(query, [userId]);
      console.log(`[QueryOptimizer] Database query result for userId ${userId}:`, result.rows[0]);
      return result.rows[0];
    });
  }

  /**
   * Get reviews for a session with optimized query
   * @param sessionId - The session ID
   * @returns The reviews
   */
  public async getSessionReviews(sessionId: number): Promise<any[]> {
    const queryKey = `sessionReviews:${sessionId}`;

    return this.executeQuery(queryKey, async () => {
      const query = `
        SELECT r.*,
               u.email as reviewer_email,
               p.name as reviewer_name,
               p.avatar as reviewer_avatar
        FROM content.reviews r
        LEFT JOIN auth.users u ON r.user_id = u.id
        LEFT JOIN profiles.user_profiles p ON r.user_id = p.user_id
        WHERE r.session_id = ${sessionId}
      `;

      const result = await db.query(query, []);
      return result.rows;
    });
  }

  /**
   * Get top teachers with optimized query
   * @param limit - The maximum number of teachers to return
   * @returns The top teachers
   */
  public async getTopTeachers(limit: number | string = 10): Promise<any[]> {
    // Convert string to number if needed
    const limitNum = typeof limit === 'string' ? parseInt(limit) || 10 : limit;
    const queryKey = `topTeachers:${limitNum}`;

    return this.executeQuery(queryKey, async () => {
      const query = `
        SELECT p.*, u.email
        FROM profiles.user_profiles p
        JOIN auth.users u ON p.user_id = u.id
        WHERE p.is_teacher = true
        ORDER BY p.rating DESC NULLS LAST
        LIMIT $1
      `;

      const result = await db.query(query, [limitNum]);
      return result.rows;
    });
  }
}

export default QueryOptimizerService.getInstance();
