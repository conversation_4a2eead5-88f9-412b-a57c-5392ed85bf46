/**
 * Stripe Analytics Service
 * Uses Supabase Stripe Wrapper for enhanced payment analytics
 */

import { supabaseAdmin as supabase } from '../lib/supabase';

export interface BookingAnalytics {
    booking_id: string;
    user_id: string;
    session_id: string;
    booking_status: string;
    payment_id: string | null;
    local_payment_status: string | null;
    local_payment_amount: number | null;
    payment_processor: string | null;
    booking_created_at: string;
    stripe_payment_intent_id: string | null;
    stripe_amount_cents: number | null;
    stripe_amount_dollars: number | null;
    currency: string | null;
    stripe_payment_status: string | null;
    stripe_created_at: string | null;
    stripe_metadata: any;
    receipt_email: string | null;
    stripe_customer_id: string | null;
    amount_variance: number | null;
    reconciliation_status: string;
}

export interface RevenueSummary {
    payment_date: string;
    currency: string;
    total_transactions: number;
    successful_transactions: number;
    failed_transactions: number;
    canceled_transactions: number;
    gross_revenue_dollars: number;
    net_revenue_dollars: number;
    avg_transaction_amount: number;
    success_rate_percent: number;
}

export interface TeacherEarnings {
    teacher_id: string;
    teacher_name: string | null;
    teacher_email: string;
    teacher_rating: number | null;
    review_count: number | null;
    total_bookings: number;
    successful_bookings: number;
    confirmed_bookings: number;
    gross_stripe_revenue: number | null;
    total_platform_fees: number | null;
    net_earnings: number | null;
    avg_earnings_per_booking: number | null;
}

export interface PaymentReconciliation {
    data_source: string;
    booking_id: string | null;
    payment_id: string;
    amount: number;
    status: string;
    created_at: string;
    issue: string;
}

export interface DataSummary {
    table_name: string;
    record_count: number;
    stripe_payments: number;
    description: string;
}

class StripeAnalyticsService {
    /**
     * Get booking analytics data
     */
    async getBookingAnalytics(limit = 100, offset = 0): Promise<BookingAnalytics[]> {
        try {
            const { data, error } = await supabase.rpc('get_booking_analytics', {
                limit_count: limit,
                offset_count: offset
            });

            if (error) {
                console.error('[StripeAnalytics] Error fetching booking analytics:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('[StripeAnalytics] Error in getBookingAnalytics:', error);
            throw error;
        }
    }

    /**
     * Get revenue summary by date range
     */
    async getRevenueSummary(
        startDate?: string,
        endDate?: string,
        limit = 30
    ): Promise<RevenueSummary[]> {
        try {
            const { data, error } = await supabase.rpc('get_revenue_summary', {
                start_date: startDate || null,
                end_date: endDate || null,
                limit_count: limit
            });

            if (error) {
                console.error('[StripeAnalytics] Error fetching revenue summary:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('[StripeAnalytics] Error in getRevenueSummary:', error);
            throw error;
        }
    }

    /**
     * Get teacher earnings data
     */
    async getTeacherEarnings(teacherId?: string): Promise<TeacherEarnings[]> {
        try {
            const { data, error } = await supabase.rpc('get_teacher_earnings', {
                teacher_id_param: teacherId || null
            });

            if (error) {
                console.error('[StripeAnalytics] Error fetching teacher earnings:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('[StripeAnalytics] Error in getTeacherEarnings:', error);
            throw error;
        }
    }

    /**
     * Get payment reconciliation issues
     */
    async getPaymentReconciliation(): Promise<PaymentReconciliation[]> {
        try {
            const { data, error } = await supabase.rpc('get_payment_reconciliation');

            if (error) {
                console.error('[StripeAnalytics] Error fetching payment reconciliation:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('[StripeAnalytics] Error in getPaymentReconciliation:', error);
            throw error;
        }
    }

    /**
     * Get current data summary
     */
    async getDataSummary(): Promise<DataSummary[]> {
        try {
            const { data, error } = await supabase.rpc('get_data_summary');

            if (error) {
                console.error('[StripeAnalytics] Error fetching data summary:', error);
                throw error;
            }

            return data || [];
        } catch (error) {
            console.error('[StripeAnalytics] Error in getDataSummary:', error);
            throw error;
        }
    }

    /**
     * Get total revenue metrics
     */
    async getTotalRevenueMetrics(): Promise<{
        total_revenue: number;
        total_transactions: number;
        avg_transaction_value: number;
        success_rate: number;
    }> {
        try {
            const { data, error } = await supabase.rpc('get_total_revenue_metrics');

            if (error) {
                // If the RPC doesn't exist, calculate from revenue_summary view
                const revenueSummary = await this.getRevenueSummary(undefined, undefined, 1000);

                const totals = revenueSummary.reduce((acc, day) => ({
                    total_revenue: acc.total_revenue + (day.net_revenue_dollars || 0),
                    total_transactions: acc.total_transactions + day.total_transactions,
                    successful_transactions: acc.successful_transactions + day.successful_transactions,
                }), { total_revenue: 0, total_transactions: 0, successful_transactions: 0 });

                return {
                    total_revenue: totals.total_revenue,
                    total_transactions: totals.total_transactions,
                    avg_transaction_value: totals.total_transactions > 0
                        ? totals.total_revenue / totals.total_transactions
                        : 0,
                    success_rate: totals.total_transactions > 0
                        ? (totals.successful_transactions / totals.total_transactions) * 100
                        : 0
                };
            }

            return data;
        } catch (error) {
            console.error('[StripeAnalytics] Error in getTotalRevenueMetrics:', error);
            throw error;
        }
    }

    /**
     * Get teacher performance metrics
     */
    async getTeacherPerformanceMetrics(teacherId: string): Promise<{
        total_earnings: number;
        total_bookings: number;
        success_rate: number;
        avg_booking_value: number;
    }> {
        try {
            const teacherData = await this.getTeacherEarnings(teacherId);

            if (teacherData.length === 0) {
                return {
                    total_earnings: 0,
                    total_bookings: 0,
                    success_rate: 0,
                    avg_booking_value: 0
                };
            }

            const teacher = teacherData[0];

            return {
                total_earnings: teacher.net_earnings || 0,
                total_bookings: teacher.total_bookings,
                success_rate: teacher.total_bookings > 0
                    ? (teacher.successful_bookings / teacher.total_bookings) * 100
                    : 0,
                avg_booking_value: teacher.avg_earnings_per_booking || 0
            };
        } catch (error) {
            console.error('[StripeAnalytics] Error in getTeacherPerformanceMetrics:', error);
            throw error;
        }
    }

    /**
     * Test Stripe wrapper connection
     */
    async testConnection(): Promise<{
        connected: boolean;
        stripe_payment_intents_accessible: boolean;
        local_bookings_count: number;
        error?: string;
    }> {
        try {
            const { data, error } = await supabase.rpc('test_stripe_connection');

            if (error) {
                console.error('[StripeAnalytics] Connection test failed:', error);
                return {
                    connected: false,
                    stripe_payment_intents_accessible: false,
                    local_bookings_count: 0,
                    error: error.message
                };
            }

            return {
                connected: data.connected,
                stripe_payment_intents_accessible: data.stripe_payment_intents_accessible,
                local_bookings_count: data.local_bookings_count
            };
        } catch (error) {
            console.error('[StripeAnalytics] Connection test failed:', error);
            return {
                connected: false,
                stripe_payment_intents_accessible: false,
                local_bookings_count: 0,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}

export const stripeAnalyticsService = new StripeAnalyticsService();
export default stripeAnalyticsService; 