/**
 * Backend Supabase Profile Service
 *
 * This service handles profile-related operations that require the service role key.
 * These operations should NEVER be exposed directly to the frontend.
 */

import { supabaseAdmin } from '../../lib/supabase';

/**
 * Types for profile operations
 */
export interface UserProfile {
  id?: string;
  user_id: string;
  username?: string;
  name?: string;
  avatar?: string;
  cover_photo?: string;
  cover_photo_position?: string;
  bio?: string;
  location?: string;
  timezone?: string;
  website?: string;
  is_teacher?: boolean;
  is_teacher?: boolean;
  is_admin?: boolean;
  specializations?: string[];
  skills?: string[];
  certifications?: string[];
  experience?: string;
  education?: string;
  facebook_url?: string;
  twitter_url?: string;
  instagram_url?: string;
  linkedin_url?: string;
  youtube_url?: string;
  tiktok_url?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get a user profile
 * @param userId User ID
 * @returns User profile
 */
export async function getUserProfile(userId: string) {
  try {
    console.log(`[Backend:ProfileService] Getting user profile for user ${userId}`);

    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('[Backend:ProfileService] Error getting profile:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('[Backend:ProfileService] Error getting profile:', error);
    throw error;
  }
}

/**
 * Create a user profile
 * @param profileData Profile data
 * @returns Created profile
 */
export async function createUserProfile(profileData: UserProfile) {
  try {
    console.log(`[Backend:ProfileService] Creating profile for user ${profileData.user_id}`);

    // Add timestamps
    const data = {
      ...profileData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: profile, error } = await supabaseAdmin
      .from('user_profiles')
      .insert([data])
      .select()
      .single();

    if (error) {
      // If the error is a duplicate key violation (unique constraint violation)
      if (error.code === '23505' || error.message?.includes('duplicate key value')) {
        console.log('[Backend:ProfileService] Profile already exists (concurrent creation), fetching existing profile');
        // If it's a duplicate key error, just fetch the existing profile
        const { data: existingProfile, error: fetchError } = await supabaseAdmin
          .from('user_profiles')
          .select('*')
          .eq('user_id', profileData.user_id)
          .single();

        if (fetchError) {
          console.error('[Backend:ProfileService] Error fetching existing profile:', fetchError);
          throw fetchError;
        }

        return existingProfile;
      } else {
        console.error('[Backend:ProfileService] Error creating profile:', error);
        throw error;
      }
    }

    return profile;
  } catch (error) {
    console.error('[Backend:ProfileService] Error creating profile:', error);
    throw error;
  }
}

/**
 * Update a user profile
 * @param userId User ID
 * @param profileData Profile data to update
 * @returns Updated profile
 */
export async function updateUserProfile(userId: string, profileData: Partial<UserProfile>) {
  try {
    console.log(`[Backend:ProfileService] Updating profile for user ${userId}`);

    // Add updated_at timestamp
    const data = {
      ...profileData,
      updated_at: new Date().toISOString()
    };

    const { data: profile, error } = await supabaseAdmin
      .from('user_profiles')
      .update(data)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('[Backend:ProfileService] Error updating profile:', error);
      throw error;
    }

    return profile;
  } catch (error) {
    console.error('[Backend:ProfileService] Error updating profile:', error);
    throw error;
  }
}

/**
 * Delete a user profile
 * @param userId User ID
 * @returns Success status
 */
export async function deleteUserProfile(userId: string) {
  try {
    console.log(`[Backend:ProfileService] Deleting profile for user ${userId}`);

    const { error } = await supabaseAdmin
      .from('user_profiles')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('[Backend:ProfileService] Error deleting profile:', error);
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('[Backend:ProfileService] Error deleting profile:', error);
    throw error;
  }
}

/**
 * Ensure a user profile exists
 * @param userId User ID
 * @param profileData Optional profile data
 * @returns User profile
 */
export async function ensureUserProfile(userId: string, profileData?: Partial<UserProfile>) {
  try {
    console.log(`[Backend:ProfileService] Ensuring profile exists for user ${userId}`);

    // Check if profile exists
    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (error && error.code !== 'PGRST116') {
      console.error('[Backend:ProfileService] Error checking profile:', error);
      throw error;
    }

    if (data) {
      console.log('[Backend:ProfileService] Profile exists, returning it');
      return data;
    }

    console.log('[Backend:ProfileService] Profile does not exist, creating it');

    // Get user data from auth
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);

    if (userError) {
      console.error('[Backend:ProfileService] Error getting user data:', userError);
      throw userError;
    }

    // Create profile with user data
    const newProfileData: UserProfile = {
      user_id: userId,
      name: profileData?.name || userData.user?.user_metadata?.name || userData.user?.email?.split('@')[0] || 'User',
      avatar: profileData?.avatar || userData.user?.user_metadata?.avatar_url || null,
      ...profileData
    };

    return await createUserProfile(newProfileData);
  } catch (error) {
    console.error('[Backend:ProfileService] Error ensuring profile exists:', error);
    throw error;
  }
}

/**
 * Get teacher profiles
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of teacher profiles
 */
export async function getTeacherProfiles(limit = 20, offset = 0) {
  try {
    console.log('[Backend:ProfileService] Getting teacher profiles');

    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('is_teacher', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:ProfileService] Error getting teacher profiles:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('[Backend:ProfileService] Error getting teacher profiles:', error);
    throw error;
  }
}

/**
 * Set user as teacher
 * @param userId User ID
 * @param isTeacher Whether the user is a teacher
 * @returns Updated profile
 */
export async function setUserAsTeacher(userId: string, isTeacher: boolean) {
  try {
    console.log(`[Backend:ProfileService] Setting user ${userId} as teacher: ${isTeacher}`);

    return await updateUserProfile(userId, { is_teacher: isTeacher });
  } catch (error) {
    console.error('[Backend:ProfileService] Error setting user as teacher:', error);
    throw error;
  }
}

/**
 * Set user as admin
 * @param userId User ID
 * @param isAdmin Whether the user is an admin
 * @returns Updated profile
 */
export async function setUserAsAdmin(userId: string, isAdmin: boolean) {
  try {
    console.log(`[Backend:ProfileService] Setting user ${userId} as admin: ${isAdmin}`);

    return await updateUserProfile(userId, { is_admin: isAdmin });
  } catch (error) {
    console.error('[Backend:ProfileService] Error setting user as admin:', error);
    throw error;
  }
}

export const profileService = {
  getUserProfile,
  createUserProfile,
  updateUserProfile,
  deleteUserProfile,
  ensureUserProfile,
  getTeacherProfiles,
  setUserAsTeacher,
  setUserAsAdmin
};
