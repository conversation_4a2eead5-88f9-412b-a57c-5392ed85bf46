/**
 * Backend Supabase Booking Service
 * 
 * This service handles booking-related operations that require the service role key.
 * These operations should NEVER be exposed directly to the frontend.
 */

import { supabaseAdmin } from '../../lib/supabase';

/**
 * Types for booking operations
 */
export interface Booking {
  id?: string;
  session_id: string;
  time_slot_id?: string;
  user_id: string;
  status: string;
  payment_status: string;
  payment_intent_id?: string;
  amount_paid?: number;
  booking_notes?: string;
  cancellation_reason?: string;
  refund_amount?: number;
  refund_status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface TimeSlot {
  id?: string;
  session_id: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  max_bookings?: number;
  current_bookings?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get all bookings (bypassing RLS)
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of bookings
 */
export async function getAllBookings(limit = 20, offset = 0) {
  try {
    console.log('[Backend:BookingService] Getting all bookings');

    const { data, error } = await supabaseAdmin
      .from('bookings')
      .select('*, sessions(*), user_profiles(*)')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:BookingService] Error getting bookings:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('[Backend:BookingService] Error getting bookings:', error);
    throw error;
  }
}

/**
 * Get a booking by ID (bypassing RLS)
 * @param bookingId Booking ID
 * @returns Booking
 */
export async function getBooking(bookingId: string) {
  try {
    console.log(`[Backend:BookingService] Getting booking ${bookingId}`);

    const { data, error } = await supabaseAdmin
      .from('bookings')
      .select('*, sessions(*), time_slots(*)')
      .eq('id', bookingId)
      .single();

    if (error) {
      console.error('[Backend:BookingService] Error getting booking:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('[Backend:BookingService] Error getting booking:', error);
    throw error;
  }
}

/**
 * Create a booking (bypassing RLS)
 * @param bookingData Booking data
 * @returns Created booking
 */
export async function createBooking(bookingData: Booking) {
  try {
    console.log('[Backend:BookingService] Creating booking');

    // Add timestamps
    const data = {
      ...bookingData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: booking, error } = await supabaseAdmin
      .from('bookings')
      .insert([data])
      .select()
      .single();

    if (error) {
      console.error('[Backend:BookingService] Error creating booking:', error);
      throw error;
    }

    // If there's a time slot, update its current_bookings count
    if (booking.time_slot_id) {
      await updateTimeSlotBookingCount(booking.time_slot_id);
    }

    return booking;
  } catch (error) {
    console.error('[Backend:BookingService] Error creating booking:', error);
    throw error;
  }
}

/**
 * Update a booking (bypassing RLS)
 * @param bookingId Booking ID
 * @param bookingData Booking data to update
 * @returns Updated booking
 */
export async function updateBooking(bookingId: string, bookingData: Partial<Booking>) {
  try {
    console.log(`[Backend:BookingService] Updating booking ${bookingId}`);

    // Add updated_at timestamp
    const data = {
      ...bookingData,
      updated_at: new Date().toISOString()
    };

    const { data: booking, error } = await supabaseAdmin
      .from('bookings')
      .update(data)
      .eq('id', bookingId)
      .select()
      .single();

    if (error) {
      console.error('[Backend:BookingService] Error updating booking:', error);
      throw error;
    }

    return booking;
  } catch (error) {
    console.error('[Backend:BookingService] Error updating booking:', error);
    throw error;
  }
}

/**
 * Delete a booking (bypassing RLS)
 * @param bookingId Booking ID
 * @returns Success status
 */
export async function deleteBooking(bookingId: string) {
  try {
    console.log(`[Backend:BookingService] Deleting booking ${bookingId}`);

    // Get the booking to get the time slot ID
    const booking = await getBooking(bookingId);

    const { error } = await supabaseAdmin
      .from('bookings')
      .delete()
      .eq('id', bookingId);

    if (error) {
      console.error('[Backend:BookingService] Error deleting booking:', error);
      throw error;
    }

    // If there's a time slot, update its current_bookings count
    if (booking.time_slot_id) {
      await updateTimeSlotBookingCount(booking.time_slot_id);
    }

    return { success: true };
  } catch (error) {
    console.error('[Backend:BookingService] Error deleting booking:', error);
    throw error;
  }
}

/**
 * Cancel a booking with refund
 * @param bookingId Booking ID
 * @param reason Cancellation reason
 * @param refundAmount Optional refund amount
 * @returns Updated booking
 */
export async function cancelBookingWithRefund(bookingId: string, reason: string, refundAmount?: number) {
  try {
    console.log(`[Backend:BookingService] Cancelling booking ${bookingId} with refund`);

    // Get the booking
    const booking = await getBooking(bookingId);

    if (!booking) {
      throw new Error('Booking not found');
    }

    // Calculate refund amount if not provided
    const refund = refundAmount !== undefined ? refundAmount : booking.amount_paid;

    // Process refund (this would typically involve a payment gateway)
    // For now, we'll just update the booking status

    // Update the booking
    const updatedBooking = await updateBooking(bookingId, {
      status: 'cancelled',
      cancellation_reason: reason,
      refund_amount: refund,
      refund_status: 'completed'
    });

    // If there's a time slot, update its current_bookings count
    if (booking.time_slot_id) {
      await updateTimeSlotBookingCount(booking.time_slot_id);
    }

    return updatedBooking;
  } catch (error) {
    console.error('[Backend:BookingService] Error cancelling booking with refund:', error);
    throw error;
  }
}

/**
 * Get bookings for a session (bypassing RLS)
 * @param sessionId Session ID
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of bookings
 */
export async function getSessionBookings(sessionId: string, limit = 20, offset = 0) {
  try {
    console.log(`[Backend:BookingService] Getting bookings for session ${sessionId}`);

    const { data, error } = await supabaseAdmin
      .from('bookings')
      .select('*, user_profiles(*)')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:BookingService] Error getting session bookings:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('[Backend:BookingService] Error getting session bookings:', error);
    throw error;
  }
}

/**
 * Get bookings for a user (bypassing RLS)
 * @param userId User ID
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of bookings
 */
export async function getUserBookings(userId: string, limit = 20, offset = 0) {
  try {
    console.log(`[Backend:BookingService] Getting bookings for user ${userId}`);

    // Try to get bookings with session data using the foreign key relationship
    let { data, error } = await supabaseAdmin
      .from('bookings')
      .select(`
        *,
        sessions:session_id (*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:BookingService] Error getting user bookings with join:', error);

      // If the join fails, try without the join and manually fetch sessions
      const { data: bookingsData, error: bookingsError } = await supabaseAdmin
        .from('bookings')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (bookingsError) {
        console.error('[Backend:BookingService] Error getting user bookings without join:', bookingsError);
        throw bookingsError;
      }

      // If no bookings, return empty array
      if (!bookingsData || bookingsData.length === 0) {
        console.log(`[Backend:BookingService] No bookings found for user ${userId}`);
        return [];
      }

      // Manually fetch session data for each booking
      const bookingsWithSessions = await Promise.all(
        bookingsData.map(async (booking) => {
          if (booking.session_id) {
            const { data: sessionData } = await supabaseAdmin
              .from('sessions')
              .select('*')
              .eq('id', booking.session_id)
              .single();

            return {
              ...booking,
              sessions: sessionData
            };
          }
          return booking;
        })
      );

      return bookingsWithSessions;
    }

    return data || [];
  } catch (error) {
    console.error('[Backend:BookingService] Error getting user bookings:', error);
    throw error;
  }
}

/**
 * Create a time slot (bypassing RLS)
 * @param timeSlotData Time slot data
 * @returns Created time slot
 */
export async function createTimeSlot(timeSlotData: TimeSlot) {
  try {
    console.log('[Backend:BookingService] Creating time slot');

    // Add timestamps
    const data = {
      ...timeSlotData,
      current_bookings: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: timeSlot, error } = await supabaseAdmin
      .from('time_slots')
      .insert([data])
      .select()
      .single();

    if (error) {
      console.error('[Backend:BookingService] Error creating time slot:', error);
      throw error;
    }

    return timeSlot;
  } catch (error) {
    console.error('[Backend:BookingService] Error creating time slot:', error);
    throw error;
  }
}

/**
 * Update time slot booking count
 * @param timeSlotId Time slot ID
 * @returns Updated time slot
 */
export async function updateTimeSlotBookingCount(timeSlotId: string) {
  try {
    console.log(`[Backend:BookingService] Updating booking count for time slot ${timeSlotId}`);

    // Count active bookings for this time slot
    const { count, error: countError } = await supabaseAdmin
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('time_slot_id', timeSlotId)
      .eq('status', 'confirmed');

    if (countError) {
      console.error('[Backend:BookingService] Error counting bookings:', countError);
      throw countError;
    }

    // Update the time slot
    const { data: timeSlot, error } = await supabaseAdmin
      .from('time_slots')
      .update({
        current_bookings: count || 0,
        updated_at: new Date().toISOString()
      })
      .eq('id', timeSlotId)
      .select()
      .single();

    if (error) {
      console.error('[Backend:BookingService] Error updating time slot:', error);
      throw error;
    }

    return timeSlot;
  } catch (error) {
    console.error('[Backend:BookingService] Error updating time slot booking count:', error);
    throw error;
  }
}

export const bookingService = {
  getAllBookings,
  getBooking,
  createBooking,
  updateBooking,
  deleteBooking,
  cancelBookingWithRefund,
  getSessionBookings,
  getUserBookings,
  createTimeSlot,
  updateTimeSlotBookingCount
};
