/**
 * Backend Supabase Session Service
 *
 * This service handles session-related operations that require the service role key.
 * These operations should NEVER be exposed directly to the frontend.
 */

import { supabaseAdmin } from '../../lib/supabase';

/**
 * Types for session operations
 */
export interface Session {
  id?: string;
  title: string;
  description?: string;
  teacher_id: string;
  image_url?: string;
  session_type?: string;
  session_format?: string;
  skill_level?: string;
  price: number;
  duration: number;
  max_participants?: number;
  location_type?: string;
  location_details?: any;
  is_published?: boolean;
  is_featured?: boolean;
  is_private?: boolean;
  cancellation_policy?: string;
  rating?: number;
  review_count?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get all sessions (including unpublished)
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of sessions with teacher data
 */
export async function getAllSessions(limit = 20, offset = 0) {
  try {
    console.log('[Backend:SessionService] Getting all sessions');

    const { data, error } = await supabaseAdmin
      .from('sessions_view')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:SessionService] Error getting sessions:', error);
      throw error;
    }

    // Transform the data to include teacher information
    const sessionsWithTeacher = data?.map(session => ({
      ...session,
      teacher: session.teacher_name ? {
        id: session.teacher_id,
        name: session.teacher_name,
        username: session.teacher_username,
        avatar: session.teacher_avatar,
        bio: session.teacher_bio
      } : null
    })) || [];

    console.log(`[Backend:SessionService] Found ${sessionsWithTeacher.length} sessions with teacher data`);
    return sessionsWithTeacher;
  } catch (error) {
    console.error('[Backend:SessionService] Error getting sessions:', error);
    throw error;
  }
}

/**
 * Get a session by ID (bypassing RLS)
 * @param sessionId Session ID
 * @returns Session with teacher data
 */
export async function getSession(sessionId: string) {
  try {
    console.log(`[Backend:SessionService] Getting session ${sessionId}`);

    // Try sessions_view first (which should have teacher data pre-joined)
    const { data: viewData, error: viewError } = await supabaseAdmin
      .from('sessions_view')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (!viewError && viewData) {
      // Transform the view data to include teacher information
      const sessionWithTeacher = {
        ...viewData,
        teacher: viewData.teacher_name ? {
          id: viewData.teacher_id,
          name: viewData.teacher_name,
          username: viewData.teacher_username,
          avatar: viewData.teacher_avatar,
          bio: viewData.teacher_bio
        } : null
      };

      console.log(`[Backend:SessionService] Found session ${sessionId} from view with teacher:`, sessionWithTeacher?.teacher?.name || 'no teacher');
      return sessionWithTeacher;
    }

    console.log(`[Backend:SessionService] View query failed for session ${sessionId}, trying direct session table:`, viewError);

    // Fallback: Get session directly and then fetch teacher separately
    const { data: sessionData, error: sessionError } = await supabaseAdmin
      .from('sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (sessionError) {
      console.error('[Backend:SessionService] Error getting session from sessions table:', sessionError);
      throw sessionError;
    }

    if (!sessionData) {
      console.log(`[Backend:SessionService] Session ${sessionId} not found`);
      return null;
    }

    // Fetch teacher data separately
    let teacher = null;
    if (sessionData.teacher_id) {
      const { data: teacherData, error: teacherError } = await supabaseAdmin
        .from('user_profiles')
        .select('id, name, username, avatar, bio')
        .eq('id', sessionData.teacher_id)
        .single();

      if (!teacherError && teacherData) {
        teacher = teacherData;
      } else {
        console.log(`[Backend:SessionService] Could not fetch teacher ${sessionData.teacher_id}:`, teacherError);
      }
    }

    const sessionWithTeacher = {
      ...sessionData,
      teacher: teacher
    };

    console.log(`[Backend:SessionService] Found session ${sessionId} with manual teacher join:`, sessionWithTeacher?.teacher?.name || 'no teacher');
    return sessionWithTeacher;
  } catch (error) {
    console.error('[Backend:SessionService] Error getting session:', error);
    throw error;
  }
}

/**
 * Create a session (bypassing RLS)
 * @param sessionData Session data
 * @returns Created session
 */
export async function createSession(sessionData: Session) {
  try {
    console.log('[Backend:SessionService] Creating session');

    // Add timestamps
    const data = {
      ...sessionData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: session, error } = await supabaseAdmin
      .from('sessions')
      .insert([data])
      .select()
      .single();

    if (error) {
      console.error('[Backend:SessionService] Error creating session:', error);
      throw error;
    }

    return session;
  } catch (error) {
    console.error('[Backend:SessionService] Error creating session:', error);
    throw error;
  }
}

/**
 * Update a session (bypassing RLS)
 * @param sessionId Session ID
 * @param sessionData Session data to update
 * @returns Updated session
 */
export async function updateSession(sessionId: string, sessionData: Partial<Session>) {
  try {
    console.log(`[Backend:SessionService] Updating session ${sessionId}`);

    // Add updated_at timestamp
    const data = {
      ...sessionData,
      updated_at: new Date().toISOString()
    };

    const { data: session, error } = await supabaseAdmin
      .from('sessions')
      .update(data)
      .eq('id', sessionId)
      .select()
      .single();

    if (error) {
      console.error('[Backend:SessionService] Error updating session:', error);
      throw error;
    }

    return session;
  } catch (error) {
    console.error('[Backend:SessionService] Error updating session:', error);
    throw error;
  }
}

/**
 * Delete a session (bypassing RLS)
 * @param sessionId Session ID
 * @returns Success status
 */
export async function deleteSession(sessionId: string) {
  try {
    console.log(`[Backend:SessionService] Deleting session ${sessionId}`);

    const { error } = await supabaseAdmin
      .from('sessions')
      .delete()
      .eq('id', sessionId);

    if (error) {
      console.error('[Backend:SessionService] Error deleting session:', error);
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('[Backend:SessionService] Error deleting session:', error);
    throw error;
  }
}

/**
 * Feature a session
 * @param sessionId Session ID
 * @param isFeatured Whether the session is featured
 * @returns Updated session
 */
export async function featureSession(sessionId: string, isFeatured: boolean) {
  try {
    console.log(`[Backend:SessionService] Setting session ${sessionId} as featured: ${isFeatured}`);

    return await updateSession(sessionId, { is_featured: isFeatured });
  } catch (error) {
    console.error('[Backend:SessionService] Error featuring session:', error);
    throw error;
  }
}

/**
 * Get featured sessions
 * @param limit Optional limit
 * @returns Array of featured sessions with teacher data
 */
export async function getFeaturedSessions(limit = 10) {
  try {
    console.log('[Backend:SessionService] Getting featured sessions');

    const { data, error } = await supabaseAdmin
      .from('sessions_view')
      .select('*')
      .eq('is_featured', true)
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('[Backend:SessionService] Error getting featured sessions:', error);
      throw error;
    }

    // Transform the data to include teacher information
    const sessionsWithTeacher = data?.map(session => ({
      ...session,
      teacher: session.teacher_name ? {
        id: session.teacher_id,
        name: session.teacher_name,
        username: session.teacher_username,
        avatar: session.teacher_avatar,
        bio: session.teacher_bio
      } : null
    })) || [];

    console.log(`[Backend:SessionService] Found ${sessionsWithTeacher.length} featured sessions with teacher data`);
    return sessionsWithTeacher;
  } catch (error) {
    console.error('[Backend:SessionService] Error getting featured sessions:', error);
    throw error;
  }
}

/**
 * Get sessions by teacher (bypassing RLS)
 * @param teacherId Teacher ID
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of sessions with teacher data
 */
export async function getSessionsByTeacher(teacherId: string, limit = 20, offset = 0) {
  try {
    console.log(`[Backend:SessionService] Getting sessions for teacher ${teacherId}`);

    const { data, error } = await supabaseAdmin
      .from('sessions_view')
      .select('*')
      .eq('teacher_id', teacherId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:SessionService] Error getting sessions:', error);
      throw error;
    }

    // Transform the data to include teacher information
    const sessionsWithTeacher = data?.map(session => ({
      ...session,
      teacher: session.teacher_name ? {
        id: session.teacher_id,
        name: session.teacher_name,
        username: session.teacher_username,
        avatar: session.teacher_avatar,
        bio: session.teacher_bio
      } : null
    })) || [];

    console.log(`[Backend:SessionService] Found ${sessionsWithTeacher.length} sessions for teacher ${teacherId}`);
    return sessionsWithTeacher;
  } catch (error) {
    console.error('[Backend:SessionService] Error getting sessions:', error);
    throw error;
  }
}

/**
 * Get all published sessions
 * @param limit Optional limit
 * @param offset Optional offset
 * @returns Array of sessions with teacher data
 */
export async function getPublishedSessions(limit = 20, offset = 0) {
  try {
    console.log('[Backend:SessionService] Getting published sessions');

    const { data, error } = await supabaseAdmin
      .from('sessions_view')
      .select('*')
      .eq('is_public', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('[Backend:SessionService] Error getting published sessions:', error);
      throw error;
    }

    // Transform the data to include teacher information
    const sessionsWithTeacher = data?.map(session => ({
      ...session,
      teacher: session.teacher_name ? {
        id: session.teacher_id,
        name: session.teacher_name,
        username: session.teacher_username,
        avatar: session.teacher_avatar,
        bio: session.teacher_bio
      } : null
    })) || [];

    console.log(`[Backend:SessionService] Found ${sessionsWithTeacher.length} published sessions with teacher data`);
    return sessionsWithTeacher;
  } catch (error) {
    console.error('[Backend:SessionService] Error getting published sessions:', error);
    throw error;
  }
}

export const sessionService = {
  getAllSessions,
  getSession,
  createSession,
  updateSession,
  deleteSession,
  featureSession,
  getFeaturedSessions,
  getSessionsByTeacher,
  getPublishedSessions
};
