import { supabaseAdmin } from '../lib/supabase';
import { SupabaseBaseRepository } from '../repositories/SupabaseBaseRepository';

interface Booking {
    id: string;
    student_id: string;
    teacher_id: string;
    session_id: string;
    created_at: string;
}

interface Session {
    id: string;
    title: string;
    date: string;
    duration: number;
    automated_messages_json?: string;
    automated_messages?: Array<{
        id: string;
        title: string;
        content: string;
        triggerType: 'before' | 'after' | 'specific';
        triggerTime: number;
        triggerUnit: 'hours' | 'days' | 'weeks';
        specificDate?: Date;
        specificTime?: string;
        enabled: boolean;
    }>;
}

interface User {
    id: string;
    name: string;
    username: string;
    email?: string;
}

interface ScheduledMessage {
    id: string;
    conversation_id: string;
    sender_id: string;
    recipient_id: string;
    content: string;
    scheduled_time: string;
    status: 'pending' | 'sent' | 'failed';
    booking_id?: string;
    session_id?: string;
    metadata?: any;
    created_at: string;
    updated_at: string;
}

interface Conversation {
    id: string;
    participants: string[];
    booking_id?: string;
    created_at: string;
}

/**
 * Production-ready Message Scheduler using only Supabase
 * Eliminates PostgreSQL connection dependencies
 */
export class ProductionMessageScheduler extends SupabaseBaseRepository {
    private intervalId: NodeJS.Timeout | null = null;
    private isRunning = false;
    private readonly pollIntervalMs = 30000; // 30 seconds
    private consecutiveErrors = 0;
    private readonly maxConsecutiveErrors = 5;

    constructor() {
        super('scheduled_messages');
    }

    /**
     * Start the message scheduler
     */
    public start(): void {
        if (this.isRunning) {
            console.log('[ProductionMessageScheduler] Already running');
            return;
        }

        this.isRunning = true;
        console.log('[ProductionMessageScheduler] Starting message scheduler');

        // Run immediately
        this.processMessages().catch(error => {
            console.error('[ProductionMessageScheduler] Error in initial message processing:', error);
        });

        // Then run on interval
        this.intervalId = setInterval(() => {
            this.processMessages().catch(error => {
                console.error('[ProductionMessageScheduler] Error in scheduled message processing:', error);
                this.consecutiveErrors++;

                if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                    console.error(`[ProductionMessageScheduler] Too many consecutive errors (${this.consecutiveErrors}). Stopping scheduler.`);
                    this.stop();
                }
            });
        }, this.pollIntervalMs);
    }

    /**
     * Stop the message scheduler
     */
    public stop(): void {
        if (!this.isRunning) {
            console.log('[ProductionMessageScheduler] Not running');
            return;
        }

        this.isRunning = false;

        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        console.log('[ProductionMessageScheduler] Message scheduler stopped');
    }

    /**
     * Process pending scheduled messages
     */
    private async processMessages(): Promise<void> {
        try {
            console.log('[ProductionMessageScheduler] Checking for pending messages...');

            // Get pending messages that are due
            const pendingMessages = await this.executeQuery<ScheduledMessage>(
                'Get pending scheduled messages',
                () => this.client
                    .from(this.tableName)
                    .select('*')
                    .eq('status', 'pending')
                    .lte('scheduled_time', new Date().toISOString())
                    .order('scheduled_time', { ascending: true })
                    .limit(10)
            );

            if (pendingMessages.length === 0) {
                console.log('[ProductionMessageScheduler] No pending messages found');
                this.consecutiveErrors = 0; // Reset error count on success
                return;
            }

            console.log(`[ProductionMessageScheduler] Found ${pendingMessages.length} pending messages`);

            // Process each message
            for (const message of pendingMessages) {
                try {
                    await this.processMessage(message);
                    this.consecutiveErrors = 0; // Reset error count on success
                } catch (error) {
                    console.error(`[ProductionMessageScheduler] Error processing message ${message.id}:`, error);
                    await this.markMessageFailed(message.id, error instanceof Error ? error.message : String(error));
                }
            }

        } catch (error) {
            console.error('[ProductionMessageScheduler] Error fetching pending messages:', error);
            throw error;
        }
    }

    /**
     * Process a single message
     */
    private async processMessage(message: ScheduledMessage): Promise<void> {
        console.log(`[ProductionMessageScheduler] Processing message ${message.id} for conversation ${message.conversation_id}`);

        try {
            // Insert the message into the messages table
            await this.client
                .from('messages')
                .insert({
                    conversation_id: message.conversation_id,
                    sender_id: message.sender_id,
                    content: message.content,
                    created_at: new Date().toISOString(),
                    metadata: {
                        ...message.metadata,
                        automated: true,
                        original_scheduled_time: message.scheduled_time
                    }
                });

            // Mark message as sent
            await this.markMessageSent(message.id);

            console.log(`[ProductionMessageScheduler] Message ${message.id} sent successfully`);

        } catch (error) {
            console.error(`[ProductionMessageScheduler] Failed to send message ${message.id}:`, error);
            throw error;
        }
    }

    /**
     * Mark a message as sent
     */
    private async markMessageSent(messageId: string): Promise<void> {
        await this.executeUpdate(
            `Mark message ${messageId} as sent`,
            {
                status: 'sent',
                updated_at: new Date().toISOString()
            },
            (query) => query.eq('id', messageId)
        );
    }

    /**
     * Mark a message as failed
     */
    private async markMessageFailed(messageId: string, errorMessage: string): Promise<void> {
        await this.executeUpdate(
            `Mark message ${messageId} as failed`,
            {
                status: 'failed',
                error_message: errorMessage,
                updated_at: new Date().toISOString()
            },
            (query) => query.eq('id', messageId)
        );
    }

    /**
     * Schedule messages for a booking
     */
    public async scheduleMessagesForBooking(
        booking: Booking,
        session: Session,
        student: User,
        teacher: User
    ): Promise<void> {
        try {
            console.log(`[ProductionMessageScheduler] Scheduling messages for booking ${booking.id}`);

            // Find or create a conversation
            const conversation = await this.findOrCreateConversation(student.id, teacher.id, booking.id);

            // Process automated messages
            let automatedMessages: any[] = [];

            // Try to parse automatedMessagesJson if it exists
            if (session.automated_messages_json) {
                try {
                    automatedMessages = JSON.parse(session.automated_messages_json);
                } catch (e) {
                    console.error(`[ProductionMessageScheduler] Error parsing automated_messages_json:`, e);
                }
            }

            // Use automated_messages property if available and JSON parsing failed
            if ((!automatedMessages || automatedMessages.length === 0) && session.automated_messages) {
                automatedMessages = session.automated_messages;
            }

            if (!Array.isArray(automatedMessages) || automatedMessages.length === 0) {
                console.log(`[ProductionMessageScheduler] No automated messages found for session ${session.id}`);
                return;
            }

            for (const message of automatedMessages) {
                if (!message.enabled) continue;

                // Calculate when the message should be sent
                const messageTime = this.calculateMessageTime(message, session);

                // Only schedule future messages
                if (messageTime <= new Date()) {
                    console.log(`[ProductionMessageScheduler] Skipping message "${message.title}" as scheduled time is in the past`);
                    continue;
                }

                // Process message content with placeholders
                const content = this.replacePlaceholders(message.content, student, teacher, session);

                // Schedule the message
                await this.scheduleMessage(
                    conversation.id,
                    teacher.id,
                    student.id,
                    content,
                    messageTime,
                    {
                        messageId: message.id,
                        title: message.title,
                        triggerType: message.triggerType,
                        bookingId: booking.id,
                        sessionId: session.id
                    }
                );

                console.log(`[ProductionMessageScheduler] Scheduled message "${message.title}" for ${messageTime.toISOString()}`);
            }

            console.log(`[ProductionMessageScheduler] Finished scheduling messages for booking ${booking.id}`);
        } catch (error) {
            console.error('[ProductionMessageScheduler] Error scheduling messages:', error);
            throw error;
        }
    }

    /**
     * Find or create a conversation between two users
     */
    private async findOrCreateConversation(studentId: string, teacherId: string, bookingId: string): Promise<Conversation> {
        // Try to find existing conversation
        const existingConversations = await this.executeQuery<Conversation>(
            'Find existing conversation',
            () => this.client
                .from('conversations')
                .select('*')
                .contains('participants', [studentId, teacherId])
        );

        if (existingConversations.length > 0) {
            return existingConversations[0];
        }

        // Create new conversation
        const newConversations = await this.executeInsert<Conversation>(
            'Create new conversation',
            {
                participants: [studentId, teacherId],
                booking_id: bookingId,
                created_at: new Date().toISOString()
            }
        );

        return newConversations[0];
    }

    /**
     * Calculate when a message should be sent
     */
    private calculateMessageTime(message: any, session: Session): Date {
        let messageTime = new Date();

        if (message.triggerType === 'specific' && message.specificDate) {
            messageTime = new Date(message.specificDate);
            if (message.specificTime) {
                const [hours, minutes] = message.specificTime.split(':').map(Number);
                messageTime.setHours(hours, minutes, 0, 0);
            }
        } else if (message.triggerType === 'before') {
            messageTime = new Date(session.date);
            if (message.triggerUnit === 'hours') messageTime.setHours(messageTime.getHours() - message.triggerTime);
            else if (message.triggerUnit === 'days') messageTime.setDate(messageTime.getDate() - message.triggerTime);
            else if (message.triggerUnit === 'weeks') messageTime.setDate(messageTime.getDate() - (message.triggerTime * 7));
        } else if (message.triggerType === 'after') {
            messageTime = new Date(session.date);
            messageTime.setMinutes(messageTime.getMinutes() + session.duration);
            if (message.triggerUnit === 'hours') messageTime.setHours(messageTime.getHours() + message.triggerTime);
            else if (message.triggerUnit === 'days') messageTime.setDate(messageTime.getDate() + message.triggerTime);
            else if (message.triggerUnit === 'weeks') messageTime.setDate(messageTime.getDate() + (message.triggerTime * 7));
        }

        return messageTime;
    }

    /**
     * Replace placeholders in message content
     */
    private replacePlaceholders(content: string, student: User, teacher: User, session: Session): string {
        return content
            .replace(/{student}/g, student.name || student.username)
            .replace(/{teacher}/g, teacher.name || teacher.username)
            .replace(/{session}/g, session.title);
    }

    /**
     * Schedule a new message
     */
    private async scheduleMessage(
        conversationId: string,
        senderId: string,
        recipientId: string,
        content: string,
        scheduledTime: Date,
        metadata?: any
    ): Promise<string> {
        const messageData = {
            conversation_id: conversationId,
            sender_id: senderId,
            recipient_id: recipientId,
            content,
            scheduled_time: scheduledTime.toISOString(),
            status: 'pending' as const,
            metadata,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        const result = await this.executeInsert<ScheduledMessage>(
            'Schedule new message',
            messageData
        );

        return result[0].id;
    }

    /**
     * Get scheduler status
     */
    public getStatus(): { running: boolean; intervalMs: number; consecutiveErrors: number } {
        return {
            running: this.isRunning,
            intervalMs: this.pollIntervalMs,
            consecutiveErrors: this.consecutiveErrors
        };
    }

    /**
     * Get pending message count
     */
    public async getPendingMessageCount(): Promise<number> {
        const result = await this.executeQuery<{ count: number }>(
            'Get pending message count',
            () => this.client
                .from(this.tableName)
                .select('*', { count: 'exact', head: true })
                .eq('status', 'pending')
        );

        return result.length;
    }
}

// Export singleton instance
export const productionMessageScheduler = new ProductionMessageScheduler(); 