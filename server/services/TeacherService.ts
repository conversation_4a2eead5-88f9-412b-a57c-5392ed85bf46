import { BaseService } from './BaseService';
import { User, UserWithProfile } from '@shared/schema';
import { errorHandler } from './index';

/**
 * Teacher service for teacher-related business logic
 */
export class TeacherService extends BaseService {
  /**
   * Constructor
   * @param storage - Storage instance
   * @param repositories - Repository factory
   */
  constructor(storage: any, repositories: any) {
    super(storage, repositories);
  }

  /**
   * Get a teacher by ID
   * @param id - Teacher ID
   * @returns Teacher or undefined
   */
  async getTeacher(id: number): Promise<UserWithProfile | undefined> {
    try {
      // Get the user with profile
      const teacher = await this.repositories.getUserRepository().getUserWithProfile(id);

      // Check if the user is a teacher
      if (teacher && (!teacher.isTeacher && !teacher.isInstructor)) {
        return undefined;
      }

      return teacher;
    } catch (error) {
      this.handleError(error, 'TeacherService.getTeacher');
    }
  }

  /**
   * Get all teachers
   * @returns Array of all teachers
   */
  async getAllTeachers(): Promise<UserWithProfile[]> {
    try {
      // Get all teachers from the user repository
      return await this.repositories.getUserRepository().getAllTeachers();
    } catch (error) {
      this.handleError(error, 'TeacherService.getAllTeachers');
      return [];
    }
  }

  /**
   * Get top teachers
   * @param limit - Maximum number of teachers to return
   * @returns Array of top teachers
   */
  async getTopTeachers(limit: number = 5): Promise<any[]> {
    try {
      // Use the query optimizer for better performance
      return await this.queryOptimizer.getTopTeachers(limit);
    } catch (error) {
      this.handleError(error, 'TeacherService.getTopTeachers');
    }
  }

  /**
   * Get teacher sessions
   * @param teacherId - Teacher ID
   * @returns Array of teacher sessions
   */
  async getTeacherSessions(teacherId: number): Promise<any[]> {
    try {
      // Get the teacher's sessions
      return await this.repositories.getSessionRepository().getSessionsByInstructor(teacherId);
    } catch (error) {
      this.handleError(error, 'TeacherService.getTeacherSessions');
    }
  }

  /**
   * Get teacher reviews
   * @param teacherId - Teacher ID
   * @returns Array of teacher reviews
   */
  async getTeacherReviews(teacherId: number): Promise<any[]> {
    try {
      // Get the teacher's reviews
      return await this.repositories.getReviewRepository().getInstructorReviews(teacherId);
    } catch (error) {
      this.handleError(error, 'TeacherService.getTeacherReviews');
    }
  }

  /**
   * Update teacher profile
   * @param teacherId - Teacher ID
   * @param profileData - Profile data to update
   * @returns Updated teacher profile
   */
  async updateTeacherProfile(teacherId: number, profileData: any): Promise<UserWithProfile> {
    try {
      // Get the user with profile
      const teacher = await this.repositories.getUserRepository().getUserWithProfile(teacherId);

      // Check if the user is a teacher
      if (!teacher || (!teacher.isTeacher && !teacher.isInstructor)) {
        throw errorHandler.notFound(`Teacher with ID ${teacherId} not found`);
      }

      // Update the teacher profile
      await this.repositories.getUserRepository().updateUserProfile(teacherId, profileData);

      // Get the updated teacher profile
      const updatedTeacher = await this.repositories.getUserRepository().getUserWithProfile(teacherId);

      return updatedTeacher;
    } catch (error) {
      this.handleError(error, 'TeacherService.updateTeacherProfile');
    }
  }
}
