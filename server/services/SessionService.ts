import { BaseService } from './BaseService';
import { Session, InsertSession, SessionWithTeacher } from '@shared/schema';
import { errorHandler } from './index';
import {
  validateSessionTitle,
  validateSessionDescription,
  validateSessionPrice,
  validateSessionDuration,
  validateSessionDate
} from '../utils/validation';

/**
 * Session service for session-related business logic
 */
export class SessionService extends BaseService {
  /**
   * Get a session by ID
   * @param id - Session ID
   * @returns Session or undefined
   */
  async getSession(id: string): Promise<Session | undefined> {
    try {
      const result = await this.repositories.getSessionRepository().getSession(id);
      return result || undefined;
    } catch (error) {
      this.handleError(error, 'SessionService.getSession');
    }
  }

  /**
   * Get a session with teacher details
   * @param id - Session ID
   * @returns Session with teacher or undefined
   */
  async getSessionWithTeacher(id: string): Promise<SessionWithTeacher | undefined> {
    try {
      const result = await this.repositories.getSessionRepository().getSessionWithTeacher(id);
      return result || undefined;
    } catch (error) {
      this.handleError(error, 'SessionService.getSessionWithTeacher');
    }
  }

  /**
   * Create a new session
   * @param sessionData - Session data
   * @returns Created session
   */
  async createSession(sessionData: InsertSession): Promise<Session> {
    try {
      // Validate session data
      this.validateSessionData(sessionData);

      // Check if the teacher exists
      const teacherId = sessionData.teacherId;
      const teacher = await this.repositories.getUserRepository().getUserProfile(teacherId);
      if (!teacher) {
        throw errorHandler.notFound(`Teacher with ID ${teacherId} not found`);
      }

      // Check if the teacher is actually a teacher
      if (!teacher.isTeacher) {
        throw errorHandler.forbidden(`User with ID ${teacherId} is not a teacher`);
      }

      // Check for scheduling conflicts
      const conflict = await this.repositories.getSessionRepository().checkSessionSchedulingConflict(
        teacherId,
        sessionData.date,
        sessionData.duration
      );

      if (conflict.hasConflict) {
        throw errorHandler.conflict(
          `Scheduling conflict with another session: ${conflict.conflictingSession?.title}`
        );
      }

      // Create the session
      return await this.repositories.getSessionRepository().createSession(sessionData);
    } catch (error) {
      this.handleError(error, 'SessionService.createSession');
    }
  }

  /**
   * Update a session
   * @param id - Session ID
   * @param sessionData - Session data to update
   * @returns Updated session
   */
  async updateSession(id: string, sessionData: Partial<Session>): Promise<Session | undefined> {
    try {
      // Get the existing session
      const existingSession = await this.repositories.getSessionRepository().getSession(id);
      if (!existingSession) {
        throw errorHandler.notFound(`Session with ID ${id} not found`);
      }

      // Validate session data
      this.validateSessionData(sessionData);

      // Check for scheduling conflicts if date or duration is changing
      if (sessionData.date || sessionData.duration) {
        const date = sessionData.date || existingSession.date;
        const duration = sessionData.duration || existingSession.duration;
        const teacherId = existingSession.teacherId;

        const conflict = await this.repositories.getSessionRepository().checkSessionSchedulingConflict(
          teacherId,
          date,
          duration
        );

        if (conflict.hasConflict) {
          throw errorHandler.conflict(
            `Scheduling conflict with another session: ${conflict.conflictingSession?.title}`
          );
        }
      }

      // Update the session
      return await this.repositories.getSessionRepository().updateSession(id, sessionData);
    } catch (error) {
      this.handleError(error, 'SessionService.updateSession');
    }
  }

  /**
   * Delete a session
   * @param id - Session ID
   * @returns True if successful
   */
  async deleteSession(id: string): Promise<boolean> {
    try {
      // Get the existing session
      const existingSession = await this.repositories.getSessionRepository().getSession(id);
      if (!existingSession) {
        throw errorHandler.notFound(`Session with ID ${id} not found`);
      }

      // Check if the session has bookings
      // TODO: Update BookingRepository to use string IDs
      try {
        const bookings = await this.repositories.getBookingRepository().getSessionBookings(parseInt(id, 10));
        if (bookings.length > 0) {
          throw errorHandler.conflict(`Cannot delete session with ID ${id} because it has bookings`);
        }
      } catch (error) {
        // If we can't check bookings due to ID format mismatch, skip the check for now
        console.warn(`Could not check bookings for session ${id}: ${error}`);
      }

      // Delete the session
      await this.repositories.getSessionRepository().deleteSession(id);
      return true;
    } catch (error) {
      this.handleError(error, 'SessionService.deleteSession');
    }
  }

  /**
   * Get sessions with optional filters
   * @param filters - Optional filters
   * @param includePrivate - Whether to include private sessions
   * @returns Array of sessions with teacher details
   */
  async getSessions(filters: any = {}, includePrivate: boolean = false): Promise<SessionWithTeacher[]> {
    try {
      // Add includePrivate to filters if provided
      const finalFilters = includePrivate ? { ...filters, includePrivate } : filters;
      return await this.repositories.getSessionRepository().getSessions(finalFilters);
    } catch (error) {
      this.handleError(error, 'SessionService.getSessions');
    }
  }

  /**
   * Get sessions by teacher
   * @param teacherId - Teacher ID
   * @returns Array of sessions with teacher details
   */
  async getSessionsByTeacher(teacherId: string): Promise<SessionWithTeacher[]> {
    try {
      return await this.repositories.getSessionRepository().getSessions({ teacherId });
    } catch (error) {
      this.handleError(error, 'SessionService.getSessionsByTeacher');
    }
  }

  /**
   * Count sessions with optional filters
   * @param filters - Optional filters
   * @returns Number of sessions
   */
  async countSessions(filters: any = {}): Promise<number> {
    try {
      const sessions = await this.repositories.getSessionRepository().getSessions(filters);
      return sessions.length;
    } catch (error) {
      this.handleError(error, 'SessionService.countSessions');
    }
  }

  /**
   * Get top sessions
   * @param limit - Maximum number of sessions to return
   * @returns Array of top sessions with teacher details
   */
  async getTopSessions(limit: number = 10): Promise<SessionWithTeacher[]> {
    try {
      const sessions = await this.repositories.getSessionRepository().getSessions();
      return sessions.slice(0, limit);
    } catch (error) {
      this.handleError(error, 'SessionService.getTopSessions');
    }
  }

  /**
   * Validate session data
   * @param sessionData - Session data to validate
   * @throws ApiError if validation fails
   */
  private validateSessionData(sessionData: Partial<InsertSession | Session>): void {
    // Validate required fields if they are provided
    if (sessionData.title) {
      validateSessionTitle(sessionData.title);
    }

    if (sessionData.description) {
      validateSessionDescription(sessionData.description);
    }

    if (sessionData.price !== undefined) {
      validateSessionPrice(sessionData.price);
    }

    if (sessionData.duration !== undefined) {
      validateSessionDuration(sessionData.duration);
    }

    if (sessionData.date) {
      validateSessionDate(sessionData.date);
    }
  }
}

export default SessionService;
