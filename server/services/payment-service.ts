/**
 * Payment service for handling teacher payments
 */

import { pool } from '../db/connection';
import crypto from 'crypto';

// Encryption key for sensitive payment details
const ENCRYPTION_KEY = process.env.PAYMENT_ENCRYPTION_KEY || 'sessionhub-payment-encryption-key-2025';
const ENCRYPTION_IV = process.env.PAYMENT_ENCRYPTION_IV || 'sessionhub-iv-2025';

/**
 * Encrypt sensitive payment details
 */
function encryptPaymentDetails(details: any): string {
  const cipher = crypto.createCipheriv(
    'aes-256-cbc',
    Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)),
    Buffer.from(ENCRYPTION_IV.padEnd(16).slice(0, 16))
  );

  let encrypted = cipher.update(JSON.stringify(details), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

/**
 * Decrypt sensitive payment details
 */
function decryptPaymentDetails(encryptedDetails: string): any {
  try {
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)),
      Buffer.from(ENCRYPTION_IV.padEnd(16).slice(0, 16))
    );

    let decrypted = decipher.update(encryptedDetails, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('Error decrypting payment details:', error);
    return {};
  }
}

/**
 * Get all payment methods for an teacher
 */
export async function getTeacherPaymentMethods(userId: number) {
  try {
    const result = await pool.query(
      `SELECT id, user_id, payment_type, is_default, details, created_at, updated_at
       FROM bookings.teacher_payment_methods
       WHERE user_id = $1
       ORDER BY is_default DESC, created_at DESC`,
      [userId]
    );

    // Decrypt the payment details
    return result.rows.map(method => ({
      ...method,
      details: JSON.parse(method.details)
    }));
  } catch (error) {
    console.error('Error getting teacher payment methods:', error);
    throw error;
  }
}

/**
 * Add a new payment method for an teacher
 */
export async function addTeacherPaymentMethod(
  userId: number,
  paymentType: string,
  details: any,
  isDefault: boolean = false
) {
  try {
    // If this is the default method, unset any existing default
    if (isDefault) {
      await pool.query(
        `UPDATE bookings.teacher_payment_methods
         SET is_default = false
         WHERE user_id = $1 AND is_default = true`,
        [userId]
      );
    }

    // Insert the new payment method
    const result = await pool.query(
      `INSERT INTO bookings.teacher_payment_methods
       (user_id, payment_type, is_default, details, created_at, updated_at)
       VALUES ($1, $2, $3, $4, NOW(), NOW())
       RETURNING id, user_id, payment_type, is_default, created_at, updated_at`,
      [userId, paymentType, isDefault, JSON.stringify(details)]
    );

    return {
      ...result.rows[0],
      details
    };
  } catch (error) {
    console.error('Error adding teacher payment method:', error);
    throw error;
  }
}

/**
 * Update an existing payment method
 */
export async function updateTeacherPaymentMethod(
  id: number,
  userId: number,
  updates: {
    paymentType?: string;
    details?: any;
    isDefault?: boolean;
  }
) {
  try {
    // If this is being set as default, unset any existing default
    if (updates.isDefault) {
      await pool.query(
        `UPDATE bookings.teacher_payment_methods
         SET is_default = false
         WHERE user_id = $1 AND is_default = true AND id != $2`,
        [userId, id]
      );
    }

    // Build the update query dynamically
    const updateFields = [];
    const queryParams = [id, userId];
    let paramIndex = 3;

    if (updates.paymentType !== undefined) {
      updateFields.push(`payment_type = $${paramIndex++}`);
      queryParams.push(updates.paymentType);
    }

    if (updates.details !== undefined) {
      updateFields.push(`details = $${paramIndex++}`);
      queryParams.push(JSON.stringify(updates.details));
    }

    if (updates.isDefault !== undefined) {
      updateFields.push(`is_default = $${paramIndex++}`);
      queryParams.push(updates.isDefault);
    }

    updateFields.push(`updated_at = NOW()`);

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    const result = await pool.query(
      `UPDATE bookings.teacher_payment_methods
       SET ${updateFields.join(', ')}
       WHERE id = $1 AND user_id = $2
       RETURNING id, user_id, payment_type, is_default, details, created_at, updated_at`,
      queryParams
    );

    if (result.rows.length === 0) {
      throw new Error('Payment method not found or not owned by this user');
    }

    return {
      ...result.rows[0],
      details: JSON.parse(result.rows[0].details)
    };
  } catch (error) {
    console.error('Error updating teacher payment method:', error);
    throw error;
  }
}

/**
 * Delete a payment method
 */
export async function deleteTeacherPaymentMethod(id: number, userId: number) {
  try {
    // Check if this is the default method
    const checkResult = await pool.query(
      `SELECT is_default FROM bookings.teacher_payment_methods WHERE id = $1 AND user_id = $2`,
      [id, userId]
    );

    if (checkResult.rows.length === 0) {
      throw new Error('Payment method not found or not owned by this user');
    }

    const isDefault = checkResult.rows[0].is_default;

    // Delete the payment method
    await pool.query(
      `DELETE FROM bookings.teacher_payment_methods WHERE id = $1 AND user_id = $2`,
      [id, userId]
    );

    // If this was the default method, set another one as default if available
    if (isDefault) {
      const remainingMethods = await pool.query(
        `SELECT id FROM bookings.teacher_payment_methods WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1`,
        [userId]
      );

      if (remainingMethods.rows.length > 0) {
        await pool.query(
          `UPDATE bookings.teacher_payment_methods SET is_default = true WHERE id = $1`,
          [remainingMethods.rows[0].id]
        );
      }
    }

    return true;
  } catch (error) {
    console.error('Error deleting teacher payment method:', error);
    throw error;
  }
}

/**
 * Create a payout record
 */
export async function createPayout(
  userId: number,
  amount: number,
  currency: string = 'USD',
  status: string = 'pending',
  notes?: string,
  paymentMethodId?: number
) {
  try {
    // If no payment method ID is provided and status is pending, use the default one
    if (!paymentMethodId && status === 'pending') {
      const defaultMethodResult = await pool.query(
        `SELECT id FROM bookings.teacher_payment_methods WHERE user_id = $1 AND is_default = true LIMIT 1`,
        [userId]
      );

      if (defaultMethodResult.rows.length === 0) {
        // If no default payment method, check if any payment method exists
        const anyMethodResult = await pool.query(
          `SELECT id FROM bookings.teacher_payment_methods WHERE user_id = $1 LIMIT 1`,
          [userId]
        );

        if (anyMethodResult.rows.length === 0) {
          // No payment method found, but we'll create the payout anyway with null payment_method_id
          console.log(`No payment method found for user ${userId}, creating payout with null payment_method_id`);
        } else {
          paymentMethodId = anyMethodResult.rows[0].id;
        }
      } else {
        paymentMethodId = defaultMethodResult.rows[0].id;
      }
    }

    // Create the payout record
    const result = await pool.query(
      `INSERT INTO payouts
       (user_id, amount, currency, status, payment_method_id, notes, created_at, completed_at)
       VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7)
       RETURNING id, user_id, amount, currency, status, payment_method_id, notes, created_at, completed_at`,
      [
        userId,
        amount,
        currency,
        status,
        paymentMethodId || null,
        notes || null,
        status === 'paid' ? 'NOW()' : null // If status is 'paid', set completed_at to now
      ]
    );

    return result.rows[0];
  } catch (error) {
    console.error('Error creating payout:', error);
    throw error;
  }
}

/**
 * Get all payouts for an teacher
 */
export async function getTeacherPayouts(userId: number) {
  try {
    const result = await pool.query(
      `SELECT p.id, p.user_id, p.amount, p.currency, p.status,
              p.payment_method_id, p.external_reference, p.notes,
              p.created_at, p.completed_at,
              pm.payment_type
       FROM payouts p
       LEFT JOIN bookings.teacher_payment_methods pm ON p.payment_method_id = pm.id
       WHERE p.user_id = $1
       ORDER BY p.created_at DESC`,
      [userId]
    );

    return result.rows;
  } catch (error) {
    console.error('Error getting teacher payouts:', error);
    throw error;
  }
}

/**
 * Update a payout status
 */
export async function updatePayoutStatus(
  id: number,
  status: string,
  externalReference?: string
) {
  try {
    const updates: any = { status };

    if (externalReference) {
      updates.external_reference = externalReference;
    }

    if (status === 'completed') {
      updates.completed_at = 'NOW()';
    }

    // Build the update query dynamically
    const updateFields = Object.entries(updates).map(([key, value], index) => {
      return `${key} = ${value === 'NOW()' ? 'NOW()' : `$${index + 2}`}`;
    });

    const queryParams = [id];
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== 'NOW()') {
        queryParams.push(value);
      }
    });

    const result = await pool.query(
      `UPDATE payouts
       SET ${updateFields.join(', ')}
       WHERE id = $1
       RETURNING id, user_id, amount, currency, status, payment_method_id, external_reference, notes, created_at, completed_at`,
      queryParams
    );

    if (result.rows.length === 0) {
      throw new Error('Payout not found');
    }

    return result.rows[0];
  } catch (error) {
    console.error('Error updating payout status:', error);
    throw error;
  }
}

/**
 * Calculate platform fee for a booking
 */
export async function calculatePlatformFee(bookingId: number, percentage: number = 10) {
  try {
    // Get the booking amount
    const bookingResult = await pool.query(
      `SELECT payment_amount FROM bookings WHERE id = $1`,
      [bookingId]
    );

    if (bookingResult.rows.length === 0) {
      throw new Error('Booking not found');
    }

    const bookingAmount = bookingResult.rows[0].payment_amount;

    if (!bookingAmount) {
      throw new Error('Booking has no payment amount');
    }

    // Calculate the fee
    const feeAmount = (bookingAmount * percentage) / 100;

    // Record the fee
    const result = await pool.query(
      `INSERT INTO platform_fees
       (booking_id, amount, percentage, created_at)
       VALUES ($1, $2, $3, NOW())
       RETURNING id, booking_id, amount, percentage, created_at`,
      [bookingId, feeAmount, percentage]
    );

    return result.rows[0];
  } catch (error) {
    console.error('Error calculating platform fee:', error);
    throw error;
  }
}

/**
 * Record a platform fee for a booking (used for Connect payments)
 */
export async function recordPlatformFee(bookingId: number, amount: number, currency: string = 'USD') {
  try {
    // Calculate the percentage based on the booking amount
    const bookingResult = await pool.query(
      `SELECT payment_amount FROM bookings WHERE id = $1`,
      [bookingId]
    );

    if (bookingResult.rows.length === 0) {
      throw new Error('Booking not found');
    }

    const bookingAmount = bookingResult.rows[0].payment_amount;

    if (!bookingAmount) {
      throw new Error('Booking has no payment amount');
    }

    // Calculate the percentage
    const percentage = (amount / bookingAmount) * 100;

    // Record the fee
    const result = await pool.query(
      `INSERT INTO platform_fees
       (booking_id, amount, percentage, currency, created_at)
       VALUES ($1, $2, $3, $4, NOW())
       RETURNING id, booking_id, amount, percentage, currency, created_at`,
      [bookingId, amount, percentage, currency]
    );

    return result.rows[0];
  } catch (error) {
    console.error('Error recording platform fee:', error);
    throw error;
  }
}

export default {
  getTeacherPaymentMethods,
  addTeacherPaymentMethod,
  updateTeacherPaymentMethod,
  deleteTeacherPaymentMethod,
  createPayout,
  getTeacherPayouts,
  updatePayoutStatus,
  calculatePlatformFee,
  recordPlatformFee
};
