import path from 'path';
import { IStorage } from '../storage';
import { Booking, Session, User, InsertMessage, InsertScheduledMessage } from '@shared/schema';

// Add a custom type extension to include automatedMessages
type SessionWithMessages = Session & {
  automatedMessages?: Array<{
    id: string;
    title: string;
    content: string;
    triggerType: 'before' | 'after' | 'specific';
    triggerTime: number;
    triggerUnit: 'hours' | 'days' | 'weeks';
    specificDate?: Date;
    specificTime?: string;
    enabled: boolean;
    frequency: number;
    attachments?: any[];
    links?: any[];
  }>;
};

// Define message job types
interface MessageJob {
  type: 'welcome' | 'reminder' | 'follow_up';
  conversationId: number;
  senderId: number;
  recipientId: number;
  content: string;
  sessionId: number;
  bookingId: number;
}

// Using Postgres for message scheduling instead of Redis

/**
 * Supabase/Postgres-based Automated Message Scheduler
 */
export class MessageScheduler {
  private storage: IStorage;
  private pollInterval: NodeJS.Timeout | null = null;

  constructor(storage: IStorage) {
    this.storage = storage;
  }

  /**
   * Schedule messages for a booking (insert into scheduled_messages)
   *
   * Note: This is a simplified implementation since we don't have direct SQL access
   * through the IStorage interface. In a production environment, you would need to
   * add methods to IStorage to handle scheduled messages.
   */
  async scheduleMessagesForBooking(
    booking: Booking,
    session: Session & { automatedMessages?: any[] },
    student: User,
    teacher: User
  ): Promise<void> {
    try {
      console.log(`[MessageScheduler] Scheduling messages for booking ${booking.id}`);

      // Find or create a conversation between student and teacher
      let conversation;
      try {
        // First try to find an existing conversation
        const conversations = await this.storage.getUserConversations(student.id);
        conversation = conversations.find(c =>
          c.participantIds.includes(teacher.id) &&
          c.participantIds.includes(student.id)
        );

        if (!conversation) {
          // Create a new conversation if none exists
          conversation = await this.storage.createConversation({
            participantIds: [student.id, teacher.id],
            name: `${student.username} and ${teacher.username}`,
            bookingId: booking.id
          });
          console.log(`[MessageScheduler] Created new conversation ${conversation.id}`);
        } else {
          console.log(`[MessageScheduler] Using existing conversation ${conversation.id}`);
        }
      } catch (error) {
        console.error(`[MessageScheduler] Error finding/creating conversation:`, error);
        return;
      }

      // Process automated messages
      let automatedMessages = [];

      // Try to parse automatedMessagesJson if it exists
      if (session.automatedMessagesJson) {
        try {
          automatedMessages = JSON.parse(session.automatedMessagesJson);
        } catch (e) {
          console.error(`[MessageScheduler] Error parsing automatedMessagesJson:`, e);
        }
      }

      // Use automatedMessages property if available and automatedMessagesJson parsing failed
      if ((!automatedMessages || automatedMessages.length === 0) &&
        session.automatedMessages && Array.isArray(session.automatedMessages)) {
        automatedMessages = session.automatedMessages;
      }

      if (Array.isArray(automatedMessages) && automatedMessages.length > 0) {
        for (const message of automatedMessages) {
          if (!message.enabled) continue;

          // Calculate when the message should be sent
          let messageTime = new Date();
          let description = '';

          if (message.triggerType === 'specific' && message.specificDate) {
            // For specific date/time
            messageTime = new Date(message.specificDate);
            if (message.specificTime) {
              const [hours, minutes] = message.specificTime.split(':').map(Number);
              messageTime.setHours(hours, minutes, 0, 0);
            }
            description = `at specific time: ${messageTime.toISOString()}`;
          } else if (message.triggerType === 'before') {
            // For messages before the session
            // Get the session date from the session object
            messageTime = new Date(session.date);
            if (message.triggerUnit === 'hours') messageTime.setHours(messageTime.getHours() - message.triggerTime);
            else if (message.triggerUnit === 'days') messageTime.setDate(messageTime.getDate() - message.triggerTime);
            else if (message.triggerUnit === 'weeks') messageTime.setDate(messageTime.getDate() - (message.triggerTime * 7));
            description = `${message.triggerTime} ${message.triggerUnit} before the session`;
          } else if (message.triggerType === 'after') {
            // For messages after the session
            // Get the session date from the session object
            messageTime = new Date(session.date);
            // Add session duration to get the end time
            messageTime.setMinutes(messageTime.getMinutes() + session.duration);
            if (message.triggerUnit === 'hours') messageTime.setHours(messageTime.getHours() + message.triggerTime);
            else if (message.triggerUnit === 'days') messageTime.setDate(messageTime.getDate() + message.triggerTime);
            else if (message.triggerUnit === 'weeks') messageTime.setDate(messageTime.getDate() + (message.triggerTime * 7));
            description = `${message.triggerTime} ${message.triggerUnit} after the session`;
          }

          // Process message content with placeholders
          let content = message.content;
          content = content.replace(/{student}/g, student.name || student.username);
          content = content.replace(/{teacher}/g, teacher.name || teacher.username);
          content = content.replace(/{session}/g, session.title);

          // Only schedule future messages
          if (messageTime > new Date()) {
            console.log(`[MessageScheduler] Scheduling message "${message.title}" to be sent ${description}`);

            // Store in database
            try {
              const scheduledMessage: InsertScheduledMessage = {
                conversationId: conversation.id,
                senderId: teacher.id,
                content,
                scheduledTime: messageTime,
                status: 'pending',
                bookingId: booking.id,
                sessionId: session.id,
                metadata: {
                  messageId: message.id,
                  title: message.title,
                  triggerType: message.triggerType,
                  triggerTime: message.triggerTime,
                  triggerUnit: message.triggerUnit
                }
              };

              await this.storage.createScheduledMessage(scheduledMessage);
              console.log(`[MessageScheduler] Successfully scheduled message for ${messageTime.toISOString()}`);
            } catch (error) {
              console.error(`[MessageScheduler] Error storing scheduled message:`, error);
            }
          } else {
            console.log(`[MessageScheduler] Skipping message "${message.title}" as scheduled time is in the past`);
          }
        }
      } else {
        console.log(`[MessageScheduler] No automated messages found for session ${session.id}`);
      }

      console.log(`[MessageScheduler] Finished scheduling messages for booking ${booking.id}`);
    } catch (error) {
      console.error('[MessageScheduler] Error scheduling messages:', error);
    }
  }

  /**
   * Poll for due scheduled messages and deliver them
   */
  startPolling(intervalMs = 60000) {
    if (this.pollInterval) clearInterval(this.pollInterval);

    // Keep track of consecutive errors
    let consecutiveErrors = 0;
    const maxConsecutiveErrors = 5;

    // Use a more robust polling approach with error handling
    this.pollInterval = setInterval(async () => {
      try {
        await this.deliverDueMessages();
        // Reset error count on success
        consecutiveErrors = 0;
      } catch (error) {
        console.error('[MessageScheduler] Error in polling interval:', error);

        // Count consecutive errors
        consecutiveErrors++;

        // If too many consecutive errors, slow down polling to avoid overwhelming the system
        if (consecutiveErrors >= maxConsecutiveErrors) {
          console.error(`[MessageScheduler] Too many consecutive errors (${consecutiveErrors}). Temporarily increasing polling interval.`);

          // Temporarily pause and restart with increased interval
          this.stopPolling();

          // Restart with a longer interval (5 minutes) after a short pause
          setTimeout(() => {
            console.log('[MessageScheduler] Restarting polling with longer interval after errors');
            this.startPolling(300000); // 5 minutes
          }, 10000); // 10 second pause

          return;
        }

        // Continue polling despite errors
      }
    }, intervalMs);

    // Run immediately on start, but wrap in try/catch
    setTimeout(() => {
      try {
        this.deliverDueMessages().catch(error => {
          console.error('[MessageScheduler] Error in initial message delivery:', error);
        });
      } catch (error) {
        console.error('[MessageScheduler] Error in initial message delivery:', error);
      }
    }, 5000); // Wait 5 seconds before first run to ensure system is fully initialized
  }

  /**
   * Replace placeholders in message content with actual values
   */
  private async replacePlaceholders(content: string, studentId: number, teacherId: number, sessionId: number): Promise<string> {
    try {
      // Get student, teacher, and session details
      const student = await this.storage.getUser(studentId);
      const teacher = await this.storage.getUser(teacherId);
      const session = await this.storage.getSession(sessionId);

      // Replace placeholders if entities exist
      let processedContent = content;

      if (student) {
        processedContent = processedContent.replace(/\{student\}/g, student.name || 'Student');
      }

      if (teacher) {
        processedContent = processedContent.replace(/\{teacher\}/g, teacher.name || 'Teacher');
      }

      if (session) {
        processedContent = processedContent.replace(/\{session\}/g, session.title || 'Session');
      }

      return processedContent;
    } catch (error) {
      console.error('[MessageScheduler] Error replacing placeholders:', error);
      return content; // Return original content if replacement fails
    }
  }

  /**
   * Process pending scheduled messages
   */
  async deliverDueMessages() {
    try {
      // Check if the function exists
      if (typeof this.storage.getPendingScheduledMessages !== 'function') {
        console.error('[MessageScheduler] getPendingScheduledMessages function not available');
        return 0;
      }

      // Get pending messages
      let pendingMessages;
      try {
        pendingMessages = await this.storage.getPendingScheduledMessages();
        console.log(`[MessageScheduler] Processing ${pendingMessages.length} pending messages`);
      } catch (dbError) {
        console.error('[MessageScheduler] Database error fetching pending messages:', dbError);
        // Don't throw - just return gracefully to prevent crashing the server
        return 0;
      }

      let successCount = 0;
      let errorCount = 0;

      // Process messages with individual error handling for each
      for (const message of pendingMessages) {
        try {
          // Set message status to processing to prevent duplicate delivery
          await this.storage.updateScheduledMessageStatus(message.id, 'processing');

          // Replace placeholders in content
          let content = message.content;
          if (message.bookingId && message.sessionId) {
            try {
              content = await this.replacePlaceholders(
                content,
                message.recipientId,
                message.senderId,
                message.sessionId
              );
            } catch (contentError) {
              console.warn(`[MessageScheduler] Error replacing placeholders for message ${message.id}:`, contentError);
              // Continue with original content if replacement fails
            }
          }

          // Deliver the message
          try {
            const newMessage: InsertMessage = {
              conversationId: message.conversationId,
              senderId: message.senderId,
              content,
              sentAt: new Date(),
              status: 'sent',
              metadata: {
                scheduledMessageId: message.id,
                automationOrigin: 'scheduler',
                ...message.metadata
              }
            };

            await this.storage.createMessage(newMessage);
            await this.storage.updateScheduledMessageStatus(message.id, 'delivered');
            successCount++;
            console.log(`[MessageScheduler] Successfully delivered message ${message.id}`);
          } catch (deliveryError) {
            console.error(`[MessageScheduler] Error delivering message ${message.id}:`, deliveryError);

            // Update retry count and handle max retries
            const retryCount = (message.retryCount || 0) + 1;
            const maxRetries = 3; // Maximum retries before giving up

            if (retryCount >= maxRetries) {
              await this.storage.updateScheduledMessageStatus(message.id, 'failed', retryCount, String(deliveryError));
              console.error(`[MessageScheduler] Message ${message.id} failed after ${maxRetries} retries`);
            } else {
              // Mark for retry with exponential backoff
              const backoffMinutes = Math.pow(2, retryCount); // 2, 4, 8 minutes backoff
              const nextAttempt = new Date();
              nextAttempt.setMinutes(nextAttempt.getMinutes() + backoffMinutes);

              await this.storage.rescheduleMessage(
                message.id,
                nextAttempt,
                retryCount,
                String(deliveryError)
              );

              console.log(`[MessageScheduler] Message ${message.id} scheduled for retry in ${backoffMinutes} minutes (attempt ${retryCount})`);
            }

            errorCount++;
          }
        } catch (messageError) {
          console.error(`[MessageScheduler] Unexpected error processing message ${message.id}:`, messageError);
          errorCount++;

          // Try to mark as failed but don't throw if this fails too
          try {
            await this.storage.updateScheduledMessageStatus(message.id, 'failed', 0, String(messageError));
          } catch (updateError) {
            console.error(`[MessageScheduler] Could not update message ${message.id} status:`, updateError);
          }
        }
      }

      console.log(`[MessageScheduler] Successfully processed ${successCount} messages with ${errorCount} errors`);
      return pendingMessages.length;
    } catch (error) {
      console.error('[MessageScheduler] Unexpected error in deliverDueMessages:', error);

      // Return 0 to indicate no messages were processed
      return 0;
    }
  }

  stopPolling() {
    if (this.pollInterval) clearInterval(this.pollInterval);
    this.pollInterval = null;
  }

  /**
   * Shutdown the scheduler (for graceful server shutdown)
   */
  async shutdown() {
    this.stopPolling();
    // Close any other resources if needed
    console.log('[MessageScheduler] Shutdown complete');
  }

  // Helper: get conversation by booking ID (reuse from old logic)
  private async getConversationByBookingId(bookingId: number): Promise<{ id: number } | null> {
    try {
      const booking = await this.storage.getBooking(bookingId);
      if (!booking) return null;
      const session = await this.storage.getSession(booking.sessionId);
      if (!session) return null;
      const studentId = booking.userId;
      const teacherId = session.teacherId;
      const teacherConversations = await this.storage.getUserConversations(teacherId);
      const targetConversation = teacherConversations.find(conv =>
        conv.participantIds.includes(studentId) && conv.bookingId === bookingId
      );
      return targetConversation ? { id: targetConversation.id } : null;
    } catch (error) {
      return null;
    }
  }
}

// Create a singleton instance
let messageSchedulerInstance: MessageScheduler | null = null;

/**
 * Initialize the message scheduler
 * @param storage The storage interface
 */
export function initializeMessageScheduler(storage: IStorage): MessageScheduler {
  if (!messageSchedulerInstance) {
    messageSchedulerInstance = new MessageScheduler(storage);
  }
  return messageSchedulerInstance;
}

/**
 * Get the message scheduler instance
 */
export function getMessageScheduler(): MessageScheduler | null {
  return messageSchedulerInstance;
}

// Expose a handler for on-demand delivery (for serverless/cron)
export async function deliverDueMessagesHandler(_req: any, res: any) {
  try {
    if (!messageSchedulerInstance) {
      res.status(500).json({ error: 'Scheduler not initialized' });
      return;
    }
    await messageSchedulerInstance.deliverDueMessages();
    res.status(200).json({ success: true });
  } catch (err) {
    res.status(500).json({ error: String(err) });
  }
}

// NOTE: We do NOT use polling in production to avoid unnecessary database load. Use the deliverDueMessagesHandler with a cron or external trigger instead.

export default {
  initializeMessageScheduler,
  getMessageScheduler
};