import { BaseService } from './BaseService';
import { DeviceToken, NotificationPreference } from '../storage';
import { errorHandler } from './index';

/**
 * Notification service for notification-related business logic
 */
export class NotificationService extends BaseService {
  /**
   * Register a device token
   * @param userId - User ID
   * @param token - Device token
   * @param platform - Platform (ios, android, web)
   * @returns Device token
   */
  async registerDeviceToken(userId: number, token: string, platform: string): Promise<DeviceToken> {
    try {
      // Check if the user exists
      const user = await this.repositories.getUserRepository().getUser(userId);
      if (!user) {
        throw errorHandler.notFound(`User with ID ${userId} not found`);
      }

      // Validate platform
      if (!['ios', 'android', 'web'].includes(platform)) {
        throw errorHandler.validation(`Invalid platform: ${platform}. Must be one of: ios, android, web`);
      }

      // Register the device token
      return await this.repositories.getNotificationRepository().registerDeviceToken(userId, token, platform);
    } catch (error) {
      this.handleError(error, 'NotificationService.registerDeviceToken');
    }
  }

  /**
   * Get device tokens for a user
   * @param userId - User ID
   * @returns Array of device tokens
   */
  async getUserDevices(userId: number): Promise<DeviceToken[]> {
    try {
      return await this.repositories.getNotificationRepository().getUserDevices(userId);
    } catch (error) {
      this.handleError(error, 'NotificationService.getUserDevices');
    }
  }

  /**
   * Remove a device token
   * @param token - Device token
   * @returns True if successful
   */
  async removeDeviceToken(token: string): Promise<boolean> {
    try {
      return await this.repositories.getNotificationRepository().removeDeviceToken(token);
    } catch (error) {
      this.handleError(error, 'NotificationService.removeDeviceToken');
    }
  }

  /**
   * Get notification preferences for a user
   * @param userId - User ID
   * @returns Notification preferences or undefined
   */
  async getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined> {
    try {
      return await this.repositories.getNotificationRepository().getNotificationPreferences(userId);
    } catch (error) {
      this.handleError(error, 'NotificationService.getNotificationPreferences');
    }
  }

  /**
   * Update notification preferences for a user
   * @param userId - User ID
   * @param preferences - Notification preferences to update
   * @returns Updated notification preferences
   */
  async updateNotificationPreferences(userId: number, preferences: Partial<NotificationPreference>): Promise<NotificationPreference> {
    try {
      // Check if the user exists
      const user = await this.repositories.getUserRepository().getUser(userId);
      if (!user) {
        throw errorHandler.notFound(`User with ID ${userId} not found`);
      }

      // Update the notification preferences
      return await this.repositories.getNotificationRepository().updateNotificationPreferences(userId, preferences);
    } catch (error) {
      this.handleError(error, 'NotificationService.updateNotificationPreferences');
    }
  }

  /**
   * Send a notification to a user
   * @param userId - User ID
   * @param title - Notification title
   * @param body - Notification body
   * @param data - Additional data
   * @returns True if successful
   */
  async sendNotification(userId: number, title: string, body: string, data: any = {}): Promise<boolean> {
    try {
      // Check if the user exists
      const user = await this.repositories.getUserRepository().getUser(userId);
      if (!user) {
        throw errorHandler.notFound(`User with ID ${userId} not found`);
      }

      // Get the user's notification preferences
      const preferences = await this.repositories.getNotificationRepository().getNotificationPreferences(userId);

      // Check if the user has enabled notifications for this type
      if (!this.shouldSendNotification(preferences, data.type)) {
        return false;
      }

      // Get the user's device tokens
      const devices = await this.repositories.getNotificationRepository().getUserDevices(userId);
      if (devices.length === 0) {
        return false;
      }

      // In a real implementation, you would send the notification to each device
      // For now, we'll just log it
      console.log(`Sending notification to user ${userId}:`, { title, body, data });

      return true;
    } catch (error) {
      this.handleError(error, 'NotificationService.sendNotification');
    }
  }

  /**
   * Send a new message notification
   * @param userId - User ID
   * @param senderId - Sender ID
   * @param senderName - Sender name
   * @param messageContent - Message content
   * @returns True if successful
   */
  async sendNewMessageNotification(userId: number, senderId: number, senderName: string, messageContent: string): Promise<boolean> {
    try {
      return await this.sendNotification(
        userId,
        `New message from ${senderName}`,
        messageContent.length > 100 ? `${messageContent.substring(0, 97)}...` : messageContent,
        {
          type: 'new_message',
          senderId,
          messageContent
        }
      );
    } catch (error) {
      this.handleError(error, 'NotificationService.sendNewMessageNotification');
    }
  }

  /**
   * Send a new booking notification
   * @param userId - User ID
   * @param bookingId - Booking ID
   * @param sessionTitle - Session title
   * @param studentName - Student name
   * @returns True if successful
   */
  async sendNewBookingNotification(userId: number, bookingId: number, sessionTitle: string, studentName: string): Promise<boolean> {
    try {
      return await this.sendNotification(
        userId,
        `New booking for ${sessionTitle}`,
        `${studentName} has booked your session.`,
        {
          type: 'new_booking',
          bookingId,
          sessionTitle,
          studentName
        }
      );
    } catch (error) {
      this.handleError(error, 'NotificationService.sendNewBookingNotification');
    }
  }

  /**
   * Send a booking reminder notification
   * @param userId - User ID
   * @param bookingId - Booking ID
   * @param sessionTitle - Session title
   * @param sessionDate - Session date
   * @returns True if successful
   */
  async sendBookingReminderNotification(userId: number, bookingId: number, sessionTitle: string, sessionDate: Date): Promise<boolean> {
    try {
      // Format the date
      const formattedDate = sessionDate.toLocaleString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      });

      return await this.sendNotification(
        userId,
        `Reminder: ${sessionTitle}`,
        `Your session is scheduled for ${formattedDate}.`,
        {
          type: 'booking_reminder',
          bookingId,
          sessionTitle,
          sessionDate
        }
      );
    } catch (error) {
      this.handleError(error, 'NotificationService.sendBookingReminderNotification');
    }
  }

  /**
   * Send a booking change notification
   * @param userId - User ID
   * @param bookingId - Booking ID
   * @param sessionTitle - Session title
   * @param changeType - Type of change
   * @param changeDetails - Details of the change
   * @returns True if successful
   */
  async sendBookingChangeNotification(
    userId: number,
    bookingId: number,
    sessionTitle: string,
    changeType: 'canceled' | 'rescheduled' | 'updated',
    changeDetails: string
  ): Promise<boolean> {
    try {
      let title: string;
      let body: string;

      switch (changeType) {
        case 'canceled':
          title = `Session canceled: ${sessionTitle}`;
          body = `Your session has been canceled. ${changeDetails}`;
          break;
        case 'rescheduled':
          title = `Session rescheduled: ${sessionTitle}`;
          body = `Your session has been rescheduled. ${changeDetails}`;
          break;
        case 'updated':
          title = `Session updated: ${sessionTitle}`;
          body = `Your session has been updated. ${changeDetails}`;
          break;
      }

      return await this.sendNotification(
        userId,
        title,
        body,
        {
          type: 'booking_changes',
          bookingId,
          sessionTitle,
          changeType,
          changeDetails
        }
      );
    } catch (error) {
      this.handleError(error, 'NotificationService.sendBookingChangeNotification');
    }
  }

  /**
   * Check if a notification should be sent based on user preferences
   * @param preferences - Notification preferences
   * @param type - Notification type
   * @returns True if the notification should be sent
   */
  private shouldSendNotification(preferences: NotificationPreference | undefined, type: string): boolean {
    if (!preferences) {
      return true; // Default to sending if no preferences are set
    }

    switch (type) {
      case 'new_message':
        return preferences.new_message;
      case 'new_booking':
        return preferences.new_booking;
      case 'booking_reminder':
        return preferences.booking_reminder;
      case 'booking_changes':
        return preferences.booking_changes;
      default:
        return true;
    }
  }

  /**
   * Get notifications for a user
   * @param userId - User ID
   * @param limit - Limit the number of results
   * @param offset - Offset the results
   * @param unreadOnly - Only return unread notifications
   * @returns Array of notifications
   */
  async getUserNotifications(userId: number, limit: number, offset: number, unreadOnly: boolean): Promise<any[]> {
    try {
      // TODO: Implement fetching user notifications from the database
      console.warn(`[NotificationService] getUserNotifications not fully implemented. User ID: ${userId}, Limit: ${limit}, Offset: ${offset}, Unread Only: ${unreadOnly}`);
      return []; // Return empty array for now
    } catch (error) {
      this.handleError(error, 'NotificationService.getUserNotifications');
      return [];
    }
  }

  /**
   * Get the number of unread notifications for a user
   * @param userId - User ID
   * @returns Number of unread notifications
   */
  async getUnreadNotificationCount(userId: number): Promise<number> {
    try {
      // TODO: Implement fetching unread notification count from the database
      console.warn(`[NotificationService] getUnreadNotificationCount not fully implemented. User ID: ${userId}`);
      return 0; // Return 0 for now
    } catch (error) {
      this.handleError(error, 'NotificationService.getUnreadNotificationCount');
      return 0;
    }
  }

  /**
   * Get a notification by ID
   * @param notificationId - Notification ID
   * @returns The notification or undefined
   */
  async getNotification(notificationId: number): Promise<any | undefined> {
    try {
      // TODO: Implement fetching a notification by ID from the database
      console.warn(`[NotificationService] getNotification not fully implemented. Notification ID: ${notificationId}`);
      return undefined; // Return undefined for now
    } catch (error) {
      this.handleError(error, 'NotificationService.getNotification');
      return undefined;
    }
  }

  /**
   * Mark a notification as read
   * @param notificationId - Notification ID
   * @returns True if successful
   */
  async markNotificationAsRead(notificationId: number): Promise<boolean> {
    try {
      // TODO: Implement marking a notification as read in the database
      console.warn(`[NotificationService] markNotificationAsRead not fully implemented. Notification ID: ${notificationId}`);
      return false; // Return false for now
    } catch (error) {
      this.handleError(error, 'NotificationService.markNotificationAsRead');
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId - User ID
   * @returns Number of notifications marked as read
   */
  async markAllNotificationsAsRead(userId: number): Promise<number> {
    try {
      // TODO: Implement marking all notifications as read in the database
      console.warn(`[NotificationService] markAllNotificationsAsRead not fully implemented. User ID: ${userId}`);
      return 0; // Return 0 for now
    } catch (error) {
      this.handleError(error, 'NotificationService.markAllNotificationsAsRead');
      return 0;
    }
  }
}

export default NotificationService;
