import { supabaseAdmin } from '../lib/supabase';
import { SupabaseBaseRepository } from '../repositories/SupabaseBaseRepository';

interface ScheduledMessage {
    id: string;
    recipient_id: string;
    message_content: string;
    scheduled_time: string;
    status: 'pending' | 'sent' | 'failed';
    created_at: string;
    updated_at: string;
}

/**
 * Supabase-based Message Scheduler
 * Production-ready implementation without PostgreSQL dependencies
 */
export class SupabaseMessageScheduler extends SupabaseBaseRepository {
    private intervalId: NodeJS.Timeout | null = null;
    private isRunning = false;
    private readonly pollIntervalMs = 30000; // 30 seconds

    constructor() {
        super('scheduled_messages');
    }

    /**
     * Start the message scheduler
     */
    public start(): void {
        if (this.isRunning) {
            console.log('[SupabaseMessageScheduler] Already running');
            return;
        }

        this.isRunning = true;
        console.log('[SupabaseMessageScheduler] Starting message scheduler');

        // Run immediately
        this.processMessages().catch(error => {
            console.error('[SupabaseMessageScheduler] Error in initial message processing:', error);
        });

        // Then run on interval
        this.intervalId = setInterval(() => {
            this.processMessages().catch(error => {
                console.error('[SupabaseMessageScheduler] Error in scheduled message processing:', error);
            });
        }, this.pollIntervalMs);
    }

    /**
     * Stop the message scheduler
     */
    public stop(): void {
        if (!this.isRunning) {
            console.log('[SupabaseMessageScheduler] Not running');
            return;
        }

        this.isRunning = false;

        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        console.log('[SupabaseMessageScheduler] Message scheduler stopped');
    }

    /**
     * Process pending scheduled messages
     */
    private async processMessages(): Promise<void> {
        try {
            console.log('[SupabaseMessageScheduler] Checking for pending messages...');

            // Get pending messages that are due
            const pendingMessages = await this.executeQuery<ScheduledMessage>(
                'Get pending scheduled messages',
                () => this.client
                    .from(this.tableName)
                    .select('*')
                    .eq('status', 'pending')
                    .lte('scheduled_time', new Date().toISOString())
                    .order('scheduled_time', { ascending: true })
                    .limit(10)
            );

            if (pendingMessages.length === 0) {
                console.log('[SupabaseMessageScheduler] No pending messages found');
                return;
            }

            console.log(`[SupabaseMessageScheduler] Found ${pendingMessages.length} pending messages`);

            // Process each message
            for (const message of pendingMessages) {
                try {
                    await this.processMessage(message);
                } catch (error) {
                    console.error(`[SupabaseMessageScheduler] Error processing message ${message.id}:`, error);

                    // Mark message as failed
                    await this.markMessageFailed(message.id, error instanceof Error ? error.message : String(error));
                }
            }

        } catch (error) {
            console.error('[SupabaseMessageScheduler] Error fetching pending messages:', error);
        }
    }

    /**
     * Process a single message
     */
    private async processMessage(message: ScheduledMessage): Promise<void> {
        console.log(`[SupabaseMessageScheduler] Processing message ${message.id} for recipient ${message.recipient_id}`);

        try {
            // Here you would implement your actual message sending logic
            // For now, we'll just simulate message delivery
            await this.simulateMessageDelivery(message);

            // Mark message as sent
            await this.markMessageSent(message.id);

            console.log(`[SupabaseMessageScheduler] Message ${message.id} sent successfully`);

        } catch (error) {
            console.error(`[SupabaseMessageScheduler] Failed to send message ${message.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate message delivery (replace with actual implementation)
     */
    private async simulateMessageDelivery(message: ScheduledMessage): Promise<void> {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 100));

        // Here you would integrate with your actual messaging service
        // Examples: email service, SMS service, push notifications, etc.
        console.log(`[SupabaseMessageScheduler] Simulating delivery of message: "${message.message_content.slice(0, 50)}..."`);
    }

    /**
     * Mark a message as sent
     */
    private async markMessageSent(messageId: string): Promise<void> {
        await this.executeUpdate(
            `Mark message ${messageId} as sent`,
            {
                status: 'sent',
                updated_at: new Date().toISOString()
            },
            (query) => query.eq('id', messageId)
        );
    }

    /**
     * Mark a message as failed
     */
    private async markMessageFailed(messageId: string, errorMessage: string): Promise<void> {
        await this.executeUpdate(
            `Mark message ${messageId} as failed`,
            {
                status: 'failed',
                error_message: errorMessage,
                updated_at: new Date().toISOString()
            },
            (query) => query.eq('id', messageId)
        );
    }

    /**
     * Schedule a new message
     */
    public async scheduleMessage(
        recipientId: string,
        messageContent: string,
        scheduledTime: Date
    ): Promise<string> {
        const messageData = {
            recipient_id: recipientId,
            message_content: messageContent,
            scheduled_time: scheduledTime.toISOString(),
            status: 'pending' as const,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        const result = await this.executeInsert<ScheduledMessage>(
            'Schedule new message',
            messageData
        );

        if (result.length === 0) {
            throw new Error('Failed to schedule message');
        }

        const messageId = result[0].id;
        console.log(`[SupabaseMessageScheduler] Scheduled message ${messageId} for ${scheduledTime.toISOString()}`);

        return messageId;
    }

    /**
     * Cancel a scheduled message
     */
    public async cancelMessage(messageId: string): Promise<void> {
        await this.executeUpdate(
            `Cancel message ${messageId}`,
            {
                status: 'cancelled',
                updated_at: new Date().toISOString()
            },
            (query) => query.eq('id', messageId).eq('status', 'pending')
        );

        console.log(`[SupabaseMessageScheduler] Cancelled message ${messageId}`);
    }

    /**
     * Get scheduler status
     */
    public getStatus(): { running: boolean; intervalMs: number } {
        return {
            running: this.isRunning,
            intervalMs: this.pollIntervalMs
        };
    }

    /**
     * Get pending message count
     */
    public async getPendingMessageCount(): Promise<number> {
        const result = await this.executeQuery<{ count: number }>(
            'Get pending message count',
            () => this.client
                .from(this.tableName)
                .select('*', { count: 'exact', head: true })
                .eq('status', 'pending')
        );

        return result.length;
    }
}

// Export singleton instance
export const supabaseMessageScheduler = new SupabaseMessageScheduler(); 