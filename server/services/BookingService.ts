import { BaseService } from './BaseService';
import { Booking, InsertBooking, BookingWithSession } from '@shared/schema';
import { errorHandler } from './index';

/**
 * Booking service for booking-related business logic
 */
export class BookingService extends BaseService {
  /**
   * Get a booking by ID
   * @param id - Booking ID
   * @returns Booking or undefined
   */
  async getBooking(id: string): Promise<Booking | undefined> {
    try {
      return await this.repositories.getBookingRepository().getBooking(id);
    } catch (error) {
      this.handleError(error, 'BookingService.getBooking');
    }
  }

  /**
   * Get a booking with session details
   * @param id - Booking ID
   * @returns Booking with session or undefined
   */
  async getBookingWithSession(id: string): Promise<BookingWithSession | undefined> {
    try {
      return await this.repositories.getBookingRepository().getBookingWithSession(id);
    } catch (error) {
      this.handleError(error, 'BookingService.getBookingWithSession');
    }
  }

  /**
   * Create a new booking
   * @param bookingData - Booking data
   * @returns Created booking
   */
  async createBooking(bookingData: InsertBooking): Promise<Booking> {
    try {
      // Check if the session exists
      const session = await this.repositories.getSessionRepository().getSession(bookingData.sessionId);
      if (!session) {
        throw errorHandler.notFound(`Session with ID ${bookingData.sessionId} not found`);
      }

      // Check if the user exists
      const user = await this.repositories.getUserRepository().getUser(bookingData.userId);
      if (!user) {
        throw errorHandler.notFound(`User with ID ${bookingData.userId} not found`);
      }

      // Check if the session is in the past
      if (session.date < new Date()) {
        throw errorHandler.badRequest(`Cannot book a session that has already started`);
      }

      // Check if the session is full
      const bookings = await this.repositories.getBookingRepository().getSessionBookings(bookingData.sessionId);
      const confirmedBookings = bookings.filter(booking => booking.status === 'confirmed');
      if (confirmedBookings.length >= session.maxParticipants) {
        throw errorHandler.conflict(`Session is already full`);
      }

      // Check if the user has already booked this session
      const existingBooking = bookings.find(booking => booking.userId === bookingData.userId);
      if (existingBooking) {
        throw errorHandler.conflict(`User has already booked this session`);
      }

      // Create the booking
      const booking = await this.repositories.getBookingRepository().createBooking(bookingData);

      // Create a conversation for the booking if it doesn't exist
      const existingConversations = await this.repositories.getConversationRepository().getUserConversations(bookingData.userId);
      const sessionTeacherId = session.teacherId || session.teacherId;

      // Check if a conversation already exists between the user and the teacher
      const existingConversation = existingConversations.find(conversation =>
        conversation.participantIds.includes(sessionTeacherId)
      );

      if (!existingConversation) {
        // Create a new conversation
        await this.repositories.getConversationRepository().createConversation({
          participantIds: [bookingData.userId, sessionTeacherId],
          bookingId: booking.id
        });
      }

      return booking;
    } catch (error) {
      this.handleError(error, 'BookingService.createBooking');
    }
  }

  /**
   * Update a booking
   * @param id - Booking ID
   * @param bookingData - Booking data to update
   * @returns Updated booking
   */
  async updateBooking(id: string, bookingData: Partial<Booking>): Promise<Booking | undefined> {
    try {
      // Get the existing booking
      const existingBooking = await this.repositories.getBookingRepository().getBooking(id);
      if (!existingBooking) {
        throw errorHandler.notFound(`Booking with ID ${id} not found`);
      }

      // Update the booking
      return await this.repositories.getBookingRepository().updateBooking(id, bookingData);
    } catch (error) {
      this.handleError(error, 'BookingService.updateBooking');
    }
  }

  /**
   * Get bookings for a user
   * @param userId - User ID
   * @returns Array of bookings with session details
   */
  async getUserBookings(userId: string): Promise<BookingWithSession[]> {
    try {
      return await this.repositories.getBookingRepository().getUserBookings(userId);
    } catch (error) {
      this.handleError(error, 'BookingService.getUserBookings');
    }
  }

  /**
   * Get bookings for a session
   * @param sessionId - Session ID
   * @returns Array of bookings
   */
  async getSessionBookings(sessionId: string): Promise<Booking[]> {
    try {
      return await this.repositories.getBookingRepository().getSessionBookings(sessionId);
    } catch (error) {
      this.handleError(error, 'BookingService.getSessionBookings');
    }
  }

  /**
   * Get bookings for a session with user details
   * @param sessionId - Session ID
   * @returns Array of bookings with user details
   */
  async getBookingsBySessionId(sessionId: string): Promise<any[]> {
    try {
      return await this.repositories.getBookingRepository().getBookingsBySessionId(sessionId);
    } catch (error) {
      this.handleError(error, 'BookingService.getBookingsBySessionId');
    }
  }

  /**
   * Cancel a booking
   * @param id - Booking ID
   * @param userId - User ID (for authorization)
   * @returns Updated booking
   */
  async cancelBooking(id: string, userId: string): Promise<Booking | undefined> {
    try {
      // Get the existing booking
      const existingBooking = await this.repositories.getBookingRepository().getBooking(id);
      if (!existingBooking) {
        throw errorHandler.notFound(`Booking with ID ${id} not found`);
      }

      // Check if the user is authorized to cancel the booking
      if (existingBooking.userId !== userId) {
        // Check if the user is the teacher of the session
        const session = await this.repositories.getSessionRepository().getSession(existingBooking.sessionId);
        if (!session || session.teacherId !== userId) {
          throw errorHandler.forbidden(`User is not authorized to cancel this booking`);
        }
      }

      // Check if the booking is already canceled
      if (existingBooking.status === 'canceled') {
        throw errorHandler.badRequest(`Booking is already canceled`);
      }

      // Check if the session has already started
      const session = await this.repositories.getSessionRepository().getSession(existingBooking.sessionId);
      if (session && session.date < new Date()) {
        throw errorHandler.badRequest(`Cannot cancel a booking for a session that has already started`);
      }

      // Update the booking status to canceled
      return await this.repositories.getBookingRepository().updateBooking(id, {
        status: 'canceled'
      });
    } catch (error) {
      this.handleError(error, 'BookingService.cancelBooking');
    }
  }

  /**
   * Complete a booking
   * @param id - Booking ID
   * @param teacherId - Teacher ID (for authorization)
   * @returns Updated booking
   */
  async completeBooking(id: string, teacherId: string): Promise<Booking | undefined> {
    try {
      // Get the existing booking
      const existingBooking = await this.repositories.getBookingRepository().getBooking(id);
      if (!existingBooking) {
        throw errorHandler.notFound(`Booking with ID ${id} not found`);
      }

      // Get the session
      const session = await this.repositories.getSessionRepository().getSession(existingBooking.sessionId);
      if (!session) {
        throw errorHandler.notFound(`Session for booking with ID ${id} not found`);
      }

      // Check if the user is the teacher of the session
      if (session.teacherId !== teacherId) {
        throw errorHandler.forbidden(`User is not authorized to complete this booking`);
      }

      // Check if the booking is already completed
      if (existingBooking.status === 'completed') {
        throw errorHandler.badRequest(`Booking is already completed`);
      }

      // Check if the booking is canceled
      if (existingBooking.status === 'canceled') {
        throw errorHandler.badRequest(`Cannot complete a canceled booking`);
      }

      // Update the booking status to completed
      return await this.repositories.getBookingRepository().updateBooking(id, {
        status: 'completed'
      });
    } catch (error) {
      this.handleError(error, 'BookingService.completeBooking');
    }
  }
}

export default BookingService;
