import { BaseRepository } from './BaseRepository';
import { SocialAccount, InsertSocialAccount } from '@shared/schema';

/**
 * Repository interface for authentication-related operations
 */
export interface AuthRepository extends BaseRepository<any, string> {
  /**
   * Find a social account by provider and provider ID
   * @param provider The provider (e.g., 'google', 'facebook')
   * @param providerId The provider-specific ID
   * @returns The social account or undefined if not found
   */
  findSocialAccount(provider: string, providerId: string): Promise<SocialAccount | undefined>;

  /**
   * Create a new social account
   * @param socialAccount The social account to create
   * @returns The created social account
   */
  createSocialAccount(socialAccount: InsertSocialAccount): Promise<SocialAccount>;

  /**
   * Update a social account
   * @param id The social account ID
   * @param socialAccount The social account data to update
   * @returns The updated social account or undefined if not found
   */
  updateSocialAccount(id: string, socialAccount: Partial<SocialAccount>): Promise<SocialAccount | undefined>;

  /**
   * Delete a social account
   * @param id The social account ID
   * @returns True if deleted, false if not found
   */
  deleteSocialAccount(id: string): Promise<boolean>;

  /**
   * Find social accounts by user ID
   * @param userId The user ID
   * @returns Array of social accounts
   */
  findSocialAccountsByUserId(userId: string): Promise<SocialAccount[]>;

  /**
   * Generate a password reset token
   * @param userId The user ID
   * @param expiryHours The token expiry in hours (default: 24)
   * @returns The generated token
   */
  generatePasswordResetToken(userId: string, expiryHours?: number): Promise<string>;

  /**
   * Verify a password reset token
   * @param token The token to verify
   * @returns The user ID if valid, undefined if invalid or expired
   */
  verifyPasswordResetToken(token: string): Promise<string | undefined>;

  /**
   * Invalidate a password reset token
   * @param token The token to invalidate
   * @returns True if invalidated, false if not found
   */
  invalidatePasswordResetToken(token: string): Promise<boolean>;

  /**
   * Generate an email verification token
   * @param userId The user ID
   * @param expiryHours The token expiry in hours (default: 72)
   * @returns The generated token
   */
  generateEmailVerificationToken(userId: string, expiryHours?: number): Promise<string>;

  /**
   * Verify an email verification token
   * @param token The token to verify
   * @returns The user ID if valid, undefined if invalid or expired
   */
  verifyEmailVerificationToken(token: string): Promise<string | undefined>;

  /**
   * Invalidate an email verification token
   * @param token The token to invalidate
   * @returns True if invalidated, false if not found
   */
  invalidateEmailVerificationToken(token: string): Promise<boolean>;

  /**
   * Record a login attempt
   * @param userId The user ID
   * @param success Whether the login was successful
   * @param ipAddress The IP address of the login attempt
   * @param userAgent The user agent of the login attempt
   * @returns The ID of the login attempt record
   */
  recordLoginAttempt(userId: string, success: boolean, ipAddress: string, userAgent: string): Promise<string>;

  /**
   * Get recent login attempts for a user
   * @param userId The user ID
   * @param limit The maximum number of attempts to return (default: 10)
   * @returns Array of login attempts
   */
  getRecentLoginAttempts(userId: string, limit?: number): Promise<any[]>;

  /**
   * Check if a user account is locked
   * @param userId The user ID
   * @returns True if the account is locked
   */
  isAccountLocked(userId: string): Promise<boolean>;

  /**
   * Lock a user account
   * @param userId The user ID
   * @param reason The reason for locking the account
   * @returns True if locked successfully
   */
  lockAccount(userId: string, reason: string): Promise<boolean>;

  /**
   * Unlock a user account
   * @param userId The user ID
   * @returns True if unlocked successfully
   */
  unlockAccount(userId: string): Promise<boolean>;

  /**
   * Clean up expired tokens
   * @returns Number of tokens deleted
   */
  cleanupExpiredTokens(): Promise<number>;
}
