import { BaseRepository } from './BaseRepository';
import { UserProfile, InsertUserProfile } from '@shared/schema';

/**
 * Repository interface for UserProfile entity
 */
export interface ProfileRepository extends BaseRepository<UserProfile, string> {
  /**
   * Find a profile by user ID
   * @param userId The user ID
   * @returns The profile or undefined if not found
   */
  findByUserId(userId: string): Promise<UserProfile | undefined>;

  /**
   * Update a profile by user ID
   * @param userId The user ID
   * @param profile The profile data to update
   * @returns The updated profile or undefined if not found
   */
  updateByUserId(userId: string, profile: Partial<UserProfile>): Promise<UserProfile | undefined>;

  /**
   * Find profiles by specialization
   * @param specialization The specialization to search for
   * @returns Array of matching profiles
   */
  findBySpecialization(specialization: string): Promise<UserProfile[]>;

  /**
   * Find profiles by skill
   * @param skill The skill to search for
   * @returns Array of matching profiles
   */
  findBySkill(skill: string): Promise<UserProfile[]>;

  /**
   * Find profiles by location
   * @param location The location to search for
   * @returns Array of matching profiles
   */
  findByLocation(location: string): Promise<UserProfile[]>;

  /**
   * Find teacher profiles
   * @returns Array of teacher profiles
   */
  findTeacherProfiles(): Promise<UserProfile[]>;

  /**
   * Search profiles by name or bio
   * @param query The search query
   * @param limit Maximum number of results to return
   * @returns Array of matching profiles
   */
  search(query: string, limit?: number): Promise<UserProfile[]>;
}
