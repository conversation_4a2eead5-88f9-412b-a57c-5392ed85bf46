import { BaseRepository } from './BaseRepository';
import { Session, InsertSession, SessionWithTeacher } from '@shared/schema';

/**
 * Repository interface for Session entity
 */
export interface SessionRepository extends BaseRepository<Session, string> {
  /**
   * Create a new session
   * @param sessionData Session data to create
   * @returns Created session
   */
  createSession(sessionData: InsertSession): Promise<Session>;

  /**
   * Get a session by ID
   * @param sessionId Session ID
   * @returns Session or null
   */
  getSession(sessionId: string): Promise<Session | null>;

  /**
   * Get a session with teacher details by ID
   * @param sessionId Session ID
   * @returns Session with teacher or null
   */
  getSessionWithTeacher(sessionId: string): Promise<SessionWithTeacher | null>;

  /**
   * Get sessions with optional filters
   * @param filters Optional filters
   * @returns Sessions array
   */
  getSessions(filters?: any): Promise<SessionWithTeacher[]>;

  /**
   * Update a session
   * @param sessionId Session ID
   * @param updateData Data to update
   * @returns Updated session
   */
  updateSession(sessionId: string, updateData: Partial<Session>): Promise<Session>;

  /**
   * Delete a session
   * @param sessionId Session ID
   * @returns Deleted session
   */
  deleteSession(sessionId: string): Promise<Session>;

  /**
   * Check for session scheduling conflicts
   * @param teacherId Teacher ID
   * @param sessionDate Session date
   * @param duration Session duration in minutes
   * @returns Conflict check result
   */
  checkSessionSchedulingConflict(
    teacherId: string,
    sessionDate: Date,
    duration: number
  ): Promise<{ hasConflict: boolean; conflictingSession?: Session }>;
}
