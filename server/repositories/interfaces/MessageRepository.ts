import { BaseRepository } from './BaseRepository';
import { Message, InsertMessage, MessageWithSender, Conversation, InsertConversation, ConversationWithMessages } from '@shared/schema';

/**
 * Repository interface for Message entity
 */
export interface MessageRepository extends BaseRepository<Message, string> {
  /**
   * Find messages by conversation ID
   * @param conversationId The conversation ID
   * @returns Array of messages with sender details
   */
  findByConversationId(conversationId: string): Promise<MessageWithSender[]>;

  /**
   * Find messages by sender ID
   * @param senderId The sender ID
   * @returns Array of messages
   */
  findBySenderId(senderId: string): Promise<Message[]>;

  /**
   * Find messages by recipient ID
   * @param recipientId The recipient ID
   * @returns Array of messages
   */
  findByRecipientId(recipientId: string): Promise<Message[]>;

  /**
   * Mark messages as read
   * @param conversationId The conversation ID
   * @param userId The user ID
   * @returns Void
   */
  markAsRead(conversationId: string, userId: string): Promise<void>;

  /**
   * Get unread message count for a user
   * @param userId The user ID
   * @returns Number of unread messages
   */
  getUnreadCount(userId: string): Promise<number>;

  /**
   * Create a new conversation
   * @param conversation The conversation data
   * @returns The created conversation
   */
  createConversation(conversation: InsertConversation): Promise<Conversation>;

  /**
   * Find a conversation by ID
   * @param id The conversation ID
   * @returns The conversation or undefined if not found
   */
  findConversationById(id: string): Promise<Conversation | undefined>;

  /**
   * Find a conversation with messages by ID
   * @param id The conversation ID
   * @returns The conversation with messages or undefined if not found
   */
  findConversationWithMessagesById(id: string): Promise<ConversationWithMessages | undefined>;

  /**
   * Find conversations by user ID
   * @param userId The user ID
   * @returns Array of conversations with messages
   */
  findConversationsByUserId(userId: string): Promise<ConversationWithMessages[]>;

  /**
   * Delete a conversation
   * @param conversationId The conversation ID
   * @param userId The user ID (for permission check)
   * @returns True if deleted, false if not found or not authorized
   */
  deleteConversation(conversationId: string, userId: string): Promise<boolean>;
}
