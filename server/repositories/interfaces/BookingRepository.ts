import { BaseRepository } from './BaseRepository';
import { Booking, InsertBooking, BookingWithSession } from '@shared/schema';

/**
 * Repository interface for Booking entity
 */
export interface BookingRepository extends BaseRepository<Booking, string> {
  /**
   * Find bookings by user ID
   * @param userId The user ID
   * @returns Array of bookings with session details
   */
  findByUserId(userId: string): Promise<BookingWithSession[]>;

  /**
   * Find bookings by session ID
   * @param sessionId The session ID
   * @returns Array of bookings
   */
  findBySessionId(sessionId: string): Promise<Booking[]>;

  /**
   * Find bookings by teacher ID
   * @param teacherId The teacher ID
   * @returns Array of bookings with session details
   */
  findByTeacherId(teacherId: string): Promise<BookingWithSession[]>;

  /**
   * Find bookings by status
   * @param status The booking status
   * @returns Array of bookings
   */
  findByStatus(status: string): Promise<Booking[]>;

  /**
   * Find bookings by date range
   * @param startDate The start date
   * @param endDate The end date
   * @returns Array of bookings
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<Booking[]>;

  /**
   * Update booking status
   * @param bookingId The booking ID
   * @param status The new status
   * @returns The updated booking or undefined if not found
   */
  updateStatus(bookingId: string, status: string): Promise<Booking | undefined>;

  /**
   * Get upcoming bookings for a user
   * @param userId The user ID
   * @returns Array of upcoming bookings with session details
   */
  getUpcomingBookings(userId: string): Promise<BookingWithSession[]>;

  /**
   * Get past bookings for a user
   * @param userId The user ID
   * @returns Array of past bookings with session details
   */
  getPastBookings(userId: string): Promise<BookingWithSession[]>;

  /**
   * Check if a user has booked a session
   * @param userId The user ID
   * @param sessionId The session ID
   * @returns True if the user has booked the session
   */
  hasUserBookedSession(userId: string, sessionId: string): Promise<boolean>;

  /**
   * Get a booking by ID
   * @param id Booking ID
   * @returns Booking or null
   */
  getBooking(id: string): Promise<Booking | null>;

  /**
   * Create a new booking
   * @param bookingData Booking data
   * @returns Created booking
   */
  createBooking(bookingData: InsertBooking): Promise<Booking>;

  /**
   * Update a booking
   * @param id Booking ID
   * @param updateData Data to update
   * @returns Updated booking
   */
  updateBooking(id: string, updateData: Partial<Booking>): Promise<Booking>;

  /**
   * Get bookings for a user with session details
   * @param userId User ID
   * @returns Array of bookings with session details
   */
  getUserBookings(userId: string): Promise<BookingWithSession[]>;

  /**
   * Get bookings for a session
   * @param sessionId Session ID
   * @returns Array of bookings
   */
  getSessionBookings(sessionId: string): Promise<Booking[]>;

  /**
   * Get bookings for a session with user details
   * @param sessionId Session ID
   * @returns Array of bookings with user details
   */
  getBookingsBySessionId(sessionId: string): Promise<any[]>;

  /**
   * Update booking status
   * @param id Booking ID
   * @param status New status
   * @returns Updated booking
   */
  updateBookingStatus(id: string, status: string): Promise<Booking>;

  /**
   * Delete a booking
   * @param id Booking ID
   * @returns Deleted booking
   */
  deleteBooking(id: string): Promise<Booking>;
}
