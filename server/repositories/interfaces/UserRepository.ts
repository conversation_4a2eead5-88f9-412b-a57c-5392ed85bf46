import { BaseRepository } from './BaseRepository';
import { User, InsertUser } from '@shared/schema';

/**
 * Repository interface for User entity
 */
export interface UserRepository extends BaseRepository<User, string> {
  /**
   * Find a user by username
   * @param username The username to search for
   * @returns The user or undefined if not found
   */
  findByUsername(username: string): Promise<User | undefined>;

  /**
   * Find a user by email
   * @param email The email to search for
   * @returns The user or undefined if not found
   */
  findByEmail(email: string): Promise<User | undefined>;

  /**
   * Find a user by social provider and provider ID
   * @param provider The social provider (e.g., 'google', 'facebook')
   * @param providerId The provider-specific ID
   * @returns The user or undefined if not found
   */
  findBySocialProvider(provider: string, providerId: string): Promise<User | undefined>;

  /**
   * Create a user from social login data
   * @param socialData The social login data
   * @returns The created user
   */
  createFromSocial(socialData: {
    name: string;
    email: string;
    provider: string;
    providerId: string;
    username?: string;
    profileUrl?: string;
    avatarUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<User>;

  /**
   * Get all users with teacher role
   * @returns Array of teacher users
   */
  getAllTeachers(): Promise<User[]>;

  /**
   * Get top teachers based on rating and review count
   * @param limit Maximum number of teachers to return
   * @returns Array of top teacher users
   */
  getTopTeachers(limit: number): Promise<User[]>;

  /**
   * Search users by partial username
   * @param partialUsername The partial username to search for
   * @param limit Maximum number of results to return
   * @returns Array of matching users
   */
  searchByUsername(partialUsername: string, limit?: number): Promise<User[]>;

  /**
   * Verify a user's email
   * @param userId The user ID
   * @returns True if successful
   */
  verifyEmail(userId: number): Promise<boolean>;

  /**
   * Link a social account to a user
   * @param userId The user ID
   * @param socialAccount The social account data
   * @returns True if successful
   */
  linkSocialAccount(userId: number, socialAccount: {
    provider: string;
    providerId: string;
    username?: string;
    email?: string;
    profileUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<boolean>;

  /**
   * Get social accounts linked to a user
   * @param userId The user ID
   * @returns Array of social accounts
   */
  getSocialAccounts(userId: number): Promise<Array<{
    provider: string;
    providerId: string;
    username: string;
    profileUrl: string;
  }>>;

  /**
   * Delete a social account from a user
   * @param userId The user ID
   * @param provider The social provider
   * @param providerId The provider-specific ID
   * @returns True if successful
   */
  deleteSocialAccount(userId: number, provider: string, providerId: string): Promise<boolean>;
}
