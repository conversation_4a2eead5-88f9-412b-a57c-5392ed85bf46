import { BaseRepository } from './BaseRepository';
import { Review, InsertReview, ReviewWithUser } from '@shared/schema';

/**
 * Repository interface for Review entity
 */
export interface ReviewRepository extends BaseRepository<Review, string> {
  /**
   * Find reviews by user ID
   * @param userId The user ID
   * @returns Array of reviews
   */
  findByUserId(userId: string): Promise<Review[]>;

  /**
   * Find reviews by session ID
   * @param sessionId The session ID
   * @returns Array of reviews with user details
   */
  findBySessionId(sessionId: string): Promise<ReviewWithUser[]>;

  /**
   * Find reviews by teacher ID
   * @param teacherId The teacher ID
   * @returns Array of reviews with user details
   */
  findByTeacherId(teacherId: string): Promise<ReviewWithUser[]>;

  /**
   * Get average rating for a session
   * @param sessionId The session ID
   * @returns The average rating or null if no reviews
   */
  getAverageRatingForSession(sessionId: string): Promise<number | null>;

  /**
   * Get average rating for a teacher
   * @param teacherId The teacher ID
   * @returns The average rating or null if no reviews
   */
  getAverageRatingForTeacher(teacherId: string): Promise<number | null>;

  /**
   * Get review count for a session
   * @param sessionId The session ID
   * @returns The review count
   */
  getReviewCountForSession(sessionId: string): Promise<number>;

  /**
   * Get review count for a teacher
   * @param teacherId The teacher ID
   * @returns The review count
   */
  getReviewCountForTeacher(teacherId: string): Promise<number>;

  /**
   * Check if a user has reviewed a session
   * @param userId The user ID
   * @param sessionId The session ID
   * @returns True if the user has reviewed the session
   */
  hasUserReviewedSession(userId: string, sessionId: string): Promise<boolean>;
}
