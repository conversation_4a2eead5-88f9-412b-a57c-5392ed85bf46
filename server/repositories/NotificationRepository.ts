import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { DeviceToken, NotificationPreference } from '../storage';
import { errorHandler } from '../services';

/**
 * Notification repository for notification-related database operations using Supabase client
 */
export class NotificationRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('notifications');
  }

  /**
   * Register a device token
   * @param userId - User ID
   * @param token - Device token
   * @param platform - Platform (ios, android, web)
   * @returns Device token
   */
  async registerDeviceToken(userId: string, token: string, platform: string): Promise<DeviceToken> {
    try {
      // Check if the token already exists
      const existingTokens = await this.executeQuery<DeviceToken>(
        `Check existing device token`,
        () => this.client.from('device_tokens').select('*').eq('token', token),
        60
      );

      if (existingTokens.length > 0) {
        // Update the existing token
        const tokens = await this.executeUpdate<DeviceToken>(
          `Update existing device token`,
          {
            user_id: userId,
            platform,
            last_active: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          (query) => query.eq('token', token)
        );
        return tokens[0];
      } else {
        // Insert a new token
        const tokens = await this.executeInsert<DeviceToken>(
          'Register new device token',
          {
            user_id: userId,
            token,
            platform,
            created_at: new Date().toISOString(),
            last_active: new Date().toISOString()
          }
        );
        return tokens[0];
      }
    } catch (error) {
      console.error(`[NotificationRepository] Error registering device token for user ${userId}:`, error);
      throw errorHandler.database(`Error registering device token for user ${userId}`, error);
    }
  }

  /**
   * Get device tokens for a user
   * @param userId - User ID
   * @returns Array of device tokens
   */
  async getUserDevices(userId: string): Promise<DeviceToken[]> {
    try {
      return await this.executeQuery<DeviceToken>(
        `Get user ${userId} devices`,
        () => this.client.from('device_tokens').select('*').eq('user_id', userId),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[NotificationRepository] Error getting device tokens for user ${userId}:`, error);
      throw errorHandler.database(`Error getting device tokens for user ${userId}`, error);
    }
  }

  /**
   * Remove a device token
   * @param token - Device token
   * @returns True if successful
   */
  async removeDeviceToken(token: string): Promise<boolean> {
    try {
      const result = await this.executeDelete<DeviceToken>(
        `Remove device token`,
        (query) => query.eq('token', token)
      );
      return result.length > 0;
    } catch (error) {
      console.error(`[NotificationRepository] Error removing device token ${token}:`, error);
      throw errorHandler.database(`Error removing device token ${token}`, error);
    }
  }

  /**
   * Get notification preferences for a user
   * @param userId - User ID
   * @returns Notification preferences or undefined
   */
  async getNotificationPreferences(userId: string): Promise<NotificationPreference | undefined> {
    try {
      const preferences = await this.executeSingleQuery<NotificationPreference>(
        `Get notification preferences for user ${userId}`,
        () => this.client.from('notification_preferences').select('*').eq('user_id', userId),
        60 // Cache for 1 minute
      );

      if (preferences) {
        return preferences;
      }

      // If no preferences exist, create default preferences
      return this.createDefaultNotificationPreferences(userId);
    } catch (error) {
      console.error(`[NotificationRepository] Error getting notification preferences for user ${userId}:`, error);
      throw errorHandler.database(`Error getting notification preferences for user ${userId}`, error);
    }
  }

  /**
   * Create default notification preferences for a user
   * @param userId - User ID
   * @returns Default notification preferences
   */
  private async createDefaultNotificationPreferences(userId: string): Promise<NotificationPreference> {
    try {
      const preferences = await this.executeInsert<NotificationPreference>(
        'Create default notification preferences',
        {
          user_id: userId,
          new_message: true,
          new_booking: true,
          booking_reminder: true,
          booking_changes: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      );
      return preferences[0];
    } catch (error) {
      console.error(`[NotificationRepository] Error creating default notification preferences for user ${userId}:`, error);
      throw errorHandler.database(`Error creating default notification preferences for user ${userId}`, error);
    }
  }

  /**
   * Update notification preferences for a user
   * @param userId - User ID
   * @param preferences - Notification preferences to update
   * @returns Updated notification preferences
   */
  async updateNotificationPreferences(userId: string, preferences: Partial<NotificationPreference>): Promise<NotificationPreference> {
    try {
      // Check if preferences exist
      const existingPreferences = await this.getNotificationPreferences(userId);

      if (!existingPreferences) {
        // Create default preferences first
        await this.createDefaultNotificationPreferences(userId);
      }

      // Update the preferences
      const updatedPreferences = await this.executeUpdate<NotificationPreference>(
        `Update notification preferences for user ${userId}`,
        {
          ...preferences,
          updated_at: new Date().toISOString()
        },
        (query) => query.eq('user_id', userId)
      );

      if (updatedPreferences.length === 0) {
        throw errorHandler.notFound(`Notification preferences for user ${userId} not found`);
      }

      return updatedPreferences[0];
    } catch (error) {
      console.error(`[NotificationRepository] Error updating notification preferences for user ${userId}:`, error);
      throw errorHandler.database(`Error updating notification preferences for user ${userId}`, error);
    }
  }
}

export default NotificationRepository;
