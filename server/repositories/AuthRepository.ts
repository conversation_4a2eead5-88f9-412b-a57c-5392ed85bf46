import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { errorHandler } from '../services';
import { randomBytes } from 'crypto';

/**
 * Auth repository for authentication-related database operations using Supabase client
 */
export class AuthRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('password_reset_tokens');
  }

  /**
   * Store a password reset token
   * @param userId - User ID
   * @param token - Password reset token
   * @param expiry - Token expiry date
   * @returns True if successful
   */
  async storePasswordResetToken(userId: string, token: string, expiry: Date): Promise<boolean> {
    try {
      // Store in Supabase for persistence
      const tokens = await this.executeInsert<any>(
        'Store password reset token',
        {
          user_id: userId,
          token,
          expiry: expiry.toISOString(),
          created_at: new Date().toISOString()
        }
      );

      return tokens.length > 0;
    } catch (error) {
      console.error(`[AuthRepository] Error storing password reset token for user ${userId}:`, error);
      throw errorHandler.database(`Error storing password reset token for user ${userId}`, error);
    }
  }

  /**
   * Get a password reset token
   * @param token - Password reset token
   * @returns User ID and expiry date or null
   */
  async getPasswordResetToken(token: string): Promise<{ userId: string, expiry: Date } | null> {
    try {
      // If not in the global map, check Supabase
      const tokenRecord = await this.executeSingleQuery<any>(
        'Get password reset token',
        () => this.client.from(this.tableName).select('user_id, expiry').eq('token', token),
        60
      );

      if (tokenRecord) {
        const expiry = new Date(tokenRecord.expiry);
        const userId = String(tokenRecord.user_id); // Ensure string type

        // Check if the token has expired
        if (expiry > new Date()) {
          return { userId, expiry };
        } else {
          // Token has expired, delete it
          await this.executeDelete<any>(
            'Delete expired password reset token',
            (query) => query.eq('token', token)
          );
          return null;
        }
      }

      return null;
    } catch (error) {
      console.error(`[AuthRepository] Error getting password reset token ${token}:`, error);
      throw errorHandler.database(`Error getting password reset token ${token}`, error);
    }
  }

  /**
   * Delete a password reset token
   * @param token - Password reset token
   */
  async deletePasswordResetToken(token: string): Promise<void> {
    try {
      // Delete from Supabase
      await this.executeDelete<any>(
        'Delete password reset token',
        (query) => query.eq('token', token)
      );
    } catch (error) {
      console.error(`[AuthRepository] Error deleting password reset token ${token}:`, error);
      throw errorHandler.database(`Error deleting password reset token ${token}`, error);
    }
  }

  /**
   * Store an email verification token
   * @param userId - User ID
   * @param token - Email verification token
   * @param expiry - Token expiry date
   * @returns True if successful
   */
  async storeEmailVerificationToken(userId: string, token: string, expiry: Date): Promise<boolean> {
    try {
      // Store in Supabase for persistence using the email_verification_tokens table
      const tokens = await this.executeQuery<any>(
        'Store email verification token',
        () => this.client.from('email_verification_tokens').insert({
          user_id: userId,
          token,
          expiry: expiry.toISOString(),
          created_at: new Date().toISOString()
        }).select(),
        0 // No cache for this
      );

      return tokens.length > 0;
    } catch (error) {
      console.error(`[AuthRepository] Error storing email verification token for user ${userId}:`, error);
      throw errorHandler.database(`Error storing email verification token for user ${userId}`, error);
    }
  }

  /**
   * Get an email verification token
   * @param token - Email verification token
   * @returns User ID and expiry date or null
   */
  async getEmailVerificationToken(token: string): Promise<{ userId: string, expiry: Date } | null> {
    try {
      // Check Supabase
      const tokenRecord = await this.executeSingleQuery<any>(
        'Get email verification token',
        () => this.client.from('email_verification_tokens').select('user_id, expiry').eq('token', token),
        60
      );

      if (tokenRecord) {
        const expiry = new Date(tokenRecord.expiry);
        const userId = String(tokenRecord.user_id);

        // Check if the token has expired
        if (expiry > new Date()) {
          return { userId, expiry };
        } else {
          // Token has expired, delete it
          await this.executeQuery<any>(
            'Delete expired email verification token',
            () => this.client.from('email_verification_tokens').delete().eq('token', token),
            0 // No cache
          );
          return null;
        }
      }

      return null;
    } catch (error) {
      console.error(`[AuthRepository] Error getting email verification token ${token}:`, error);
      throw errorHandler.database(`Error getting email verification token ${token}`, error);
    }
  }

  /**
   * Delete an email verification token
   * @param token - Email verification token
   */
  async deleteEmailVerificationToken(token: string): Promise<void> {
    try {
      // Delete from Supabase
      await this.executeQuery<any>(
        'Delete email verification token',
        () => this.client.from('email_verification_tokens').delete().eq('token', token),
        0 // No cache
      );
    } catch (error) {
      console.error(`[AuthRepository] Error deleting email verification token ${token}:`, error);
      throw errorHandler.database(`Error deleting email verification token ${token}`, error);
    }
  }

  /**
   * Generate a secure random token
   * @returns Secure random token
   */
  generateToken(): string {
    return randomBytes(32).toString('hex');
  }
}

export default AuthRepository;