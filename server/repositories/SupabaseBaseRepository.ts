import { SupabaseClient } from '@supabase/supabase-js';
import { supabaseAdmin } from '../lib/supabase';

/**
 * Base repository class using Supabase client for all database operations
 * This is the recommended approach for production systems
 */
export class SupabaseBaseRepository {
    protected client: SupabaseClient;
    protected tableName: string;

    // Cache settings
    protected cacheEnabled: boolean = true;
    protected cacheTtl: number = 300; // 5 minutes
    private queryCache = new Map<string, { data: any[], timestamp: number }>();

    // Performance metrics
    private queryTimings: { operation: string, time: number, rows: number }[] = [];

    /**
     * Constructor
     * @param tableName - Database table name
     * @param client - Optional Supabase client (defaults to admin client)
     */
    constructor(tableName: string, client?: SupabaseClient) {
        this.client = client || supabaseAdmin;
        this.tableName = tableName;
    }

    /**
     * Execute a Supabase query with optional caching
     * @param operation - Description of the operation
     * @param queryFn - Function that returns a Supabase query
     * @param cacheDuration - Cache duration in seconds (0 to disable caching)
     * @returns Query result
     */
    protected async executeQuery<T>(
        operation: string,
        queryFn: () => any,
        cacheDuration: number = 0
    ): Promise<T[]> {
        // Generate cache key
        const cacheKey = this.generateCacheKey(operation, queryFn.toString());

        // Check cache if enabled and duration > 0
        if (this.cacheEnabled && cacheDuration > 0) {
            const cachedResult = this.queryCache.get(cacheKey);
            if (cachedResult && Date.now() - cachedResult.timestamp < cacheDuration * 1000) {
                console.log(`[Cache Hit] ${operation}`);
                return cachedResult.data as T[];
            }
        }

        // Measure query execution time
        const startTime = Date.now();

        try {
            // Execute Supabase query
            const { data, error } = await queryFn();

            if (error) {
                throw error;
            }

            // Record query timing
            const endTime = Date.now();
            const executionTime = endTime - startTime;

            this.queryTimings.push({
                operation,
                time: executionTime,
                rows: data?.length || 0
            });

            // Log slow queries (> 100ms)
            if (executionTime > 100) {
                console.warn(`[SLOW QUERY] ${executionTime}ms: ${operation}`);
            } else {
                console.log(`[Query] ${executionTime}ms: ${operation} (${data?.length || 0} rows)`);
            }

            // Cache result if enabled and duration > 0
            if (this.cacheEnabled && cacheDuration > 0) {
                this.queryCache.set(cacheKey, {
                    data: data || [],
                    timestamp: Date.now()
                });
            }

            return (data || []) as T[];
        } catch (error) {
            const endTime = Date.now();
            const executionTime = endTime - startTime;

            console.error(`[Query Error] ${executionTime}ms: ${operation}`, error);
            throw new Error(`Supabase query failed for ${operation}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Execute a single row query
     * @param operation - Description of the operation
     * @param queryFn - Function that returns a Supabase query
     * @param cacheDuration - Cache duration in seconds (0 to disable caching)
     * @returns Single query result or null
     */
    protected async executeSingleQuery<T>(
        operation: string,
        queryFn: () => any,
        cacheDuration: number = 0
    ): Promise<T | null> {
        const results = await this.executeQuery<T>(operation, queryFn, cacheDuration);
        return results.length > 0 ? results[0] : null;
    }

    /**
     * Execute an insert operation
     * @param operation - Description of the operation
     * @param data - Data to insert
     * @returns Inserted record(s)
     */
    protected async executeInsert<T>(
        operation: string,
        data: any | any[]
    ): Promise<T[]> {
        const startTime = Date.now();

        try {
            const { data: result, error } = await this.client
                .from(this.tableName)
                .insert(data)
                .select();

            if (error) {
                throw error;
            }

            const endTime = Date.now();
            const executionTime = endTime - startTime;

            this.queryTimings.push({
                operation,
                time: executionTime,
                rows: result?.length || 0
            });

            console.log(`[Insert] ${executionTime}ms: ${operation} (${result?.length || 0} rows)`);

            // Clear relevant cache entries
            this.clearCacheForTable();

            return (result || []) as T[];
        } catch (error) {
            console.error(`[Insert Error] ${operation}`, error);
            throw new Error(`Supabase insert failed for ${operation}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Execute an update operation
     * @param operation - Description of the operation
     * @param data - Data to update
     * @param conditions - Update conditions
     * @returns Updated record(s)
     */
    protected async executeUpdate<T>(
        operation: string,
        data: any,
        conditions: (query: any) => any
    ): Promise<T[]> {
        const startTime = Date.now();

        try {
            let query = this.client.from(this.tableName).update(data);
            query = conditions(query);

            const { data: result, error } = await query.select();

            if (error) {
                throw error;
            }

            const endTime = Date.now();
            const executionTime = endTime - startTime;

            this.queryTimings.push({
                operation,
                time: executionTime,
                rows: result?.length || 0
            });

            console.log(`[Update] ${executionTime}ms: ${operation} (${result?.length || 0} rows)`);

            // Clear relevant cache entries
            this.clearCacheForTable();

            return (result || []) as T[];
        } catch (error) {
            console.error(`[Update Error] ${operation}`, error);
            throw new Error(`Supabase update failed for ${operation}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Execute a delete operation
     * @param operation - Description of the operation
     * @param conditions - Delete conditions
     * @returns Deleted record(s)
     */
    protected async executeDelete<T>(
        operation: string,
        conditions: (query: any) => any
    ): Promise<T[]> {
        const startTime = Date.now();

        try {
            let query = this.client.from(this.tableName);
            query = conditions(query);

            const { data: result, error } = await query.delete().select();

            if (error) {
                throw error;
            }

            const endTime = Date.now();
            const executionTime = endTime - startTime;

            this.queryTimings.push({
                operation,
                time: executionTime,
                rows: result?.length || 0
            });

            console.log(`[Delete] ${executionTime}ms: ${operation} (${result?.length || 0} rows)`);

            // Clear relevant cache entries
            this.clearCacheForTable();

            return (result || []) as T[];
        } catch (error) {
            console.error(`[Delete Error] ${operation}`, error);
            throw new Error(`Supabase delete failed for ${operation}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Generate a cache key for a query
     * @param operation - Operation name
     * @param queryString - Query function string
     * @returns Cache key
     */
    private generateCacheKey(operation: string, queryString: string): string {
        return `${this.tableName}:${operation}:${queryString.slice(0, 100)}`;
    }

    /**
     * Clear the query cache
     */
    public clearCache(): void {
        this.queryCache.clear();
    }

    /**
     * Clear cache entries for this table
     */
    public clearCacheForTable(): void {
        const keysToDelete = Array.from(this.queryCache.keys())
            .filter(key => key.startsWith(`${this.tableName}:`));

        keysToDelete.forEach(key => this.queryCache.delete(key));
    }

    /**
     * Clear a specific cache entry
     * @param operation - Operation name
     * @param queryString - Query function string
     */
    public clearCacheEntry(operation: string, queryString: string): void {
        const cacheKey = this.generateCacheKey(operation, queryString);
        this.queryCache.delete(cacheKey);
    }

    /**
     * Get query performance metrics
     * @returns Query timings
     */
    public getQueryMetrics(): { operation: string, time: number, rows: number }[] {
        return [...this.queryTimings];
    }

    /**
     * Reset query performance metrics
     */
    public resetQueryMetrics(): void {
        this.queryTimings = [];
    }

    /**
     * Enable or disable caching
     * @param enabled - Whether to enable caching
     */
    public setCacheEnabled(enabled: boolean): void {
        this.cacheEnabled = enabled;
        if (!enabled) {
            this.clearCache();
        }
    }

    /**
     * Set cache TTL
     * @param ttl - Time to live in seconds
     */
    public setCacheTtl(ttl: number): void {
        this.cacheTtl = ttl;
    }
}

export default SupabaseBaseRepository; 