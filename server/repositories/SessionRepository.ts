import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { Session, InsertSession, SessionWithTeacher } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * Session repository for session-related database operations using Supabase client
 */
export class SessionRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('sessions');
  }

  /**
   * Map database fields (snake_case) to interface fields (camelCase)
   * @param rawSession Raw session from database
   * @returns Mapped session
   */
  private mapDatabaseToSession(rawSession: any): Session {
    // Create a clean object with properly mapped fields based on the actual Session schema
    const mapped: Session = {
      // Direct fields from the sessions table that match the Session interface
      id: rawSession.id,
      title: rawSession.title,
      description: rawSession.description,
      type: rawSession.type,
      price: rawSession.price,
      duration: rawSession.duration,
      date: rawSession.date,
      language: rawSession.language,
      format: rawSession.format,
      requirements: rawSession.requirements,
      rating: rawSession.rating || 0,

      // Map snake_case database fields to camelCase interface fields
      teacherId: rawSession.teacher_id || rawSession.teacherId,
      isPublic: rawSession.is_public !== undefined ? rawSession.is_public : rawSession.isPublic,
      imageUrl: rawSession.image_url || rawSession.imageUrl,
      maxParticipants: rawSession.max_participants || rawSession.maxParticipants,
      skillLevel: rawSession.skill_level || rawSession.skillLevel,
      learningOutcomes: rawSession.learning_outcomes || rawSession.learningOutcomes,
      zoomLink: rawSession.zoom_link || rawSession.zoomLink,
      reviewCount: rawSession.review_count || rawSession.reviewCount || 0,
      isFeatured: rawSession.is_featured !== undefined ? rawSession.is_featured : (rawSession.isFeatured || false),
      locationType: rawSession.location_type || rawSession.locationType,
      locationDetails: rawSession.location_details || rawSession.locationDetails,
      onlineHostingService: rawSession.online_hosting_service || rawSession.onlineHostingService,
      cancellationPolicy: rawSession.cancellation_policy || rawSession.cancellationPolicy,
      welcomeMessage: rawSession.welcome_message || rawSession.welcomeMessage,
      reminderMessage: rawSession.reminder_message || rawSession.reminderMessage,
      preparationNotes: rawSession.preparation_notes || rawSession.preparationNotes,
      followUpMessage: rawSession.follow_up_message || rawSession.followUpMessage,
      automatedMessagesJson: rawSession.automated_messages_json || rawSession.automatedMessagesJson,
      recurringPattern: rawSession.recurring_pattern || rawSession.recurringPattern,
      recurringDay: rawSession.recurring_day || rawSession.recurringDay,
      numOccurrences: rawSession.num_occurrences || rawSession.numOccurrences,
      cancellationTimeframe: rawSession.cancellation_timeframe || rawSession.cancellationTimeframe,
      customCancellationHours: rawSession.custom_cancellation_hours || rawSession.customCancellationHours,
      cancellationFeePercentage: rawSession.cancellation_fee_percentage || rawSession.cancellationFeePercentage,
      noShowFeePercentage: rawSession.no_show_fee_percentage || rawSession.noShowFeePercentage,
      legalAgreement: rawSession.legal_agreement || rawSession.legalAgreement,
      agreementType: rawSession.agreement_type || rawSession.agreementType,
      customAgreement: rawSession.custom_agreement !== undefined ? rawSession.custom_agreement : (rawSession.customAgreement || false),
      schedulingMode: rawSession.scheduling_mode || rawSession.schedulingMode,
      mediaGallery: rawSession.media_gallery || rawSession.mediaGallery || [],
      createdAt: rawSession.created_at || rawSession.createdAt,
      updatedAt: rawSession.updated_at || rawSession.updatedAt,
    };

    return mapped;
  }

  /**
   * Map interface fields (camelCase) to database fields (snake_case)
   * @param sessionData Session data from interface
   * @returns Mapped session data for database
   */
  private mapSessionToDatabase(sessionData: Partial<Session>): any {
    const mapped: any = { ...sessionData };

    // Map camelCase interface fields to snake_case database fields
    if (sessionData.teacherId !== undefined) mapped.teacher_id = sessionData.teacherId;
    if (sessionData.isPublic !== undefined) mapped.is_public = sessionData.isPublic;
    if (sessionData.imageUrl !== undefined) mapped.image_url = sessionData.imageUrl;
    if (sessionData.maxParticipants !== undefined) mapped.max_participants = sessionData.maxParticipants;
    if (sessionData.skillLevel !== undefined) mapped.skill_level = sessionData.skillLevel;
    if (sessionData.learningOutcomes !== undefined) mapped.learning_outcomes = sessionData.learningOutcomes;
    if (sessionData.zoomLink !== undefined) mapped.zoom_link = sessionData.zoomLink;
    if (sessionData.reviewCount !== undefined) mapped.review_count = sessionData.reviewCount;
    if (sessionData.isFeatured !== undefined) mapped.is_featured = sessionData.isFeatured;
    if (sessionData.locationType !== undefined) mapped.location_type = sessionData.locationType;
    if (sessionData.locationDetails !== undefined) mapped.location_details = sessionData.locationDetails;
    if (sessionData.onlineHostingService !== undefined) mapped.online_hosting_service = sessionData.onlineHostingService;
    if (sessionData.cancellationPolicy !== undefined) mapped.cancellation_policy = sessionData.cancellationPolicy;
    if (sessionData.welcomeMessage !== undefined) mapped.welcome_message = sessionData.welcomeMessage;
    if (sessionData.reminderMessage !== undefined) mapped.reminder_message = sessionData.reminderMessage;
    if (sessionData.preparationNotes !== undefined) mapped.preparation_notes = sessionData.preparationNotes;
    if (sessionData.followUpMessage !== undefined) mapped.follow_up_message = sessionData.followUpMessage;
    if (sessionData.automatedMessagesJson !== undefined) mapped.automated_messages_json = sessionData.automatedMessagesJson;
    if (sessionData.recurringPattern !== undefined) mapped.recurring_pattern = sessionData.recurringPattern;
    if (sessionData.recurringDay !== undefined) mapped.recurring_day = sessionData.recurringDay;
    if (sessionData.numOccurrences !== undefined) mapped.num_occurrences = sessionData.numOccurrences;
    if (sessionData.cancellationTimeframe !== undefined) mapped.cancellation_timeframe = sessionData.cancellationTimeframe;
    if (sessionData.customCancellationHours !== undefined) mapped.custom_cancellation_hours = sessionData.customCancellationHours;
    if (sessionData.cancellationFeePercentage !== undefined) mapped.cancellation_fee_percentage = sessionData.cancellationFeePercentage;
    if (sessionData.noShowFeePercentage !== undefined) mapped.no_show_fee_percentage = sessionData.noShowFeePercentage;
    if (sessionData.legalAgreement !== undefined) mapped.legal_agreement = sessionData.legalAgreement;
    if (sessionData.agreementType !== undefined) mapped.agreement_type = sessionData.agreementType;
    if (sessionData.customAgreement !== undefined) mapped.custom_agreement = sessionData.customAgreement;
    if (sessionData.schedulingMode !== undefined) mapped.scheduling_mode = sessionData.schedulingMode;
    if (sessionData.mediaGallery !== undefined) mapped.media_gallery = sessionData.mediaGallery;
    if (sessionData.createdAt !== undefined) mapped.created_at = sessionData.createdAt;
    if (sessionData.updatedAt !== undefined) mapped.updated_at = sessionData.updatedAt;

    // Remove camelCase fields to avoid conflicts
    delete mapped.teacherId;
    delete mapped.isPublic;
    delete mapped.imageUrl;
    delete mapped.maxParticipants;
    delete mapped.skillLevel;
    delete mapped.learningOutcomes;
    delete mapped.zoomLink;
    delete mapped.reviewCount;
    delete mapped.isFeatured;
    delete mapped.locationType;
    delete mapped.locationDetails;
    delete mapped.onlineHostingService;
    delete mapped.cancellationPolicy;
    delete mapped.welcomeMessage;
    delete mapped.reminderMessage;
    delete mapped.preparationNotes;
    delete mapped.followUpMessage;
    delete mapped.automatedMessagesJson;
    delete mapped.recurringPattern;
    delete mapped.recurringDay;
    delete mapped.numOccurrences;
    delete mapped.cancellationTimeframe;
    delete mapped.customCancellationHours;
    delete mapped.cancellationFeePercentage;
    delete mapped.noShowFeePercentage;
    delete mapped.legalAgreement;
    delete mapped.agreementType;
    delete mapped.customAgreement;
    delete mapped.schedulingMode;
    delete mapped.mediaGallery;
    delete mapped.createdAt;
    delete mapped.updatedAt;

    return mapped;
  }

  /**
   * Create a new session
   * @param sessionData - Session data
   * @returns Created session
   */
  async createSession(sessionData: InsertSession): Promise<Session> {
    try {
      // Convert camelCase interface fields to snake_case database fields
      const dbSessionData = this.mapSessionToDatabase(sessionData as any);

      // Create session in the actual table, not the view
      const rawSessions = await this.executeQuery<any>(
        'Create session',
        () => this.client.from('sessions').insert([dbSessionData]).select('*')
      );
      if (rawSessions.length === 0) {
        throw errorHandler.internal('Failed to create session');
      }

      // Map the response back to interface format
      return this.mapDatabaseToSession(rawSessions[0]);
    } catch (error) {
      console.error('[SessionRepository] Error creating session:', error);
      throw errorHandler.database('Error creating session', error);
    }
  }

  /**
   * Get a session by ID
   * @param sessionId - Session ID
   * @returns Session or null
   */
  async getSession(sessionId: string): Promise<Session | null> {
    try {
      const rawSession = await this.executeSingleQuery<any>(
        `Get session ${sessionId}`,
        () => this.client.from(this.tableName).select('*').eq('id', sessionId),
        300 // Cache for 5 minutes
      );

      if (!rawSession) {
        return null;
      }

      // Map database fields to interface fields
      return this.mapDatabaseToSession(rawSession);
    } catch (error) {
      console.error(`[SessionRepository] Error getting session ${sessionId}:`, error);
      throw errorHandler.database(`Error getting session ${sessionId}`, error);
    }
  }

  /**
   * Get a session with teacher details by ID
   * @param sessionId - Session ID
   * @returns Session with teacher or null
   */
  async getSessionWithTeacher(sessionId: string): Promise<SessionWithTeacher | null> {
    try {
      // First get the session
      const rawSession = await this.executeSingleQuery<any>(
        `Get session ${sessionId}`,
        () => this.client.from(this.tableName).select('*').eq('id', sessionId),
        300 // Cache for 5 minutes
      );

      if (!rawSession) {
        return null;
      }

      // Map database fields to interface fields
      const session = this.mapDatabaseToSession(rawSession);

      // Then get the teacher profile by joining user_profiles via user_id
      // Note: teacher_id is a UUID, need to query user_profiles where user_id matches
      const teacher = await this.executeSingleQuery<any>(
        `Get teacher ${session.teacherId} for session ${sessionId}`,
        () => this.client
          .from('user_profiles')
          .select('id, user_id, name, avatar, bio, created_at, updated_at')
          .eq('user_id', session.teacherId),
        300 // Cache for 5 minutes
      );

      return {
        ...session,
        teacher: teacher || null
      } as SessionWithTeacher;
    } catch (error) {
      console.error(`[SessionRepository] Error getting session ${sessionId} with teacher:`, error);
      throw errorHandler.database(`Error getting session ${sessionId} with teacher`, error);
    }
  }

  /**
   * Get sessions with optional filters
   * @param filters - Optional filters
   * @returns Sessions array
   */
  async getSessions(filters: any = {}): Promise<SessionWithTeacher[]> {
    try {
      // First get sessions
      const rawSessions = await this.executeQuery<any>(
        'Get sessions with filters',
        () => {
          let query = this.client
            .from(this.tableName)
            .select('*');

          // Apply filters (convert camelCase to snake_case for database)
          if (filters.teacherId) {
            query = query.eq('teacher_id', filters.teacherId);
          }
          if (filters.isPublic !== undefined) {
            query = query.eq('is_public', filters.isPublic);
          }
          if (filters.category) {
            query = query.eq('category', filters.category);
          }

          return query.order('created_at', { ascending: false });
        },
        60 // Cache for 1 minute
      );

      // For each session, map fields and get the teacher profile
      const sessionsWithTeacher: SessionWithTeacher[] = [];

      for (const rawSession of rawSessions) {
        const session = this.mapDatabaseToSession(rawSession);
        try {
          const teacher = await this.executeSingleQuery<any>(
            `Get teacher ${session.teacherId} for sessions list`,
            () => this.client
              .from('user_profiles')
              .select('id, user_id, name, avatar, bio, created_at, updated_at')
              .eq('user_id', session.teacherId),
            300 // Cache for 5 minutes
          );

          sessionsWithTeacher.push({
            ...session,
            teacher: teacher || null
          } as unknown as SessionWithTeacher);
        } catch (teacherError) {
          console.warn(`[SessionRepository] Could not fetch teacher ${session.teacherId} for session ${session.id}:`, teacherError);
          // Include session without teacher data rather than failing completely
          sessionsWithTeacher.push({
            ...session,
            teacher: null
          } as unknown as SessionWithTeacher);
        }
      }

      return sessionsWithTeacher;
    } catch (error) {
      console.error('[SessionRepository] Error getting sessions:', error);
      throw errorHandler.database('Error getting sessions', error);
    }
  }

  /**
   * Update a session
   * @param sessionId - Session ID
   * @param updateData - Data to update
   * @returns Updated session
   */
  async updateSession(sessionId: string, updateData: Partial<Session>): Promise<Session> {
    try {
      // Convert camelCase interface fields to snake_case database fields
      const dbUpdateData = this.mapSessionToDatabase(updateData);

      // Update session in the actual table, not the view
      const rawSessions = await this.executeQuery<any>(
        `Update session ${sessionId}`,
        () => this.client.from('sessions').update(dbUpdateData).eq('id', sessionId).select('*')
      );

      if (rawSessions.length === 0) {
        throw errorHandler.notFound(`Session ${sessionId} not found`);
      }

      // Map the response back to interface format
      return this.mapDatabaseToSession(rawSessions[0]);
    } catch (error) {
      console.error(`[SessionRepository] Error updating session ${sessionId}:`, error);
      throw errorHandler.database(`Error updating session ${sessionId}`, error);
    }
  }

  /**
   * Delete a session
   * @param sessionId - Session ID
   * @returns Deleted session
   */
  async deleteSession(sessionId: string): Promise<Session> {
    try {
      // Delete session from the actual table, not the view
      const rawSessions = await this.executeQuery<any>(
        `Delete session ${sessionId}`,
        () => this.client.from('sessions').delete().eq('id', sessionId).select('*')
      );

      if (rawSessions.length === 0) {
        throw errorHandler.notFound(`Session ${sessionId} not found`);
      }

      // Map the response back to interface format
      return this.mapDatabaseToSession(rawSessions[0]);
    } catch (error) {
      console.error(`[SessionRepository] Error deleting session ${sessionId}:`, error);
      throw errorHandler.database(`Error deleting session ${sessionId}`, error);
    }
  }

  /**
   * Check for session scheduling conflicts
   * @param teacherId - Teacher ID
   * @param sessionDate - Session date
   * @param duration - Session duration in minutes
   * @returns Conflict check result
   */
  async checkSessionSchedulingConflict(
    teacherId: string,
    sessionDate: Date,
    duration: number
  ): Promise<{ hasConflict: boolean; conflictingSession?: Session }> {
    try {
      const endTime = new Date(sessionDate.getTime() + duration * 60000);

      const conflictingSessions = await this.executeQuery<Session>(
        `Check scheduling conflict for teacher ${teacherId}`,
        () => this.client
          .from(this.tableName)
          .select('*')
          .eq('teacher_id', teacherId)
          .gte('date', sessionDate.toISOString())
          .lte('date', endTime.toISOString())
      );

      return {
        hasConflict: conflictingSessions.length > 0,
        conflictingSession: conflictingSessions[0] || undefined
      };
    } catch (error) {
      console.error('[SessionRepository] Error checking scheduling conflict:', error);
      throw errorHandler.database('Error checking scheduling conflict', error);
    }
  }

  static mapRawSessionToSession(rawSession: any): Session {
    return {
      id: rawSession.id,
      title: rawSession.title,
      description: rawSession.description,
      teacherId: rawSession.teacher_id || rawSession.teacherId,
      type: rawSession.type,
      price: rawSession.price,
      duration: rawSession.duration,
      date: rawSession.date,
      language: rawSession.language,
      skillLevel: rawSession.skill_level || rawSession.skillLevel,
      format: rawSession.format,
      maxParticipants: rawSession.max_participants || rawSession.maxParticipants,
      zoomLink: rawSession.zoom_link || rawSession.zoomLink,
      learningOutcomes: rawSession.learning_outcomes || rawSession.learningOutcomes,
      requirements: rawSession.requirements,
      rating: rawSession.rating || 0,
      reviewCount: rawSession.review_count || rawSession.reviewCount || 0,
      createdAt: rawSession.created_at || rawSession.createdAt,
      updatedAt: rawSession.updated_at || rawSession.updatedAt,
      imageUrl: rawSession.image_url || rawSession.imageUrl,
      isPublic: rawSession.is_public !== undefined ? rawSession.is_public : rawSession.isPublic,
      // Automated messages
      automatedMessagesJson: rawSession.automated_messages_json || rawSession.automatedMessagesJson,
      // Cancellation policy
      cancellationPolicy: rawSession.cancellation_policy || rawSession.cancellationPolicy,
      cancellationTimeframe: rawSession.cancellation_timeframe || rawSession.cancellationTimeframe,
      customCancellationHours: rawSession.custom_cancellation_hours || rawSession.customCancellationHours,
      cancellationFeePercentage: rawSession.cancellation_fee_percentage || rawSession.cancellationFeePercentage,
      noShowFeePercentage: rawSession.no_show_fee_percentage || rawSession.noShowFeePercentage,
      // Legal agreement
      legalAgreement: rawSession.legal_agreement || rawSession.legalAgreement,
      agreementType: rawSession.agreement_type || rawSession.agreementType,
      customAgreement: rawSession.custom_agreement || rawSession.customAgreement,
      // Scheduling
      schedulingMode: rawSession.scheduling_mode || rawSession.schedulingMode,
      // Automated messages
      welcomeMessage: rawSession.welcome_message || rawSession.welcomeMessage,
      reminderMessage: rawSession.reminder_message || rawSession.reminderMessage,
      preparationNotes: rawSession.preparation_notes || rawSession.preparationNotes,
      followUpMessage: rawSession.follow_up_message || rawSession.followUpMessage,
      // Recurring sessions
      recurringPattern: rawSession.recurring_pattern || rawSession.recurringPattern,
      recurringDay: rawSession.recurring_day || rawSession.recurringDay,
      numOccurrences: rawSession.num_occurrences || rawSession.numOccurrences,
      // Location and hosting
      locationType: rawSession.location_type || rawSession.locationType,
      locationDetails: rawSession.location_details || rawSession.locationDetails,
      onlineHostingService: rawSession.online_hosting_service || rawSession.onlineHostingService,
      isFeatured: rawSession.is_featured || rawSession.isFeatured || false,
      mediaGallery: rawSession.media_gallery || rawSession.mediaGallery || []
    };
  }
}

export default SessionRepository;
