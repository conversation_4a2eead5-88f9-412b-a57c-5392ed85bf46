import { Pool, QueryResult } from 'pg';
import { errorHandler } from '../services';
import { supabaseAdmin } from '../lib/supabase';

/**
 * Base repository class with common database operations and Supabase fallback
 */
export class BaseRepository {
  protected pool: Pool;
  protected schema: string;
  protected tableName: string;
  protected useSupabaseFallback: boolean = false;

  // Cache settings
  protected cacheEnabled: boolean = true;
  protected cacheTtl: number = 300; // 5 minutes
  private queryCache = new Map<string, { data: any[], timestamp: number }>();

  // Performance metrics
  private queryTimings: { query: string, time: number, rows: number }[] = [];

  /**
   * Constructor
   * @param pool - Database connection pool
   * @param schema - Database schema
   * @param tableName - Database table name
   */
  constructor(pool: Pool, schema: string, tableName: string) {
    this.pool = pool;
    this.schema = schema;
    this.tableName = tableName;
  }

  /**
   * Execute a SQL query with optional caching and Supabase fallback
   * @param query - SQL query
   * @param params - Query parameters
   * @param cacheDuration - Cache duration in seconds (0 to disable caching)
   * @returns Query result
   */
  protected async executeQuery<T>(
    query: string,
    params: any[] = [],
    cacheDuration: number = 0
  ): Promise<T[]> {
    // Generate cache key
    const cacheKey = this.generateCacheKey(query, params);

    // Check cache if enabled and duration > 0
    if (this.cacheEnabled && cacheDuration > 0) {
      const cachedResult = this.queryCache.get(cacheKey);
      if (cachedResult && Date.now() - cachedResult.timestamp < cacheDuration * 1000) {
        return cachedResult.data as T[];
      }
    }

    // Measure query execution time
    const startTime = Date.now();

    try {
      // Try PostgreSQL first if not in fallback mode
      if (!this.useSupabaseFallback) {
        // Execute query
        const result: QueryResult = await this.pool.query(query, params);

        // Record query timing
        const endTime = Date.now();
        const executionTime = endTime - startTime;

        this.queryTimings.push({
          query,
          time: executionTime,
          rows: result.rowCount || 0
        });

        // Log slow queries (> 100ms)
        if (executionTime > 100) {
          console.warn(`[SLOW QUERY] ${executionTime}ms: ${query}`);
        }

        // Cache result if enabled and duration > 0
        if (this.cacheEnabled && cacheDuration > 0) {
          this.queryCache.set(cacheKey, {
            data: result.rows,
            timestamp: Date.now()
          });
        }

        return result.rows as T[];
      }
    } catch (error) {
      console.warn('[BaseRepository] PostgreSQL query failed, falling back to Supabase:', error);
      this.useSupabaseFallback = true;
    }

    // Fall back to Supabase for specific queries
    try {
      return await this.executeWithSupabase<T>(query, params);
    } catch (supabaseError) {
      console.error('[BaseRepository] Both PostgreSQL and Supabase queries failed:', supabaseError);
      throw errorHandler.database(`Database error executing query: ${query}`, supabaseError);
    }
  }

  /**
   * Execute query using Supabase client as fallback
   */
  private async executeWithSupabase<T>(query: string, params: any[] = []): Promise<T[]> {
    console.log('[BaseRepository] Executing query via Supabase fallback:', query.slice(0, 100));

    // Simple queries we can handle with Supabase
    if (query.toLowerCase().includes('select now()')) {
      const result = [{ now: new Date() }] as T[];
      return result;
    }

    // For session-related queries, try to convert to Supabase operations
    if (this.tableName === 'sessions' && query.toLowerCase().includes('select')) {
      try {
        // Extract session ID from WHERE clause if present
        const sessionIdMatch = query.match(/WHERE\s+.*?id\s*=\s*\$1/i);
        if (sessionIdMatch && params.length > 0) {
          const sessionId = params[0];
          console.log(`[BaseRepository] Fetching session ${sessionId} via Supabase`);

          const { data, error } = await supabaseAdmin
            .from('sessions')
            .select(`
              *,
              teacher:user_profiles(*)
            `)
            .eq('id', sessionId)
            .single();

          if (error) {
            console.error('[BaseRepository] Supabase session query error:', error);
            throw error;
          }

          if (data) {
            // Map the Supabase response to match expected format
            const mappedData = {
              ...data,
              teacher_name: data.teacher?.name || null,
              teacher_username: data.teacher?.email || null,
              teacher_avatar: data.teacher?.avatar || null,
              teacher_bio: data.teacher?.bio || null,
            };
            return [mappedData] as T[];
          }
        } else {
          // Get all sessions
          console.log('[BaseRepository] Fetching all sessions via Supabase');

          const { data, error } = await supabaseAdmin
            .from('sessions')
            .select(`
              *,
              teacher:user_profiles(*)
            `)
            .eq('is_public', true)
            .order('created_at', { ascending: false });

          if (error) {
            console.error('[BaseRepository] Supabase sessions query error:', error);
            throw error;
          }

          if (data) {
            // Map the Supabase response to match expected format
            const mappedData = data.map(session => ({
              ...session,
              teacher_name: session.teacher?.name || null,
              teacher_username: session.teacher?.email || null,
              teacher_avatar: session.teacher?.avatar || null,
              teacher_bio: session.teacher?.bio || null,
            }));
            return mappedData as T[];
          }
        }
      } catch (supabaseError) {
        console.error('[BaseRepository] Supabase fallback failed:', supabaseError);
        throw supabaseError;
      }
    }

    // For other queries, return empty array or throw error
    console.warn('[BaseRepository] Supabase fallback not implemented for this query type:', query.slice(0, 50));
    return [] as T[];
  }

  /**
   * Generate a cache key for a query
   * @param query - SQL query
   * @param params - Query parameters
   * @returns Cache key
   */
  private generateCacheKey(query: string, params: any[]): string {
    return `${query}:${JSON.stringify(params)}`;
  }

  /**
   * Clear the query cache
   */
  public clearCache(): void {
    this.queryCache.clear();
  }

  /**
   * Clear a specific cache entry
   * @param query - SQL query
   * @param params - Query parameters
   */
  public clearCacheEntry(query: string, params: any[] = []): void {
    const cacheKey = this.generateCacheKey(query, params);
    this.queryCache.delete(cacheKey);
  }

  /**
   * Get query performance metrics
   * @returns Query timings
   */
  public getQueryMetrics(): { query: string, time: number, rows: number }[] {
    return [...this.queryTimings];
  }

  /**
   * Reset query performance metrics
   */
  public resetQueryMetrics(): void {
    this.queryTimings = [];
  }

  /**
   * Begin a transaction
   */
  protected async beginTransaction(): Promise<void> {
    if (this.useSupabaseFallback) {
      console.warn('[BaseRepository] Transactions not supported in Supabase fallback mode');
      return;
    }
    await this.executeQuery('BEGIN');
  }

  /**
   * Commit a transaction
   */
  protected async commitTransaction(): Promise<void> {
    if (this.useSupabaseFallback) {
      console.warn('[BaseRepository] Transactions not supported in Supabase fallback mode');
      return;
    }
    await this.executeQuery('COMMIT');
  }

  /**
   * Rollback a transaction
   */
  protected async rollbackTransaction(): Promise<void> {
    if (this.useSupabaseFallback) {
      console.warn('[BaseRepository] Transactions not supported in Supabase fallback mode');
      return;
    }
    await this.executeQuery('ROLLBACK');
  }

  /**
   * Execute a function within a transaction
   * @param fn - Function to execute
   * @returns Result of the function
   */
  protected async withTransaction<T>(fn: () => Promise<T>): Promise<T> {
    if (this.useSupabaseFallback) {
      console.warn('[BaseRepository] Transactions not supported in Supabase fallback mode, executing without transaction');
      return await fn();
    }

    try {
      await this.beginTransaction();
      const result = await fn();
      await this.commitTransaction();
      return result;
    } catch (error) {
      await this.rollbackTransaction();
      throw error;
    }
  }

  /**
   * Enable Supabase fallback mode
   */
  public enableSupabaseFallback(): void {
    this.useSupabaseFallback = true;
    console.log('[BaseRepository] Supabase fallback mode enabled');
  }

  /**
   * Check if using Supabase fallback
   */
  public isUsingSupabaseFallback(): boolean {
    return this.useSupabaseFallback;
  }
}

export default BaseRepository;
