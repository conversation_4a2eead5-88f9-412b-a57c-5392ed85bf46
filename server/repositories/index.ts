import { Pool } from 'pg';
import { BaseRepository } from './BaseRepository';
import { UserRepository } from './UserRepository';
import { SessionRepository } from './SessionRepository';
import { BookingRepository } from './BookingRepository';
import { ReviewRepository } from './ReviewRepository';
import { ConversationRepository } from './ConversationRepository';
import { MessageRepository } from './MessageRepository';
import { NotificationRepository } from './NotificationRepository';
import { AuthRepository } from './AuthRepository';

/**
 * Repository factory for creating repositories
 */
export class RepositoryFactory {
  private pool: Pool;
  private repositories: Map<string, any>;

  /**
   * Constructor
   * @param pool - Database connection pool
   */
  constructor(pool: Pool) {
    this.pool = pool;
    this.repositories = new Map();
  }

  /**
   * Get a user repository
   * @returns User repository
   */
  getUserRepository(): UserRepository {
    if (!this.repositories.has('user')) {
      this.repositories.set('user', new UserRepository());
    }
    return this.repositories.get('user') as UserRepository;
  }

  /**
   * Get a session repository
   * @returns Session repository
   */
  getSessionRepository(): SessionRepository {
    if (!this.repositories.has('session')) {
      this.repositories.set('session', new SessionRepository());
    }
    return this.repositories.get('session') as SessionRepository;
  }

  /**
   * Get a booking repository
   * @returns Booking repository
   */
  getBookingRepository(): BookingRepository {
    if (!this.repositories.has('booking')) {
      this.repositories.set('booking', new BookingRepository());
    }
    return this.repositories.get('booking') as BookingRepository;
  }

  /**
   * Get a review repository
   * @returns Review repository
   */
  getReviewRepository(): ReviewRepository {
    if (!this.repositories.has('review')) {
      this.repositories.set('review', new ReviewRepository());
    }
    return this.repositories.get('review') as ReviewRepository;
  }

  /**
   * Get a conversation repository
   * @returns Conversation repository
   */
  getConversationRepository(): ConversationRepository {
    if (!this.repositories.has('conversation')) {
      this.repositories.set('conversation', new ConversationRepository());
    }
    return this.repositories.get('conversation') as ConversationRepository;
  }

  /**
   * Get a message repository
   * @returns Message repository
   */
  getMessageRepository(): MessageRepository {
    if (!this.repositories.has('message')) {
      this.repositories.set('message', new MessageRepository());
    }
    return this.repositories.get('message') as MessageRepository;
  }

  /**
   * Get a notification repository
   * @returns Notification repository
   */
  getNotificationRepository(): NotificationRepository {
    if (!this.repositories.has('notification')) {
      this.repositories.set('notification', new NotificationRepository());
    }
    return this.repositories.get('notification') as NotificationRepository;
  }

  /**
   * Get an auth repository
   * @returns Auth repository
   */
  getAuthRepository(): AuthRepository {
    if (!this.repositories.has('auth')) {
      this.repositories.set('auth', new AuthRepository());
    }
    return this.repositories.get('auth') as AuthRepository;
  }

  /**
   * Clear all repository caches
   */
  clearCaches(): void {
    this.repositories.forEach(repository => repository.clearCache());
  }
}

export {
  BaseRepository,
  UserRepository,
  SessionRepository,
  BookingRepository,
  ReviewRepository,
  ConversationRepository,
  MessageRepository,
  NotificationRepository,
  AuthRepository
};
