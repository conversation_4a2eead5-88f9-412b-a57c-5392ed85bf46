import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { Booking, InsertBooking, BookingWithSession } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * Booking repository for booking-related database operations using Supabase client
 */
export class BookingRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('bookings');
  }

  /**
   * Get a booking by ID
   * @param id - Booking ID
   * @returns Booking or null
   */
  async getBooking(id: string): Promise<Booking | null> {
    try {
      return await this.executeSingleQuery<Booking>(
        `Get booking ${id}`,
        () => this.client.from(this.tableName).select('*').eq('id', id),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[BookingRepository] Error getting booking ${id}:`, error);
      throw errorHandler.database(`Error getting booking ${id}`, error);
    }
  }

  /**
   * Create a new booking
   * @param bookingData - Booking data
   * @returns Created booking
   */
  async createBooking(bookingData: InsertBooking): Promise<Booking> {
    try {
      const bookings = await this.executeInsert<Booking>('Create booking', bookingData);
      if (bookings.length === 0) {
        throw errorHandler.internal('Failed to create booking');
      }
      return bookings[0];
    } catch (error) {
      console.error('[BookingRepository] Error creating booking:', error);
      throw errorHandler.database('Error creating booking', error);
    }
  }

  /**
   * Update a booking
   * @param id - Booking ID
   * @param updateData - Data to update
   * @returns Updated booking
   */
  async updateBooking(id: string, updateData: Partial<Booking>): Promise<Booking> {
    try {
      const bookings = await this.executeUpdate<Booking>(
        `Update booking ${id}`,
        updateData,
        (query) => query.eq('id', id)
      );

      if (bookings.length === 0) {
        throw errorHandler.notFound(`Booking ${id} not found`);
      }

      return bookings[0];
    } catch (error) {
      console.error(`[BookingRepository] Error updating booking ${id}:`, error);
      throw errorHandler.database(`Error updating booking ${id}`, error);
    }
  }

  /**
   * Get bookings for a user with session details
   * @param userId - User ID
   * @returns Array of bookings with session details
   */
  async getUserBookings(userId: string): Promise<BookingWithSession[]> {
    try {
      // First try with join to get session details
      try {
        return await this.executeQuery<BookingWithSession>(
          `Get user ${userId} bookings with join`,
          () => this.client
            .from(this.tableName)
            .select(`
              *,
              sessions!session_id(
                *,
                user_profiles!teacher_id(
                  id,
                  name,
                  email,
                  username,
                  avatar,
                  bio,
                  created_at,
                  updated_at
                )
              )
            `)
            .eq('user_id', userId)
            .order('created_at', { ascending: false }),
          60 // Cache for 1 minute
        );
      } catch (joinError) {
        console.error(`[BookingRepository] Error getting user bookings with join:`, joinError);

        // Fallback: Get bookings without join and manually fetch session details
        const bookings = await this.executeQuery<Booking>(
          `Get user ${userId} bookings without join`,
          () => this.client
            .from(this.tableName)
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false }),
          60 // Cache for 1 minute
        );

        // For now, return bookings without session details
        // TODO: Implement manual session fetching if needed
        return bookings.map(booking => ({
          ...booking,
          session: null
        })) as unknown as BookingWithSession[];
      }
    } catch (error) {
      console.error(`[BookingRepository] Error getting bookings for user ${userId}:`, error);
      throw errorHandler.database(`Error getting bookings for user ${userId}`, error);
    }
  }

  /**
   * Get bookings for a session with user details (enhanced version)
   * @param sessionId - Session ID
   * @returns Array of bookings with user details
   */
  async getBookingsBySessionId(sessionId: string): Promise<any[]> {
    try {
      // First try with user join
      try {
        return await this.executeQuery<any>(
          `Get bookings for session ${sessionId} with users`,
          () => this.client
            .from(this.tableName)
            .select(`
              *,
              user_profiles!user_id(
                id,
                name,
                email,
                username,
                avatar,
                bio,
                created_at,
                updated_at
              )
            `)
            .eq('session_id', sessionId)
            .order('created_at', { ascending: false }),
          60 // Cache for 1 minute
        );
      } catch (joinError) {
        console.error(`[BookingRepository] Error getting session bookings with join:`, joinError);

        // Fallback: Get bookings without join
        return await this.executeQuery<Booking>(
          `Get bookings for session ${sessionId} without join`,
          () => this.client
            .from(this.tableName)
            .select('*')
            .eq('session_id', sessionId)
            .order('created_at', { ascending: false }),
          60 // Cache for 1 minute
        );
      }
    } catch (error) {
      console.error(`[BookingRepository] Error getting bookings for session ${sessionId}:`, error);
      throw errorHandler.database(`Error getting bookings for session ${sessionId}`, error);
    }
  }

  /**
   * Get bookings for a session
   * @param sessionId - Session ID
   * @returns Array of bookings
   */
  async getSessionBookings(sessionId: string): Promise<Booking[]> {
    try {
      return await this.executeQuery<Booking>(
        `Get session ${sessionId} bookings`,
        () => this.client
          .from(this.tableName)
          .select('*')
          .eq('session_id', sessionId)
          .order('created_at', { ascending: false }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[BookingRepository] Error getting bookings for session ${sessionId}:`, error);
      throw errorHandler.database(`Error getting bookings for session ${sessionId}`, error);
    }
  }

  /**
   * Check if user has booked a session
   * @param userId - User ID
   * @param sessionId - Session ID
   * @returns True if user has booked the session
   */
  async hasUserBookedSession(userId: string, sessionId: string): Promise<boolean> {
    try {
      const bookings = await this.executeQuery<Booking>(
        `Check if user ${userId} booked session ${sessionId}`,
        () => this.client
          .from(this.tableName)
          .select('id')
          .eq('user_id', userId)
          .eq('session_id', sessionId)
          .neq('status', 'cancelled'),
        60 // Cache for 1 minute
      );

      return bookings.length > 0;
    } catch (error) {
      console.error(`[BookingRepository] Error checking if user ${userId} booked session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Update booking status
   * @param id - Booking ID
   * @param status - New status
   * @returns Updated booking
   */
  async updateBookingStatus(id: string, status: string): Promise<Booking> {
    try {
      return await this.updateBooking(id, { status });
    } catch (error) {
      console.error(`[BookingRepository] Error updating booking ${id} status:`, error);
      throw errorHandler.database(`Error updating booking ${id} status`, error);
    }
  }

  /**
   * Delete a booking
   * @param id - Booking ID
   * @returns Deleted booking
   */
  async deleteBooking(id: string): Promise<Booking> {
    try {
      const bookings = await this.executeDelete<Booking>(
        `Delete booking ${id}`,
        (query) => query.eq('id', id)
      );

      if (bookings.length === 0) {
        throw errorHandler.notFound(`Booking ${id} not found`);
      }

      return bookings[0];
    } catch (error) {
      console.error(`[BookingRepository] Error deleting booking ${id}:`, error);
      throw errorHandler.database(`Error deleting booking ${id}`, error);
    }
  }
}

export default BookingRepository;
