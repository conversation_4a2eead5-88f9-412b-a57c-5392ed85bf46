import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { User, InsertUser, UserProfile } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * User repository for user-related database operations using Supabase client
 */
export class UserRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('user_profiles');
  }

  /**
   * Create a new user (basic user creation - auth handled by Supabase Auth)
   * @param userData - User data
   * @returns Created user profile
   */
  async createUserProfile(userData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const profiles = await this.executeInsert<UserProfile>('Create user profile', userData);
      if (profiles.length === 0) {
        throw errorHandler.internal('Failed to create user profile');
      }
      return profiles[0];
    } catch (error) {
      console.error('[UserRepository] Error creating user profile:', error);
      throw errorHandler.database('Error creating user profile', error);
    }
  }

  /**
   * Get a user profile by ID
   * @param userId - User ID
   * @returns User profile or null
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      return await this.executeSingleQuery<UserProfile>(
        `Get user profile ${userId}`,
        () => this.client.from(this.tableName).select('*').eq('user_id', userId),
        300 // Cache for 5 minutes
      );
    } catch (error) {
      console.error(`[UserRepository] Error getting user profile ${userId}:`, error);
      throw errorHandler.database(`Error getting user profile ${userId}`, error);
    }
  }

  /**
   * Update a user profile
   * @param userId - User ID
   * @param updateData - Data to update
   * @returns Updated user profile
   */
  async updateUserProfile(userId: string, updateData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const profiles = await this.executeUpdate<UserProfile>(
        `Update user profile ${userId}`,
        updateData,
        (query) => query.eq('user_id', userId)
      );

      if (profiles.length === 0) {
        throw errorHandler.notFound(`User profile ${userId} not found`);
      }

      return profiles[0];
    } catch (error) {
      console.error(`[UserRepository] Error updating user profile ${userId}:`, error);
      throw errorHandler.database(`Error updating user profile ${userId}`, error);
    }
  }

  /**
   * Get teacher profiles
   * @param limit - Maximum number of profiles to return
   * @param offset - Number of profiles to skip
   * @returns Array of teacher profiles
   */
  async getTeacherProfiles(limit: number = 50, offset: number = 0): Promise<UserProfile[]> {
    try {
      return await this.executeQuery<UserProfile>(
        'Get teacher profiles',
        () => this.client
          .from(this.tableName)
          .select('*')
          .eq('is_teacher', true)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error('[UserRepository] Error getting teacher profiles:', error);
      throw errorHandler.database('Error getting teacher profiles', error);
    }
  }

  /**
   * Search users by name or email
   * @param searchTerm - Search term
   * @param limit - Maximum number of results
   * @returns Array of user profiles
   */
  async searchUsers(searchTerm: string, limit: number = 10): Promise<UserProfile[]> {
    try {
      return await this.executeQuery<UserProfile>(
        `Search users: ${searchTerm}`,
        () => this.client
          .from(this.tableName)
          .select('*')
          .or(`name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
          .limit(limit),
        30 // Cache for 30 seconds
      );
    } catch (error) {
      console.error(`[UserRepository] Error searching users with term "${searchTerm}":`, error);
      throw errorHandler.database(`Error searching users with term "${searchTerm}"`, error);
    }
  }

  /**
   * Get user by email (for auth purposes)
   * @param email - User email
   * @returns User profile or null
   */
  async getUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      return await this.executeSingleQuery<UserProfile>(
        `Get user by email`,
        () => this.client.from(this.tableName).select('*').eq('email', email),
        300 // Cache for 5 minutes
      );
    } catch (error) {
      console.error(`[UserRepository] Error getting user by email:`, error);
      throw errorHandler.database('Error getting user by email', error);
    }
  }

  /**
   * Check if user exists by ID
   * @param userId - User ID
   * @returns True if user exists
   */
  async userExists(userId: string): Promise<boolean> {
    try {
      const profile = await this.getUserProfile(userId);
      return profile !== null;
    } catch (error) {
      console.error(`[UserRepository] Error checking if user ${userId} exists:`, error);
      return false;
    }
  }

  /**
   * Delete user profile
   * @param userId - User ID
   * @returns Deleted user profile
   */
  async deleteUserProfile(userId: string): Promise<UserProfile> {
    try {
      const profiles = await this.executeDelete<UserProfile>(
        `Delete user profile ${userId}`,
        (query) => query.eq('user_id', userId)
      );

      if (profiles.length === 0) {
        throw errorHandler.notFound(`User profile ${userId} not found`);
      }

      return profiles[0];
    } catch (error) {
      console.error(`[UserRepository] Error deleting user profile ${userId}:`, error);
      throw errorHandler.database(`Error deleting user profile ${userId}`, error);
    }
  }
}

export default UserRepository;
