import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { Conversation, InsertConversation } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * Conversation repository for conversation-related database operations using Supabase client
 */
export class ConversationRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('conversations');
  }

  /**
   * Get a conversation by ID
   * @param id - Conversation ID
   * @returns Conversation or null
   */
  async getConversation(id: number): Promise<Conversation | null> {
    try {
      return await this.executeSingleQuery<Conversation>(
        `Get conversation ${id}`,
        () => this.client.from(this.tableName).select('*').eq('id', id),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[ConversationRepository] Error getting conversation ${id}:`, error);
      throw errorHandler.database(`Error getting conversation ${id}`, error);
    }
  }

  /**
   * Create a new conversation
   * @param conversationData - Conversation data
   * @returns Created conversation
   */
  async createConversation(conversationData: InsertConversation): Promise<Conversation> {
    try {
      const conversations = await this.executeInsert<Conversation>('Create conversation', conversationData);
      if (conversations.length === 0) {
        throw errorHandler.internal('Failed to create conversation');
      }
      return conversations[0];
    } catch (error) {
      console.error('[ConversationRepository] Error creating conversation:', error);
      throw errorHandler.database('Error creating conversation', error);
    }
  }

  /**
   * Get conversations for a user
   * @param userId - User ID
   * @returns Array of conversations
   */
  async getUserConversations(userId: string): Promise<Conversation[]> {
    try {
      return await this.executeQuery<Conversation>(
        `Get user ${userId} conversations`,
        () => this.client
          .from(this.tableName)
          .select('*')
          .contains('participant_ids', [userId])
          .order('updated_at', { ascending: false }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[ConversationRepository] Error getting conversations for user ${userId}:`, error);
      throw errorHandler.database(`Error getting conversations for user ${userId}`, error);
    }
  }

  /**
   * Update a conversation
   * @param id - Conversation ID
   * @param updateData - Data to update
   * @returns Updated conversation
   */
  async updateConversation(id: number, updateData: Partial<Conversation>): Promise<Conversation> {
    try {
      const conversations = await this.executeUpdate<Conversation>(
        `Update conversation ${id}`,
        updateData,
        (query) => query.eq('id', id)
      );

      if (conversations.length === 0) {
        throw errorHandler.notFound(`Conversation ${id} not found`);
      }

      return conversations[0];
    } catch (error) {
      console.error(`[ConversationRepository] Error updating conversation ${id}:`, error);
      throw errorHandler.database(`Error updating conversation ${id}`, error);
    }
  }

  /**
   * Delete a conversation
   * @param id - Conversation ID
   * @returns Deleted conversation
   */
  async deleteConversation(id: number): Promise<Conversation> {
    try {
      const conversations = await this.executeDelete<Conversation>(
        `Delete conversation ${id}`,
        (query) => query.eq('id', id)
      );

      if (conversations.length === 0) {
        throw errorHandler.notFound(`Conversation ${id} not found`);
      }

      return conversations[0];
    } catch (error) {
      console.error(`[ConversationRepository] Error deleting conversation ${id}:`, error);
      throw errorHandler.database(`Error deleting conversation ${id}`, error);
    }
  }
}

export default ConversationRepository;
