import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { Message, InsertMessage, MessageWithSender, ScheduledMessage, InsertScheduledMessage } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * Message repository for message-related database operations using Supabase client
 */
export class MessageRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('messages');
  }

  /**
   * Get a message by ID
   * @param messageId - Message ID
   * @returns Message or null
   */
  async getMessage(messageId: string): Promise<Message | null> {
    try {
      return await this.executeSingleQuery<Message>(
        `Get message ${messageId}`,
        () => this.client.from(this.tableName).select('*').eq('id', messageId),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting message ${messageId}:`, error);
      throw errorHandler.database(`<PERSON>rror getting message ${messageId}`, error);
    }
  }

  /**
   * Get a message with sender details
   * @param messageId - Message ID
   * @returns Message with sender or null
   */
  async getMessageWithSender(messageId: string): Promise<MessageWithSender | null> {
    try {
      return await this.executeSingleQuery<MessageWithSender>(
        `Get message ${messageId} with sender`,
        () => this.client
          .from(this.tableName)
          .select(`
            *,
            sender:user_profiles!sender_id(
              id,
              name,
              email,
              avatar,
              created_at,
              updated_at
            )
          `)
          .eq('id', messageId),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting message ${messageId} with sender:`, error);
      throw errorHandler.database(`Error getting message ${messageId} with sender`, error);
    }
  }

  /**
   * Create a new message
   * @param messageData - Message data
   * @returns Created message
   */
  async createMessage(messageData: InsertMessage): Promise<Message> {
    try {
      const messages = await this.executeInsert<Message>('Create message', messageData);
      if (messages.length === 0) {
        throw errorHandler.internal('Failed to create message');
      }
      return messages[0];
    } catch (error) {
      console.error('[MessageRepository] Error creating message:', error);
      throw errorHandler.database('Error creating message', error);
    }
  }

  /**
   * Get conversation messages
   * @param conversationId - Conversation ID
   * @param limit - Maximum number of messages to return
   * @returns Array of messages with sender details
   */
  async getConversationMessages(conversationId: string, limit: number = 50): Promise<MessageWithSender[]> {
    try {
      return await this.executeQuery<MessageWithSender>(
        `Get conversation ${conversationId} messages`,
        () => this.client
          .from(this.tableName)
          .select(`
            *,
            sender:user_profiles!sender_id(
              id,
              name,
              email,
              avatar,
              created_at,
              updated_at
            )
          `)
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: false })
          .limit(limit),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting conversation ${conversationId} messages:`, error);
      throw errorHandler.database(`Error getting conversation ${conversationId} messages`, error);
    }
  }

  /**
   * Mark messages as read
   * @param conversationId - Conversation ID
   * @param userId - User ID
   */
  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      await this.executeUpdate(
        `Mark messages as read for conversation ${conversationId}`,
        { is_read: true },
        (query) => query
          .eq('conversation_id', conversationId)
          .neq('sender_id', userId)
          .eq('is_read', false)
      );
    } catch (error) {
      console.error(`[MessageRepository] Error marking messages as read for conversation ${conversationId}:`, error);
      throw errorHandler.database(`Error marking messages as read for conversation ${conversationId}`, error);
    }
  }

  /**
   * Get unread message count for a user
   * @param userId - User ID
   * @returns Number of unread messages
   */
  async getUnreadMessageCount(userId: string): Promise<number> {
    try {
      const messages = await this.executeQuery<Message>(
        `Get unread message count for user ${userId}`,
        () => this.client
          .from(this.tableName)
          .select('id')
          .neq('sender_id', userId)
          .eq('is_read', false),
        30 // Cache for 30 seconds
      );

      return messages.length;
    } catch (error) {
      console.error(`[MessageRepository] Error getting unread message count for user ${userId}:`, error);
      throw errorHandler.database(`Error getting unread message count for user ${userId}`, error);
    }
  }

  /**
   * Create a scheduled message
   * @param messageData - Scheduled message data
   * @returns Created scheduled message
   */
  async createScheduledMessage(messageData: InsertScheduledMessage): Promise<ScheduledMessage> {
    try {
      const startTime = Date.now();
      const { data, error } = await this.client
        .from('scheduled_messages')
        .insert(messageData)
        .select();

      if (error) {
        throw error;
      }

      const endTime = Date.now();
      console.log(`[Insert] ${endTime - startTime}ms: Create scheduled message (${data?.length || 0} rows)`);

      if (!data || data.length === 0) {
        throw errorHandler.internal('Failed to create scheduled message');
      }
      return data[0] as ScheduledMessage;
    } catch (error) {
      console.error('[MessageRepository] Error creating scheduled message:', error);
      throw errorHandler.database('Error creating scheduled message', error);
    }
  }

  /**
   * Get a scheduled message by ID
   * @param messageId - Scheduled message ID
   * @returns Scheduled message or null
   */
  async getScheduledMessage(messageId: string): Promise<ScheduledMessage | null> {
    try {
      return await this.executeSingleQuery<ScheduledMessage>(
        `Get scheduled message ${messageId}`,
        () => this.client.from('scheduled_messages').select('*').eq('id', messageId),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting scheduled message ${messageId}:`, error);
      throw errorHandler.database(`Error getting scheduled message ${messageId}`, error);
    }
  }

  /**
   * Get scheduled messages by conversation
   * @param conversationId - Conversation ID
   * @returns Array of scheduled messages
   */
  async getScheduledMessagesByConversation(conversationId: string): Promise<ScheduledMessage[]> {
    try {
      return await this.executeQuery<ScheduledMessage>(
        `Get scheduled messages for conversation ${conversationId}`,
        () => this.client
          .from('scheduled_messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('scheduled_time', { ascending: true }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting scheduled messages for conversation ${conversationId}:`, error);
      throw errorHandler.database(`Error getting scheduled messages for conversation ${conversationId}`, error);
    }
  }

  /**
   * Get scheduled messages by booking
   * @param bookingId - Booking ID
   * @returns Array of scheduled messages
   */
  async getScheduledMessagesByBooking(bookingId: string): Promise<ScheduledMessage[]> {
    try {
      return await this.executeQuery<ScheduledMessage>(
        `Get scheduled messages for booking ${bookingId}`,
        () => this.client
          .from('scheduled_messages')
          .select('*')
          .eq('booking_id', bookingId)
          .order('scheduled_time', { ascending: true }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[MessageRepository] Error getting scheduled messages for booking ${bookingId}:`, error);
      throw errorHandler.database(`Error getting scheduled messages for booking ${bookingId}`, error);
    }
  }

  /**
   * Get pending scheduled messages
   * @param limit - Maximum number of messages to return
   * @returns Array of pending scheduled messages
   */
  async getPendingScheduledMessages(limit: number = 50): Promise<ScheduledMessage[]> {
    try {
      return await this.executeQuery<ScheduledMessage>(
        'Get pending scheduled messages',
        () => this.client
          .from('scheduled_messages')
          .select('*')
          .eq('status', 'pending')
          .lte('scheduled_time', new Date().toISOString())
          .order('scheduled_time', { ascending: true })
          .limit(limit)
      );
    } catch (error) {
      console.error('[MessageRepository] Error getting pending scheduled messages:', error);
      throw errorHandler.database('Error getting pending scheduled messages', error);
    }
  }

  /**
 * Update scheduled message status
 * @param messageId - Message ID
 * @param status - New status
 * @returns Updated scheduled message
 */
  async updateScheduledMessageStatus(messageId: string, status: string): Promise<ScheduledMessage> {
    try {
      const startTime = Date.now();
      const updateData = {
        status,
        delivered_at: status === 'delivered' ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await this.client
        .from('scheduled_messages')
        .update(updateData)
        .eq('id', messageId)
        .select();

      if (error) {
        throw error;
      }

      const endTime = Date.now();
      console.log(`[Update] ${endTime - startTime}ms: Update scheduled message ${messageId} status (${data?.length || 0} rows)`);

      if (!data || data.length === 0) {
        throw errorHandler.notFound(`Scheduled message ${messageId} not found`);
      }

      return data[0] as ScheduledMessage;
    } catch (error) {
      console.error(`[MessageRepository] Error updating scheduled message ${messageId} status:`, error);
      throw errorHandler.database(`Error updating scheduled message ${messageId} status`, error);
    }
  }

  /**
   * Delete a scheduled message
   * @param messageId - Scheduled message ID
   * @returns Deleted scheduled message
   */
  async deleteScheduledMessage(messageId: string): Promise<ScheduledMessage> {
    try {
      const startTime = Date.now();
      const { data, error } = await this.client
        .from('scheduled_messages')
        .delete()
        .eq('id', messageId)
        .select();

      if (error) {
        throw error;
      }

      const endTime = Date.now();
      console.log(`[Delete] ${endTime - startTime}ms: Delete scheduled message ${messageId} (${data?.length || 0} rows)`);

      if (!data || data.length === 0) {
        throw errorHandler.notFound(`Scheduled message ${messageId} not found`);
      }

      return data[0] as ScheduledMessage;
    } catch (error) {
      console.error(`[MessageRepository] Error deleting scheduled message ${messageId}:`, error);
      throw errorHandler.database(`Error deleting scheduled message ${messageId}`, error);
    }
  }
}

export default MessageRepository;
