import { SessionRepository } from '../interfaces/SessionRepository';
import { Session, InsertSession, SessionWithTeacher } from '@shared/schema';
import { DatabaseUtils } from '../../db/utils/DatabaseUtils';
import { logger } from '../../services/logger';

/**
 * PostgreSQL implementation of the SessionRepository interface
 */
export class PostgresSessionRepository implements SessionRepository {
  private dbUtils: DatabaseUtils;
  private sessionCache: Map<number, { session: SessionWithTeacher, timestamp: number }> = new Map();
  private CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Create a new PostgresSessionRepository instance
   * @param dbUtils The database utilities
   */
  constructor(dbUtils: DatabaseUtils) {
    this.dbUtils = dbUtils;
  }

  /**
   * Find a session by ID
   * @param id The session ID
   * @returns The session or undefined if not found
   */
  async findById(id: number): Promise<Session | undefined> {
    try {
      // Check if session is in cache and not expired
      const cachedData = this.sessionCache.get(id);
      const now = Date.now();

      if (cachedData && (now - cachedData.timestamp < this.CACHE_TTL)) {
        // Use cached session data if available and fresh
        return cachedData.session;
      }

      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.id = $1
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [id]);

      if (rows.length === 0) {
        return undefined;
      }

      // Transform to Session object
      const session = this.transformSessionRow(rows[0]);

      // Cache the session for future requests
      this.sessionCache.set(id, { session: session as SessionWithTeacher, timestamp: now });

      return session;
    } catch (error) {
      logger.error(`Error finding session by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all sessions
   * @returns Array of sessions
   */
  async findAll(): Promise<Session[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query);
      return rows.map(row => this.transformSessionRow(row));
    } catch (error) {
      logger.error('Error finding all sessions:', error);
      throw error;
    }
  }

  /**
   * Create a new session
   * @param sessionData The session data to create
   * @returns The created session
   */
  async create(sessionData: InsertSession): Promise<Session> {
    try {
      const query = `
        INSERT INTO content.sessions (
          title, teacher_id, type, description, price, duration, date, language, skill_level, format,
          max_participants, zoom_link, learning_outcomes, requirements, image_url, is_public
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
        ) RETURNING *
      `;

      const values = [
        sessionData.title,
        sessionData.teacherId,
        sessionData.type,
        sessionData.description,
        sessionData.price,
        sessionData.duration,
        sessionData.date,
        sessionData.language,
        sessionData.skillLevel,
        sessionData.format,
        sessionData.maxParticipants,
        sessionData.zoomLink,
        sessionData.learningOutcomes,
        sessionData.requirements,
        sessionData.imageUrl,
        sessionData.isPublic
      ];

      const rows = await this.dbUtils.executeQuery<any>(query, values);
      return this.transformSessionRow(rows[0]) as Session;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw error;
    }
  }

  /**
   * Update an existing session
   * @param id The session ID
   * @param sessionData The session data to update
   * @returns The updated session or undefined if not found
   */
  async update(id: number, sessionData: Partial<Session>): Promise<Session | undefined> {
    try {
      // Get current session data to preserve fields that aren't being updated
      const currentSession = await this.findById(id);
      if (!currentSession) {
        return undefined;
      }

      // Build the update query dynamically based on provided fields
      let updateQuery = 'UPDATE content.sessions SET ';
      const updateValues: any[] = [];
      const updateFields: string[] = [];
      let paramIdx = 1;

      // Add each field to the update query
      for (const [key, value] of Object.entries(sessionData)) {
        if (value !== undefined) {
          // Convert camelCase to snake_case for database fields
          const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
          updateFields.push(`${dbField} = $${paramIdx}`);
          updateValues.push(value);
          paramIdx++;
        }
      }

      // Add updated_at timestamp
      updateFields.push(`updated_at = NOW()`);

      // Complete the query
      updateQuery += updateFields.join(', ');
      updateQuery += ` WHERE id = $${paramIdx} RETURNING *`;
      updateValues.push(id);

      // Execute the query
      const rows = await this.dbUtils.executeQuery<any>(updateQuery, updateValues);

      if (rows.length === 0) {
        return undefined;
      }

      // Get the teacher details
      const teacherQuery = `
        SELECT u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE u.id = $1
      `;

      const teacherRows = await this.dbUtils.executeQuery<any>(teacherQuery, [rows[0].teacher_id]);

      // Combine session and teacher data
      const sessionData = {
        ...rows[0],
        ...(teacherRows.length > 0 ? teacherRows[0] : {})
      };

      // Invalidate cache
      this.sessionCache.delete(id);

      // Transform to Session object
      return this.transformSessionRow(sessionData);
    } catch (error) {
      logger.error(`Error updating session ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a session by ID
   * @param id The session ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    try {
      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Delete bookings for this session first (foreign key constraint)
        await client.query('DELETE FROM bookings.bookings WHERE session_id = $1', [id]);

        // Delete reviews for this session
        await client.query('DELETE FROM content.reviews WHERE session_id = $1', [id]);

        // Delete session
        const result = await client.query('DELETE FROM content.sessions WHERE id = $1', [id]);

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Invalidate cache
        this.sessionCache.delete(id);

        return result.rowCount > 0;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error(`Error deleting session ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by teacher/teacher ID
   * @param teacherId The teacher/teacher ID
   * @returns Array of sessions
   */
  async findByTeacherId(teacherId: number): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.teacher_id = $1
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [teacherId]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by teacher ID ${teacherId}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by type
   * @param type The session type
   * @returns Array of sessions
   */
  async findByType(type: string): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.type = $1 AND s.is_public = true
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [type]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by type ${type}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by skill level
   * @param skillLevel The skill level
   * @returns Array of sessions
   */
  async findBySkillLevel(skillLevel: string): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.skill_level = $1 AND s.is_public = true
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [skillLevel]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by skill level ${skillLevel}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by format
   * @param format The session format
   * @returns Array of sessions
   */
  async findByFormat(format: string): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.format = $1 AND s.is_public = true
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [format]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by format ${format}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by price range
   * @param minPrice The minimum price
   * @param maxPrice The maximum price
   * @returns Array of sessions
   */
  async findByPriceRange(minPrice: number, maxPrice: number): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.price >= $1 AND s.price <= $2 AND s.is_public = true
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [minPrice, maxPrice]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by price range ${minPrice}-${maxPrice}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by date range
   * @param startDate The start date
   * @param endDate The end date
   * @returns Array of sessions
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.date >= $1 AND s.date <= $2 AND s.is_public = true
        ORDER BY s.date ASC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [startDate, endDate]);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error finding sessions by date range ${startDate}-${endDate}:`, error);
      throw error;
    }
  }

  /**
   * Find sessions by multiple criteria
   * @param criteria The search criteria
   * @returns Array of sessions
   */
  async findByCriteria(criteria: {
    type?: string;
    skillLevel?: string;
    format?: string;
    minPrice?: number;
    maxPrice?: number;
    startDate?: Date;
    endDate?: Date;
    teacherId?: number;
    language?: string;
  }): Promise<SessionWithTeacher[]> {
    try {
      let query = `
        SELECT 
          s.id, s.title, s.description, s.type, s.skill_level, s.format, s.price, s.language,
          s.duration, s.date, s.learning_outcomes, s.requirements, s.max_participants,
          s.teacher_id, s.zoom_link, s.image_url, s.is_public, s.rating, s.review_count,
          s.created_at, s.updated_at,
          u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE 1=1
      `;

      const queryParams: any[] = [];
      let paramIdx = 1;

      if (criteria.type) {
        query += ` AND s.type = $${paramIdx}`;
        queryParams.push(criteria.type);
        paramIdx++;
      }

      if (criteria.skillLevel) {
        query += ` AND s.skill_level = $${paramIdx}`;
        queryParams.push(criteria.skillLevel);
        paramIdx++;
      }

      if (criteria.format) {
        query += ` AND s.format = $${paramIdx}`;
        queryParams.push(criteria.format);
        paramIdx++;
      }

      if (criteria.minPrice !== undefined) {
        query += ` AND s.price >= $${paramIdx}`;
        queryParams.push(criteria.minPrice);
        paramIdx++;
      }

      if (criteria.maxPrice !== undefined) {
        query += ` AND s.price <= $${paramIdx}`;
        queryParams.push(criteria.maxPrice);
        paramIdx++;
      }

      if (criteria.startDate) {
        query += ` AND s.date >= $${paramIdx}`;
        queryParams.push(criteria.startDate);
        paramIdx++;
      }

      if (criteria.endDate) {
        query += ` AND s.date <= $${paramIdx}`;
        queryParams.push(criteria.endDate);
        paramIdx++;
      }

      if (criteria.teacherId) {
        query += ` AND s.teacher_id = $${paramIdx}`;
        queryParams.push(criteria.teacherId);
        paramIdx++;
      }

      if (criteria.language) {
        query += ` AND s.language = $${paramIdx}`;
        queryParams.push(criteria.language);
        paramIdx++;
      }

      // Only show public sessions unless an teacher ID is specified
      if (!criteria.teacherId) {
        query += ` AND s.is_public = true`;
      }

      // Order by date
      query += ` ORDER BY s.date ASC`;

      const rows = await this.dbUtils.executeQuery<any>(query, queryParams);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error('Error finding sessions by criteria:', error);
      throw error;
    }
  }

  /**
   * Get featured sessions
   * @param limit Maximum number of sessions to return
   * @returns Array of featured sessions
   */
  async getFeaturedSessions(limit: number): Promise<SessionWithTeacher[]> {
    try {
      // Featured sessions are those with highest ratings and review counts
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.is_public = true AND s.date >= NOW()
        ORDER BY s.rating DESC NULLS LAST, s.review_count DESC NULLS LAST, s.date ASC
        LIMIT $1
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [limit], true); // Use cache for this query
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error getting featured sessions (limit ${limit}):`, error);
      throw error;
    }
  }

  /**
   * Get upcoming sessions
   * @param limit Maximum number of sessions to return
   * @returns Array of upcoming sessions
   */
  async getUpcomingSessions(limit: number): Promise<SessionWithTeacher[]> {
    try {
      const query = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.is_public = true AND s.date >= NOW()
        ORDER BY s.date ASC
        LIMIT $1
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [limit], true); // Use cache for this query
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error getting upcoming sessions (limit ${limit}):`, error);
      throw error;
    }
  }

  /**
   * Search sessions by title or description
   * @param query The search query
   * @param limit Maximum number of results to return
   * @returns Array of matching sessions
   */
  async search(query: string, limit?: number): Promise<SessionWithTeacher[]> {
    try {
      // Use PostgreSQL full-text search
      const searchQuery = `
        SELECT s.*, u.name as teacher_name, u.avatar as teacher_avatar, p.rating as teacher_rating
        FROM content.sessions s
        LEFT JOIN auth.users u ON s.teacher_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.is_public = true AND (
          s.title ILIKE $1 OR
          s.description ILIKE $1 OR
          s.type ILIKE $1 OR
          s.skill_level ILIKE $1 OR
          s.format ILIKE $1 OR
          u.name ILIKE $1
        )
        ORDER BY s.date ASC
        ${limit ? 'LIMIT $2' : ''}
      `;

      const searchPattern = `%${query}%`;
      const queryParams = limit ? [searchPattern, limit] : [searchPattern];

      const rows = await this.dbUtils.executeQuery<any>(searchQuery, queryParams);
      return rows.map(row => this.transformSessionRow(row)) as SessionWithTeacher[];
    } catch (error) {
      logger.error(`Error searching sessions for "${query}":`, error);
      throw error;
    }
  }

  /**
   * Transform a database row to a Session object
   * @param row The database row
   * @returns The Session object
   */
  private transformSessionRow(row: any): Session {
    // Create the base session object
    const session: any = {
      id: row.id,
      title: row.title,
      description: row.description,
      type: row.type,
      skillLevel: row.skill_level,
      format: row.format,
      price: parseFloat(row.price),
      language: row.language,
      duration: row.duration,
      date: row.date instanceof Date ? row.date : new Date(row.date),
      learningOutcomes: row.learning_outcomes,
      requirements: row.requirements,
      maxParticipants: row.max_participants,
      teacherId: row.teacher_id,
      zoomLink: row.zoom_link,
      imageUrl: row.image_url,
      isPublic: this.dbUtils.parseBoolean(row.is_public),
      rating: row.rating || 0,
      reviewCount: row.review_count || 0,
      createdAt: row.created_at ? new Date(row.created_at) : new Date(),
      updatedAt: row.updated_at ? new Date(row.updated_at) : new Date()
    };

    // Add teacher details if available
    if (row.teacher_name) {
      session.teacher = {
        id: row.teacher_id,
        name: row.teacher_name,
        avatar: row.teacher_avatar,
        rating: row.teacher_rating || 0
      };
    }

    return session;
  }
}
