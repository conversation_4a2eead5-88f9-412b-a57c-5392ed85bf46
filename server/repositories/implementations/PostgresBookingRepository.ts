import { BookingRepository } from '../interfaces/BookingRepository';
import { Booking, InsertBooking, BookingWithSession } from '@shared/schema';
import { DatabaseUtils } from '../../db/utils/DatabaseUtils';
import { logger } from '../../services/logger';

/**
 * PostgreSQL implementation of the BookingRepository interface
 */
export class PostgresBookingRepository implements BookingRepository {
  private dbUtils: DatabaseUtils;

  /**
   * Create a new PostgresBookingRepository instance
   * @param dbUtils The database utilities
   */
  constructor(dbUtils: DatabaseUtils) {
    this.dbUtils = dbUtils;
  }

  /**
   * Find a booking by ID
   * @param id The booking ID
   * @returns The booking or undefined if not found
   */
  async findById(id: string): Promise<Booking | undefined> {
    try {
      const query = `
        SELECT 
          b.id, b.user_id, b.session_id, b.time_slot_id, b.payment_id, 
          b.payment_status, b.payment_amount, b.payment_processor, b.created_at, b.updated_at,
          s.title as session_title, s.description as session_description, s.type as session_type,
          s.skill_level as session_skill_level, s.format as session_format, s.price as session_price,
          s.language as session_language, s.duration as session_duration, s.date as session_date,
          s.learning_outcomes as session_learning_outcomes, s.requirements as session_requirements,
          s.max_participants as session_max_participants, s.teacher_id as session_teacher_id,
          s.zoom_link as session_zoom_link, s.image_url as session_image_url, s.is_public as session_is_public,
          s.rating as session_rating, s.review_count as session_review_count,
          s.created_at as session_created_at, s.updated_at as session_updated_at,
          u.name as user_name, u.email as user_email, u.avatar as user_avatar,
          t.name as teacher_name, t.email as teacher_email, t.avatar as teacher_avatar
        FROM bookings.bookings b
        LEFT JOIN content.sessions s ON b.session_id = s.id
        LEFT JOIN auth.users u ON b.user_id = u.id
        LEFT JOIN auth.users t ON s.teacher_id = t.id
        WHERE b.id = $1
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [id]);

      if (rows.length === 0) {
        return undefined;
      }

      return this.transformBookingRow(rows[0]);
    } catch (error) {
      logger.error(`Error finding booking by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all bookings
   * @returns Array of bookings
   */
  async findAll(): Promise<Booking[]> {
    try {
      const query = `
        SELECT * FROM bookings.bookings
        ORDER BY created_at DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query);
      return rows.map(row => this.transformBookingRow(row));
    } catch (error) {
      logger.error('Error finding all bookings:', error);
      throw error;
    }
  }

  /**
   * Create a new booking
   * @param booking The booking to create
   * @returns The created booking
   */
  async create(booking: InsertBooking): Promise<Booking> {
    try {
      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Check if the session has available spots
        const sessionQuery = `
          SELECT max_participants FROM content.sessions
          WHERE id = $1
        `;
        const sessionResult = await client.query(sessionQuery, [booking.sessionId]);

        if (sessionResult.rows.length === 0) {
          throw new Error('Session not found');
        }

        const maxParticipants = sessionResult.rows[0].max_participants;

        // Count existing bookings for this session
        const bookingCountQuery = `
          SELECT COUNT(*) as count FROM bookings.bookings
          WHERE session_id = $1 AND status != 'cancelled'
        `;
        const bookingCountResult = await client.query(bookingCountQuery, [booking.sessionId]);
        const currentBookings = parseInt(bookingCountResult.rows[0].count);

        // Check if the session is full
        if (maxParticipants !== null && currentBookings >= maxParticipants) {
          throw new Error('Session is full');
        }

        // Insert the booking
        const insertQuery = `
          INSERT INTO bookings.bookings (
            user_id, session_id, status, payment_status, payment_method,
            payment_id, amount, currency, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
          RETURNING *
        `;

        const values = [
          booking.userId,
          booking.sessionId,
          booking.status || 'pending',
          booking.paymentStatus || 'pending',
          booking.paymentMethod,
          booking.paymentId,
          booking.amount,
          booking.currency || 'USD'
        ];

        const result = await client.query(insertQuery, values);

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        return this.transformBookingRow(result.rows[0]);
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error('Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Update an existing booking
   * @param id The booking ID
   * @param bookingData The booking data to update
   * @returns The updated booking or undefined if not found
   */
  async update(id: number, bookingData: Partial<Booking>): Promise<Booking | undefined> {
    try {
      // Get current booking data to preserve fields that aren't being updated
      const currentBooking = await this.findById(id.toString());
      if (!currentBooking) {
        return undefined;
      }

      // Build the update query dynamically based on provided fields
      let updateQuery = 'UPDATE bookings.bookings SET ';
      const updateValues: any[] = [];
      const updateFields: string[] = [];
      let paramIdx = 1;

      // Add each field to the update query
      for (const [key, value] of Object.entries(bookingData)) {
        if (value !== undefined) {
          // Convert camelCase to snake_case for database fields
          const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
          updateFields.push(`${dbField} = $${paramIdx}`);
          updateValues.push(value);
          paramIdx++;
        }
      }

      // Add updated_at timestamp
      updateFields.push(`updated_at = NOW()`);

      // Complete the query
      updateQuery += updateFields.join(', ');
      updateQuery += ` WHERE id = $${paramIdx} RETURNING *`;
      updateValues.push(id);

      // Execute the query
      const rows = await this.dbUtils.executeQuery<any>(updateQuery, updateValues);

      if (rows.length === 0) {
        return undefined;
      }

      return this.transformBookingRow(rows[0]);
    } catch (error) {
      logger.error(`Error updating booking ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a booking by ID
   * @param id The booking ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    try {
      const query = `
        DELETE FROM bookings.bookings
        WHERE id = $1
      `;

      const result = await this.dbUtils.executeQuery<any>(query, [id]);
      return result.length > 0;
    } catch (error) {
      logger.error(`Error deleting booking ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find bookings by user ID
   * @param userId The user ID
   * @returns Array of bookings with session details
   */
  async findByUserId(userId: number): Promise<BookingWithSession[]> {
    try {
      const query = `
        SELECT b.*, 
               s.title as session_title, s.description as session_description, 
               s.type as session_type, s.skill_level as session_skill_level,
               s.format as session_format, s.price as session_price,
               s.language as session_language, s.duration as session_duration,
               s.date as session_date,
               s.image_url as session_image_url,
               u.name as teacher_name, u.avatar as teacher_avatar
        FROM bookings.bookings b
        JOIN content.sessions s ON b.session_id = s.id
        JOIN auth.users u ON s.teacher_id = u.id
        WHERE b.user_id = $1
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [userId]);
      return rows.map(row => this.transformBookingWithSessionRow(row));
    } catch (error) {
      logger.error(`Error finding bookings by user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Find bookings by session ID
   * @param sessionId The session ID
   * @returns Array of bookings
   */
  async findBySessionId(sessionId: number): Promise<Booking[]> {
    try {
      const query = `
        SELECT * FROM bookings.bookings
        WHERE session_id = $1
        ORDER BY created_at DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [sessionId]);
      return rows.map(row => this.transformBookingRow(row));
    } catch (error) {
      logger.error(`Error finding bookings by session ID ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Find bookings by teacher ID
   * @param teacherId The teacher ID
   * @returns Array of bookings with session details
   */
  async findByTeacherId(teacherId: number): Promise<BookingWithSession[]> {
    try {
      const query = `
        SELECT b.*, 
               s.title as session_title, s.description as session_description, 
               s.type as session_type, s.skill_level as session_skill_level,
               s.format as session_format, s.price as session_price,
               s.language as session_language, s.duration as session_duration,
               s.date as session_date,
               s.image_url as session_image_url,
               u.name as teacher_name, u.avatar as teacher_avatar,
               u2.name as user_name, u2.avatar as user_avatar
        FROM bookings.bookings b
        JOIN content.sessions s ON b.session_id = s.id
        JOIN auth.users u ON s.teacher_id = u.id
        JOIN auth.users u2 ON b.user_id = u2.id
        WHERE s.teacher_id = $1
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [teacherId]);
      return rows.map(row => this.transformBookingWithSessionRow(row));
    } catch (error) {
      logger.error(`Error finding bookings by teacher ID ${teacherId}:`, error);
      throw error;
    }
  }

  /**
   * Find bookings by status
   * @param status The booking status
   * @returns Array of bookings
   */
  async findByStatus(status: string): Promise<Booking[]> {
    try {
      const query = `
        SELECT * FROM bookings.bookings
        WHERE status = $1
        ORDER BY created_at DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [status]);
      return rows.map(row => this.transformBookingRow(row));
    } catch (error) {
      logger.error(`Error finding bookings by status ${status}:`, error);
      throw error;
    }
  }

  /**
   * Find bookings by date range
   * @param startDate The start date
   * @param endDate The end date
   * @returns Array of bookings
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<Booking[]> {
    try {
      const query = `
        SELECT b.* FROM bookings.bookings b
        JOIN content.sessions s ON b.session_id = s.id
        WHERE s.date >= $1 AND s.date <= $2
        ORDER BY s.date ASC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [startDate, endDate]);
      return rows.map(row => this.transformBookingRow(row));
    } catch (error) {
      logger.error(`Error finding bookings by date range ${startDate}-${endDate}:`, error);
      throw error;
    }
  }

  /**
   * Update booking status
   * @param bookingId The booking ID
   * @param status The new status
   * @returns The updated booking or undefined if not found
   */
  async updateStatus(bookingId: number, status: string): Promise<Booking | undefined> {
    try {
      const query = `
        UPDATE bookings.bookings
        SET status = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [status, bookingId]);

      if (rows.length === 0) {
        return undefined;
      }

      return this.transformBookingRow(rows[0]);
    } catch (error) {
      logger.error(`Error updating booking status for booking ${bookingId}:`, error);
      throw error;
    }
  }

  /**
   * Get upcoming bookings for a user
   * @param userId The user ID
   * @returns Array of upcoming bookings with session details
   */
  async getUpcomingBookings(userId: number): Promise<BookingWithSession[]> {
    try {
      const query = `
        SELECT b.*, 
               s.title as session_title, s.description as session_description, 
               s.type as session_type, s.skill_level as session_skill_level,
               s.format as session_format, s.price as session_price,
               s.language as session_language, s.duration as session_duration,
               s.date as session_date,
               s.image_url as session_image_url,
               u.name as teacher_name, u.avatar as teacher_avatar
        FROM bookings.bookings b
        JOIN content.sessions s ON b.session_id = s.id
        JOIN auth.users u ON s.teacher_id = u.id
        WHERE b.user_id = $1 AND s.date >= NOW() AND b.status != 'cancelled'
        ORDER BY s.date ASC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [userId]);
      return rows.map(row => this.transformBookingWithSessionRow(row));
    } catch (error) {
      logger.error(`Error getting upcoming bookings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get past bookings for a user
   * @param userId The user ID
   * @returns Array of past bookings with session details
   */
  async getPastBookings(userId: number): Promise<BookingWithSession[]> {
    try {
      const query = `
        SELECT b.*, 
               s.title as session_title, s.description as session_description, 
               s.type as session_type, s.skill_level as session_skill_level,
               s.format as session_format, s.price as session_price,
               s.language as session_language, s.duration as session_duration,
               s.date as session_date,
               s.image_url as session_image_url,
               u.name as teacher_name, u.avatar as teacher_avatar
        FROM bookings.bookings b
        JOIN content.sessions s ON b.session_id = s.id
        JOIN auth.users u ON s.teacher_id = u.id
        WHERE b.user_id = $1 AND s.date < NOW()
        ORDER BY s.date DESC
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [userId]);
      return rows.map(row => this.transformBookingWithSessionRow(row));
    } catch (error) {
      logger.error(`Error getting past bookings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Check if a user has booked a session
   * @param userId The user ID
   * @param sessionId The session ID
   * @returns True if the user has booked the session
   */
  async hasUserBookedSession(userId: number, sessionId: number): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as count FROM bookings.bookings
        WHERE user_id = $1 AND session_id = $2 AND status != 'cancelled'
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [userId, sessionId]);
      return parseInt(rows[0].count) > 0;
    } catch (error) {
      logger.error(`Error checking if user ${userId} has booked session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Transform a database row to a Booking object
   * @param row The database row
   * @returns The Booking object
   */
  private transformBookingRow(row: any): Booking {
    return {
      id: row.id,
      userId: row.user_id,
      sessionId: row.session_id,
      status: row.status,
      paymentStatus: row.payment_status,
      paymentMethod: row.payment_method,
      paymentId: row.payment_id,
      amount: parseFloat(row.amount),
      currency: row.currency,
      createdAt: row.created_at ? new Date(row.created_at) : new Date(),
      updatedAt: row.updated_at ? new Date(row.updated_at) : new Date()
    };
  }

  /**
   * Transform a database row to a BookingWithSession object
   * @param row The database row
   * @returns The BookingWithSession object
   */
  private transformBookingWithSessionRow(row: any): BookingWithSession {
    const booking = this.transformBookingRow(row);

    return {
      ...booking,
      session: {
        id: row.session_id,
        title: row.session_title,
        description: row.session_description,
        type: row.session_type,
        price: row.session_price,
        duration: row.session_duration,
        date: row.session_date,
        language: row.session_language,
        skillLevel: row.session_skill_level,
        format: row.session_format,
        maxParticipants: row.session_max_participants,
        teacherId: row.session_teacher_id,
        zoomLink: row.session_zoom_link,
        learningOutcomes: row.session_learning_outcomes,
        requirements: row.session_requirements,
        rating: row.session_rating || 0,
        reviewCount: row.session_review_count || 0,
        createdAt: row.session_created_at,
        updatedAt: row.session_updated_at,
        imageUrl: row.session_image_url,
        isPublic: row.session_is_public
      },
      user: row.user_name ? {
        id: row.user_id,
        name: row.user_name,
        avatar: row.user_avatar
      } : undefined
    };
  }
}
