import { UserRepository } from '../interfaces/UserRepository';
import { User, InsertUser } from '@shared/schema';
import { DatabaseUtils } from '../../db/utils/DatabaseUtils';
import bcrypt from 'bcryptjs';
import { logger } from '../../services/logger';
import { getDefaultAvatarUrl } from '../../utils/default-images';

/**
 * PostgreSQL implementation of the UserRepository interface
 */
export class PostgresUserRepository implements UserRepository {
  private dbUtils: DatabaseUtils;
  private userCache: Map<number, { user: User, timestamp: number }> = new Map();
  private CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Create a new PostgresUserRepository instance
   * @param dbUtils The database utilities
   */
  constructor(dbUtils: DatabaseUtils) {
    this.dbUtils = dbUtils;
  }

  /**
   * Find a user by ID
   * @param id The user ID
   * @returns The user or undefined if not found
   */
  async findById(id: number): Promise<User | undefined> {
    try {
      // Check if user is in cache and not expired
      const cachedData = this.userCache.get(id);
      const now = Date.now();

      if (cachedData && (now - cachedData.timestamp < this.CACHE_TTL)) {
        // Use cached user data if available and fresh
        return cachedData.user;
      }

      // Get authentication data from auth.users table
      const userRows = await this.dbUtils.executeQuery<any>(
        'SELECT id, username, password, email, email_verified, role, created_at, updated_at, last_login_at FROM auth.users WHERE id = $1',
        [id]
      );

      if (userRows.length === 0) {
        return undefined;
      }

      // Get profile data from profiles.user_profiles table
      const profileRows = await this.dbUtils.executeQuery<any>(
        'SELECT name, bio, avatar, timezone, cover_photo, phone, is_teacher, is_teacher FROM profiles.user_profiles WHERE user_id = $1',
        [id]
      );

      // Combine the data
      const userData = {
        ...userRows[0],
        ...(profileRows.length > 0 ? profileRows[0] : {})
      };

      // Transform to User object
      const user = this.transformUserRow(userData);

      // Cache the user for future requests
      this.userCache.set(id, { user, timestamp: now });

      return user;
    } catch (error) {
      logger.error(`Error finding user by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all users
   * @returns Array of users
   */
  async findAll(): Promise<User[]> {
    try {
      const query = `
        SELECT u.id, u.username, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        ORDER BY u.id
      `;

      const rows = await this.dbUtils.executeQuery<any>(query);
      return rows.map(row => this.transformUserRow(row));
    } catch (error) {
      logger.error('Error finding all users:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param user The user to create
   * @returns The created user
   */
  async create(user: InsertUser): Promise<User> {
    try {
      // Hash password before storing
      const hashedPassword = await bcrypt.hash(user.password, 10);

      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // 1. Insert authentication data into auth.users table
        const userResult = await client.query(
          `INSERT INTO auth.users
          (username, password, email, email_verified, role, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          RETURNING id, username, email, email_verified, role, created_at, updated_at`,
          [
            user.username,
            hashedPassword,
            user.email,
            user.emailVerified || false,
            user.role || 'user'
          ]
        );

        if (userResult.rows.length === 0) {
          await this.dbUtils.rollbackTransaction(client);
          throw new Error('Failed to create user');
        }

        const userId = userResult.rows[0].id;

        // 2. Insert profile data into profiles.user_profiles table
        const profileResult = await client.query(
          `INSERT INTO profiles.user_profiles
          (user_id, name, bio, avatar, timezone, is_teacher, is_teacher, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
          RETURNING id`,
          [
            userId,
            user.name || user.username,
            user.bio || '',
            user.avatar || getDefaultAvatarUrl(userId),
            user.timezone || 'UTC',
            user.isTeacher || false,
            user.isTeacher || false
          ]
        );

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Return the created user
        return this.findById(userId) as Promise<User>;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update an existing user
   * @param id The user ID
   * @param userData The user data to update
   * @returns The updated user or undefined if not found
   */
  async update(id: number, userData: Partial<User>): Promise<User | undefined> {
    try {
      logger.debug(`Updating user ${id} with data:`, JSON.stringify(userData, null, 2));

      // Get current user data to preserve fields that aren't being updated
      const currentUser = await this.findById(id);
      if (!currentUser) {
        logger.debug(`User ${id} not found for update`);
        return undefined;
      }

      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // 1. Update authentication data in users table
        if (userData.username || userData.password || userData.email || userData.emailVerified !== undefined || userData.role !== undefined) {
          let authQuery = 'UPDATE auth.users SET ';
          const authValues: any[] = [];
          const authUpdateFields: string[] = [];
          let authParamIdx = 1;

          // Handle password hashing if provided
          if (userData.password) {
            userData.password = await bcrypt.hash(userData.password, 10);
          }

          // Authentication fields
          const authFields = ['username', 'password', 'email', 'email_verified', 'role'];

          // Add each auth field to the update query
          for (const [key, value] of Object.entries(userData)) {
            if (value !== undefined) {
              // Convert camelCase to snake_case for database fields
              const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();

              // Only include fields that exist in the users table
              if (authFields.includes(dbField)) {
                authUpdateFields.push(`${dbField} = $${authParamIdx}`);
                authValues.push(value);
                authParamIdx++;
              }
            }
          }

          if (authUpdateFields.length > 0) {
            // Add updated_at timestamp
            authUpdateFields.push(`updated_at = NOW()`);

            // Complete the query
            authQuery += authUpdateFields.join(', ');
            authQuery += ` WHERE id = $${authParamIdx}`;
            authValues.push(id);

            logger.debug(`Executing user auth update query:`, {
              query: authQuery,
              values: authValues.map(v => typeof v === 'string' && v.length > 50 ? v.substring(0, 50) + '...' : v)
            });

            // Execute the query
            await client.query(authQuery, authValues);
          }
        }

        // 2. Update profile data in user_profiles table
        if (userData.name || userData.bio || userData.avatar || userData.timezone ||
          userData.coverPhoto || userData.phone || userData.isTeacher !== undefined ||
          userData.isTeacher !== undefined) {

          let profileQuery = 'UPDATE profiles.user_profiles SET ';
          const profileValues: any[] = [];
          const profileUpdateFields: string[] = [];
          let profileParamIdx = 1;

          // Profile fields mapping (camelCase to snake_case)
          const profileFieldsMap: Record<string, string> = {
            'name': 'name',
            'bio': 'bio',
            'avatar': 'avatar',
            'timezone': 'timezone',
            'coverPhoto': 'cover_photo',
            'phone': 'phone',
            'isTeacher': 'is_teacher',
            'isTeacher': 'is_teacher'
          };

          // Add each profile field to the update query
          for (const [key, value] of Object.entries(userData)) {
            if (value !== undefined && key in profileFieldsMap) {
              profileUpdateFields.push(`${profileFieldsMap[key]} = $${profileParamIdx}`);
              profileValues.push(value);
              profileParamIdx++;
            }
          }

          if (profileUpdateFields.length > 0) {
            // Add updated_at timestamp
            profileUpdateFields.push(`updated_at = NOW()`);

            // Complete the query
            profileQuery += profileUpdateFields.join(', ');
            profileQuery += ` WHERE user_id = $${profileParamIdx}`;
            profileValues.push(id);

            logger.debug(`Executing profile update query:`, {
              query: profileQuery,
              values: profileValues.map(v => typeof v === 'string' && v.length > 50 ? v.substring(0, 50) + '...' : v)
            });

            // Execute the query
            await client.query(profileQuery, profileValues);
          }
        }

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Invalidate cache
        this.userCache.delete(id);

        logger.debug(`User ${id} updated successfully`);

        // Get the updated user
        return this.findById(id);
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user by ID
   * @param id The user ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    try {
      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Delete profile data first (foreign key constraint)
        await client.query('DELETE FROM profiles.user_profiles WHERE user_id = $1', [id]);

        // Delete user
        const result = await client.query('DELETE FROM auth.users WHERE id = $1', [id]);

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Invalidate cache
        this.userCache.delete(id);

        return result.rowCount > 0;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find a user by username
   * @param username The username to search for
   * @returns The user or undefined if not found
   */
  async findByUsername(username: string): Promise<User | undefined> {
    try {
      const query = `
        SELECT u.id, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.username, p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE p.username = $1
      `;
      const rows = await this.dbUtils.executeQuery<any>(query, [username]);
      if (rows.length === 0) {
        return undefined;
      }
      const user = this.transformUserRow(rows[0]);
      this.userCache.set(user.id, { user, timestamp: Date.now() });
      return user;
    } catch (error) {
      logger.error(`Error finding user by username ${username}:`, error);
      throw error;
    }
  }

  /**
   * Find a user by email
   * @param email The email to search for
   * @returns The user or undefined if not found
   */
  async findByEmail(email: string): Promise<User | undefined> {
    try {
      // Get authentication data from auth.users table
      const userRows = await this.dbUtils.executeQuery<any>(
        'SELECT id, username, password, email, email_verified, role, created_at, updated_at, last_login_at FROM auth.users WHERE email = $1',
        [email]
      );

      if (userRows.length === 0) {
        return undefined;
      }

      // Get profile data from profiles.user_profiles table
      const profileRows = await this.dbUtils.executeQuery<any>(
        'SELECT name, bio, avatar, timezone, cover_photo, phone, is_teacher, is_teacher FROM profiles.user_profiles WHERE user_id = $1',
        [userRows[0].id]
      );

      // Combine the data
      const userData = {
        ...userRows[0],
        ...(profileRows.length > 0 ? profileRows[0] : {})
      };

      // Transform to User object
      const user = this.transformUserRow(userData);

      // Cache the user for future requests
      this.userCache.set(user.id, { user, timestamp: Date.now() });

      return user;
    } catch (error) {
      logger.error(`Error finding user by email ${email}:`, error);
      throw error;
    }
  }

  /**
   * Find a user by social provider and provider ID
   * @param provider The social provider (e.g., 'google', 'facebook')
   * @param providerId The provider-specific ID
   * @returns The user or undefined if not found
   */
  async findBySocialProvider(provider: string, providerId: string): Promise<User | undefined> {
    try {
      const query = `
        SELECT u.id, u.username, u.password, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        LEFT JOIN auth.social_accounts sa ON u.id = sa.user_id
        WHERE sa.provider = $1 AND sa.provider_id = $2
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [provider, providerId]);

      if (rows.length === 0) {
        return undefined;
      }

      // Transform to User object
      const user = this.transformUserRow(rows[0]);

      // Cache the user for future requests
      this.userCache.set(user.id, { user, timestamp: Date.now() });

      return user;
    } catch (error) {
      logger.error(`Error finding user by social provider ${provider}/${providerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a user from social login data
   * @param socialData The social login data
   * @returns The created user
   */
  async createFromSocial(socialData: {
    name: string;
    email: string;
    provider: string;
    providerId: string;
    username?: string;
    profileUrl?: string;
    avatarUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<User> {
    try {
      // Generate a random password for the user
      const password = crypto.randomBytes(16).toString('hex');
      const hashedPassword = await bcrypt.hash(password, 10);

      // Generate a username if not provided
      const username = socialData.username || socialData.email.split('@')[0] + Math.floor(Math.random() * 1000);

      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // 1. Insert authentication data into auth.users table
        const userResult = await client.query(
          `INSERT INTO auth.users
          (username, password, email, email_verified, role, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          RETURNING id, username, email, email_verified, role, created_at, updated_at`,
          [
            username,
            hashedPassword,
            socialData.email,
            true, // Email is verified through social login
            'user'
          ]
        );

        if (userResult.rows.length === 0) {
          await this.dbUtils.rollbackTransaction(client);
          throw new Error('Failed to create user from social login');
        }

        const userId = userResult.rows[0].id;

        // 2. Insert profile data into profiles.user_profiles table
        await client.query(
          `INSERT INTO profiles.user_profiles
          (user_id, name, avatar, created_at, updated_at)
          VALUES ($1, $2, $3, NOW(), NOW())`,
          [
            userId,
            socialData.name,
            socialData.avatarUrl || getDefaultAvatarUrl(userId)
          ]
        );

        // 3. Insert social account data
        await client.query(
          `INSERT INTO auth.social_accounts
          (user_id, provider, provider_id, username, profile_url, access_token, refresh_token, expires_at, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())`,
          [
            userId,
            socialData.provider,
            socialData.providerId,
            socialData.username || null,
            socialData.profileUrl || null,
            socialData.accessToken || null,
            socialData.refreshToken || null,
            socialData.expiresAt || null
          ]
        );

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Return the created user
        return this.findById(userId) as Promise<User>;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error('Error creating user from social login:', error);
      throw error;
    }
  }

  /**
   * Get all users with teacher role
   * @returns Array of teacher users
   */
  async getAllTeachers(): Promise<User[]> {
    try {
      const query = `
        SELECT u.id, u.username, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher,
               p.specializations, p.skills, p.certifications, p.education, p.experience, p.rating, p.review_count
        FROM auth.users u
        JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE p.is_teacher = true
        ORDER BY p.rating DESC NULLS LAST, p.review_count DESC NULLS LAST
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [], true); // Use cache for this query
      return rows.map(row => this.transformUserRow(row));
    } catch (error) {
      logger.error('Error getting all teachers:', error);
      throw error;
    }
  }

  /**
   * Get top teachers based on rating and review count
   * @param limit Maximum number of teachers to return
   * @returns Array of top teacher users
   */
  async getTopTeachers(limit: number): Promise<User[]> {
    try {
      const query = `
        SELECT u.id, u.username, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher,
               p.specializations, p.skills, p.certifications, p.education, p.experience, p.rating, p.review_count
        FROM auth.users u
        JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE p.is_teacher = true
        ORDER BY p.rating DESC NULLS LAST, p.review_count DESC NULLS LAST
        LIMIT $1
      `;

      const rows = await this.dbUtils.executeQuery<any>(query, [limit], true); // Use cache for this query
      return rows.map(row => this.transformUserRow(row));
    } catch (error) {
      logger.error(`Error getting top ${limit} teachers:`, error);
      throw error;
    }
  }

  /**
   * Search users by partial username
   * @param partialUsername The partial username to search for
   * @param limit Maximum number of results to return
   * @returns Array of matching users
   */
  async searchByUsername(partialUsername: string, limit: number = 10): Promise<User[]> {
    try {
      const query = `
        SELECT u.id, u.email, u.email_verified, u.role, u.created_at, u.updated_at, u.last_login_at,
               p.username, p.name, p.bio, p.avatar, p.timezone, p.cover_photo, p.phone, p.is_teacher, p.is_teacher
        FROM auth.users u
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE p.username ILIKE $1
        ORDER BY p.username
        LIMIT $2
      `;
      const rows = await this.dbUtils.executeQuery<any>(query, [`%${partialUsername}%`, limit]);
      return rows.map(row => this.transformUserRow(row));
    } catch (error) {
      logger.error(`Error searching users by username ${partialUsername}:`, error);
      throw error;
    }
  }

  /**
   * Verify a user's email
   * @param userId The user ID
   * @returns True if successful
   */
  async verifyEmail(userId: number): Promise<boolean> {
    try {
      const result = await this.dbUtils.executeQuery(
        'UPDATE auth.users SET email_verified = true, updated_at = NOW() WHERE id = $1',
        [userId]
      );

      // Invalidate cache
      this.userCache.delete(userId);

      return true;
    } catch (error) {
      logger.error(`Error verifying email for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Link a social account to a user
   * @param userId The user ID
   * @param socialAccount The social account data
   * @returns True if successful
   */
  async linkSocialAccount(userId: number, socialAccount: {
    provider: string;
    providerId: string;
    username?: string;
    email?: string;
    profileUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<boolean> {
    try {
      // Check if the social account is already linked to another user
      const existingAccount = await this.dbUtils.executeQuery<any>(
        'SELECT user_id FROM auth.social_accounts WHERE provider = $1 AND provider_id = $2',
        [socialAccount.provider, socialAccount.providerId]
      );

      if (existingAccount.length > 0 && existingAccount[0].user_id !== userId) {
        throw new Error('Social account already linked to another user');
      }

      // Insert or update the social account
      const query = `
        INSERT INTO auth.social_accounts
        (user_id, provider, provider_id, username, email, profile_url, access_token, refresh_token, expires_at, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        ON CONFLICT (provider, provider_id) DO UPDATE
        SET username = $4, email = $5, profile_url = $6, access_token = $7, refresh_token = $8, expires_at = $9, updated_at = NOW()
      `;

      await this.dbUtils.executeQuery(query, [
        userId,
        socialAccount.provider,
        socialAccount.providerId,
        socialAccount.username || null,
        socialAccount.email || null,
        socialAccount.profileUrl || null,
        socialAccount.accessToken || null,
        socialAccount.refreshToken || null,
        socialAccount.expiresAt || null
      ]);

      return true;
    } catch (error) {
      logger.error(`Error linking social account for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get social accounts linked to a user
   * @param userId The user ID
   * @returns Array of social accounts
   */
  async getSocialAccounts(userId: number): Promise<Array<{
    provider: string;
    providerId: string;
    username: string;
    profileUrl: string;
  }>> {
    try {
      const query = `
        SELECT provider, provider_id, username, profile_url
        FROM auth.social_accounts
        WHERE user_id = $1
      `;

      return await this.dbUtils.executeQuery<any>(query, [userId]);
    } catch (error) {
      logger.error(`Error getting social accounts for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a social account from a user
   * @param userId The user ID
   * @param provider The social provider
   * @param providerId The provider-specific ID
   * @returns True if successful
   */
  async deleteSocialAccount(userId: number, provider: string, providerId: string): Promise<boolean> {
    try {
      const result = await this.dbUtils.executeQuery(
        'DELETE FROM auth.social_accounts WHERE user_id = $1 AND provider = $2 AND provider_id = $3',
        [userId, provider, providerId]
      );

      return true;
    } catch (error) {
      logger.error(`Error deleting social account for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Transform a database row to a User object
   * @param row The database row
   * @returns The User object
   */
  private transformUserRow(row: any): User {
    return {
      id: row.id,
      username: row.username,
      password: row.password,
      email: row.email,
      name: row.name || '',
      bio: row.bio || '',
      avatar: row.avatar || '',
      timezone: row.timezone || 'UTC',
      coverPhoto: row.cover_photo || null,
      phone: row.phone || null,
      isTeacher: this.dbUtils.parseBoolean(row.is_teacher),
      isTeacher: this.dbUtils.parseBoolean(row.is_teacher),
      emailVerified: this.dbUtils.parseBoolean(row.email_verified),
      role: row.role || 'user',
      createdAt: row.created_at ? new Date(row.created_at) : new Date(),
      updatedAt: row.updated_at ? new Date(row.updated_at) : new Date(),
      lastLoginAt: row.last_login_at ? new Date(row.last_login_at) : undefined,
      specializations: this.dbUtils.parseArrayField(row.specializations) || [],
      skills: this.dbUtils.parseArrayField(row.skills) || [],
      certifications: this.dbUtils.parseArrayField(row.certifications) || [],
      education: row.education || '',
      experience: row.experience || '',
      rating: row.rating || 0,
      reviewCount: row.review_count || 0
    };
  }
}
