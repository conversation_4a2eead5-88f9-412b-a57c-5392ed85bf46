import { ReviewRepository } from '../interfaces/ReviewRepository';
import { Review, InsertReview, ReviewWithUser } from '@shared/schema';
import { DatabaseUtils } from '../../db/utils/DatabaseUtils';
import { logger } from '../../services/logger';

/**
 * PostgreSQL implementation of the ReviewRepository interface
 */
export class PostgresReviewRepository implements ReviewRepository {
  private dbUtils: DatabaseUtils;
  private reviewCache: Map<number, { review: ReviewWithUser, timestamp: number }> = new Map();
  private CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Create a new PostgresReviewRepository instance
   * @param dbUtils The database utilities
   */
  constructor(dbUtils: DatabaseUtils) {
    this.dbUtils = dbUtils;
  }

  /**
   * Find a review by ID
   * @param id The review ID
   * @returns The review or undefined if not found
   */
  async findById(id: number): Promise<Review | undefined> {
    try {
      const rows = await this.dbUtils.executeQuery<Review>(
        'SELECT * FROM content.reviews WHERE id = $1',
        [id]
      );

      return rows.length > 0 ? rows[0] : undefined;
    } catch (error) {
      logger.error(`Error finding review by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find all reviews
   * @returns Array of reviews
   */
  async findAll(): Promise<Review[]> {
    try {
      return await this.dbUtils.executeQuery<Review>(
        'SELECT * FROM content.reviews ORDER BY created_at DESC'
      );
    } catch (error) {
      logger.error('Error finding all reviews:', error);
      throw error;
    }
  }

  /**
   * Create a new review
   * @param review The review to create
   * @returns The created review
   */
  async create(review: InsertReview): Promise<Review> {
    try {
      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Insert the review
        const reviewResult = await client.query(
          `INSERT INTO content.reviews
          (user_id, session_id, rating, comment, created_at)
          VALUES ($1, $2, $3, $4, NOW())
          RETURNING *`,
          [
            review.userId,
            review.sessionId,
            review.rating,
            review.comment || null
          ]
        );

        // Get the session's teacher ID
        const sessionResult = await client.query(
          'SELECT teacher_id FROM content.sessions WHERE id = $1',
          [review.sessionId]
        );

        if (sessionResult.rows.length > 0) {
          const teacherId = sessionResult.rows[0].teacher_id;

          // Update the teacher's rating
          await this.updateTeacherRating(client, teacherId);
        }

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        return reviewResult.rows[0];
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error('Error creating review:', error);
      throw error;
    }
  }

  /**
   * Update an existing review
   * @param id The review ID
   * @param review The review data to update
   * @returns The updated review or undefined if not found
   */
  async update(id: number, review: Partial<Review>): Promise<Review | undefined> {
    try {
      // Check if review exists
      const existingReview = await this.findById(id);
      if (!existingReview) {
        return undefined;
      }

      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Build update query
        let updateQuery = 'UPDATE content.reviews SET ';
        const updateValues: any[] = [];
        const updateFields: string[] = [];
        let paramIdx = 1;

        if (review.rating !== undefined) {
          updateFields.push(`rating = $${paramIdx++}`);
          updateValues.push(review.rating);
        }

        if (review.comment !== undefined) {
          updateFields.push(`comment = $${paramIdx++}`);
          updateValues.push(review.comment);
        }

        // Add updated_at timestamp
        updateFields.push(`updated_at = NOW()`);

        // Finalize query
        updateQuery += updateFields.join(', ');
        updateQuery += ` WHERE id = $${paramIdx++} RETURNING *`;
        updateValues.push(id);

        // Execute update
        const result = await client.query(updateQuery, updateValues);

        // Get the session's teacher ID
        const sessionResult = await client.query(
          'SELECT teacher_id FROM content.sessions WHERE id = $1',
          [existingReview.sessionId]
        );

        if (sessionResult.rows.length > 0) {
          const teacherId = sessionResult.rows[0].teacher_id;

          // Update the teacher's rating
          await this.updateTeacherRating(client, teacherId);
        }

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Clear review cache
        this.reviewCache.delete(id);

        return result.rows.length > 0 ? result.rows[0] : undefined;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error(`Error updating review ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a review by ID
   * @param id The review ID
   * @returns True if deleted, false if not found
   */
  async delete(id: number): Promise<boolean> {
    try {
      // Get the review to find its session ID
      const review = await this.findById(id);
      if (!review) {
        return false;
      }

      // Begin transaction
      const client = await this.dbUtils.beginTransaction();

      try {
        // Delete the review
        const result = await client.query(
          'DELETE FROM content.reviews WHERE id = $1',
          [id]
        );

        // Get the session's teacher ID
        const sessionResult = await client.query(
          'SELECT teacher_id FROM content.sessions WHERE id = $1',
          [review.sessionId]
        );

        if (sessionResult.rows.length > 0) {
          const teacherId = sessionResult.rows[0].teacher_id;

          // Update the teacher's rating
          await this.updateTeacherRating(client, teacherId);
        }

        // Commit transaction
        await this.dbUtils.commitTransaction(client);

        // Clear review cache
        this.reviewCache.delete(id);

        return result.rowCount > 0;
      } catch (error) {
        // Rollback transaction in case of error
        await this.dbUtils.rollbackTransaction(client);
        throw error;
      }
    } catch (error) {
      logger.error(`Error deleting review ${id}:`, error);
      throw error;
    }
  }

  /**
   * Find reviews by user ID
   * @param userId The user ID
   * @returns Array of reviews
   */
  async findByUserId(userId: number): Promise<Review[]> {
    try {
      return await this.dbUtils.executeQuery<Review>(
        'SELECT * FROM content.reviews WHERE user_id = $1 ORDER BY created_at DESC',
        [userId]
      );
    } catch (error) {
      logger.error(`Error finding reviews by user ID ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Find reviews by session ID
   * @param sessionId The session ID
   * @returns Array of reviews with user details
   */
  async findBySessionId(sessionId: number): Promise<ReviewWithUser[]> {
    try {
      const query = `
        SELECT r.*, u.name as reviewer_name, u.username as reviewer_username, p.avatar as reviewer_avatar
        FROM content.reviews r
        JOIN auth.users u ON r.user_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE r.session_id = $1
        ORDER BY r.created_at DESC
      `;

      return await this.dbUtils.executeQuery<ReviewWithUser>(query, [sessionId]);
    } catch (error) {
      logger.error(`Error finding reviews by session ID ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Find reviews by teacher ID
   * @param teacherId The teacher ID
   * @returns Array of reviews with user details
   */
  async findByTeacherId(teacherId: number): Promise<ReviewWithUser[]> {
    try {
      const query = `
        SELECT r.*, s.title as session_title, u.name as reviewer_name, u.username as reviewer_username, p.avatar as reviewer_avatar
        FROM content.reviews r
        JOIN content.sessions s ON r.session_id = s.id
        JOIN auth.users u ON r.user_id = u.id
        LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE s.teacher_id = $1
        ORDER BY r.created_at DESC
      `;

      return await this.dbUtils.executeQuery<ReviewWithUser>(query, [teacherId]);
    } catch (error) {
      logger.error(`Error finding reviews by teacher ID ${teacherId}:`, error);
      throw error;
    }
  }

  /**
   * Get average rating for a session
   * @param sessionId The session ID
   * @returns The average rating or null if no reviews
   */
  async getAverageRatingForSession(sessionId: number): Promise<number | null> {
    try {
      const query = `
        SELECT AVG(rating) as average_rating
        FROM content.reviews
        WHERE session_id = $1
      `;

      const rows = await this.dbUtils.executeQuery<{ average_rating: string }>(query, [sessionId]);
      return rows[0]?.average_rating ? parseFloat(rows[0].average_rating) : null;
    } catch (error) {
      logger.error(`Error getting average rating for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Get average rating for a teacher
   * @param teacherId The teacher ID
   * @returns The average rating or null if no reviews
   */
  async getAverageRatingForTeacher(teacherId: number): Promise<number | null> {
    try {
      const query = `
        SELECT AVG(r.rating) as average_rating
        FROM content.reviews r
        JOIN content.sessions s ON r.session_id = s.id
        WHERE s.teacher_id = $1
      `;

      const rows = await this.dbUtils.executeQuery<{ average_rating: string }>(query, [teacherId]);
      return rows[0]?.average_rating ? parseFloat(rows[0].average_rating) : null;
    } catch (error) {
      logger.error(`Error getting average rating for teacher ${teacherId}:`, error);
      throw error;
    }
  }

  /**
   * Get review count for a session
   * @param sessionId The session ID
   * @returns The review count
   */
  async getReviewCountForSession(sessionId: number): Promise<number> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM content.reviews
        WHERE session_id = $1
      `;

      const rows = await this.dbUtils.executeQuery<{ count: string }>(query, [sessionId]);
      return parseInt(rows[0]?.count || '0', 10);
    } catch (error) {
      logger.error(`Error getting review count for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Get review count for a teacher
   * @param teacherId The teacher ID
   * @returns The review count
   */
  async getReviewCountForTeacher(teacherId: number): Promise<number> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM content.reviews r
        JOIN content.sessions s ON r.session_id = s.id
        WHERE s.teacher_id = $1
      `;

      const rows = await this.dbUtils.executeQuery<{ count: string }>(query, [teacherId]);
      return parseInt(rows[0]?.count || '0', 10);
    } catch (error) {
      logger.error(`Error getting review count for teacher ${teacherId}:`, error);
      throw error;
    }
  }

  /**
   * Check if a user has reviewed a session
   * @param userId The user ID
   * @param sessionId The session ID
   * @returns True if the user has reviewed the session
   */
  async hasUserReviewedSession(userId: number, sessionId: number): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM content.reviews
        WHERE user_id = $1 AND session_id = $2
      `;

      const rows = await this.dbUtils.executeQuery<{ count: string }>(query, [userId, sessionId]);
      return parseInt(rows[0]?.count || '0', 10) > 0;
    } catch (error) {
      logger.error(`Error checking if user ${userId} has reviewed session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Update an teacher's rating based on their reviews
   * @param client The database client
   * @param teacherId The teacher ID
   */
  private async updateTeacherRating(client: any, teacherId: number): Promise<void> {
    try {
      // Calculate the average rating
      const ratingResult = await client.query(
        `SELECT AVG(r.rating) as average_rating, COUNT(*) as review_count
        FROM content.reviews r
        JOIN content.sessions s ON r.session_id = s.id
        WHERE s.teacher_id = $1`,
        [teacherId]
      );

      const averageRating = ratingResult.rows[0]?.average_rating ? parseFloat(ratingResult.rows[0].average_rating) : null;
      const reviewCount = parseInt(ratingResult.rows[0]?.review_count || '0', 10);

      // Update the teacher's profile
      await client.query(
        `UPDATE profiles.user_profiles
        SET rating = $1, review_count = $2, updated_at = NOW()
        WHERE user_id = $3`,
        [averageRating, reviewCount, teacherId]
      );
    } catch (error) {
      logger.error(`Error updating teacher ${teacherId} rating:`, error);
      throw error;
    }
  }
}
