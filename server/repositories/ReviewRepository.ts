import { SupabaseBaseRepository } from './SupabaseBaseRepository';
import { Review, InsertReview } from '@shared/schema';
import { errorHandler } from '../services';

/**
 * Review repository for review-related database operations using Supabase client
 */
export class ReviewRepository extends SupabaseBaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('reviews');
  }

  /**
   * Get a review by ID
   * @param id - Review ID
   * @returns Review or null
   */
  async getReview(id: number): Promise<Review | null> {
    try {
      return await this.executeSingleQuery<Review>(
        `Get review ${id}`,
        () => this.client.from(this.tableName).select('*').eq('id', id),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[ReviewRepository] Error getting review ${id}:`, error);
      throw errorHandler.database(`Error getting review ${id}`, error);
    }
  }

  /**
   * Create a new review
   * @param reviewData - Review data
   * @returns Created review
   */
  async createReview(reviewData: InsertReview): Promise<Review> {
    try {
      const reviews = await this.executeInsert<Review>('Create review', reviewData);
      if (reviews.length === 0) {
        throw errorHandler.internal('Failed to create review');
      }
      return reviews[0];
    } catch (error) {
      console.error('[ReviewRepository] Error creating review:', error);
      throw errorHandler.database('Error creating review', error);
    }
  }

  /**
   * Update a review
   * @param id - Review ID
   * @param updateData - Data to update
   * @returns Updated review
   */
  async updateReview(id: number, updateData: Partial<Review>): Promise<Review> {
    try {
      const reviews = await this.executeUpdate<Review>(
        `Update review ${id}`,
        updateData,
        (query) => query.eq('id', id)
      );

      if (reviews.length === 0) {
        throw errorHandler.notFound(`Review ${id} not found`);
      }

      return reviews[0];
    } catch (error) {
      console.error(`[ReviewRepository] Error updating review ${id}:`, error);
      throw errorHandler.database(`Error updating review ${id}`, error);
    }
  }

  /**
   * Get reviews for a session with user details
   * @param sessionId - Session ID
   * @returns Array of reviews with user details
   */
  async getSessionReviews(sessionId: number): Promise<any[]> {
    try {
      return await this.executeQuery<any>(
        `Get session ${sessionId} reviews`,
        () => this.client
          .from(this.tableName)
          .select(`
            *,
            session:sessions(
              id,
              title,
              teacher_id
            ),
            reviewer:user_profiles!user_id(
              id,
              name,
              username,
              avatar
            )
          `)
          .eq('session_id', sessionId)
          .order('created_at', { ascending: false }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[ReviewRepository] Error getting reviews for session ${sessionId}:`, error);
      throw errorHandler.database(`Error getting reviews for session ${sessionId}`, error);
    }
  }

  /**
   * Get reviews for a user (as reviewer)
   * @param userId - User ID
   * @returns Array of reviews with session details
   */
  async getUserReviews(userId: string): Promise<any[]> {
    try {
      return await this.executeQuery<any>(
        `Get user ${userId} reviews`,
        () => this.client
          .from(this.tableName)
          .select(`
            *,
            session:sessions(
              id,
              title,
              teacher:user_profiles!teacher_id(
                id,
                name,
                username,
                avatar
              )
            )
          `)
          .eq('user_id', userId)
          .order('created_at', { ascending: false }),
        60 // Cache for 1 minute
      );
    } catch (error) {
      console.error(`[ReviewRepository] Error getting reviews for user ${userId}:`, error);
      throw errorHandler.database(`Error getting reviews for user ${userId}`, error);
    }
  }

  /**
   * Get average rating for a session
   * @param sessionId - Session ID
   * @returns Average rating or null
   */
  async getSessionAverageRating(sessionId: number): Promise<number | null> {
    try {
      const result = await this.executeQuery<{ rating: number }>(
        `Get session ${sessionId} average rating`,
        () => this.client
          .from(this.tableName)
          .select('rating')
          .eq('session_id', sessionId),
        300 // Cache for 5 minutes
      );

      if (result.length === 0) return null;

      const sum = result.reduce((acc, review) => acc + review.rating, 0);
      return sum / result.length;
    } catch (error) {
      console.error(`[ReviewRepository] Error getting average rating for session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Get review count for a session
   * @param sessionId - Session ID
   * @returns Review count
   */
  async getSessionReviewCount(sessionId: number): Promise<number> {
    try {
      const result = await this.executeQuery<Review>(
        `Get session ${sessionId} review count`,
        () => this.client
          .from(this.tableName)
          .select('id', { count: 'exact' })
          .eq('session_id', sessionId),
        300 // Cache for 5 minutes
      );

      return result.length;
    } catch (error) {
      console.error(`[ReviewRepository] Error getting review count for session ${sessionId}:`, error);
      return 0;
    }
  }

  /**
   * Check if user has reviewed a session
   * @param userId - User ID
   * @param sessionId - Session ID
   * @returns True if user has reviewed the session
   */
  async hasUserReviewedSession(userId: string, sessionId: number): Promise<boolean> {
    try {
      const reviews = await this.executeQuery<Review>(
        `Check if user ${userId} reviewed session ${sessionId}`,
        () => this.client
          .from(this.tableName)
          .select('id')
          .eq('user_id', userId)
          .eq('session_id', sessionId),
        60 // Cache for 1 minute
      );

      return reviews.length > 0;
    } catch (error) {
      console.error(`[ReviewRepository] Error checking if user ${userId} reviewed session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * Delete a review
   * @param id - Review ID
   * @returns Deleted review
   */
  async deleteReview(id: number): Promise<Review> {
    try {
      const reviews = await this.executeDelete<Review>(
        `Delete review ${id}`,
        (query) => query.eq('id', id)
      );

      if (reviews.length === 0) {
        throw errorHandler.notFound(`Review ${id} not found`);
      }

      return reviews[0];
    } catch (error) {
      console.error(`[ReviewRepository] Error deleting review ${id}:`, error);
      throw errorHandler.database(`Error deleting review ${id}`, error);
    }
  }
}

export default ReviewRepository;
