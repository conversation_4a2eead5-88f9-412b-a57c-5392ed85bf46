/**
 * Robust Database Module
 * 
 * This module exports a configured instance of the robust database connection
 * manager and database service for use throughout the application.
 */

import { RobustConnectionManager, createConnectionManager } from './RobustConnectionManager';
import { DatabaseService, createDatabaseService } from './DatabaseService';
import { promises as dns } from 'dns';

// Get the database connection string from environment variables
let connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Fix hostname if it contains the db. prefix that doesn't resolve
if (connectionString.includes('db.akcknfwrkbzazrytpkng.supabase.co')) {
  connectionString = connectionString.replace('db.akcknfwrkbzazrytpkng.supabase.co', 'db.frksndjujrbjhlrcjvtf.supabase.co');
  console.log('[Database] Updated connection string to new Supabase project');
}

// Try alternate ports if standard port is used
if (connectionString.includes('akcknfwrkbzazrytpkng.supabase.co:5432')) {
  console.log('[Database] Using new Supabase project instead of old one');
  connectionString = connectionString.replace('akcknfwrkbzazrytpkng.supabase.co:5432', 'db.frksndjujrbjhlrcjvtf.supabase.co:5432');
}

// Parse the database name from the connection string
try {
  const dbName = connectionString.split('/').pop()?.split('?')[0];
  console.log(`[Database] Parsed database name: ${dbName}`);

  // Extract host and user from connection string
  const hostMatch = connectionString.match(/@([^:@/]+)/);
  const userMatch = connectionString.match(/\/\/([^:@]+):/);
  const portMatch = connectionString.match(/:(\d+)\//);

  if (hostMatch && userMatch) {
    const host = hostMatch[1];
    const user = userMatch[1];
    const port = portMatch ? portMatch[1] : 'default';
    console.log(`[Database] Connecting to PostgreSQL: ${host} (port: ${port}) as ${user} to database ${dbName}`);

    // Test hostname resolution
    dns.lookup(host).then(({ address, family }) => {
      console.log(`[Database] DNS resolution successful: ${host} -> ${address} (IPv${family})`);
    }).catch((err) => {
      console.error(`[Database] DNS resolution failed: ${err.message}`);
    });
  }

  console.log(`[Database] SSL enabled: ${process.env.NODE_ENV === 'production' || connectionString.includes('ssl')}`);
} catch (error) {
  console.error('[Database] Error parsing database connection string:', error);
}

// Create the connection manager
const connectionManager = createConnectionManager({
  connectionString,
  maxConnections: 10,  // Reduced to prevent too many connections
  minConnections: 1,   // Minimum connections in the pool
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 15000,  // Increased timeout
  maxRetries: 5,
  retryDelayMs: 2000,  // Increased retry delay
  enableSsl: true,
  rejectUnauthorized: false
});

// Create the database service
const databaseService = createDatabaseService(connectionManager);

// Set up event listeners
connectionManager.on('error', (error) => {
  console.error('[Database] Connection error:', error.message);
});

connectionManager.on('reconnected', () => {
  console.log('[Database] Successfully reconnected to database');

  // Reset search path after reconnection
  databaseService.setSearchPath([
    'public',
    'auth',
    'profiles',
    'content',
    'bookings',
    'messaging',
    'backup'
  ]).catch(err => {
    console.error('[Database] Error setting search path after reconnection:', err);
  });
});

// Initialize the database
async function initializeDatabase() {
  try {
    // Check database health
    const health = await databaseService.checkHealth();
    console.log(`[Database] Connected successfully. Server time: ${health.time} (Status: ${health.status})`);

    // If using Supabase fallback, skip PostgreSQL-specific operations
    if (databaseService.isUsingSupabaseFallback()) {
      console.log('[Database] Using Supabase fallback mode - skipping PostgreSQL-specific operations');
      return true;
    }

    // Set search path (only for PostgreSQL)
    await databaseService.setSearchPath([
      'public',
      'auth',
      'profiles',
      'content',
      'bookings',
      'messaging',
      'backup'
    ]);

    // Check if required tables exist
    const tableChecks = {
      users_exist: await databaseService.tableExists('auth', 'users'),
      sessions_exist: await databaseService.tableExists('content', 'sessions'),
      profiles_exist: await databaseService.tableExists('profiles', 'user_profiles'),
      bookings_exist: await databaseService.tableExists('bookings', 'bookings'),
      messaging_exist: await databaseService.tableExists('messaging', 'messages')
    };

    console.log('[Database] Table check results:', tableChecks);

    if (Object.values(tableChecks).every(exists => exists)) {
      console.log('[Database] Database tables verified successfully');
    } else {
      console.warn('[Database] Some required tables are missing');
    }

    // Count sessions (only for PostgreSQL)
    try {
      const sessionCount = await databaseService.query('SELECT COUNT(*) FROM content.sessions');
      console.log(`[Database] Found ${sessionCount.rows[0].count} sessions in database`);
    } catch (error: unknown) {
      console.warn('[Database] Could not count sessions:', error instanceof Error ? error.message : String(error));
    }

    console.log('[Database] Database initialized successfully');
    return true;
  } catch (error: unknown) {
    console.error('[Database] Initialization error:', error instanceof Error ? error : String(error));

    // Try enabling Supabase fallback
    try {
      console.log('[Database] Attempting to enable Supabase fallback...');
      databaseService.enableSupabaseFallback();

      // Test the fallback
      const health = await databaseService.checkHealth();
      console.log(`[Database] Supabase fallback enabled successfully. Status: ${health.status}`);
      return true;
    } catch (fallbackError) {
      console.error('[Database] Supabase fallback also failed:', fallbackError);
      return false;
    }
  }
}

// Export the database service and connection manager
export {
  connectionManager,
  databaseService,
  initializeDatabase
};

// Default export for convenience
export default databaseService;
