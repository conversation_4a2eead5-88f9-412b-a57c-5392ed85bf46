import { Pool, PoolConfig } from 'pg';

/**
 * Database connection manager
 * Handles database connection pooling and configuration
 */
export class ConnectionManager {
  private static instance: ConnectionManager;
  private pool: Pool;

  /**
   * Private constructor to enforce singleton pattern
   * @param config - Pool configuration
   */
  private constructor(config?: PoolConfig) {
    const connectionString = process.env.DATABASE_URL;

    if (!connectionString) {
      console.error('DATABASE_URL environment variable is not set');
      // Depending on desired behavior, you might want to throw an error or exit here
      // For now, proceeding with undefined connectionString will likely cause a pg error
    }

    // Parse the database name from the connection string if it exists
    const databaseName = connectionString ? connectionString.split('/').pop()?.split('?')[0] : undefined;

    // Default configuration
    const defaultConfig: PoolConfig = {
      connectionString: connectionString,
      database: databaseName, // Explicitly set the database name
      ssl: { rejectUnauthorized: false }, // Always use SSL for Supabase
      // Optimized connection pool settings
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection cannot be established
    };

    // Create pool with provided config or default config
    this.pool = new Pool(config || defaultConfig);

    // Log pool events
    this.setupPoolEventHandlers();
  }

  /**
   * Get singleton instance
   * @param config - Pool configuration (only used if instance doesn't exist)
   * @returns ConnectionManager instance
   */
  public static getInstance(config?: PoolConfig): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager(config);
    }
    return ConnectionManager.instance;
  }

  /**
   * Get database pool
   * @returns Database pool
   */
  public getPool(): Pool {
    return this.pool;
  }

  /**
   * Setup pool event handlers
   */
  private setupPoolEventHandlers(): void {
    this.pool.on('connect', (client) => {
      console.log('[Database] New client connected to database');
    });

    this.pool.on('error', (err, client) => {
      console.error('[Database] Unexpected error on idle client', err);
    });

    this.pool.on('remove', (client) => {
      console.log('[Database] Client removed from pool');
    });
  }

  /**
   * Close all connections in the pool
   */
  public async closePool(): Promise<void> {
    console.log('[Database] Closing connection pool');
    await this.pool.end();
  }

  /**
   * Check database connection
   * @returns True if connection is successful
   */
  public async checkConnection(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      client.release();
      console.log('[Database] Connection check successful');
      return true;
    } catch (error) {
      console.error('[Database] Connection check failed:', error);
      return false;
    }
  }

  /**
   * Get database statistics
   * @returns Database statistics
   */
  public getPoolStatistics(): any {
    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount
    };
  }
}

export default ConnectionManager;
