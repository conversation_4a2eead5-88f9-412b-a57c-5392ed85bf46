/**
 * Robust Database Connection Manager
 * 
 * This class provides a resilient database connection manager with:
 * - Proper connection pooling
 * - Automatic reconnection
 * - Circuit breaker pattern
 * - Connection health monitoring
 * - Query retry mechanism
 */

import { Pool, PoolClient, QueryResult } from 'pg';
import EventEmitter from 'events';

// Configuration interface
interface ConnectionConfig {
  connectionString: string;
  maxConnections?: number;
  minConnections?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
  maxRetries?: number;
  retryDelayMs?: number;
  enableSsl?: boolean;
  rejectUnauthorized?: boolean;
}

// Circuit breaker states
enum CircuitState {
  CLOSED, // Normal operation
  OPEN,   // Not allowing connections
  HALF_OPEN // Testing if connections can be restored
}

export class RobustConnectionManager extends EventEmitter {
  private static instance: RobustConnectionManager;
  private pool: Pool;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxRetries: number;
  private retryDelayMs: number;
  private circuitState: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private failureThreshold: number = 10;
  private resetTimeout: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(config: ConnectionConfig) {
    super();

    // Extract configuration values with defaults
    const {
      connectionString,
      maxConnections = 20,
      minConnections = 0,
      idleTimeoutMillis = 60000,
      connectionTimeoutMillis = 30000,
      maxRetries = 10,
      retryDelayMs = 3000,
      enableSsl = process.env.NODE_ENV === 'production' || connectionString.includes('ssl'),
      rejectUnauthorized = false
    } = config;

    this.maxRetries = maxRetries;
    this.retryDelayMs = retryDelayMs;

    // Create the connection pool
    this.pool = new Pool({
      connectionString,
      max: maxConnections,
      min: minConnections,
      idleTimeoutMillis,
      connectionTimeoutMillis,
      ssl: { rejectUnauthorized: false },
      application_name: 'sessionhub-development',
      keepAlive: true,
      keepAliveInitialDelayMillis: 30000,
    });

    // Set up event handlers
    this.setupEventHandlers();

    // Start health check
    this.startHealthCheck();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(config?: ConnectionConfig): RobustConnectionManager {
    if (!RobustConnectionManager.instance) {
      if (!config) {
        throw new Error('Configuration must be provided when creating the first instance');
      }
      RobustConnectionManager.instance = new RobustConnectionManager(config);
    }
    return RobustConnectionManager.instance;
  }

  /**
   * Set up event handlers for the pool
   */
  private setupEventHandlers(): void {
    this.pool.on('error', (err: Error) => {
      console.error('[Database] Pool error:', err.message);
      this.handleFailure(err);
      this.emit('error', err);
    });

    this.pool.on('connect', (client: PoolClient) => {
      console.log('[Database] New client connected');

      client.on('error', (err: Error) => {
        console.error('[Database] Client error:', err.message);
        this.handleFailure(err);
      });
    });
  }

  /**
   * Start periodic health check
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        if (this.circuitState !== CircuitState.OPEN) {
          await this.checkConnection();
        }
      } catch (error) {
        console.error('[Database] Health check failed:', error);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Check if the connection is working
   */
  private async checkConnection(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      try {
        await client.query('SELECT 1');
        this.isConnected = true;

        // Reset failure count on successful connection
        if (this.circuitState === CircuitState.HALF_OPEN) {
          this.closeCircuit();
        }

        return true;
      } finally {
        client.release();
      }
    } catch (error) {
      this.isConnected = false;
      this.handleFailure(error);
      return false;
    }
  }

  /**
   * Handle connection failure
   */
  private handleFailure(error: any): void {
    this.failureCount++;
    console.error(`[Database] Connection failure #${this.failureCount}:`, error.message);

    // If we've reached the threshold, open the circuit
    if (this.failureCount >= this.failureThreshold && this.circuitState === CircuitState.CLOSED) {
      this.openCircuit();
    }
  }

  /**
   * Open the circuit (stop allowing connections)
   */
  private openCircuit(): void {
    console.log('[Database] Circuit breaker opened - stopping new connections');
    this.circuitState = CircuitState.OPEN;

    // Schedule a reset to half-open state
    this.resetTimeout = setTimeout(() => {
      this.halfOpenCircuit();
    }, 5000); // Wait 5 seconds before trying again
  }

  /**
   * Set circuit to half-open (test if connections can be restored)
   */
  private halfOpenCircuit(): void {
    console.log('[Database] Circuit breaker half-open - testing connection');
    this.circuitState = CircuitState.HALF_OPEN;

    // Try to check the connection
    this.checkConnection().catch(err => {
      console.error('[Database] Connection test failed in half-open state:', err.message);
      this.openCircuit(); // Back to open if it fails
    });
  }

  /**
   * Close the circuit (normal operation)
   */
  private closeCircuit(): void {
    console.log('[Database] Circuit breaker closed - resuming normal operation');
    this.circuitState = CircuitState.CLOSED;
    this.failureCount = 0;
    this.reconnectAttempts = 0;
    this.emit('reconnected');
  }

  /**
   * Execute a query with retry logic
   */
  public async query<T>(text: string, params: any[] = []): Promise<QueryResult<T>> {
    // If circuit is open, fail fast
    if (this.circuitState === CircuitState.OPEN) {
      throw new Error('Database circuit breaker is open - not accepting new connections');
    }

    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt < this.maxRetries) {
      try {
        attempt++;
        const start = Date.now();
        const result = await this.pool.query<T>(text, params);
        const duration = Date.now() - start;

        // Log slow queries
        if (duration > 500) {
          console.log(`[Database] Slow query (${duration}ms): ${text.substring(0, 100)}...`);
        }

        // If we're in half-open state and query succeeds, close the circuit
        if (this.circuitState === CircuitState.HALF_OPEN) {
          this.closeCircuit();
        }

        return result;
      } catch (error: any) {
        lastError = error;

        // Check if this is a connection error
        const isConnectionError = error.message.includes('terminating connection') ||
          error.message.includes('Connection terminated') ||
          error.message.includes('cannot perform') ||
          error.message.includes('connection is closed') ||
          error.message.includes('pool is draining') ||
          error.message.includes('db_termination');

        if (isConnectionError) {
          this.handleFailure(error);

          // If we still have retries left, wait before retrying
          if (attempt < this.maxRetries) {
            const delay = this.retryDelayMs * Math.pow(2, attempt - 1); // Exponential backoff
            console.log(`[Database] Connection error, retrying in ${delay}ms (attempt ${attempt}/${this.maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        } else {
          // For non-connection errors, don't retry
          break;
        }
      }
    }

    // If we get here, all retries failed
    throw lastError || new Error('Query failed after maximum retry attempts');
  }

  /**
   * Get a client from the pool with retry logic
   */
  public async getClient(): Promise<PoolClient> {
    // If circuit is open, fail fast
    if (this.circuitState === CircuitState.OPEN) {
      throw new Error('Database circuit breaker is open - not accepting new connections');
    }

    let lastError: Error | null = null;
    let attempt = 0;

    while (attempt < this.maxRetries) {
      try {
        attempt++;
        return await this.pool.connect();
      } catch (error: any) {
        lastError = error;

        // Check if this is a connection error
        const isConnectionError = error.message.includes('terminating connection') ||
          error.message.includes('Connection terminated') ||
          error.message.includes('cannot perform') ||
          error.message.includes('connection is closed') ||
          error.message.includes('pool is draining') ||
          error.message.includes('db_termination');

        if (isConnectionError) {
          this.handleFailure(error);

          // If we still have retries left, wait before retrying
          if (attempt < this.maxRetries) {
            const delay = this.retryDelayMs * Math.pow(2, attempt - 1); // Exponential backoff
            console.log(`[Database] Connection error, retrying in ${delay}ms (attempt ${attempt}/${this.maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        } else {
          // For non-connection errors, don't retry
          break;
        }
      }
    }

    // If we get here, all retries failed
    throw lastError || new Error('Failed to get client after maximum retry attempts');
  }

  /**
   * Close the connection pool
   */
  public async end(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.resetTimeout) {
      clearTimeout(this.resetTimeout);
      this.resetTimeout = null;
    }

    await this.pool.end();
    this.isConnected = false;
  }
}

// Export a factory function to create the connection manager
export function createConnectionManager(config: ConnectionConfig): RobustConnectionManager {
  return RobustConnectionManager.getInstance(config);
}
