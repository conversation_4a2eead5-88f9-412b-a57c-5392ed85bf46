/**
 * Database Service
 * 
 * This service provides a high-level interface for database operations
 * using the robust connection manager with Supabase fallback.
 */

import { QueryResult } from 'pg';
import { RobustConnectionManager } from './RobustConnectionManager';
import { supabaseAdmin } from '../lib/supabase';

export class DatabaseService {
  private connectionManager: RobustConnectionManager;
  private static instance: DatabaseService;
  private useSupabaseFallback = false;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor(connectionManager: RobustConnectionManager) {
    this.connectionManager = connectionManager;
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(connectionManager?: RobustConnectionManager): DatabaseService {
    if (!DatabaseService.instance) {
      if (!connectionManager) {
        throw new Error('Connection manager must be provided when creating the first instance');
      }
      DatabaseService.instance = new DatabaseService(connectionManager);
    }
    return DatabaseService.instance;
  }

  /**
   * Execute a query with Supabase fallback
   */
  public async query<T>(text: string, params: any[] = []): Promise<QueryResult<T>> {
    try {
      // Try PostgreSQL connection first if not in fallback mode
      if (!this.useSupabaseFallback) {
        return await this.connectionManager.query<T>(text, params);
      }
    } catch (error) {
      console.warn('[DatabaseService] PostgreSQL query failed, falling back to Supabase:', error);
      this.useSupabaseFallback = true;
    }

    // Fall back to Supabase for specific queries
    try {
      return await this.executeWithSupabase<T>(text, params);
    } catch (supabaseError) {
      console.error('[DatabaseService] Both PostgreSQL and Supabase queries failed:', supabaseError);
      throw supabaseError;
    }
  }

  /**
   * Execute query using Supabase client
   */
  private async executeWithSupabase<T>(text: string, params: any[] = []): Promise<QueryResult<T>> {
    console.log('[DatabaseService] Executing query via Supabase fallback:', text.slice(0, 100));

    // For simple SELECT queries, try to convert to Supabase query
    if (text.toLowerCase().includes('select now()')) {
      const result = {
        rows: [{ now: new Date() }],
        rowCount: 1,
        command: 'SELECT',
        oid: 0,
        fields: []
      } as QueryResult<T>;
      return result;
    }

    // For other queries, we'll throw an error since we can't convert all SQL to Supabase
    throw new Error('Supabase fallback not implemented for this query type: ' + text.slice(0, 50));
  }

  /**
   * Execute a transaction
   */
  public async transaction<T>(callback: (query: (text: string, params?: any[]) => Promise<QueryResult<any>>) => Promise<T>): Promise<T> {
    if (this.useSupabaseFallback) {
      console.warn('[DatabaseService] Transactions not supported in Supabase fallback mode');
      throw new Error('Transactions not supported when using Supabase fallback');
    }

    const client = await this.connectionManager.getClient();

    try {
      await client.query('BEGIN');

      // Create a query function that uses this client
      const query = async (text: string, params: any[] = []) => {
        return client.query(text, params);
      };

      // Execute the callback
      const result = await callback(query);

      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('[DatabaseService] Transaction error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Check if a table exists
   */
  public async tableExists(schema: string, tableName: string): Promise<boolean> {
    if (this.useSupabaseFallback) {
      // For Supabase fallback, assume tables exist or check via introspection
      try {
        const { data, error } = await supabaseAdmin
          .from(tableName)
          .select('*')
          .limit(1);

        return !error || !error.message.includes('relation') && !error.message.includes('does not exist');
      } catch {
        return false;
      }
    }

    const result = await this.query<{ exists: boolean }>(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = $1 AND table_name = $2
      ) as exists`,
      [schema, tableName]
    );

    return result.rows[0].exists;
  }

  /**
   * Get the current database time
   */
  public async getDatabaseTime(): Promise<Date> {
    if (this.useSupabaseFallback) {
      return new Date(); // Use local time as fallback
    }

    const result = await this.query<{ now: Date }>('SELECT NOW() as now');
    return result.rows[0].now;
  }

  /**
   * Set the search path
   */
  public async setSearchPath(schemas: string[]): Promise<void> {
    if (this.useSupabaseFallback) {
      console.log('[DatabaseService] Search path not supported in Supabase fallback mode');
      return;
    }

    const searchPath = schemas.join(', ');
    await this.query(`SET search_path TO ${searchPath}`);
    console.log(`[DatabaseService] Search path set to: ${searchPath}`);
  }

  /**
   * Check database health
   */
  public async checkHealth(): Promise<{ status: string, time: Date }> {
    try {
      const time = await this.getDatabaseTime();
      const status = this.useSupabaseFallback ? 'healthy (supabase-fallback)' : 'healthy';
      return { status, time };
    } catch (error) {
      console.error('[DatabaseService] Health check failed:', error);

      // Try Supabase as final fallback
      try {
        this.useSupabaseFallback = true;
        const time = await this.getDatabaseTime();
        return { status: 'healthy (supabase-fallback)', time };
      } catch (supabaseError) {
        throw new Error('Database health check failed');
      }
    }
  }

  /**
   * Check if using Supabase fallback
   */
  public isUsingSupabaseFallback(): boolean {
    return this.useSupabaseFallback;
  }

  /**
   * Force Supabase fallback mode
   */
  public enableSupabaseFallback(): void {
    this.useSupabaseFallback = true;
    console.log('[DatabaseService] Supabase fallback mode enabled');
  }

  /**
   * Close the database connection
   */
  public async close(): Promise<void> {
    if (!this.useSupabaseFallback) {
      await this.connectionManager.end();
    }
  }
}

// Export a factory function to create the database service
export function createDatabaseService(connectionManager: RobustConnectionManager): DatabaseService {
  return DatabaseService.getInstance(connectionManager);
}
