/**
 * Utility functions for default user images
 */

// Supabase storage configuration
const SUPABASE_PROJECT_URL = process.env.SUPABASE_URL || 'https://frksndjujrbjhlrcjvtf.supabase.co';
const STORAGE_BUCKET = 'profiles'; // Using Supabase storage bucket

// Base URL for Supabase Storage objects
const SUPABASE_STORAGE_URL = `${SUPABASE_PROJECT_URL}/storage/v1/object/public/${STORAGE_BUCKET}`;

/**
 * Get a default avatar URL for a new user
 * @param userId The user ID
 * @returns URL to a default avatar image
 */
export function getDefaultAvatarUrl(userId: number): string {
  // Use a deterministic approach to select an avatar based on user ID
  const avatarTypes = ['professional', 'creative', 'casual', 'minimal'];
  const typeIndex = userId % avatarTypes.length;
  const numberIndex = (userId % 2) + 1; // Either 1 or 2

  const avatarType = avatarTypes[typeIndex];
  return `${SUPABASE_STORAGE_URL}/avatars/${avatarType}-${numberIndex}.jpg`;
}

/**
 * Get a default cover photo URL for a new user
 * @param userId The user ID (optional)
 * @returns URL to a default cover photo
 */
export function getDefaultCoverPhotoUrl(userId?: number): string {
  // For now, use a single default cover photo
  return `${SUPABASE_STORAGE_URL}/placeholders/default-cover.jpg`;
}

// Diverse avatar URLs from Unsplash - different people, ethnicities, ages, genders
export const DIVERSE_AVATAR_URLS = [
  'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&auto=format&fit=crop', // Woman, professional
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop', // Man, casual
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&auto=format&fit=crop', // Woman, friendly
  'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=400&auto=format&fit=crop', // Woman, warm smile
  'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&auto=format&fit=crop', // Woman, business
  'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&auto=format&fit=crop', // Man, professional
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&auto=format&fit=crop', // Man, bearded
  'https://images.unsplash.com/photo-1545167622-3a6ac756afa4?w=400&auto=format&fit=crop', // Man, mature
  'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&auto=format&fit=crop', // Woman, curly hair
  'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&auto=format&fit=crop', // Woman, young
  'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&auto=format&fit=crop', // Woman, artistic
  'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=400&auto=format&fit=crop', // Man, confident
  'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=400&auto=format&fit=crop', // Man, modern
  'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400&auto=format&fit=crop', // Man, avatar style
  'https://images.unsplash.com/photo-1542190891-2093d38760f2?w=400&auto=format&fit=crop', // Woman, professional
  'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=400&auto=format&fit=crop', // Man, smiling
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&auto=format&fit=crop', // Woman, elegant
  'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&auto=format&fit=crop', // Man, business
  'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&auto=format&fit=crop', // Woman, creative
  'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400&auto=format&fit=crop', // Woman, friendly
];

// Diverse cover photo URLs
export const DIVERSE_COVER_URLS = [
  'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop', // Gradient
  'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop', // Purple gradient
  'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop', // Abstract colors
  'https://images.unsplash.com/photo-1559251606-c623743a6d76?w=1200&auto=format&fit=crop', // Soft gradient
  'https://images.unsplash.com/photo-1566041510639-8d95a2490bfb?w=1200&auto=format&fit=crop', // Texture
  'https://images.unsplash.com/photo-1523580494863-6f3031224c94?w=1200&auto=format&fit=crop', // Pattern
  'https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=1200&auto=format&fit=crop', // Nature
  'https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=1200&auto=format&fit=crop', // Abstract
  'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=1200&auto=format&fit=crop', // Minimal
  'https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1200&auto=format&fit=crop', // Office
  'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200&auto=format&fit=crop', // Technology
  'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200&auto=format&fit=crop', // Meeting
  'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=1200&auto=format&fit=crop', // Workspace
  'https://images.unsplash.com/photo-1531482615713-2afd69097998?w=1200&auto=format&fit=crop', // Team
  'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=1200&auto=format&fit=crop', // Office space
  'https://images.unsplash.com/photo-1521791136064-7986c2920216?w=1200&auto=format&fit=crop', // Handshake
];

// Session type images - diverse within each category
export const SESSION_TYPE_IMAGES: Record<string, string[]> = {
  yoga: [
    'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&auto=format&fit=crop', // Outdoor yoga
    'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=800&auto=format&fit=crop', // Sunrise yoga
    'https://images.unsplash.com/photo-1545389336-cf090694435e?w=800&auto=format&fit=crop', // Group yoga
    'https://images.unsplash.com/photo-1599447421416-3414500d18a5?w=800&auto=format&fit=crop', // Individual pose
    'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=800&auto=format&fit=crop', // Studio yoga
  ],
  music: [
    'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&auto=format&fit=crop', // Guitar
    'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=800&auto=format&fit=crop', // Piano
    'https://images.unsplash.com/photo-1519892300165-cb5542fb47c7?w=800&auto=format&fit=crop', // Recording studio
    'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=800&auto=format&fit=crop', // Concert
    'https://images.unsplash.com/photo-1598387993441-a364f854c3e1?w=800&auto=format&fit=crop', // Music notes
  ],
  cooking: [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&auto=format&fit=crop', // Cooking class
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&auto=format&fit=crop', // Chef cooking
    'https://images.unsplash.com/photo-1556911220-bff31c812dba?w=800&auto=format&fit=crop', // Kitchen
    'https://images.unsplash.com/photo-1466637574441-749b8f19452f?w=800&auto=format&fit=crop', // Ingredients
    'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=800&auto=format&fit=crop', // Cooking together
  ],
  art: [
    'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=800&auto=format&fit=crop', // Art studio
    'https://images.unsplash.com/photo-1513364776144-60967b0f800f?w=800&auto=format&fit=crop', // Painting
    'https://images.unsplash.com/photo-1536924940846-227afb31e2a5?w=800&auto=format&fit=crop', // Art gallery
    'https://images.unsplash.com/photo-1549289524-06cf8837ace5?w=800&auto=format&fit=crop', // Drawing
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&auto=format&fit=crop', // Art supplies
  ],
  language: [
    'https://images.unsplash.com/photo-1546410531-bb4caa6b424d?w=800&auto=format&fit=crop', // Language learning
    'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&auto=format&fit=crop', // Study
    'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&auto=format&fit=crop', // Library
    'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=800&auto=format&fit=crop', // Classroom
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&auto=format&fit=crop', // Group study
  ],
  academic: [
    'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&auto=format&fit=crop', // University
    'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?w=800&auto=format&fit=crop', // Students
    'https://images.unsplash.com/photo-1509062522246-3755977927d7?w=800&auto=format&fit=crop', // Teaching
    'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&auto=format&fit=crop', // Library
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&auto=format&fit=crop', // Group learning
  ],
  professional: [
    'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&auto=format&fit=crop', // Team meeting
    'https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&auto=format&fit=crop', // Workshop
    'https://images.unsplash.com/photo-1531482615713-2afd69097998?w=800&auto=format&fit=crop', // Collaboration
    'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=800&auto=format&fit=crop', // Presentation
    'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&auto=format&fit=crop', // Business
  ],
  default: [
    'https://images.unsplash.com/photo-1497032628192-86f99bcd76bc?w=800&auto=format&fit=crop', // General learning
    'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&auto=format&fit=crop', // Collaboration
    'https://images.unsplash.com/photo-1515169067868-5387ec356754?w=800&auto=format&fit=crop', // Education
    'https://images.unsplash.com/photo-1513258496099-48168024aec0?w=800&auto=format&fit=crop', // Workshop
    'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800&auto=format&fit=crop', // Technology
  ]
};

/**
 * Get a diverse avatar URL based on a seed (user ID, teacher ID, etc)
 * @param seed A numeric or string seed to determine which avatar to use
 * @returns URL to a diverse avatar image
 */
export function getDiverseAvatarUrl(seed: number | string): string {
  const index = typeof seed === 'number' ? seed : seed.charCodeAt(0);
  return DIVERSE_AVATAR_URLS[index % DIVERSE_AVATAR_URLS.length];
}

/**
 * Get a diverse cover photo URL based on a seed
 * @param seed A numeric or string seed to determine which cover to use
 * @returns URL to a diverse cover photo
 */
export function getDiverseCoverUrl(seed: number | string): string {
  const index = typeof seed === 'number' ? seed : seed.charCodeAt(0);
  return DIVERSE_COVER_URLS[index % DIVERSE_COVER_URLS.length];
}

/**
 * Get a diverse session image URL based on type and session ID
 * @param type The session type (yoga, music, cooking, etc)
 * @param sessionId The session ID for deterministic selection
 * @returns URL to a diverse session image
 */
export function getDiverseSessionImageUrl(type: string, sessionId: string): string {
  const sessionType = type?.toLowerCase() || 'default';
  const images = SESSION_TYPE_IMAGES[sessionType] || SESSION_TYPE_IMAGES.default;

  // Use session ID to deterministically select an image
  const index = sessionId.charCodeAt(0) + sessionId.charCodeAt(sessionId.length - 1);
  return images[index % images.length];
}
