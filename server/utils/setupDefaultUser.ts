import { db } from '../db';
import { hashPassword } from './passwordHasher';

const DEFAULT_USER = {
  username: 'admin',
  password: 'password123',
  email: '<EMAIL>',
  name: 'Admin User',
};

export async function setupDefaultUser() {
  try {
    // Check if the default user exists using a direct query, not db.select
    const result = await db.query('SELECT * FROM users WHERE username = $1', [DEFAULT_USER.username]);

    // If user doesn't exist, create it
    if (result.rows.length === 0) {
      console.log('Creating default admin user...');
      const hashedPassword = await hashPassword(DEFAULT_USER.password);

      await db.query(
        'INSERT INTO auth.users (username, password, email, name, is_teacher) VALUES ($1, $2, $3, $4, $5)',
        [DEFAULT_USER.username, hashedPassword, DEFAULT_USER.email, DEFAULT_USER.name, true]
      );

      console.log('Default admin user created successfully');
    } else {
      console.log('Default admin user already exists');
    }

    // Check if we should force all sessions to be public
    // We've updated the env var to be false, but we want to be absolutely sure
    if (process.env.DEFAULT_SESSIONS_PUBLIC === 'true') {
      try {
        console.log('Setting all sessions to public for development...');

        // Use the proper database column name `is_public` (snake_case) instead of `isPublic` (camelCase)
        await db.query('UPDATE content.sessions SET is_public = true WHERE is_public = false');

        console.log('All sessions set to public');
      } catch (error) {
        console.error('Error setting sessions to public:', error);
      }
    } else {
      console.log('Respecting session privacy settings (not forcing public)');
    }
  } catch (error) {
    console.error('Error setting up default user:', error);
  }
}