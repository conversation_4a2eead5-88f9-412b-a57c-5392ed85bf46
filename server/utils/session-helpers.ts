/**
 * Helper to ensure camelCase fields in all session API responses
 * @param session Session object or array of session objects
 * @returns Session with normalized field names
 */
export function mapAutomatedMessagesField(session: any): any {
  if (!session) return session;

  // If array, map each
  if (Array.isArray(session)) return session.map(mapAutomatedMessagesField);

  // Map snake_case to camelCase if present
  if (session.automated_messages_json !== undefined) {
    session.automatedMessagesJson = session.automated_messages_json;
    delete session.automated_messages_json;
  }

  // Map image_url to imageUrl for frontend consistency
  if (session.image_url !== undefined) {
    session.imageUrl = session.image_url;
    // Keep both fields for backward compatibility
    // delete session.image_url;
  }

  // Always ensure the automatedMessagesJson field exists (null if missing)
  if (session.automatedMessagesJson === undefined) session.automatedMessagesJson = null;

  return session;
}
