/**
 * Environment Variable Loader Utility
 *
 * This module handles loading environment variables from .env files
 * and ensures consistent access across the application.
 */

import dotenv from 'dotenv';
import { join } from 'path';
import fs from 'fs';

/**
 * Initialize environment variables by loading from .env files
 * Returns true if successful, false otherwise
 */
export function loadEnvironment(): boolean {
  try {
    // Debug: Log current working directory
    console.log(`[ENV] Current working directory: ${process.cwd()}`);
    
    // Try to load environment variables from .env file in the current working directory
    const envPath = join(process.cwd(), '.env');
    console.log(`[ENV] Loading environment from: ${envPath}`);
    
    if (fs.existsSync(envPath)) {
      // Load the .env file
      const result = dotenv.config({ path: envPath });
      if (result.error) {
        console.error('[ENV] Error loading .env file:', result.error);
        return false;
      }
      
      // Clean up values with quotes (common issue when values are copy/pasted)
      cleanQuotedEnvVar('DATABASE_URL');
      cleanQuotedEnvVar('SUPABASE_URL');
      cleanQuotedEnvVar('SUPABASE_ANON_KEY');
      cleanQuotedEnvVar('SUPABASE_SERVICE_ROLE_KEY');
      
      // Debug: Log the loaded environment variables
      console.log('[ENV] Environment variables loaded successfully');
      console.log(`[ENV] NODE_ENV: ${process.env.NODE_ENV}`);
      console.log(`[ENV] DATABASE_URL: ${process.env.DATABASE_URL ? 'Set' : 'Not set'}`);
      console.log(`[ENV] SUPABASE_URL: ${process.env.SUPABASE_URL ? 'Set' : 'Not set'}`);
      
      return validateEnvironment();
    } else {
      console.error(`[ENV] No .env file found at: ${envPath}`);
      return false;
    }
  } catch (error) {
    console.error('Error loading environment variables:', error);
    return false;
  }
}

/**
 * Remove quotes from environment variable if present
 */
function cleanQuotedEnvVar(name: string): void {
  if (process.env[name] &&
    typeof process.env[name] === 'string' &&
    process.env[name]!.startsWith('"') &&
    process.env[name]!.endsWith('"')) {
    process.env[name] = process.env[name]!.slice(1, -1);
  }
}

/**
 * Validate that required environment variables are set
 * Logs information about configuration
 */
function validateEnvironment(): boolean {
  let isValid = true;

  // Validate DATABASE_URL - required
  if (process.env.DATABASE_URL) {
    try {
      const url = new URL(process.env.DATABASE_URL);
      console.log(`DATABASE_URL set: ${url.protocol}//${url.username}:****@${url.hostname}${url.pathname}`);
    } catch (e) {
      console.log('DATABASE_URL set (but has an invalid format)');
    }
  } else {
    console.error('DATABASE_URL not set - the application requires a valid database connection');
    isValid = false;
  }

  // No Redis configuration needed

  // Log general configuration
  console.log(`NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`PORT from env: ${process.env.PORT || 'not set'}`);

  return isValid;
}

// Default export for convenience
export default {
  loadEnvironment
};