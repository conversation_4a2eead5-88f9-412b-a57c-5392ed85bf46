import * as express from "express";
import { type Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { log } from "./vite";
import { storage } from "./storage";
import { join } from 'path';
import session from 'express-session';
import { config } from "./config";
import cookie from "cookie-parser";
import passport from "passport";
import { configurePassport, setupAuth, isAuthenticated as authIsAuthenticated } from "./auth";
import crypto from 'crypto';
import { Server as SocketIOServer } from 'socket.io';
import { setupSocketHandlers } from './socket';
import { pool } from './db/connection';

// Import the AvailabilityManager
import AvailabilityManager from './availability-manager';
// PostgresStorage import removed - using modular repositories instead

import { createServer as createHttpServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { z } from "zod";
import {
  insertUserSchema, insertSessionSchema, insertBookingSchema, insertReviewSchema,
  insertConversationSchema, insertMessageSchema,
  users, sessions, bookings, reviews
} from "@shared/schema";
import { invalidateUserCache } from "./auth";
import { db } from "./db";
import { sql } from "drizzle-orm";
import { invalidateCache, cacheMiddleware, healthCheck } from "./middleware"; // Added healthCheck import
import { optimizeImage, cacheOptimizedImage, generateSrcSet } from './utils/imageOptimizer';
import { randomBytes } from 'crypto';
import { hashPassword } from './utils/passwordHasher';
import { sendPasswordResetEmail } from './utils/email';
import { invalidateCache as invalidateCachePattern, invalidateSessionCache } from './middleware/cache';
import { Mutex } from 'async-mutex';
import multer from 'multer';
import { productionMessageScheduler } from './services/ProductionMessageScheduler';
import { verifyApiKey } from './middleware/api-auth';
// AWS routes removed
import { setupScheduledMessageRoutes } from './routes/scheduled-messages';
import socialRoutes from './routes/social-routes';
// NextAuth routes removed - using Passport.js only
import paymentRoutes from './routes/payment-routes';
// import teacherPaymentRoutes from './routes/teacher-payment-routes'; // File doesn't exist - commented out
import stripeConnectRoutes from './routes/stripe-connect-routes';
import authCallbackRoutes from './routes/auth-callback-routes';
import socialAuthRoutes from './routes/social-auth-routes';
import emailVerificationRoutes from './routes/email-verification-routes';
import passwordResetRoutes from './routes/password-reset-routes';
// uploadRoutes removed - using Supabase storage instead
import reviewRoutes from './routes/reviewRoutes';
import devToolsRoutes from './routes/dev-tools-routes';
import teacherRoutes from './routes/teacher-routes';

// Create a mutex set for handling concurrent updates
const activeLocks = new Set<string>();

// Create a mutex for session operations to prevent race conditions
const sessionMutex = new Mutex();

// Generates a token for password reset
function generatePasswordResetToken(userId: number) {
  const token = randomBytes(32).toString('hex');
  const expiry = new Date(Date.now() + 3600000); // 1 hour from now
  return { token, expiry };
}

// Authentication middleware to protect routes
// Using authIsAuthenticated imported from auth.ts

// Admin-only middleware
function isAdmin(req: Request, res: Response, next: NextFunction) {
  if (req.isAuthenticated() && req.user && (req.user as any).role === 'admin') {
    return next();
  }
  res.status(403).json({ message: "Forbidden - Admin access required" });
}

// Helper function to create system messages in conversations for user activities
async function createActivityMessage(
  activity: 'booking' | 'cancellation' | 'payment' | 'session_update' | 'note',
  userId: number,
  targetUserId: number,
  sessionId: number,
  details: any
): Promise<void> {
  try {
    // First find or create a conversation between these users
    let conversation;
    const participantIds = [userId, targetUserId].sort((a, b) => a - b); // Sort for consistency

    // Get all conversations for the first user
    // TODO: Implement conversation creation logic here
    console.log('Activity message creation:', { activity, userId, targetUserId, sessionId, details });
  } catch (error) {
    console.error('Error creating activity message:', error);
  }
}

// Export the function and any other necessary exports
export { createActivityMessage };