import { eq } from 'drizzle-orm';
import { db } from '../connection';
import { users, teacherProfiles } from '../models/schema';
import { cache } from '../cache';
import { User, InsertUser, UserWithProfile } from '@shared/schema';
import { queryMonitor } from '../connection';

export class UserQueries {
  async getUser(id: number): Promise<User | undefined> {
    const cachedUser = cache.getUser(id);
    if (cachedUser) return cachedUser;

    const startTime = Date.now();
    const result = await db.select().from(users).where(eq(users.id, id));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getUser', duration, result.length);

    const user = result[0];
    if (user) {
      cache.setUser(id, user);
    }
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const startTime = Date.now();
    const result = await db
      .select()
      .from(users)
      .leftJoin('profiles.user_profiles', 'users.id', 'profiles.user_profiles.user_id')
      .where('profiles.user_profiles.username', username);
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getUserByUsername', duration, result.length);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const startTime = Date.now();
    const result = await db.select().from(users).where(eq(users.email, email));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getUserByEmail', duration, result.length);
    return result[0];
  }

  async createUser(user: InsertUser): Promise<User> {
    const startTime = Date.now();
    const result = await db.insert(users).values(user).returning();
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('createUser', duration, result.length);

    const newUser = result[0];
    cache.setUser(newUser.id, newUser);
    return newUser;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const startTime = Date.now();
    const result = await db
      .update(users)
      .set({ ...userData, updated_at: new Date() })
      .where(eq(users.id, id))
      .returning();
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('updateUser', duration, result.length);

    const updatedUser = result[0];
    if (updatedUser) {
      cache.setUser(id, updatedUser);
    }
    return updatedUser;
  }

  async getUserWithProfile(id: number): Promise<UserWithProfile | undefined> {
    const startTime = Date.now();
    const result = await db
      .select({
        user: users,
        profile: teacherProfiles
      })
      .from(users)
      .leftJoin(teacherProfiles, eq(users.id, teacherProfiles.user_id))
      .where(eq(users.id, id));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getUserWithProfile', duration, result.length);

    if (!result[0]) return undefined;

    return {
      ...result[0].user,
      profile: result[0].profile
    };
  }

  async getTopTeachers(limit: number): Promise<User[]> {
    const cachedTeachers = cache.getTopTeachers();
    if (cachedTeachers) return cachedTeachers;

    const startTime = Date.now();
    const result = await db
      .select()
      .from(users)
      .where(eq(users.isTeacher, true))
      .orderBy(users.rating)
      .limit(limit);
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getTopTeachers', duration, result.length);

    cache.setTopTeachers(result);
    return result;
  }
}

export const userQueries = new UserQueries(); 