import { eq, and, or, gt, lt, sql } from 'drizzle-orm';
import { db } from '../connection';
import { sessions, users } from '../models/schema';
import { cache } from '../cache';
import { Session, InsertSession, SessionWithTeacher, User } from '@shared/schema';
import { queryMonitor } from '../connection';

export class SessionQueries {
  async getSession(id: number): Promise<Session | undefined> {
    const cachedSession = cache.getSession(id);
    if (cachedSession) return cachedSession;

    const startTime = Date.now();
    const result = await db.select().from(sessions).where(eq(sessions.id, id));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getSession', duration, result.length);

    const session = result[0];
    if (session) {
      // Get teacher data for caching
      const teacherResult = await db
        .select()
        .from(users)
        .where(eq(users.id, session.teacherId));
      
      if (teacherResult[0]) {
        const sessionWithTeacher = {
          ...session,
          teacher: teacherResult[0]
        } as SessionWithTeacher;
        cache.setSession(id, sessionWithTeacher);
      }
    }
    return session as Session;
  }

  /**
   * Get a session with its teacher information
   * If forceRefresh is true, bypasses the cache and fetches directly from the database
   */
  async getSessionWithTeacher(id: number, forceRefresh = false): Promise<SessionWithTeacher | undefined> {
    console.log(`[DB] getSessionWithTeacher(${id}, forceRefresh=${forceRefresh})`);
    
    // Check cache first (unless forceRefresh is true)
    if (!forceRefresh) {
      const cachedSession = cache.getSession(id);
      if (cachedSession) {
        console.log(`[DB] Session ${id} found in cache`);
        return cachedSession;
      }
    } else {
      console.log(`[DB] Force refreshing session ${id} from database - bypassing cache`);
      // Invalidate existing cache entry if we're doing a force refresh
      cache.invalidateSession(id);
    }
    
    const startTime = Date.now();
    const result = await db
      .select({
        session: sessions,
        teacher: users
      })
      .from(sessions)
      .leftJoin(users, eq(sessions.teacherId, users.id))
      .where(eq(sessions.id, id));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getSessionWithTeacher', duration, result.length);

    if (!result[0] || !result[0].teacher) return undefined;

    const sessionWithTeacher = {
      ...result[0].session,
      teacher: result[0].teacher
    } as SessionWithTeacher;
    
    // Save to cache
    cache.setSession(id, sessionWithTeacher);
    
    console.log(`[DB] Session ${id} fetched from database with date: ${new Date(sessionWithTeacher.date).toISOString()}`);
    
    return sessionWithTeacher;
  }

  async getSessions(filters: any = {}): Promise<SessionWithTeacher[]> {
    const startTime = Date.now();
    const conditions = [];

    if (filters.teacherId) {
      conditions.push(eq(sessions.teacherId, filters.teacherId));
    }
    if (filters.type) {
      conditions.push(eq(sessions.type, filters.type));
    }
    if (filters.skillLevel) {
      conditions.push(eq(sessions.skillLevel, filters.skillLevel));
    }
    if (filters.format) {
      conditions.push(eq(sessions.format, filters.format));
    }
    if (filters.language) {
      conditions.push(eq(sessions.language, filters.language));
    }
    if (filters.date) {
      conditions.push(eq(sessions.date, filters.date));
    }
    if (filters.isPublic !== undefined) {
      // Convert string 'true'/'false' to boolean if needed
      const isPublicBoolean = 
        typeof filters.isPublic === 'string' 
          ? filters.isPublic.toLowerCase() === 'true'
          : Boolean(filters.isPublic);
      
      console.log(`Adding isPublic filter: ${isPublicBoolean}, original value: ${filters.isPublic}, type: ${typeof filters.isPublic}`);
      conditions.push(eq(sessions.isPublic, isPublicBoolean));
    }
    if (filters.priceRange) {
      conditions.push(
        and(
          gt(sessions.price, filters.priceRange.min),
          lt(sessions.price, filters.priceRange.max)
        )
      );
    }

    const result = await db
      .select({
        session: sessions,
        teacher: users
      })
      .from(sessions)
      .leftJoin(users, eq(sessions.teacherId, users.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getSessions', duration, result.length);

    return result
      .filter((row): row is { session: Session; teacher: User } => row.teacher !== null)
      .map(row => {
        const sessionWithTeacher = {
          ...row.session,
          teacher: row.teacher
        } as SessionWithTeacher;
        cache.setSession(row.session.id, sessionWithTeacher);
        return sessionWithTeacher;
      });
  }

  async createSession(session: InsertSession): Promise<Session> {
    const startTime = Date.now();
    const result = await db.insert(sessions).values(session).returning();
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('createSession', duration, result.length);
    
    const newSession = result[0];
    // Get teacher data for caching
    const teacherResult = await db
      .select()
      .from(users)
      .where(eq(users.id, newSession.teacherId));
    
    if (teacherResult[0]) {
      const sessionWithTeacher = {
        ...newSession,
        teacher: teacherResult[0]
      } as SessionWithTeacher;
      cache.setSession(newSession.id, sessionWithTeacher);
    }
    return newSession as Session;
  }

  async updateSession(id: number, sessionData: Partial<Session>): Promise<Session | undefined> {
    console.log(`[DB] updateSession called for session ${id} with data:`, {
      ...sessionData,
      date: sessionData.date ? new Date(sessionData.date).toISOString() : undefined
    });
    
    // Handle date processing
    if (sessionData.date) {
      try {
        // Ensure date is properly formatted as a Date object
        if (!(sessionData.date instanceof Date)) {
          console.log(`[DB] Converting date from type ${typeof sessionData.date} to Date object`);
          // Use safe approach to convert date string/object to Date
          let dateValue: string | null = null;
          
          if (typeof sessionData.date === 'string') {
            dateValue = sessionData.date;
          } else if (typeof sessionData.date === 'object' && sessionData.date !== null) {
            try {
              dateValue = String(sessionData.date);
            } catch (e) {
              console.error('Error converting date to string:', e);
            }
          }
              
          if (dateValue) {
            sessionData.date = new Date(dateValue);
            console.log(`[DB] Converted date to: ${sessionData.date.toISOString()}`);
          } else {
            console.error('[DB] Invalid date value provided:', sessionData.date);
            delete sessionData.date; // Remove invalid date to prevent database errors
          }
        }
      } catch (e) {
        console.error(`[DB] Error processing date: ${e}`);
        delete sessionData.date; // Remove problematic date to prevent database errors
      }
    }
    
    const startTime = Date.now();
    const result = await db
      .update(sessions)
      .set({ ...sessionData, updatedAt: new Date() })
      .where(eq(sessions.id, id))
      .returning();
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('updateSession', duration, result.length);

    const updatedSession = result[0];
    if (updatedSession) {
      // Get teacher data for caching
      const teacherResult = await db
        .select()
        .from(users)
        .where(eq(users.id, updatedSession.teacherId));
      
      if (teacherResult[0]) {
        // Use type assertion to fix type compatibility issues
        const sessionWithTeacher = {
          ...updatedSession,
          teacher: teacherResult[0]
        } as SessionWithTeacher;
        
        cache.setSession(id, sessionWithTeacher);
      }
    }
    
    console.log(`[DB] updateSession result:`, {
      id: updatedSession?.id,
      title: updatedSession?.title,
      date: updatedSession?.date ? new Date(updatedSession.date).toISOString() : undefined
    });
    
    return updatedSession as Session;
  }

  async deleteSession(id: number): Promise<boolean> {
    const startTime = Date.now();
    const result = await db.delete(sessions).where(eq(sessions.id, id)).returning();
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('deleteSession', duration, result.length);

    if (result.length > 0) {
      cache.invalidateSession(id);
      return true;
    }
    return false;
  }

  async getSessionsByTeacher(teacherId: number): Promise<SessionWithTeacher[]> {
    const startTime = Date.now();
    const result = await db
      .select({
        session: sessions,
        teacher: users
      })
      .from(sessions)
      .leftJoin(users, eq(sessions.teacherId, users.id))
      .where(eq(sessions.teacherId, teacherId));
    const duration = Date.now() - startTime;
    queryMonitor.recordQueryTime('getSessionsByTeacher', duration, result.length);

    return result
      .filter((row): row is { session: Session; teacher: User } => row.teacher !== null)
      .map(row => {
        const sessionWithTeacher = {
          ...row.session,
          teacher: row.teacher
        } as SessionWithTeacher;
        cache.setSession(row.session.id, sessionWithTeacher);
        return sessionWithTeacher;
      });
  }
}

export const sessionQueries = new SessionQueries(); 