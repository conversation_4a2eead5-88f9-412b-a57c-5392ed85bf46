// @ts-nocheck
import pkg from 'pg';
const { Pool } = pkg;
import { drizzle } from 'drizzle-orm/node-postgres';

// Create a proper pg Pool instance for Drizzle with connection pooling
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  // Optimized connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection cannot be established
});

// Create Drizzle instance
export const db = drizzle(pool);

// Export pool for direct queries if needed
export { pool };

// Query performance monitoring
export class QueryMonitor {
  private queryTimings: { query: string; time: number; rows: number }[] = [];

  recordQueryTime(query: string, time: number, rows: number): void {
    this.queryTimings.push({ query, time, rows });
    // Keep only the last 100 queries
    if (this.queryTimings.length > 100) {
      this.queryTimings.shift();
    }
  }

  analyzeQueryPerformance(): void {
    // Sort by time, descending
    const slowQueries = this.queryTimings
      .filter(q => q.time > 200) // Queries taking >200ms
      .sort((a, b) => b.time - a.time);

    if (slowQueries.length > 0) {
      console.log(`Found ${slowQueries.length} slow queries. Top 3:`);
      slowQueries.slice(0, 3).forEach(q => {
        console.log(`Query taking ${q.time}ms returning ${q.rows} rows: ${q.query.substring(0, 100)}...`);
      });
    }

    // Clear the list
    this.queryTimings = [];
  }
}

export const queryMonitor = new QueryMonitor(); 