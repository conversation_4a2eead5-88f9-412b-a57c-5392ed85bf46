import { pgTable, serial, text, timestamp, integer, boolean, real, pgEnum } from 'drizzle-orm/pg-core';

export const sessionTypeEnum = pgEnum('session_type', [
  'Yoga', 'Pilates', 'Language', 'Music', 'Dance', 'Cooking', 'Art',
  'Academic', 'Professional', 'Health', 'Fitness', 'DIY', 'Beauty', 'Therapy',
  'Relationships', 'Spirituality'
]);

export const skillLevelEnum = pgEnum('skill_level', ['Beginner', 'Intermediate', 'Advanced', 'All Levels']);

export const sessionFormatEnum = pgEnum('session_format', ['One-on-One', 'Group', 'Workshop']);

export const users = pgTable('auth.users', {
  id: serial('id').primaryKey(),
  username: text('username').notNull().unique(),
  password: text('password').notNull(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  bio: text('bio'),
  avatar: text('avatar'),
  coverPhoto: text('cover_photo'),
  timezone: text('timezone'),
  phone: text('phone'),
  isTeacher: boolean('is_teacher').default(false).notNull(),
  specializations: text('specializations').array(),
  skills: text('skills').array(),
  certifications: text('certifications').array(),
  experience: text('experience'),
  rating: real('rating').default(0),
  reviewCount: integer('review_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Legacy profiles table has been removed
// All profile data is now stored in the unified user_profiles table

export const sessions = pgTable('content.sessions', {
  id: serial('id').primaryKey(),
  title: text('title').notNull(),
  teacherId: integer('teacher_id').notNull(),
  type: sessionTypeEnum('type').notNull(),
  description: text('description').notNull(),
  price: real('price').notNull(),
  duration: integer('duration').notNull(),
  date: timestamp('date').notNull(),
  language: text('language').notNull(),
  skillLevel: skillLevelEnum('skill_level').notNull(),
  format: sessionFormatEnum('format').notNull(),
  maxParticipants: integer('max_participants'),
  zoomLink: text('zoom_link'),
  learningOutcomes: text('learning_outcomes'),
  requirements: text('requirements'),
  rating: real('rating').default(0),
  reviewCount: integer('review_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  imageUrl: text('image_url'),
  isPublic: boolean('is_public').default(true).notNull()
});