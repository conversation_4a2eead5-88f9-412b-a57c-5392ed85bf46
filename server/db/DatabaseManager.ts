import { Pool, PoolClient } from 'pg';
import { logger } from '../services/logger';
import { DatabaseUtils } from './utils/DatabaseUtils';

/**
 * Database connection manager with advanced production-grade robustness
 */
export class DatabaseManager {
  private static instance: DatabaseManager;
  private pool: Pool;
  private dbUtils: DatabaseUtils;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectBackoffMs = 5000; // Start with 5 seconds
  private isProduction = process.env.NODE_ENV === 'production';
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private clients: Set<PoolClient> = new Set();

  /**
   * Create a new DatabaseManager instance
   */
  private constructor() {
    // Create a proper pg Pool instance with connection pooling
    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: { rejectUnauthorized: false },
      // Optimized connection pool settings
      max: this.isProduction ? 25 : 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 120000, // Increased: Close idle clients after 2 minutes of inactivity
      connectionTimeoutMillis: 10000, // Connection timeout after 10 seconds
      // Add a statement timeout to prevent long-running queries
      statement_timeout: this.isProduction ? 30000 : 60000, // 30 seconds in production, 60 in dev
      // Add application_name to identify connections in PostgreSQL logs
      application_name: `sessionhub_${process.env.NODE_ENV}`,
    });

    // Set up connection pool error handler
    this.pool.on('error', (err, client) => {
      logger.error('[DatabaseManager] Unexpected error on idle client', err);
      this.handlePoolError(err, client);
    });

    // Set up connection pool connect event
    this.pool.on('connect', (client) => {
      logger.info('[DatabaseManager] New client connected to the pool');
      this.clients.add(client);

      // Add client-specific error handler
      client.on('error', (err) => {
        logger.error('[DatabaseManager] Client connection error', err);
        this.clients.delete(client);
        this.handleClientError(err, client);
      });
    });

    // Set up connection pool remove event
    this.pool.on('remove', (client) => {
      logger.info('[DatabaseManager] Client removed from the pool');
      this.clients.delete(client);
    });

    this.dbUtils = new DatabaseUtils();

    // Start a health check in production mode
    if (this.isProduction) {
      this.startHealthCheck();
    }
  }

  /**
   * Start a periodic health check to ensure the database connection is alive
   */
  private startHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        // Simple query to check connection
        await this.query('SELECT 1 as health_check');
        logger.debug('[DatabaseManager] Health check passed');
        this.reconnectAttempts = 0; // Reset reconnect attempts on successful query
      } catch (error) {
        logger.error('[DatabaseManager] Health check failed', error);
        await this.reconnect();
      }
    }, 60000); // Check every minute
  }

  /**
   * Handle a pool error by attempting to reconnect
   */
  private async handlePoolError(err: Error, client?: any) {
    logger.error('[DatabaseManager] Pool error:', err);

    // Mark as disconnected
    this.isConnected = false;

    // If this is a fatal connection error, attempt to reconnect
    if (err.message.includes('termination') ||
      err.message.includes('connection') ||
      err.message.includes('timeout')) {
      await this.reconnect();
    }
  }

  /**
   * Handle a client error by removing it from our tracking and potentially reconnecting
   */
  private async handleClientError(err: Error, client: any) {
    logger.error('[DatabaseManager] Client error:', err);

    // If this is a fatal connection error, attempt to reconnect
    if (err.message.includes('termination') ||
      err.message.includes('connection') ||
      err.message.includes('timeout')) {
      // Only trigger reconnect if we're losing too many clients
      if (this.clients.size < this.pool.options.max / 2) {
        await this.reconnect();
      }
    }
  }

  /**
   * Attempt to reconnect if the client was terminated
   */
  public async reconnect(): Promise<boolean> {
    console.log('[DatabaseManager] Attempting to reconnect after client termination');

    try {
      // First try to gracefully end any existing connections
      try {
        await this.pool.end();
        console.log('[DatabaseManager] Successfully closed old pool');
      } catch (endError) {
        // It's okay if ending fails - the pool might already be terminated
        console.log('[DatabaseManager] Could not end old pool cleanly, creating new one anyway');
      }

      // Create a new pool with the same settings
      this.pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false },
        max: this.isProduction ? 25 : 20,
        idleTimeoutMillis: 120000,
        connectionTimeoutMillis: 10000,
        statement_timeout: this.isProduction ? 30000 : 60000,
        application_name: `sessionhub_${process.env.NODE_ENV}_reconnect`,
      });

      // Re-add the event handlers
      this.pool.on('error', (err, client) => {
        logger.error('[DatabaseManager] Unexpected error on idle client after reconnect', err);
        this.handlePoolError(err, client);
      });

      // Test the connection with a simple query
      await this.query('SELECT 1 as reconnect_test');
      logger.info('[DatabaseManager] Successfully reconnected to the database');
      this.isConnected = true;

      // Clear the clients set and rebuild it
      this.clients.clear();

      return true;
    } catch (error) {
      logger.error('[DatabaseManager] Failed to reconnect to the database', error);
      return false;
    }
  }

  /**
   * Get a database manager instance (singleton)
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Connect to the database
   */
  public async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      const client = await this.pool.connect();
      logger.info('[DatabaseManager] Successfully connected to the database');
      client.release();
      this.isConnected = true;
    } catch (error) {
      logger.error('[DatabaseManager] Error connecting to the database:', error);
      throw error;
    }
  }

  /**
   * Execute a query on the database with robust error handling
   */
  public async query(text: string, params?: any[]): Promise<any> {
    let retries = 0;
    const maxRetries = 3;

    while (retries < maxRetries) {
      let client = null;

      try {
        // Get a client from the pool
        client = await this.pool.connect();

        // Log slow queries in development mode
        const startTime = Date.now();

        // Execute the query
        const result = await client.query(text, params);

        // Check query execution time
        const duration = Date.now() - startTime;
        if (duration > 100) {
          logger.warn(`[DatabaseManager] Slow query (${duration}ms): ${text.substring(0, 100)}...`);
        }

        // Release the client back to the pool
        client.release();

        return result;
      } catch (error: any) {
        // Release the client back to the pool (if we got one)
        if (client) {
          client.release(true); // Release with error = true
        }

        // Check if this is a retriable error
        const isRetriable =
          error.code === 'ECONNRESET' ||
          error.code === '08006' ||    // Connection failure
          error.code === '08001' ||    // Unable to establish connection
          error.code === '57P01' ||    // Termination by admin
          error.code === '57P02' ||    // Crash 
          error.code === '57P03' ||    // Cannot connect now
          error.code === '40001' ||    // Serialization failure
          error.code === '40P01' ||    // Deadlock
          error.message.includes('termination') ||
          error.message.includes('connection');

        if (isRetriable && retries < maxRetries - 1) {
          retries++;
          const backoff = 100 * Math.pow(2, retries); // Exponential backoff
          logger.warn(`[DatabaseManager] Retriable error: ${error.message}. Retrying in ${backoff}ms (${retries}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, backoff));
          continue;
        }

        // Log the error
        logger.error(`[DatabaseManager] Query error: ${error.message}`);
        logger.error(`[DatabaseManager] Query: ${text}`);
        if (params) {
          logger.error(`[DatabaseManager] Params: ${JSON.stringify(params)}`);
        }

        // For production, handle certain errors gracefully
        if (this.isProduction) {
          if (error.code === '22P02') { // Invalid text representation
            logger.error('[DatabaseManager] Invalid input syntax error:', error);
            return { rows: [] }; // Return empty result instead of crashing
          }
        }

        throw error;
      }
    }
  }

  /**
   * Check if the database is connected
   */
  public isConnectedToDatabase(): boolean {
    return this.isConnected;
  }

  /**
   * Get the database utils
   */
  public getDbUtils(): DatabaseUtils {
    return this.dbUtils;
  }

  /**
   * Gracefully close the database connection
   */
  public async close(): Promise<void> {
    try {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      await this.pool.end();
      logger.info('[DatabaseManager] Database connection closed');
    } catch (error) {
      logger.error('[DatabaseManager] Error closing database connection:', error);
    }
  }
}
