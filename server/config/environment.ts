/**
 * Environment Configuration and Validation
 * Ensures all required environment variables are present before server startup
 */

interface EnvironmentConfig {
    // Database
    DATABASE_URL: string;
    SUPABASE_URL: string;
    SUPABASE_ANON_KEY: string;
    SUPABASE_SERVICE_ROLE_KEY: string;

    // Payment Processing
    STRIPE_SECRET_KEY?: string;
    STRIPE_PUBLISHABLE_KEY?: string;
    STRIPE_WEBHOOK_SECRET?: string;

    // Session Management
    SESSION_SECRET: string;

    // Server Configuration
    NODE_ENV: string;
    PORT?: string;

    // Optional Features
    SENTRY_DSN?: string;
    REDIS_URL?: string;
}

const requiredEnvVars = [
    'DATABASE_URL',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'SESSION_SECRET'
] as const;

const productionRequiredVars = [
    ...requiredEnvVars,
    'STRIPE_SECRET_KEY',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_WEBHOOK_SECRET'
] as const;

// Development environment variables - Stripe not required
const developmentRequiredVars = [
    ...requiredEnvVars
] as const;

/**
 * Validate that all required environment variables are present
 */
export function validateEnvironment(): EnvironmentConfig {
    const env = process.env;
    const isProduction = env.NODE_ENV === 'production';

    // Check required variables based on environment
    const varsToCheck = isProduction ? productionRequiredVars : developmentRequiredVars;
    const missing: string[] = [];

    for (const varName of varsToCheck) {
        if (!env[varName] || env[varName]?.trim() === '') {
            missing.push(varName);
        }
    }

    if (missing.length > 0) {
        console.error('❌ Missing required environment variables:');
        missing.forEach(varName => {
            console.error(`   - ${varName}`);
        });

        console.error('\n📝 To fix this:');
        console.error('1. Copy .env.example to .env');
        console.error('2. Fill in all required values');
        console.error('3. Restart the server');

        if (isProduction) {
            console.error('\n🚨 Production mode requires all payment variables!');
        }

        throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // Validate specific formats
    if (env.SUPABASE_URL && !env.SUPABASE_URL.startsWith('https://')) {
        throw new Error('SUPABASE_URL must start with https://');
    }

    if (env.DATABASE_URL && !env.DATABASE_URL.startsWith('postgresql://')) {
        throw new Error('DATABASE_URL must be a valid PostgreSQL connection string');
    }

    if (env.STRIPE_SECRET_KEY && !env.STRIPE_SECRET_KEY.startsWith('sk_')) {
        throw new Error('STRIPE_SECRET_KEY must be a valid Stripe secret key (starts with sk_)');
    }

    // Log successful validation
    console.log('✅ Environment validation passed');
    console.log(`   Environment: ${env.NODE_ENV || 'development'}`);
    console.log(`   Database: ${env.SUPABASE_URL ? 'Supabase configured' : 'Missing'}`);
    console.log(`   Payments: ${env.STRIPE_SECRET_KEY ? 'Stripe configured' : 'Not configured'}`);

    return env as unknown as EnvironmentConfig;
}

/**
 * Get validated environment configuration
 */
export function getConfig(): EnvironmentConfig {
    return validateEnvironment();
}

/**
 * Check if specific feature is enabled based on environment variables
 */
export function isFeatureEnabled(feature: 'payments' | 'redis' | 'sentry'): boolean {
    const env = process.env;

    switch (feature) {
        case 'payments':
            return !!(env.STRIPE_SECRET_KEY && env.STRIPE_PUBLISHABLE_KEY);
        case 'redis':
            return !!env.REDIS_URL;
        case 'sentry':
            return !!env.SENTRY_DSN;
        default:
            return false;
    }
} 