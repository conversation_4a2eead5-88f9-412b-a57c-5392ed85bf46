/**
 * Production CORS Configuration
 * Secure CORS settings for production deployment
 */

import { CorsOptions } from 'cors';

interface ProductionCorsConfig {
    allowedOrigins: string[];
    isDevelopment: boolean;
}

/**
 * Get allowed origins based on environment
 */
function getAllowedOrigins(): string[] {
    const env = process.env.NODE_ENV;

    if (env === 'production') {
        // Production domains - update these with your actual domains
        return [
            'https://sessionhub.vercel.app',
            'https://www.sessionhub.com',
            'https://sessionhub.com',
            'https://app.sessionhub.com',
            // Add your production domains here
        ];
    } else {
        // Development/staging allowed origins
        return [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://localhost:5173', // Vite default
            'http://localhost:4173', // Vite preview
        ];
    }
}

/**
 * Production-ready CORS configuration
 */
export const productionCorsConfig: CorsOptions = {
    origin: (origin: string | undefined, callback: (err: Error | null, allowed?: boolean) => void) => {
        const allowedOrigins = getAllowedOrigins();
        const isDevelopment = process.env.NODE_ENV === 'development';

        // Allow requests with no origin (like mobile apps, Postman, etc.)
        if (!origin) {
            if (isDevelopment) {
                return callback(null, true);
            } else {
                // In production, log and potentially block requests with no origin
                console.warn('[CORS] Request with no origin blocked in production');
                return callback(null, false);
            }
        }

        // Check if origin is allowed
        if (allowedOrigins.includes(origin)) {
            console.log(`[CORS] Allowed origin: ${origin}`);
            return callback(null, true);
        } else {
            console.warn(`[CORS] Blocked origin: ${origin}`);
            const error = new Error(`CORS: Origin ${origin} not allowed`);
            return callback(error, false);
        }
    },

    credentials: true,

    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],

    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'Cache-Control',
        'Pragma',
        'Expires'
    ],

    exposedHeaders: [
        'X-Rate-Limit-Limit',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset',
        'Retry-After'
    ],

    optionsSuccessStatus: 200,

    // Preflight cache time (24 hours)
    maxAge: 86400
};

/**
 * Development-only permissive CORS for local development
 */
export const developmentCorsConfig: CorsOptions = {
    origin: (origin: string | undefined, callback: (err: Error | null, allowed?: boolean) => void) => {
        // Allow all origins in development
        callback(null, true);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'X-Force-JSON',
        'Cache-Control',
        'Pragma',
        'Expires',
        'access-control-allow-origin',
        'access-control-allow-credentials'
    ],
    exposedHeaders: [
        'X-Rate-Limit-Limit',
        'X-Rate-Limit-Remaining',
        'X-Rate-Limit-Reset',
        'Retry-After'
    ]
};

/**
 * Get appropriate CORS configuration based on environment
 */
export function getCorsConfig(): CorsOptions {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
        console.log('[CORS] Using development CORS configuration (permissive)');
        return developmentCorsConfig;
    } else {
        console.log('[CORS] Using production CORS configuration (restrictive)');
        return productionCorsConfig;
    }
} 