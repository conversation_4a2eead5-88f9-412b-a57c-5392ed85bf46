import { Request, Response, NextFunction } from 'express';
import NodeCache from 'node-cache';
import { Session } from 'express-session';

// Extend express session to include userId field
declare module 'express-session' {
  interface SessionData {
    userId?: number;
  }
}

// Create a single instance of the cache
export const cache = new NodeCache({
  stdTTL: 60, // Cache expiration in seconds
  checkperiod: 120, // Regular check for expired keys
  useClones: false, // Don't clone cached data to improve performance
  deleteOnExpire: true, // Remove expired items automatically
});

// Middleware to cache API responses
export const cacheMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Only cache GET requests
  if (req.method !== 'GET') {
    console.log(`[Cache] Skipping cache for non-GET request: ${req.method} ${req.originalUrl}`);
    return next();
  }

  // Don't cache if the request explicitly asks to bypass cache
  if (req.query.refresh === 'true' || req.query.nocache === 'true') {
    console.log(`[Cache] Skipping cache due to refresh/nocache param: ${req.originalUrl}`);
    return next();
  }

  // Don't cache authenticated endpoints that might contain user-specific data
  // unless they're specifically meant to be cached (like public session listings)
  const isCacheableAuthenticatedEndpoint =
    req.originalUrl.includes('/api/sessions') ||
    req.originalUrl.includes('/api/teachers') ||
    req.originalUrl.includes('/api/teachers');

  if (req.user && !isCacheableAuthenticatedEndpoint) {
    console.log(`[Cache] Skipping cache for authenticated user-specific endpoint: ${req.originalUrl}`);
    return next();
  }

  // Generate a cache key from the URL and user ID (if authenticated)
  // This ensures different users get different cached content
  const userId = req.user ? (req.user as any).id : 'anonymous';
  const cacheKey = `${req.originalUrl}:${userId}`;

  // Check if we have a cached response
  const cachedResponse = cache.get(cacheKey);

  if (cachedResponse) {
    console.log(`[Cache] HIT for ${req.originalUrl}`);
    return res.status(200).json(cachedResponse);
  }

  console.log(`[Cache] MISS for ${req.originalUrl}`);

  // Store the original json method
  const originalJson = res.json;

  // Override the json method to cache the response before sending
  res.json = function (body) {
    // Only cache successful responses
    if (res.statusCode >= 200 && res.statusCode < 300) {
      // Don't cache error responses
      if (body && !body.error) {
        console.log(`[Cache] Storing cache for ${req.originalUrl}`);

        // For session listings, set a shorter TTL to ensure fresh data
        let ttl = 60; // Default TTL is 60 seconds

        if (req.originalUrl.includes('/api/sessions') ||
          req.originalUrl.includes('/api/teachers')) {
          // Sessions and teacher data should refresh more frequently (15 seconds)
          ttl = 15;
          console.log(`[Cache] Using shorter TTL (${ttl}s) for session/teacher data`);
        }

        cache.set(cacheKey, body, ttl);
      } else {
        console.log(`[Cache] Not caching error response for ${req.originalUrl}`);
      }
    } else {
      console.log(`[Cache] Not caching non-2xx response (${res.statusCode}) for ${req.originalUrl}`);
    }

    // Call the original method
    return originalJson.call(this, body);
  };

  next();
};

// Function to invalidate cache entries that match a pattern
export const invalidateCache = (pattern: string | string[]) => {
  if (Array.isArray(pattern)) {
    let totalInvalidated = 0;
    for (const p of pattern) {
      totalInvalidated += invalidateCache(p);
    }
    return totalInvalidated;
  }

  const keys = cache.keys();
  let invalidatedCount = 0;

  console.log(`[Cache] Invalidating cache entries matching pattern: ${pattern}`);

  // More aggressive pattern matching - handle both exact matches and partial matches
  for (const key of keys) {
    if (
      key.includes(pattern) ||
      (pattern.endsWith('*') && key.includes(pattern.slice(0, -1))) ||
      (pattern.startsWith('/api') && key.includes(pattern))
    ) {
      console.log(`[Cache] Invalidated: ${key}`);
      cache.del(key);
      invalidatedCount++;
    }
  }

  console.log(`[Cache] Invalidated ${invalidatedCount} cache entries`);
  return invalidatedCount;
};

// Function to invalidate cache entries that match a pattern (alias for compatibility)
export const invalidateCachePattern = (pattern: string) => {
  return invalidateCache(pattern);
};

// Function to clear all cache
export const clearCache = () => {
  console.log(`[Cache] Clearing entire cache with ${cache.keys().length} entries`);
  return cache.flushAll();
};

// Special handler for session visibility changes - enhanced with more aggressive invalidation
export const invalidateSessionCache = (sessionId: number | string, teacherId?: number) => {
  console.log(`[Cache] Aggressively invalidating cache for session ${sessionId}`);
  const timestamp = Date.now();

  // Clear specific session cache
  invalidateCache(`/api/sessions/${sessionId}`);

  // Clear all session listings that might include this session - with wildcard patterns
  invalidateCache('/api/sessions');
  invalidateCache('sessions:');
  invalidateCache('/api/sessions?');

  // Force client-side cache invalidation by adding timestamp to session response
  cache.set(`session_updated:${sessionId}`, timestamp, 86400); // Keep this marker for 24 hours

  // Clear teacher session listings if we know the teacher
  if (teacherId) {
    invalidateCache(`/api/teachers/${teacherId}/sessions`);
    invalidateCache(`sessions:teacher:${teacherId}`);
    invalidateCache(`/api/teachers/${teacherId}`);
  } else {
    // If we don't know the teacher, clear all teacher endpoints to be safe
    invalidateCache('/api/teachers');
    invalidateCache('teachers:');
  }

  // Also clear general public sessions cache
  invalidateCache('/api/sessions?');
  invalidateCache('public:sessions:');

  console.log(`[Cache] Session cache invalidation complete with timestamp ${timestamp}`);
};