/**
 * Supabase Authentication Middleware
 * 
 * This middleware validates Supabase JWT tokens for protected routes.
 */

import { Request, Response, NextFunction } from 'express';
import { supabaseAdmin } from '../lib/supabase';

// Define the Supabase user type for our context
interface SupabaseUser {
  id: string;
  email?: string;
  [key: string]: any;
}

// Note: Express Request interface extended elsewhere

/**
 * Extract JWT token from request
 * @param req Express request
 * @returns JWT token or null
 */
function extractToken(req: Request): string | null {
  if (req.headers.authorization && req.headers.authorization.split(' ')[0] === 'Bearer') {
    return req.headers.authorization.split(' ')[1];
  }

  if (req.cookies && req.cookies.sb_auth_token) {
    return req.cookies.sb_auth_token;
  }

  return null;
}

/**
 * Validate JWT token and attach user to request
 * @param req Express request
 * @param res Express response
 * @param next Next function
 */
export async function validateJWT(req: Request, res: Response, next: NextFunction) {
  try {
    const token = extractToken(req);

    console.log('[Auth] validateJWT called for path:', req.path);
    console.log('[Auth] Authorization header:', req.headers.authorization ? 'Present' : 'Missing');
    console.log('[Auth] Token extracted:', token ? 'Yes (length: ' + token.length + ')' : 'No');

    if (!token) {
      console.log('[Auth] No token found, returning 401');
      return res.status(401).json({
        success: false,
        error: 'No authentication token provided'
      });
    }

    // Verify the token with Supabase
    console.log('[Auth] Verifying token with Supabase...');
    const { data, error } = await supabaseAdmin.auth.getUser(token);

    console.log('[Auth] Supabase response - error:', error ? error.message : 'None');
    console.log('[Auth] Supabase response - user:', data.user ? `ID: ${data.user.id}` : 'None');

    if (error || !data.user) {
      console.error('[Auth] Invalid token:', error);
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    // Attach user to request with proper typing
    (req as any).user = data.user as SupabaseUser;
    console.log('[Auth] Authentication successful for user:', data.user.id);

    next();
  } catch (error) {
    console.error('[Auth] Token validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
}

/**
 * Check if user is an admin
 * @param req Express request
 * @param res Express response
 * @param next Next function
 */
export async function isAdmin(req: Request, res: Response, next: NextFunction) {
  try {
    // First validate the JWT
    await validateJWT(req, res, async () => {
      // Check if user is an admin
      const { data, error } = await supabaseAdmin
        .from('user_profiles')
        .select('is_admin')
        .eq('user_id', (req as any).user.id)
        .single();

      if (error || !data || !data.is_admin) {
        return res.status(403).json({
          success: false,
          error: 'Unauthorized: Admin access required'
        });
      }

      next();
    });
  } catch (error) {
    console.error('[Auth] Admin check error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
}

export { SupabaseUser };
