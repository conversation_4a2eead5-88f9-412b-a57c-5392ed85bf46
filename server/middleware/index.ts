export * from './cache';
export * from './rateLimiter';
export * from './compression';
export * from './etag';
export * from './csp';
export * from './validation.middleware';
export * from './error.middleware';
export * from './security';
import { RequestHandler, Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import compression from 'compression';
import etag from 'etag';
import { healthCheck } from './health';
import { db } from '../db';

// Cache storage
const cache = new Map<string, { data: any; timestamp: number }>();

// Export health check middleware
export { healthCheck };

// Cache invalidation function with improved handling for session paths
export function invalidateCache(paths: string[]) {
  for (const path of paths) {
    // Delete the specific path from cache
    cache.delete(path);

    // For session updates, also clear session-specific entries
    if (path.includes('/api/sessions/')) {
      const sessionId = path.split('/').pop();
      console.log(`[Cache] Invalidating cache for session ${sessionId}`);

      // Find and delete any related cache keys for this session
      // (including session lists that might contain this session)
      Array.from(cache.keys()).forEach(key => {
        if (key.includes(sessionId!) ||
          (key.includes('sessions') && !key.includes('teachers'))) {
          console.log(`[Cache] Clearing related cache: ${key}`);
          cache.delete(key);
        }
      });
    }

    // For teacher session lists, clear with specific targeting
    if (path.includes('/api/teachers/') && path.includes('/sessions')) {
      const teacherId = path.split('/')[3]; // Extract ID from path segments
      console.log(`[Cache] Invalidating teacher sessions cache for teacher ${teacherId}`);

      // Clear teacher-specific session lists
      Array.from(cache.keys()).forEach(key => {
        if (key.includes(`/teachers/${teacherId}/`)) {
          console.log(`[Cache] Clearing teacher cache: ${key}`);
          cache.delete(key);
        }
      });
    }
  }
}

// Cache middleware with TTL (time to live)
export function cacheMiddleware(ttlSeconds = 60): RequestHandler {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    const key = req.originalUrl;
    const cachedResponse = cache.get(key);
    const currentTime = Date.now();

    if (cachedResponse && currentTime - cachedResponse.timestamp < ttlSeconds * 1000) {
      return res.json(cachedResponse.data);
    }

    // Store the original res.json method
    const originalJson = res.json;

    // Override res.json to cache the response
    res.json = function (body) {
      cache.set(key, { data: body, timestamp: currentTime });
      return originalJson.call(this, body);
    };

    next();
  };
}

// Create a dummy middleware that does nothing for development mode
const dummyMiddleware = (_req: Request, _res: Response, next: NextFunction) => next();

// Rate limiter for general API endpoints - disabled in development
export const apiLimiter = process.env.NODE_ENV === 'development'
  ? dummyMiddleware
  : rateLimit({
    windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW || '15') * 60 * 1000, // Default 15 minutes
    max: parseInt(process.env.API_RATE_LIMIT || '100'), // Default max 100 requests per window
    standardHeaders: true,
    legacyHeaders: false
  });

// Stricter rate limiter for sensitive operations - disabled in development
export const strictLimiter = process.env.NODE_ENV === 'development'
  ? dummyMiddleware
  : rateLimit({
    windowMs: parseInt(process.env.STRICT_RATE_LIMIT_WINDOW || '15') * 60 * 1000, // Default 15 minutes
    max: parseInt(process.env.STRICT_RATE_LIMIT || '20'), // Default max 20 requests per window
    standardHeaders: true,
    legacyHeaders: false
  });

// Speed limiter to slow down brute force attempts - disabled in development
export const speedLimiter = process.env.NODE_ENV === 'development'
  ? dummyMiddleware
  : rateLimit({
    windowMs: parseInt(process.env.SPEED_RATE_LIMIT_WINDOW || '1') * 1000, // Default 1 second
    max: parseInt(process.env.SPEED_RATE_LIMIT || '10'), // Default max 10 requests per second
    standardHeaders: true,
    legacyHeaders: false
  });

// Compression middleware
export const compressionMiddleware = compression({
  level: 6, // Default compression level
  threshold: 1024, // Minimum size in bytes to compress
  filter: (req, res) => {
    // Skip compression for small responses
    if (res.getHeader('Content-Length') && Number(res.getHeader('Content-Length')) < 1024) {
      return false;
    }
    // Default compression filter
    return compression.filter(req, res);
  }
});

// ETag middleware for caching
export function etagMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;

    res.send = function (body) {
      // Skip for streaming responses
      if (res.headersSent) {
        return originalSend.call(this, body);
      }

      const etagValue = etag(body);
      res.setHeader('ETag', etagValue);

      // Check if ETag matches
      const ifNoneMatch = req.headers['if-none-match'];
      if (ifNoneMatch === etagValue) {
        res.status(304).end();
        return res;
      }

      return originalSend.call(this, body);
    };

    next();
  };
}

/**
 * Middleware to detect database connection errors and attempt reconnection
 */
export const databaseErrorRecovery = (req: Request, res: Response, next: NextFunction) => {
  // Save the original send method
  const originalSend = res.send;

  // Override the send method
  res.send = function (body) {
    // Check if the response indicates a database connection error
    if (body &&
      typeof body === 'string' &&
      body.includes('Cannot use a pool after calling end on the pool')) {

      console.log('Database connection error detected in response, attempting reconnection');

      // Try to reconnect the database
      db.connect()
        .then(() => {
          console.log('Database reconnected successfully, retrying original request');

          // Reset the send method to avoid recursion
          res.send = originalSend;

          // Continue with the original request now that we've reconnected
          next();
        })
        .catch((error: any) => {
          console.error('Failed to reconnect database:', error);

          // Fall back to sending the original error
          return originalSend.call(this, body);
        });

      // Don't send the response yet, we'll retry
      return this;
    }

    // Normal response, proceed with original behavior
    return originalSend.call(this, body);
  };

  next();
};
