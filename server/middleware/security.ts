import { Request, Response, NextFunction } from 'express';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { body, validationResult } from 'express-validator';
import crypto from 'crypto';
import { logger } from '../services/logger';

// Extend session interface to include csrfToken
declare module 'express-session' {
    interface SessionData {
        csrfToken?: string;
    }
}

// Enhanced security headers middleware
export const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:", "blob:"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Note: Consider removing unsafe-eval in production
            connectSrc: ["'self'", "wss:", "ws:", process.env.SUPABASE_URL || ""],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"],
            upgradeInsecureRequests: [],
        },
    },
    crossOriginEmbedderPolicy: false, // Disable for development compatibility
    hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
});

// Enhanced rate limiting with progressive delays
export const enhancedRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true,
    legacyHeaders: false,
    message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
    },
    keyGenerator: (req: Request) => {
        // Use IP + User-Agent for more accurate rate limiting
        return `${req.ip}-${crypto.createHash('md5').update(req.get('User-Agent') || '').digest('hex')}`;
    },
    skip: (req: Request) => {
        // Skip rate limiting for health checks and static assets
        return req.path.startsWith('/health') ||
            req.path.startsWith('/static') ||
            req.path.startsWith('/assets');
    },
    handler: (req: Request, res: Response) => {
        logger.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);

        res.status(429).json({
            error: 'Too many requests from this IP, please try again later.',
            retryAfter: '15 minutes'
        });
    }
});

// Strict rate limiting for authentication endpoints
export const authRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 auth attempts per windowMs
    standardHeaders: true,
    legacyHeaders: false,
    message: {
        error: 'Too many authentication attempts, please try again later.',
        retryAfter: '15 minutes'
    },
    skipSuccessfulRequests: true, // Don't count successful requests
    handler: (req: Request, res: Response) => {
        logger.error(`Auth rate limit exceeded for IP: ${req.ip}`);

        res.status(429).json({
            error: 'Too many authentication attempts, please try again later.',
            retryAfter: '15 minutes'
        });
    }
});

// Request sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
    // Remove potentially dangerous characters from query parameters
    for (const key in req.query) {
        if (typeof req.query[key] === 'string') {
            req.query[key] = (req.query[key] as string)
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .replace(/on\w+\s*=/gi, ''); // Remove event handlers
        }
    }

    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
        sanitizeObject(req.body);
    }

    next();
};

// Recursive function to sanitize objects
function sanitizeObject(obj: any): void {
    for (const key in obj) {
        if (typeof obj[key] === 'string') {
            obj[key] = obj[key]
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '');
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
            sanitizeObject(obj[key]);
        }
    }
}

// Request validation middleware
export const validateRequest = (validations: any[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        // Run all validations
        await Promise.all(validations.map(validation => validation.run(req)));

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            logger.warn('Request validation failed', {
                errors: errors.array(),
                ip: req.ip,
                path: req.path,
                method: req.method
            });

            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
        }

        next();
    };
};

// Common validation rules
export const commonValidations = {
    email: body('email').isEmail().normalizeEmail(),
    password: body('password').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
    uuid: (field: string) => body(field).isUUID(),
    text: (field: string, maxLength = 1000) => body(field).isLength({ max: maxLength }).trim().escape(),
    number: (field: string) => body(field).isNumeric(),
    boolean: (field: string) => body(field).isBoolean()
};

// Security audit logging middleware
export const securityAuditLog = (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();

    // Log security-relevant requests
    const securityPaths = ['/api/auth', '/api/admin', '/api/payment'];
    const isSecurityPath = securityPaths.some(path => req.path.startsWith(path));

    if (isSecurityPath) {
        logger.info('Security-sensitive request', {
            ip: req.ip,
            method: req.method,
            path: req.path,
            userAgent: req.get('User-Agent'),
            referer: req.get('Referer'),
            timestamp: new Date().toISOString()
        });
    }

    // Log response when it finishes
    res.on('finish', () => {
        const duration = Date.now() - startTime;

        if (isSecurityPath) {
            logger.info('Security-sensitive response', {
                ip: req.ip,
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                duration,
                timestamp: new Date().toISOString()
            });
        }

        // Log suspicious activity
        if (res.statusCode === 401 || res.statusCode === 403) {
            logger.warn('Unauthorized access attempt', {
                ip: req.ip,
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                userAgent: req.get('User-Agent')
            });
        }
    });

    next();
};

// CSRF protection middleware
export const csrfProtection = (req: Request, res: Response, next: NextFunction) => {
    // Skip CSRF for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
        return next();
    }

    // Skip CSRF for API endpoints that use JWT authentication
    if (req.path.startsWith('/api/') && req.headers.authorization) {
        return next();
    }

    const token = req.headers['x-csrf-token'] || req.body._csrf;
    const sessionToken = req.session?.csrfToken;

    if (!token || !sessionToken || token !== sessionToken) {
        logger.warn('CSRF token validation failed', {
            ip: req.ip,
            path: req.path,
            hasToken: !!token,
            hasSessionToken: !!sessionToken
        });

        return res.status(403).json({
            error: 'Invalid CSRF token'
        });
    }

    next();
};

// Generate CSRF token for session
export const generateCSRFToken = (req: Request, res: Response, next: NextFunction) => {
    if (!req.session?.csrfToken) {
        req.session.csrfToken = crypto.randomBytes(32).toString('hex');
    }
    next();
};

// IP whitelist middleware (for admin endpoints)
export const ipWhitelist = (allowedIPs: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
        const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

        if (!allowedIPs.includes(clientIP)) {
            logger.error(`IP not whitelisted: ${clientIP} attempted to access ${req.path}`);

            return res.status(403).json({
                error: 'Access denied from this IP address'
            });
        }

        next();
    };
};

// Request size limiting middleware
export const requestSizeLimit = (maxSize: string = '10mb') => {
    return (req: Request, res: Response, next: NextFunction) => {
        const contentLength = req.headers['content-length'];

        if (contentLength) {
            const sizeInBytes = parseInt(contentLength);
            const maxSizeInBytes = parseSize(maxSize);

            if (sizeInBytes > maxSizeInBytes) {
                logger.warn('Request size limit exceeded', {
                    ip: req.ip,
                    path: req.path,
                    size: sizeInBytes,
                    maxSize: maxSizeInBytes
                });

                return res.status(413).json({
                    error: 'Request entity too large'
                });
            }
        }

        next();
    };
};

// Helper function to parse size strings
function parseSize(size: string): number {
    const units: { [key: string]: number } = {
        'b': 1,
        'kb': 1024,
        'mb': 1024 * 1024,
        'gb': 1024 * 1024 * 1024
    };

    const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2] || 'b';

    return value * units[unit];
}

// Export all security middleware as a combined function
export const applySecurity = (app: any) => {
    app.use(securityHeaders);
    app.use(enhancedRateLimit);
    app.use(sanitizeInput);
    app.use(securityAuditLog);
    app.use(generateCSRFToken);
    app.use(requestSizeLimit());

    // Apply auth rate limiting to specific routes
    app.use('/api/auth', authRateLimit);

    logger.info('Security middleware applied successfully');
}; 