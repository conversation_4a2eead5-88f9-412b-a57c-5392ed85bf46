import 'dotenv/config';
// Load environment variables first
import dotenv from 'dotenv';
import { join, resolve } from 'path';

// Add declaration for server variable at the very top
let server: any; // Will be assigned when the HTTP server is created

// Load environment variables from .env file
const envPath = resolve(process.cwd(), '.env');
const result = dotenv.config({ path: envPath });

if (result.error) {
  console.error('Error loading .env file:', result.error);
  process.exit(1);
}

// Then load environment variables using our centralized utility
import { loadEnvironment } from './utils/environment';
import { validateEnvironment, getConfig } from './config/environment';

// Load environment variables before any other imports
if (!loadEnvironment()) {
  console.error('Failed to load environment variables - exiting');
  process.exit(1);
}

// Validate environment variables immediately after loading
try {
  validateEnvironment();
} catch (error) {
  console.error('Environment validation failed:', error instanceof Error ? error.message : String(error));
  process.exit(1);
}

// Log loaded environment variables for debugging
console.log('Environment variables loaded successfully');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Not set');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'Set' : 'Not set');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
import fs from 'fs';
import { db } from './db';

// Add import for AvailabilityManager
import AvailabilityManager from './availability-manager';
import createAvailabilityRoutes from './availability-routes';

// Setup global unhandled error handlers
console.log('Setting up global error handlers...');

// Add production-specific error handling and graceful shutdown
process.on('uncaughtException', (error) => {
  console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...', error);

  // In production, log but don't crash for certain known errors
  if (process.env.NODE_ENV === 'production') {
    if (error.message &&
      (error.message.includes('client termination') ||
        error.message.includes('Connection terminated') ||
        error.message.includes(':shutdown:'))) {

      console.log('Known database connection error in production - continuing execution');
      return; // Don't crash in production for these specific errors
    }
  }

  // For other errors, begin graceful shutdown
  console.log('Starting graceful shutdown after uncaught exception...');
  gracefulShutdown();
});

process.on('unhandledRejection', (error) => {
  console.error('UNHANDLED REJECTION! 💥 Shutting down...', error);

  // In production, log but don't crash for certain known errors
  if (process.env.NODE_ENV === 'production') {
    if (error instanceof Error &&
      (error.message.includes('client termination') ||
        error.message.includes('Connection terminated') ||
        error.message.includes(':shutdown:'))) {

      console.log('Known database connection error in production - continuing execution');
      return; // Don't crash in production for these specific errors
    }
  }

  // For other errors, begin graceful shutdown
  console.log('Starting graceful shutdown after unhandled rejection...');
  gracefulShutdown();
});

// Add SIGTERM handler for graceful shutdown (e.g., when deployed in containers)
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received. Shutting down gracefully...');
  gracefulShutdown();
});

// Add SIGINT handler for graceful shutdown (e.g., when pressing Ctrl+C)
process.on('SIGINT', () => {
  console.log('👋 SIGINT received. Shutting down gracefully...');
  gracefulShutdown();
});

// Simple log function to replace the one from vite.ts
function serverLog(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

// Graceful shutdown function
async function gracefulShutdown() {
  try {
    console.log('Shutting down server...');

    // Implement proper closing of message scheduler if it exists
    console.log('Shutting down message scheduler...');
    productionMessageScheduler.stop();

    // Close database connections
    console.log('Closing database connections...');
    if (DatabaseManager && DatabaseManager.getInstance) {
      try {
        await DatabaseManager.getInstance().close();
      } catch (err) {
        console.error('Error closing database connections:', err);
      }
    }

    // Close the server if it exists
    if (server) {
      console.log('Closing HTTP server...');
      await new Promise<void>((resolve) => {
        server.close(() => {
          console.log('HTTP server closed.');
          resolve();
        });
      });
    }

    console.log('Graceful shutdown completed.');
    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    // Force exit after 3 seconds if graceful shutdown fails
    setTimeout(() => {
      console.error('Forcing exit after failed graceful shutdown');
      process.exit(1);
    }, 3000);
  }
}

// Now import the rest of the modules
import express from 'express';
import type { Express } from 'express';
import statusMonitor from 'express-status-monitor';
import { registerRoutes } from "./routes/index";
import { setupVite, serveStatic, log } from "./vite";
import { performMigrations } from "./db";
import { storage } from "./storage";
import { createServer as createViteServer } from 'vite';
import {
  apiLimiter,
  strictLimiter,
  speedLimiter,
  cacheMiddleware,
  compressionMiddleware,
  etagMiddleware,
  databaseErrorRecovery,
  contentSecurityPolicy
} from './middleware';
import cors from 'cors';
import { setupDefaultUser } from "./utils/setupDefaultUser";
import { createServer } from "http";
import { AddressInfo } from "net";
import session from 'express-session';
import path from 'path';
import { Server } from 'socket.io';
import * as net from 'net';
import cookie from 'cookie';
import { createAdapter } from '@socket.io/postgres-adapter';
import { productionMessageScheduler } from './services/ProductionMessageScheduler';
import { handleDirectBookings, createTestBooking } from './bookings-fix';
import { dbOptimization } from './services';
import serviceManager from './services/ServiceManager';
import { monitoring } from './services/monitoring';
import { logger } from './services/logger';

// Import the robust database connection
import { databaseService, connectionManager, initializeDatabase } from './database/robust-db';

// Add import for DatabaseManager
import { DatabaseManager } from './db/DatabaseManager';

// Improved database connection management
let dbConnectionActive = false;
let dbReconnectTimer: NodeJS.Timeout | null = null;
const DB_IDLE_TIMEOUT = 300000; // 5 minutes in milliseconds
const DB_RECONNECT_INTERVAL = 10000; // 10 seconds in milliseconds
let connectionAttemptInProgress = false; // Track if a connection attempt is in progress
let lastConnectionAttempt = 0; // Track when the last connection attempt was made
const CONNECTION_DEBOUNCE_TIME = 5000; // Minimum time between connection attempts (5 seconds)

async function setupDatabase() {
  try {
    // Prevent multiple simultaneous connection attempts
    if (connectionAttemptInProgress) {
      console.log("Database connection attempt already in progress, skipping");
      return dbConnectionActive;
    }

    // Debounce connection attempts
    const now = Date.now();
    if (now - lastConnectionAttempt < CONNECTION_DEBOUNCE_TIME) {
      console.log(`Connection attempt too soon after previous attempt, skipping (${(now - lastConnectionAttempt) / 1000}s < ${CONNECTION_DEBOUNCE_TIME / 1000}s)`);
      return dbConnectionActive;
    }

    // Track that we're attempting a connection
    connectionAttemptInProgress = true;
    lastConnectionAttempt = now;

    console.log("Initializing database connection...");

    // Clear any existing reconnect timer
    if (dbReconnectTimer) {
      clearTimeout(dbReconnectTimer);
      dbReconnectTimer = null;
    }

    // Only initialize if not already active
    if (!dbConnectionActive) {
      try {
        // Use our new robust database initialization
        const success = await initializeDatabase();

        if (success) {
          dbConnectionActive = true;

          // Log success
          const health = await databaseService.checkHealth();
          console.log(`Database connected successfully. Server time: ${health.time}`);

          // Initialize idle timer
          resetIdleTimer();
        } else {
          throw new Error("Database initialization failed");
        }
      } catch (initError) {
        console.error("Database initialization error:", initError);
        console.log("Continuing with limited functionality - some features will not work properly");
        // Don't set dbConnectionActive to true, but don't throw an error either
        // This allows the server to start without a database connection
      }
    }

    // Connection attempt complete
    connectionAttemptInProgress = false;
    return true;
  } catch (error) {
    console.error("Database connection error:", error);
    dbConnectionActive = false;
    connectionAttemptInProgress = false;

    // Schedule reconnection attempt
    if (!dbReconnectTimer) {
      dbReconnectTimer = setTimeout(() => {
        console.log("Attempting to reconnect to database...");
        setupDatabase();
      }, DB_RECONNECT_INTERVAL);
    }

    // Return true anyway to allow the server to start
    console.log("Continuing with limited functionality - some features will not work properly");
    return true;
  }
}

function resetIdleTimer() {
  // Clear any existing idle timer
  if (idleTimer) {
    clearTimeout(idleTimer);
    idleTimer = null;
  }

  // Set new idle timer
  idleTimer = setTimeout(async () => {
    try {
      console.log(`Database idle for ${DB_IDLE_TIMEOUT / 1000} seconds, suspending connection`);

      // Don't actually end the pool, just suspend active connections
      await db.suspend();
      dbConnectionActive = false;

      console.log("Database connection suspended, will reconnect on next query");
    } catch (error) {
      console.error("Error suspending database connection:", error);
    }
  }, DB_IDLE_TIMEOUT);
}

// When a query is needed after suspension
async function ensureDatabaseConnection() {
  if (!dbConnectionActive) {
    console.log("Reactivating database connection after idle period");
    try {
      // Check if the connection manager is healthy
      try {
        await databaseService.checkHealth();
        dbConnectionActive = true;
        return true;
      } catch (healthError) {
        // If health check fails, try to reconnect
        console.log("Database health check failed, attempting full reconnection");
        return await setupDatabase();
      }
    } catch (error) {
      console.error("Failed to reactivate database connection:", error);
      console.log("Continuing with limited functionality");
      // Return true anyway to allow the operation to continue
      return true;
    }
  }
  return true;
}

// Track idle timer
let idleTimer: NodeJS.Timeout | null = null;

// Override query to check connection status
const originalDbQuery = db.query;
db.query = async function (...args: any[]) {
  try {
    await ensureDatabaseConnection();
    // Reset the idle timer on each query
    resetIdleTimer();

    // Use our robust database service for the query
    try {
      // For string queries, use the databaseService
      if (typeof args[0] === 'string') {
        const params = args.length > 1 ? args[1] : [];
        return await databaseService.query(args[0], params);
      } else {
        // For other query types, fall back to the original implementation
        // @ts-ignore - Ignore type checking for this override
        return await originalDbQuery.apply(this, args);
      }
    } catch (queryError: any) {
      // If the robust service fails, try the original implementation as fallback
      console.warn("Robust database service query failed, falling back to original implementation:",
        queryError?.message || 'Unknown error');
      // @ts-ignore - Ignore type checking for this override
      return await originalDbQuery.apply(this, args);
    }
  } catch (error) {
    console.error("Database query error:", error);

    // For SELECT queries, return an empty result set
    if (typeof args[0] === 'string' && args[0].trim().toUpperCase().startsWith('SELECT')) {
      console.log("Returning empty result set for SELECT query");
      return { rows: [], rowCount: 0 };
    }

    // For other queries, return a success response
    console.log("Returning success response for non-SELECT query");
    return { rowCount: 0, rows: [] };
  }
};

const app = express();

// Configure and add status monitor
const statusMonitorConfig = {
  title: 'SessionHub Server Status',
  path: '/admin/status',
  spans: [
    {
      interval: 1,     // Every second
      retention: 60    // Keep 60 data points (1 minute)
    },
    {
      interval: 5,     // Every 5 seconds
      retention: 60    // Keep 60 data points (5 minutes)
    },
    {
      interval: 15,    // Every 15 seconds
      retention: 60    // Keep 60 data points (15 minutes)
    }
  ],
  chartVisibility: {
    cpu: true,
    mem: true,
    load: true,
    responseTime: true,
    rps: true,
    statusCodes: true
  },
  healthChecks: [
    {
      protocol: 'http',
      host: 'localhost',
      path: '/api/health',
      port: process.env.PORT || '4005'
    }
  ]
};

// Add status monitor middleware
app.use(statusMonitor(statusMonitorConfig));

// Update CORS configuration to allow all local development ports and Cloudflare tunnels
app.use(cors({
  origin: (origin, callback) => {
    // Allow all localhost ports for development and undefined for same-origin requests
    if (!origin || /https?:\/\/localhost(:\d+)?$/.test(origin)) {
      callback(null, true);
    } else {
      console.log(`CORS blocked request from: ${origin}`);
      callback(null, true); // Allow all origins for testing
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-Request-ID',
    'X-Force-JSON',
    'Cache-Control',
    'Pragma',
    'Expires',
    'expires',
    'access-control-allow-origin',
    'access-control-allow-credentials'
  ],
  exposedHeaders: ['X-Rate-Limit-Limit', 'X-Rate-Limit-Remaining', 'X-Rate-Limit-Reset', 'Retry-After']
}));

// Specific CORS middleware for development - to handle local requests and Cloudflare tunnels
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    // Update this line to be more specific about origins
    const origin = req.headers.origin;
    if (origin && /https?:\/\/localhost(:\d+)?$/.test(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      res.header('Access-Control-Allow-Origin', '*');
    }
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, Expires, expires, access-control-allow-origin, access-control-allow-credentials');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.header('Access-Control-Allow-Credentials', 'true');

    // Handle OPTIONS requests
    if (req.method === 'OPTIONS') {
      return res.status(204).end();
    }

    next();
  });
}

// Apply compression middleware early in the stack
app.use(compressionMiddleware);

// Apply ETag middleware for improved client caching
app.use(etagMiddleware());

// Apply Content Security Policy middleware
const isDevelopment = process.env.NODE_ENV === 'development';
app.use(contentSecurityPolicy({ isDevelopment }));
console.log(`[Server] Content Security Policy enabled (${isDevelopment ? 'development mode' : 'production mode'})`);

// Basic request parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Apply rate limiting to API routes only in production mode
if (process.env.NODE_ENV !== 'development') {
  console.log('[Server] Rate limiting enabled (production mode)');

  // Apply speed limiter first
  app.use('/api/', speedLimiter);

  // Only apply rate limiting to routes that aren't covered by WebSockets
  // Skip rate limiting entirely for socket-enabled endpoints and critical user API paths
  const apiPathsToSkipRateLimiting = [
    '/api/user',             // Skip limiting user data fetching
    '/api/sessions',         // Skip limiting session operations
    '/api/teachers/top',  // Skip limiting top teachers endpoint
  ];

  app.use('/api/*', (req, res, next) => {
    // Skip rate limiting for authenticated users on critical paths
    if (req.session?.userId && apiPathsToSkipRateLimiting.some(path => req.path.startsWith(path))) {
      console.log(`[API Rate Limit] Skipping rate limit for authenticated user on path: ${req.path}`);
      next();
    } else {
      // Apply rate limiter for non-critical paths or unauthenticated users
      apiLimiter(req, res, next);
    }
  });

  // Authentication endpoints still need protection from brute force
  app.use('/api/login', strictLimiter);
  app.use('/api/register', strictLimiter);
} else {
  console.log('[Server] Rate limiting DISABLED (development mode)');
}

// Apply caching middleware to frequently accessed routes
app.use('/api/sessions', cacheMiddleware(300)); // Cache for 5 minutes
app.use('/api/teachers', cacheMiddleware(300));
app.use('/api/categories', cacheMiddleware(600)); // Cache for 10 minutes

// Add this after the other middleware applications, specifically for login
app.use('/api/login', databaseErrorRecovery);

// Also apply to other critical auth routes
app.use('/api/register', databaseErrorRecovery);
app.use('/api/user', databaseErrorRecovery);

// Add health check endpoint for client connectivity testing
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// ---- Begin WebSocket Handling ----
// Define nextHandler variable globally to avoid TypeScript errors
let nextHandler: any;

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      serverLog(logLine);
    }
  });

  next();
});

// Add more detailed logging for API routes
app.get('/api/sessions', async (req, res) => {
  try {
    console.log('[API] Getting sessions with query params:', req.query);

    // Add isPublic=true as filter for public API
    const filters = { ...req.query };
    // We're forcing isPublic to true only if it's not set already
    if (filters.isPublic === undefined) {
      // Special case for the public API
      console.log('[API] Setting isPublic filter to true for public sessions API');
      filters.isPublic = 'true'; // Need to use string as req.query is string
    }

    // Get the session service
    const sessionService = serviceManager.getSessionService();

    // Get sessions
    const sessions = await sessionService.getSessions(filters);
    console.log(`[API] Found ${sessions.length} sessions in database`);

    // Log the first session's details for debugging
    if (sessions.length > 0) {
      console.log(`[API] First session: id=${sessions[0].id}, title=${sessions[0].title}, teacher=${sessions[0].teacher?.name || 'unknown'}`);
    }

    res.json(sessions);
  } catch (error) {
    console.error('[API] Error getting sessions:', error);
    res.status(500).json({ message: 'Failed to fetch sessions' });
  }
});

app.get('/api/teachers', async (req, res) => {
  try {
    console.log('[API] Getting all teachers');

    // Get the teacher service
    const teacherService = serviceManager.getTeacherService();

    // Get all teachers
    const teachers = await teacherService.getAllTeachers();
    console.log(`[API] Found ${teachers.length} teachers in database`);

    // Log the first teacher's details for debugging
    if (teachers.length > 0) {
      console.log(`[API] First teacher: id=${teachers[0].id}, name=${teachers[0].name}, specializations=${teachers[0].profile?.specializations?.join(', ') || 'none'}`);
    }

    res.json(teachers);
  } catch (error) {
    console.error('[API] Error getting all teachers:', error);
    res.status(500).json({ message: 'Failed to fetch teachers' });
  }
});

// Backward compatibility: redirect /api/teachers to /api/teachers
app.get('/api/teachers', async (req, res) => {
  try {
    console.log('[API] Getting all teachers (redirecting to teachers)');

    // Get the teacher service
    const teacherService = serviceManager.getTeacherService();

    // Get all teachers (same as teachers)
    const teachers = await teacherService.getAllTeachers();
    console.log(`[API] Found ${teachers.length} teachers in database`);

    res.json(teachers);
  } catch (error) {
    console.error('[API] Error getting all teachers:', error);
    res.status(500).json({ message: 'Failed to fetch teachers' });
  }
});

// Import server port from shared config
import { SERVER_PORT } from '../shared/config';

// Add port fallback logic
async function findAvailablePort(startPort: number = SERVER_PORT, maxAttempts = 10): Promise<number> {
  // Always try the standard port first
  try {
    await new Promise<void>((resolve, reject) => {
      const tester = net.createServer()
        .once('error', (err: any) => {
          if (err.code === 'EADDRINUSE') {
            console.log(`Port ${startPort} in use, will try to kill the process...`);
            // We'll reject and let the caller handle this
          }
          reject(err);
        })
        .once('listening', () => {
          tester.once('close', () => resolve())
            .close();
        })
        .listen(startPort);
    });
    console.log(`Standard port ${startPort} is available`);
    return startPort;
  } catch (err) {
    console.log(`Standard port ${startPort} is not available, will try to find another port...`);
  }

  // If standard port is not available, try alternatives
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const port = startPort + attempt + 1; // Start from next port
    try {
      await new Promise<void>((resolve, reject) => {
        const tester = net.createServer()
          .once('error', (err: any) => {
            if (err.code === 'EADDRINUSE') {
              console.log(`Port ${port} in use, trying next port...`);
            }
            reject(err);
          })
          .once('listening', () => {
            tester.once('close', () => resolve())
              .close();
          })
          .listen(port);
      });
      console.log(`Found available port: ${port}`);
      return port;
    } catch (err) {
      if (attempt === maxAttempts - 1) {
        throw new Error(`Could not find an available port after ${maxAttempts} attempts`);
      }
    }
  }
  throw new Error('No ports available');
}

// Modify the server creation to capture the server instance
async function startServer(app: Express, httpServer: any) {
  try {
    // Initialize database first
    await setupDatabase();

    // Store the HTTP server instance
    server = httpServer;

    // Setup WebSocket server
    const io = setupWebSocketServer(httpServer);

    // Register all routes
    await registerRoutes(app, io);

    // Import error middleware
    const { errorMiddleware, notFoundMiddleware } = await import('./middleware');

    // Import monitoring service
    const { monitoring } = await import('./services/monitoring');

    // Add request monitoring middleware
    app.use(monitoring.createRequestMonitoringMiddleware());

    // Register error handling middleware (must be after all routes)
    app.use(notFoundMiddleware);
    app.use(errorMiddleware);

    console.log('Server initialized. Routes registered.');

    // Setup graceful shutdown
    setupGracefulShutdown(httpServer, app);

    // Get preferred port from shared config or environment variable
    const preferredPort = parseInt(process.env.PORT || SERVER_PORT.toString(), 10);
    console.log(`Preferred server port: ${preferredPort}`);

    try {
      // First check if the port is available
      const port = await findAvailablePort(preferredPort);

      // If we got a different port than preferred, log a warning
      if (port !== preferredPort) {
        console.warn(`⚠️ WARNING: Using port ${port} instead of preferred port ${preferredPort}`);
        console.warn(`⚠️ This may cause issues with client connections. Consider stopping the process using port ${preferredPort}.`);
      }

      // Start the server on the available port
      httpServer.listen(port, '0.0.0.0', () => {
        const address = httpServer.address() as AddressInfo;
        console.log(`🚀 Server running on port ${address.port}`);

        // If we're using a non-standard port, log additional information
        if (address.port !== SERVER_PORT) {
          console.log(`⚠️ Note: Server is running on non-standard port ${address.port} instead of ${SERVER_PORT}`);
          console.log(`⚠️ Client applications may need to be updated to connect to this port`);
        }
      });
    } catch (portError) {
      console.error('Failed to find available port:', portError);
      process.exit(1);
    }

    // Initialize production message scheduler (Supabase-only)
    console.log('Starting message scheduler polling (development mode only)');
    productionMessageScheduler.start();

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Add graceful shutdown handler
function setupGracefulShutdown(server: any, app: Express) {
  let shuttingDown = false;

  const gracefulShutdown = async (signal: NodeJS.Signals) => {
    if (shuttingDown) return;
    shuttingDown = true;

    console.log(`\n${signal} received. Starting graceful shutdown...`);

    // Stop accepting new connections
    server.close(() => {
      console.log('HTTP server closed');
    });

    try {
      // Cleanup tasks
      if (dbConnectionActive) {
        console.log('Closing database connections...');
        await db.end();
      }

      // Stop production message scheduler
      console.log('Stopping message scheduler...');
      productionMessageScheduler.stop();

      console.log('Cleanup complete. Exiting...');
      process.exit(0);
    } catch (error) {
      console.error('Error during shutdown:', error);
      process.exit(1);
    }
  };

  // Listen for shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
}

// Initialize WebSocket server
function setupWebSocketServer(httpServer: any) {
  try {
    console.log('Socket.IO server initialized with path: /socket.io');
    // Correct instantiation of Socket.IO server
    const io = new Server(httpServer, {
      path: '/socket.io',
      cors: {
        origin: function (origin, callback) {
          const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001'
          ];
          if (!origin ||
            process.env.NODE_ENV !== 'production' ||
            allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            console.warn(`[WebSocket] Rejecting connection from origin: ${origin}`);
            callback(new Error('Not allowed by CORS'), false);
          }
        },
        methods: ["GET", "POST"],
        credentials: true
      },
      pingTimeout: 60000,
      pingInterval: 25000
    });
    // WebSocket events for cancellation chat are now handled directly without Redis
    // --- END REDIS PUB/SUB BRIDGE ---
    // Add global error handler for database connection issues
    io.use(async (socket, next) => {
      // Update database activity whenever a socket connects
      resetIdleTimer();

      // Attach database error handler to socket
      socket.on('error', async (error) => {
        console.error('Socket.IO error:', error);
        if (error && error.message && (
          error.message.includes('pool is draining') ||
          error.message.includes('Cannot use a pool after calling end')
        )) {
          try {
            console.log('Socket.IO detected database connection issue, reconnecting...');
            await db.reconnect();
            console.log('Database reconnected for Socket.IO');
          } catch (reconnectError) {
            console.error('Failed to reconnect database for Socket.IO:', reconnectError);
          }
        }
      });

      next();
    });

    // Set up Socket.IO server
    io.on('connection', (socket) => {
      // Store the client's socket in memory for this session
      const clientId = socket.id;
      console.log(`Socket.IO client connected: ${clientId}`);

      // Handle authentication
      socket.on('authenticate', async (data) => {
        try {
          console.log('WebSocket authentication attempt with data:', data);
          const { userId } = data;

          if (!userId) {
            socket.emit('auth_response', { success: false, message: 'No user ID provided' });
            return;
          }

          // Get the user service
          const userService = serviceManager.getUserService();

          // Verify the user exists
          const user = await userService.getUser(userId);
          if (!user) {
            socket.emit('auth_response', { success: false, message: 'User not found' });
            return;
          }

          // Associate this socket with the user
          socket.data.userId = userId;
          socket.data.authenticated = true;
          console.log(`User ${userId} authenticated via Socket.IO`);

          // Join a room specific to this user
          socket.join(`user:${userId}`);

          // Acknowledge successful authentication
          socket.emit('auth_response', { success: true });

          // Send initial data to the user
          try {
            // Get the message service
            const messageService = serviceManager.getMessageService();

            // Get user conversations
            const conversations = await messageService.getUserConversations(userId);
            socket.emit('message', {
              type: 'conversations',
              conversations
            });
            console.log(`Sent conversations via WebSocket for user: ${userId}`);
          } catch (error) {
            console.error('Error sending conversations:', error);
          }
        } catch (error) {
          console.error('Error during WebSocket authentication:', error);
          socket.emit('auth_response', { success: false, message: 'Authentication error' });
        }
      });

      // Handle ping messages to keep connection alive
      socket.on('ping', () => {
        socket.emit('pong');
      });

      // Handle client messages
      socket.on('message', (data) => {
        // Process messages from clients
        if (!socket.data.authenticated) {
          console.log('Unauthenticated client attempted to send message');
          return;
        }

        // Handle different message types
        if (data.type === 'typing') {
          handleTypingIndicator(socket, data);
        }
      });

      // Handle disconnections
      socket.on('disconnect', () => {
        const userId = socket.data.userId;
        console.log(`User ${userId || 'unknown'} disconnected`);
      });

      // Add middleware for database queries
      socket.use(async ([event, ...args], next) => {
        try {
          // Before processing any event, make sure database is active
          if (!dbConnectionActive) {
            try {
              await db.reconnect();
              dbConnectionActive = true;
              console.log('Database reconnected for Socket.IO event:', event);
            } catch (error) {
              console.error(`Failed to reconnect database for Socket.IO event ${event}:`, error);
            }
          }
          return next();
        } catch (error) {
          console.error(`Socket.IO middleware error for event ${event}:`, error);
          return next(error as Error);
        }
      });
    });

    return io;
  } catch (error) {
    console.error('WebSocket server setup error:', error);
    return null;
  }
}

// Add this handler function for typing indicators
function handleTypingIndicator(socket: any, data: any) {
  try {
    const { conversationId, isTyping } = data;
    const userId = socket.data.userId;

    if (!conversationId || typeof isTyping !== 'boolean' || !userId) {
      return;
    }

    // Get the message service
    const messageService = serviceManager.getMessageService();

    // Get the conversation to find other participants
    messageService.getConversation(conversationId).then(conversation => {
      if (!conversation) return;

      // Only continue if this user is a participant
      if (!conversation.participantIds.includes(userId)) return;

      // Send typing indicator to other participants
      conversation.participantIds.forEach(participantId => {
        if (participantId !== userId) {
          socket.to(`user:${participantId}`).emit('message', {
            type: 'typing',
            conversationId,
            userId,
            isTyping
          });
        }
      });
    }).catch(err => {
      console.error('Error handling typing indicator:', err);
    });
  } catch (error) {
    console.error('Error in typing indicator handler:', error);
  }
}

// Endpoint to mark conversation as read
app.post('/api/conversations/:conversationId/read', async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.user?.id;

  if (!conversationId || !userId) {
    return res.status(400).json({ error: 'Missing conversationId or userId' });
  }

  try {
    // Get the message service
    const messageService = serviceManager.getMessageService();

    // Mark messages as read
    await messageService.markMessagesAsRead(parseInt(conversationId), userId);
    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error marking conversation as read:', error);
    return res.status(500).json({ error: 'Failed to mark conversation as read' });
  }
});

// Endpoint to delete a conversation
app.delete('/api/conversations/:conversationId', async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.user?.id;

  if (!conversationId || !userId) {
    return res.status(400).json({ error: 'Missing conversationId or userId' });
  }

  try {
    // Get the message service
    const messageService = serviceManager.getMessageService();

    // Delete the conversation
    const result = await messageService.deleteConversation(parseInt(conversationId), userId);
    if (result) {
      return res.status(200).json({ success: true });
    } else {
      return res.status(404).json({ error: 'Conversation not found or user not authorized' });
    }
  } catch (error) {
    console.error('Error deleting conversation:', error);
    return res.status(500).json({ error: 'Failed to delete conversation' });
  }
});

// Define CORS origins
const corsOrigins = process.env.NODE_ENV === 'production'
  ? ['https://sessionhub-production.up.railway.app', 'https://sessionhub.netlify.app']
  : ['http://localhost:4005', 'http://localhost:3000', 'http://127.0.0.1:4005', 'http://127.0.0.1:3000'];

startServer(app, createServer(app));