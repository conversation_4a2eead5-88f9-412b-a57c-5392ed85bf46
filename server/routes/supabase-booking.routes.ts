/**
 * Supabase Booking API Routes
 * 
 * These routes handle booking-related operations that require the service role key.
 */

import { Router } from 'express';
import { bookingService } from '../services/supabase/bookingService';
import { sessionService } from '../services/supabase/sessionService';
import { validateJWT, isAdmin } from '../middleware/supabase-auth';

const router = Router();

/**
 * Get user bookings
 * This is a protected operation that requires authentication
 */
router.get('/user', validateJWT, async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // Get user bookings
    const bookings = await bookingService.getUserBookings(userId, limit, offset);

    res.json({
      success: true,
      bookings
    });
  } catch (error) {
    console.error('[BookingRoutes] Error getting user bookings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get bookings'
    });
  }
});

/**
 * Get session bookings
 * This is a protected operation that requires authentication and session ownership
 */
router.get('/session/:sessionId', validateJWT, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // Get the session to check ownership
    const session = await sessionService.getSession(sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    // Check if user is the teacher
    if (session.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized: You can only view bookings for your own sessions'
      });
    }

    // Get session bookings
    const bookings = await bookingService.getSessionBookings(sessionId, limit, offset);

    res.json({
      success: true,
      bookings
    });
  } catch (error) {
    console.error('[BookingRoutes] Error getting session bookings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get bookings'
    });
  }
});

/**
 * Get a booking by ID
 * This is a protected operation that requires authentication and booking ownership
 */
router.get('/:bookingId', validateJWT, async (req, res) => {
  try {
    const { bookingId } = req.params;

    // Get the booking
    const booking = await bookingService.getBooking(bookingId);

    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Check if user is the booking owner or session teacher
    if (booking.user_id !== req.user.id && booking.sessions.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized: You can only view your own bookings or bookings for your sessions'
      });
    }

    res.json({
      success: true,
      booking
    });
  } catch (error) {
    console.error('[BookingRoutes] Error getting booking:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get booking'
    });
  }
});

/**
 * Create a booking with payment
 * This is a protected operation that requires authentication
 */
router.post('/create-with-payment', validateJWT, async (req, res) => {
  try {
    const { booking: bookingData, paymentMethod } = req.body;

    // Set the user ID to the authenticated user
    bookingData.user_id = req.user.id;

    // Create the booking with payment
    const booking = await bookingService.createBookingWithPayment(bookingData, paymentMethod);

    res.status(201).json({
      success: true,
      booking
    });
  } catch (error) {
    console.error('[BookingRoutes] Error creating booking with payment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create booking'
    });
  }
});

/**
 * Cancel a booking
 * This is a protected operation that requires authentication and booking ownership
 */
router.post('/:bookingId/cancel', validateJWT, async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { reason, refund } = req.body;

    // Get the booking to check ownership
    const booking = await bookingService.getBooking(bookingId);

    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Check if user is the booking owner or session teacher
    if (booking.user_id !== req.user.id && booking.sessions.teacher_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized: You can only cancel your own bookings or bookings for your sessions'
      });
    }

    // Cancel the booking
    const cancelledBooking = await bookingService.cancelBooking(bookingId, reason, refund);

    res.json({
      success: true,
      booking: cancelledBooking
    });
  } catch (error) {
    console.error('[BookingRoutes] Error cancelling booking:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel booking'
    });
  }
});

/**
 * Admin: Get all bookings
 * This is an admin-only operation
 */
router.get('/admin/all', validateJWT, isAdmin, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // Get all bookings
    const { data, error } = await bookingService.supabaseAdmin
      .from('bookings')
      .select('*, sessions(*), user_profiles(*)')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      bookings: data
    });
  } catch (error) {
    console.error('[BookingRoutes] Error getting all bookings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get bookings'
    });
  }
});

export default router;
