import express from 'express';
import { manuallyVerifyUserEmail } from '../utils/manualVerify';
import { storage } from '../storage';
import { clearCache } from '../middleware/cache';

const router = express.Router();

/**
 * Development-only route to manually verify a user's email
 * This is useful for testing and development purposes
 *
 * POST /api/dev-tools/verify-email
 * Body: { usernameOrEmail: string }
 */
router.post('/verify-email', async (req, res) => {
  // Only allow in development mode
  if (process.env.NODE_ENV !== 'development') {
    return res.status(404).json({
      success: false,
      message: 'This endpoint is only available in development mode'
    });
  }

  try {
    const { usernameOrEmail } = req.body;

    if (!usernameOrEmail) {
      return res.status(400).json({
        success: false,
        message: 'Username or email is required'
      });
    }

    const success = await manuallyVerifyUserEmail(usernameOrEmail);

    if (success) {
      return res.status(200).json({
        success: true,
        message: `Successfully verified email for user: ${usernameOrEmail}`
      });
    } else {
      return res.status(400).json({
        success: false,
        message: `Failed to verify email for user: ${usernameOrEmail}`
      });
    }
  } catch (error) {
    console.error('Error in manual email verification endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while processing your request'
    });
  }
});

// Get all profiles for a user (for debugging)
router.get('/user-profiles/:userId', async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);

    if (isNaN(userId)) {
      return res.status(400).json({ message: "Invalid user ID" });
    }

    // Get user data
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Get profile from user_profiles table
    const profile = await storage.getUserProfile(userId);

    // Check if there are any backup profiles
    const backupProfiles = await storage.executeQuery(`
      SELECT * FROM profiles_backup WHERE user_id = $1
      UNION ALL
      SELECT * FROM teacher_profiles_backup WHERE user_id = $1
    `, [userId]);

    // Return all data
    res.status(200).json({
      user,
      profile,
      backupProfiles: backupProfiles || []
    });
  } catch (error) {
    console.error('Error getting user profiles:', error);
    res.status(500).json({ message: "Error fetching user profiles", error: String(error) });
  }
});

// Clear cache
router.post('/clear-cache', (req, res) => {
  try {
    clearCache();
    res.status(200).json({ message: "Cache cleared successfully" });
  } catch (error) {
    console.error('Error clearing cache:', error);
    res.status(500).json({ message: "Error clearing cache", error: String(error) });
  }
});

// Fix missing profiles
router.post('/fix-missing-profiles', async (req, res) => {
  try {
    // Find users without profiles
    const usersWithoutProfiles = await storage.executeQuery(`
      SELECT u.id, u.username, u.email
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE up.id IS NULL
    `);

    console.log(`Found ${usersWithoutProfiles.length} users without profiles`);

    // Create empty profiles for these users
    for (const user of usersWithoutProfiles) {
      await storage.executeQuery(`
        INSERT INTO user_profiles (
          user_id,
          specializations,
          skills,
          certifications,
          experience,
          education,
          website,
          facebook_url,
          twitter_url,
          instagram_url,
          linkedin_url,
          youtube_url,
          tiktok_url,
          show_profile,
          show_social_links,
          show_contact,
          show_teaching_sessions,
          show_learning_sessions,
          created_at,
          updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
        )
      `, [
        user.id,
        [],
        [],
        [],
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        true,
        true,
        false,
        true,
        false
      ]);

      console.log(`Created empty profile for user ${user.id}`);
    }

    res.status(200).json({
      message: "Fixed missing profiles",
      count: usersWithoutProfiles.length,
      users: usersWithoutProfiles.map(u => ({ id: u.id, username: u.username }))
    });
  } catch (error) {
    console.error('Error fixing missing profiles:', error);
    res.status(500).json({ message: "Error fixing missing profiles", error: String(error) });
  }
});

export default router;
