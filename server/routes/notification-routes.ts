import { Router, Request, Response } from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import serviceManager from '../services/ServiceManager';
import { z } from 'zod';

const router = Router();
const notificationService = serviceManager.getNotificationService();

/**
 * @api {get} /api/notifications Get user notifications
 * @apiDescription Get notifications for the authenticated user
 * @apiQuery {number} [limit] Limit the number of results
 * @apiQuery {number} [offset] Offset the results
 * @apiQuery {boolean} [unreadOnly] Only return unread notifications
 */
router.get('/notifications', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 20;
    const offset = req.query.offset ? parseInt(req.query.offset as string, 10) : 0;
    const unreadOnly = req.query.unreadOnly === 'true';

    const notifications = await notificationService.getUserNotifications(userId, limit, offset, unreadOnly);

    return res.json(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return res.status(500).json({
      message: "Failed to fetch notifications",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {get} /api/notifications/count Get unread notification count
 * @apiDescription Get the number of unread notifications for the authenticated user
 */
router.get('/notifications/count', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    const count = await notificationService.getUnreadNotificationCount(userId);

    return res.json({ count });
  } catch (error) {
    console.error('Error fetching notification count:', error);
    return res.status(500).json({
      message: "Failed to fetch notification count",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {put} /api/notifications/:id/read Mark notification as read
 * @apiDescription Mark a notification as read
 * @apiParam {number} id Notification ID
 */
router.put('/notifications/:id/read', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid notification ID' });
    }

    // Check if the notification exists and belongs to the user
    const notification = await notificationService.getNotification(id);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    if (notification.userId !== req.user.id) {
      return res.status(403).json({ error: 'You do not have permission to access this notification' });
    }

    // Mark the notification as read
    const updated = await notificationService.markNotificationAsRead(id);

    if (!updated) {
      return res.status(500).json({ error: 'Failed to mark notification as read' });
    }

    return res.json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/notifications/read-all Mark all notifications as read
 * @apiDescription Mark all notifications as read for the authenticated user
 */
router.put('/notifications/read-all', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    const updated = await notificationService.markAllNotificationsAsRead(userId);

    return res.json({ message: 'All notifications marked as read', count: updated });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/device-tokens Register device token
 * @apiDescription Register a device token for push notifications
 * @apiBody {string} token Device token
 * @apiBody {string} platform Platform (ios, android, web)
 */
router.post('/device-tokens', validateJWT, async (req: Request, res: Response) => {
  try {
    // Validate request body
    const schema = z.object({
      token: z.string().min(1),
      platform: z.enum(['ios', 'android', 'web'])
    });

    const { token, platform } = schema.parse(req.body);
    const userId = req.user.id;

    // Register the device token
    const deviceToken = await notificationService.registerDeviceToken(userId, token, platform);

    return res.status(201).json(deviceToken);
  } catch (error) {
    console.error('Error registering device token:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {delete} /api/device-tokens/:token Remove device token
 * @apiDescription Remove a device token
 * @apiParam {string} token Device token
 */
router.delete('/device-tokens/:token', validateJWT, async (req: Request, res: Response) => {
  try {
    const token = req.params.token;

    // Remove the device token
    const removed = await notificationService.removeDeviceToken(token);

    if (!removed) {
      return res.status(404).json({ error: 'Device token not found' });
    }

    return res.json({ message: 'Device token removed' });
  } catch (error) {
    console.error('Error removing device token:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/notification-preferences Get notification preferences
 * @apiDescription Get notification preferences for the authenticated user
 */
router.get('/notification-preferences', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    const preferences = await notificationService.getNotificationPreferences(userId);

    return res.json(preferences);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/notification-preferences Update notification preferences
 * @apiDescription Update notification preferences for the authenticated user
 * @apiBody {boolean} [emailEnabled] Enable/disable email notifications
 * @apiBody {boolean} [pushEnabled] Enable/disable push notifications
 * @apiBody {boolean} [bookingNotifications] Enable/disable booking notifications
 * @apiBody {boolean} [messageNotifications] Enable/disable message notifications
 * @apiBody {boolean} [reviewNotifications] Enable/disable review notifications
 * @apiBody {boolean} [reminderNotifications] Enable/disable reminder notifications
 */
router.put('/notification-preferences', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    // Validate request body
    const schema = z.object({
      emailEnabled: z.boolean().optional(),
      pushEnabled: z.boolean().optional(),
      bookingNotifications: z.boolean().optional(),
      messageNotifications: z.boolean().optional(),
      reviewNotifications: z.boolean().optional(),
      reminderNotifications: z.boolean().optional()
    });

    const preferences = schema.parse(req.body);

    // Update notification preferences
    const updated = await notificationService.updateNotificationPreferences(userId, preferences);

    return res.json(updated);
  } catch (error) {
    console.error('Error updating notification preferences:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

export default router;
