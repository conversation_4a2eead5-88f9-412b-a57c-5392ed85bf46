/**
 * Supabase Teacher API Routes
 * 
 * These routes handle teacher-related operations that require the service role key.
 */

import { Router } from 'express';
import { supabaseAdmin } from '../lib/supabase';

const router = Router();

/**
 * Get all teachers
 */
router.get('/', async (req, res) => {
  try {
    console.log('[API] Fetching teachers from Supabase');

    // Query teachers from user_profiles table
    const { data: teachers, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('is_teacher', true);

    if (error) {
      console.error('Error fetching teachers from Supabase:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch teachers'
      });
    }

    console.log(`[API] Found ${teachers.length} teachers in Supabase`);

    res.json(teachers);
  } catch (error) {
    console.error('[TeacherRoutes] Error getting teachers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get teachers'
    });
  }
});

/**
 * Get teacher by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`[API] Fetching teacher ${id} from Supabase`);

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      console.error(`[API] Invalid UUID format: ${id}`);
      return res.status(400).json({
        success: false,
        error: 'Invalid teacher ID format'
      });
    }

    // Query teacher from user_profiles table using user_id
    const { data: teacher, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .eq('user_id', id)
      .single();

    if (error) {
      console.error(`Error fetching teacher ${id} from Supabase:`, error);
      return res.status(404).json({
        success: false,
        error: 'Teacher not found'
      });
    }

    if (!teacher) {
      return res.status(404).json({
        success: false,
        error: 'Teacher not found'
      });
    }

    // Check if the user is actually a teacher
    if (!teacher.is_teacher && !teacher.is_teacher) {
      console.log(`[API] User ${id} is not a teacher`);
      return res.status(404).json({
        success: false,
        error: 'User is not a teacher'
      });
    }

    // Format the response to match the expected UserWithProfile structure
    const formattedTeacher = {
      id: teacher.user_id,
      name: teacher.name,
      username: teacher.username,
      email: teacher.email, // Note: This might be null due to privacy
      avatar: teacher.avatar,
      bio: teacher.bio,
      timezone: teacher.timezone,
      isTeacher: teacher.is_teacher,
      isTeacher: teacher.is_teacher,
      profile: {
        id: teacher.id || teacher.user_id,
        user_id: teacher.user_id,
        name: teacher.name,
        avatar: teacher.avatar,
        coverPhoto: teacher.cover_photo,
        bio: teacher.bio,
        location: teacher.location,
        timezone: teacher.timezone,
        website: teacher.website,
        specializations: teacher.specializations || [],
        skills: teacher.skills || [],
        certifications: teacher.certifications || [],
        experience: teacher.experience,
        education: teacher.education,
        rating: 0, // TODO: Calculate from reviews
        reviewCount: 0, // TODO: Calculate from reviews
        facebookUrl: teacher.facebook_url,
        twitterUrl: teacher.twitter_url,
        instagramUrl: teacher.instagram_url,
        linkedinUrl: teacher.linkedin_url,
        youtubeUrl: teacher.youtube_url,
        tiktokUrl: teacher.tiktok_url,
        isTeacher: teacher.is_teacher,
        isTeacher: teacher.is_teacher,
        showTeachingSessions: true, // Default values
        showLearningSessions: true,
        showProfile: true,
        showSocialLinks: true,
        showContact: true
      }
    };

    console.log(`[API] Successfully fetched teacher: ${formattedTeacher.name}`);
    res.json(formattedTeacher);
  } catch (error) {
    console.error('[TeacherRoutes] Error getting teacher:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get teacher'
    });
  }
});

export default router;
