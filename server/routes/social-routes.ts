import express from 'express';
import { storage } from '../storage';

const router = express.Router();

/**
 * Get all social accounts for the authenticated user
 */
router.get('/api/user/social-accounts', async (req, res) => {
  try {
    const userId = req.user.id;

    // Get social accounts from database
    const socialAccounts = await storage.getSocialAccounts(userId);

    res.json(socialAccounts);
  } catch (error) {
    console.error('Error fetching social accounts:', error);
    res.status(500).json({ error: 'Failed to fetch social accounts' });
  }
});

/**
 * Link a social account to the authenticated user
 */
router.post('/api/user/social-accounts', async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider, providerId, username, profileUrl } = req.body;

    if (!provider || !providerId) {
      return res.status(400).json({ error: 'Provider and providerId are required' });
    }

    // Link social account
    await storage.linkSocialAccount(userId, {
      provider,
      providerId,
      username,
      profileUrl
    });

    res.json({ success: true });
  } catch (error) {
    console.error('Error linking social account:', error);
    res.status(500).json({ error: 'Failed to link social account' });
  }
});

/**
 * Delete a social account from the authenticated user
 */
router.delete('/api/user/social-accounts/:provider/:providerId', async (req, res) => {
  try {
    const userId = req.user.id;
    const { provider, providerId } = req.params;

    // Delete social account
    // Note: This method needs to be implemented in the storage class
    await storage.deleteSocialAccount(userId, provider, providerId);

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting social account:', error);
    res.status(500).json({ error: 'Failed to delete social account' });
  }
});

export default router;
