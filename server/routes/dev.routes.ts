import { Express, Request, Response } from 'express';
import { storage } from '../storage';
import { monitoring } from '../services/monitoring';
import { validateJWT } from '../middleware/supabase-auth';

/**
 * Register development-only routes
 * @param app Express application
 */
export function registerDevRoutes(app: Express): void {
  // Only register these routes in development mode
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.log('[Routes] Registering development routes');

  // Get all users (development only)
  app.get('/api/dev/users', async (req: Request, res: Response) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      console.error('Error fetching all users:', error);
      res.status(500).json({ message: 'Failed to fetch users' });
    }
  });

  // Create test data (development only)
  app.post('/api/dev/create-test-data', async (req: Request, res: Response) => {
    try {
      // Create test users
      const users = [];
      for (let i = 0; i < 5; i++) {
        const user = await storage.createUser({
          email: `test${i}@example.com`,
          password: 'password123',
          name: `Test User ${i}`,
          username: `testuser${i}`
        });

        // Create user profile
        await storage.createUserProfile({
          userId: user.id,
          displayName: `Test User ${i}`,
          bio: `This is a test user profile for user ${i}`,
          isTeacher: i % 2 === 0 // Make every other user a teacher
        });

        users.push(user);
      }

      // Create test sessions for teacher users
      const sessions = [];
      for (const user of users) {
        if (user.id % 2 === 0) { // Only for teacher users
          for (let i = 0; i < 3; i++) {
            const session = await storage.createSession({
              teacherId: user.id,
              title: `Test Session ${i} by ${user.name}`,
              description: `This is a test session ${i} created by ${user.name}`,
              price: 50 + i * 10,
              duration: 60,
              date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000), // Future dates
              isPublic: true
            });

            sessions.push(session);
          }
        }
      }

      // Create test bookings
      const bookings = [];
      for (const user of users) {
        if (user.id % 2 !== 0) { // Only for non-teacher users
          for (const session of sessions.slice(0, 2)) { // Book first two sessions
            const booking = await storage.createBooking({
              userId: user.id,
              sessionId: session.id,
              date: session.date,
              status: 'confirmed'
            });

            bookings.push(booking);
          }
        }
      }

      res.json({
        message: 'Test data created successfully',
        users: users.length,
        sessions: sessions.length,
        bookings: bookings.length
      });
    } catch (error) {
      console.error('Error creating test data:', error);
      res.status(500).json({ message: 'Failed to create test data' });
    }
  });

  // Reset database (development only)
  app.post('/api/dev/reset-database', async (req: Request, res: Response) => {
    try {
      // This is a placeholder for a database reset function
      // In a real implementation, you would need to be very careful with this
      res.json({ message: 'Database reset functionality is disabled for safety' });
    } catch (error) {
      console.error('Error resetting database:', error);
      res.status(500).json({ message: 'Failed to reset database' });
    }
  });

  // Console logs endpoint (development only)
  app.post('/api/dev/console-logs', (req: Request, res: Response) => {
    try {
      const { level, message, timestamp, url } = req.body;
      const logPrefix = `[BROWSER-${level.toUpperCase()}]`;
      const urlPath = new URL(url).pathname;

      // Format the log message
      const formattedMessage = `${logPrefix} ${urlPath} - ${message}`;

      // Output to console with appropriate level
      switch (level) {
        case 'error':
          console.error(formattedMessage);
          break;
        case 'warn':
          console.warn(formattedMessage);
          break;
        case 'info':
          console.info(formattedMessage);
          break;
        default:
          console.log(formattedMessage);
      }

      res.status(200).json({ success: true });
    } catch (error) {
      console.error('Error processing console log:', error);
      res.status(500).json({ error: 'Failed to process log' });
    }
  });

  // Monitoring endpoint (development only)
  if (process.env.NODE_ENV !== 'production') {
    app.get('/api/dev/monitoring', validateJWT, (req: Request, res: Response) => {
      const errorStats = monitoring.getErrorStats();
      const requestStats = monitoring.getRequestStats();

      res.json({
        errors: errorStats,
        requests: requestStats.requests,
        slowRequests: requestStats.slowRequests
      });
    });

    app.post('/api/dev/monitoring/reset', validateJWT, (req: Request, res: Response) => {
      monitoring.resetStats();
      res.json({ message: 'Monitoring stats reset successfully' });
    });
  }
}
