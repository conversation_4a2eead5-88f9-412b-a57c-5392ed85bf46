import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';

// Validation schemas
const conversationIdSchema = z.object({
  id: z.string().refine(val => !isNaN(parseInt(val)), {
    message: "Conversation ID must be a valid number"
  }).transform(val => parseInt(val))
});

const createConversationSchema = z.object({
  title: z.string().optional(),
  participantIds: z.array(z.number()).min(1, "At least one participant is required"),
  initialMessage: z.string().optional()
});

/**
 * Register conversation-related routes
 * @param app Express application
 */
export function registerConversationRoutes(app: Express): void {
  console.log('[Routes] Registering conversation routes');

  // Get user's conversations
  app.get('/api/conversations',
    asyncHandler(async (req: Request, res: Response) => {
      const userId = req.user.id;

      // Get user's conversations
      const conversations = await storage.getUserConversations(userId);

      // Sort conversations by last activity (most recent first)
      conversations.sort((a, b) => {
        const dateA = a.lastActivityAt ? new Date(a.lastActivityAt).getTime() : 0;
        const dateB = b.lastActivityAt ? new Date(b.lastActivityAt).getTime() : 0;
        return dateB - dateA;
      });

      res.json(conversations);
    })
  );

  // Get conversation by ID
  app.get('/api/conversations/:id',
    validate(conversationIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: conversationId } = req.params as unknown as z.infer<typeof conversationIdSchema>;

      // Get the conversation with messages
      const conversation = await storage.getConversationWithMessages(conversationId);

      if (!conversation) {
        throw errorHandler.notFound('Conversation not found');
      }

      // Check if user is a participant in the conversation
      const isParticipant = conversation.participants.some(p => p.id === req.user.id);

      if (!isParticipant) {
        throw errorHandler.forbidden('You are not a participant in this conversation');
      }

      // Mark messages as read for the current user
      await storage.markMessagesAsRead(conversationId, req.user.id);

      res.json(conversation);
    })
  );

  // Create a new conversation
  app.post('/api/conversations',
    validate(createConversationSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const validatedData = req.body;
      const userId = req.user.id;

      // Ensure the current user is included in participants
      if (!validatedData.participantIds.includes(userId)) {
        validatedData.participantIds.push(userId);
      }

      // Create the conversation
      const conversation = await storage.createConversation({
        title: validatedData.title || 'New Conversation',
        participantIds: validatedData.participantIds
      });

      // Send initial message if provided
      if (validatedData.initialMessage) {
        await storage.createMessage({
          conversationId: conversation.id,
          senderId: userId,
          content: validatedData.initialMessage,
          isRead: false
        });
      }

      // Get the full conversation with participants
      const fullConversation = await storage.getConversation(conversation.id);

      res.status(201).json(fullConversation);
    })
  );

  // Get unread message count
  app.get('/api/conversations/unread/count',
    asyncHandler(async (req: Request, res: Response) => {
      const userId = req.user.id;

      // Get unread message count
      const unreadCount = await storage.getUnreadMessageCount(userId);

      res.json({ count: unreadCount });
    })
  );
}
