import { Router, Request, Response } from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import serviceManager from '../services/ServiceManager';
import { invalidateCache } from '../middleware/cache';
import { z } from 'zod';
import { insertReviewSchema } from '@shared/schema';

const router = Router();
const reviewService = serviceManager.getReviewService();
const sessionService = serviceManager.getSessionService();

/**
 * @api {get} /api/reviews Get all reviews
 * @apiDescription Get all reviews, optionally filtered
 * @apiQuery {number} [sessionId] Filter by session ID
 * @apiQuery {number} [userId] Filter by user ID
 * @apiQuery {number} [limit] Limit the number of results
 * @apiQuery {number} [offset] Offset the results
 */
router.get('/reviews', async (req: Request, res: Response) => {
  try {
    const filters: any = {};

    // Convert query parameters to filters if provided
    if (req.query.sessionId) filters.sessionId = parseInt(req.query.sessionId as string, 10);
    if (req.query.userId) filters.userId = parseInt(req.query.userId as string, 10);

    // Parse numeric filters
    if (req.query.limit) filters.limit = parseInt(req.query.limit as string, 10);
    if (req.query.offset) filters.offset = parseInt(req.query.offset as string, 10);

    const reviews = await reviewService.getReviews(filters);

    return res.json(reviews);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return res.status(500).json({
      message: "Failed to fetch reviews",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {get} /api/reviews/:id Get review by ID
 * @apiDescription Get a review by ID
 * @apiParam {number} id Review ID
 */
router.get('/reviews/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid review ID' });
    }

    const review = await reviewService.getReview(id);

    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }

    return res.json(review);
  } catch (error) {
    console.error('Error getting review:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/reviews Create a new review
 * @apiDescription Create a new review
 * @apiBody {number} sessionId Session ID
 * @apiBody {number} userId User ID
 * @apiBody {number} rating Rating (1-5)
 * @apiBody {string} [comment] Review comment
 */
router.post('/reviews', validateJWT, async (req: Request, res: Response) => {
  try {
    console.log('[API] Creating review with data:', req.body);

    // Validate review data
    const reviewData = insertReviewSchema.parse({
      ...req.body,
      userId: req.user.id // Always use the authenticated user's ID
    });

    // Check if the session exists
    const session = await sessionService.getSession(reviewData.sessionId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Check if the user has already reviewed this session
    const existingReview = await reviewService.getUserSessionReview(reviewData.userId, reviewData.sessionId);

    if (existingReview) {
      return res.status(409).json({ error: 'User has already reviewed this session' });
    }

    // Create the review
    const review = await reviewService.createReview(reviewData);

    // Update session ratings
    await reviewService.updateSessionRatings(reviewData.sessionId);

    // Update user ratings
    await reviewService.updateUserRatings(session.teacherId);

    // Invalidate related caches
    invalidateCache([
      '/api/reviews',
      `/api/reviews/${review.id}`,
      `/api/sessions/${review.sessionId}`,
      `/api/users/${review.userId}/reviews`,
      `/api/sessions/${review.sessionId}/reviews`
    ]);

    return res.status(201).json(review);
  } catch (error) {
    console.error('Error creating review:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/sessions/:id/reviews Get session reviews
 * @apiDescription Get reviews for a session
 * @apiParam {number} id Session ID
 */
router.get('/sessions/:id/reviews', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid session ID' });
    }

    // Check if the session exists
    const session = await sessionService.getSession(id);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    const reviews = await reviewService.getSessionReviews(id);

    return res.json(reviews);
  } catch (error) {
    console.error('Error getting session reviews:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/reviews Get user reviews
 * @apiDescription Get reviews by a user
 * @apiParam {number} id User ID
 */
router.get('/users/:id/reviews', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const reviews = await reviewService.getUserReviews(id);

    return res.json(reviews);
  } catch (error) {
    console.error('Error getting user reviews:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {delete} /api/reviews/:id Delete a review
 * @apiDescription Delete a review
 * @apiParam {number} id Review ID
 */
router.delete('/reviews/:id', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid review ID' });
    }

    // Check if the review exists
    const review = await reviewService.getReview(id);

    if (!review) {
      return res.status(404).json({ error: 'Review not found' });
    }

    // Check if the user has permission to delete this review
    const isAdmin = req.user && (req.user as any).role === 'admin';
    const isReviewer = req.user && review.userId === req.user.id;

    if (!isAdmin && !isReviewer) {
      return res.status(403).json({ error: 'You do not have permission to delete this review' });
    }

    // Delete the review
    const deleted = await reviewService.deleteReview(id);

    if (!deleted) {
      return res.status(500).json({ error: 'Failed to delete review' });
    }

    // Update session ratings
    await reviewService.updateSessionRatings(review.sessionId);

    // Get the session to find the teacher ID
    const session = await sessionService.getSession(review.sessionId);

    if (session) {
      // Update user ratings
      await reviewService.updateUserRatings(session.teacherId);
    }

    // Invalidate related caches
    invalidateCache([
      '/api/reviews',
      `/api/reviews/${id}`,
      `/api/sessions/${review.sessionId}`,
      `/api/users/${review.userId}/reviews`,
      `/api/sessions/${review.sessionId}/reviews`
    ]);

    return res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

export default router;
