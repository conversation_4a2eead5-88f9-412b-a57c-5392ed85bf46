# SessionHub Routes

This directory contains the route handlers for the SessionHub API.

## Overview

The routes are organized by feature/domain:

- `session-routes.ts`: Session-related routes
- `teacher-routes.ts`: Teacher-related routes
- `auth-callback-routes.ts`: Authentication callback routes
- `social-auth-routes.ts`: Social authentication routes
- `email-verification-routes.ts`: Email verification routes
- `password-reset-routes.ts`: Password reset routes
- `uploads.ts`: File upload routes
- `reviewRoutes.ts`: Review-related routes
- `payment-routes.ts`: Payment-related routes
- `teacher-payment-routes.ts`: Teacher payment routes
- `stripe-connect-routes.ts`: Stripe Connect routes
- `dev-tools-routes.ts`: Development tools routes (development only)

## Route Structure

Each route file follows a similar structure:

1. Import dependencies
2. Create a router
3. Define routes
4. Export the router

Example:

```typescript
import { Router, Request, Response } from 'express';
import { isAuthenticated } from '../auth';
import serviceManager from '../services/ServiceManager';

const router = Router();
const someService = serviceManager.getSomeService();

// Define routes
router.get('/some-route', async (req, res) => {
  // Route handler
});

export default router;
```

## Service Integration

Routes should use the service layer for business logic. The `ServiceManager` provides access to all services:

```typescript
import serviceManager from '../services/ServiceManager';

const someService = serviceManager.getSomeService();
```

## Authentication

Routes that require authentication should use the `isAuthenticated` middleware:

```typescript
import { isAuthenticated } from '../auth';

router.post('/some-protected-route', isAuthenticated, async (req, res) => {
  // Route handler
});
```

## Error Handling

Routes should handle errors and return appropriate status codes:

```typescript
router.get('/some-route', async (req, res) => {
  try {
    // Route handler
  } catch (error) {
    console.error('Error:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});
```

## Caching

Routes that benefit from caching should use the cache middleware:

```typescript
import { cacheMiddleware } from '../middleware';

router.get('/some-cacheable-route', cacheMiddleware(300), async (req, res) => {
  // Route handler
});
```

## Validation

Routes should validate input data:

```typescript
import { z } from 'zod';

const schema = z.object({
  name: z.string().min(1),
  email: z.string().email()
});

router.post('/some-route', async (req, res) => {
  try {
    const data = schema.parse(req.body);
    // Route handler
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: "Invalid input data", errors: error.errors });
    }
    return res.status(500).json({ message: "Internal server error" });
  }
});
```

## API Documentation

Routes should include JSDoc comments for API documentation:

```typescript
/**
 * @api {get} /api/some-route Get some data
 * @apiDescription Get some data
 * @apiQuery {string} [param] Optional parameter
 */
router.get('/some-route', async (req, res) => {
  // Route handler
});
```
