import { Router, Request, Response } from 'express';
import { createClient } from '@supabase/supabase-js';
import { getDiverseSessionImageUrl, getDiverseAvatarUrl, getDiverseCoverUrl } from '../utils/default-images';

const router = Router();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://frksndjujrbjhlrcjvtf.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY is required for Supabase routes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Helper function to map Supabase session to our app's session format
function mapSupabaseSession(session: any) {
  // Process the image URL
  let imageUrl = session.image_url || '';

  // If there's an image_url, check if it needs diversity enhancement
  if (imageUrl) {
    // Check if it's a generic session type image
    const isGenericImage = imageUrl.includes('session-types/') && imageUrl.includes('/cover.jpg');

    if (isGenericImage || !imageUrl.startsWith('http')) {
      // Use diverse image instead
      imageUrl = getDiverseSessionImageUrl(session.type || 'default', session.id);
      console.log(`[mapSupabaseSession] Using diverse image URL for ${session.type}: ${imageUrl}`);
    } else if (imageUrl.startsWith('http')) {
      // It's already a full URL, use it directly
      console.log(`[mapSupabaseSession] Using direct image URL: ${imageUrl}`);
    }
  } else {
    // No image URL, generate a diverse one
    imageUrl = getDiverseSessionImageUrl(session.type || 'default', session.id);
    console.log(`[mapSupabaseSession] Generated diverse image URL for ${session.type}: ${imageUrl}`);
  }

  // Process teacher data if available
  let teacherData = null;
  if (session.teacher) {
    // Process teacher avatar
    let avatarUrl = session.teacher.avatar || '';
    if (avatarUrl && !avatarUrl.startsWith('http')) {
      const supabaseUrl = process.env.SUPABASE_URL || 'https://frksndjujrbjhlrcjvtf.supabase.co';

      if (avatarUrl.includes('storage/v1/object/public')) {
        if (!avatarUrl.startsWith('http')) {
          avatarUrl = `${supabaseUrl}/${avatarUrl}`;
        }
      } else if (avatarUrl.startsWith('profiles/')) {
        avatarUrl = `${supabaseUrl}/storage/v1/object/public/${avatarUrl}`;
      } else {
        avatarUrl = `${supabaseUrl}/storage/v1/object/public/profiles/avatars/${avatarUrl}`;
      }
    } else if (avatarUrl && avatarUrl.startsWith('http')) {
      // It's already a full URL, use it directly
      console.log(`[mapSupabaseSession] Using direct avatar URL: ${avatarUrl}`);
    }

    teacherData = {
      id: session.teacher.id || session.teacher.user_id,
      user_id: session.teacher.user_id,
      name: session.teacher.name,
      username: session.teacher.username,
      avatar: avatarUrl,
      isTeacher: session.teacher.is_teacher,
      is_teacher: session.teacher.is_teacher,
      bio: session.teacher.bio,
      specializations: session.teacher.specializations || [],
      rating: session.teacher.rating || 0,
      reviewCount: session.teacher.review_count || 0
    };

    console.log(`[mapSupabaseSession] Processed teacher data:`, teacherData);
  }

  return {
    id: session.id,
    title: session.title,
    description: session.description,
    teacherId: session.teacher_id, // camelCase version
    teacher_id: session.teacher_id, // snake_case version for compatibility
    type: session.type || 'Other',
    session_type: session.type || 'Other',
    price: session.price,
    duration: session.duration,
    date: session.date || new Date().toISOString(),
    maxParticipants: session.max_participants,
    max_participants: session.max_participants,
    currentParticipants: session.current_participants || 0,
    current_participants: session.current_participants || 0,
    skillLevel: session.skill_level || 'All Levels',
    skill_level: session.skill_level || 'All Levels',
    format: session.format || 'Group',
    language: session.language || 'English',
    is_public: session.is_public,
    isPublic: session.is_public,
    is_published: session.is_public,
    location: session.location || 'Online',
    imageUrl: imageUrl,
    image_url: imageUrl, // Add both formats for compatibility
    teacher: teacherData,
    reviewCount: session.review_count || 0,
    review_count: session.review_count || 0,
    rating: session.rating || 0,
    created_at: session.created_at, // snake_case version
    updated_at: session.updated_at, // snake_case version
    createdAt: session.created_at, // camelCase version for frontend compatibility
    updatedAt: session.updated_at  // camelCase version for frontend compatibility
  };
}

// Helper function to map Supabase teacher to our app's teacher format
function mapSupabaseTeacher(teacher: any) {
  // Process the avatar URL
  let avatarUrl = teacher.avatar || '';

  // Check if avatar needs diversity enhancement
  if (!avatarUrl || !avatarUrl.startsWith('http') || avatarUrl.includes('/avatar_')) {
    // Use diverse avatar based on teacher ID
    avatarUrl = getDiverseAvatarUrl(teacher.user_id || teacher.id);
    console.log(`[mapSupabaseTeacher] Using diverse avatar URL: ${avatarUrl}`);
  } else {
    console.log(`[mapSupabaseTeacher] Using direct avatar URL: ${avatarUrl}`);
  }

  // Process the cover photo URL
  let coverPhotoUrl = teacher.cover_photo || '';

  // Check if cover photo needs diversity enhancement
  if (!coverPhotoUrl || !coverPhotoUrl.startsWith('http') || coverPhotoUrl.includes('/cover_')) {
    // Use diverse cover based on teacher ID
    coverPhotoUrl = getDiverseCoverUrl(teacher.user_id || teacher.id);
    console.log(`[mapSupabaseTeacher] Using diverse cover photo URL: ${coverPhotoUrl}`);
  } else {
    console.log(`[mapSupabaseTeacher] Using direct cover photo URL: ${coverPhotoUrl}`);
  }

  return {
    id: teacher.id,
    name: teacher.full_name || teacher.username || teacher.name,
    username: teacher.username,
    email: teacher.email,
    avatar: avatarUrl,
    bio: teacher.bio,
    specializations: teacher.specializations || [],
    skills: teacher.skills || [],
    experience: teacher.experience,
    isTeacher: teacher.is_teacher, // camelCase version
    is_teacher: teacher.is_teacher, // snake_case version
    title: teacher.title || '',
    coverPhoto: coverPhotoUrl,
    cover_photo: coverPhotoUrl,
    rating: teacher.rating || 0,
    reviewCount: teacher.review_count || 0,
  };
}

/**
 * @api {get} /api/supabase/sessions Get all sessions from Supabase
 */
router.get('/supabase/sessions', async (req: Request, res: Response) => {
  try {
    console.log('[API] Fetching sessions from Supabase');

    // Try different table names since we're not sure which one is correct
    let sessions;
    let error;

    // First get sessions
    const result = await supabase
      .from('sessions')
      .select('*')
      .eq('is_public', true);

    if (result.data && result.data.length > 0) {
      sessions = result.data;
      error = null;
      console.log('[API] Found sessions in "sessions" table');
      console.log('[API] Found', result.data.length, 'sessions');

      // Get unique teacher IDs
      const teacherIds = Array.from(new Set(sessions.map(s => s.teacher_id).filter(Boolean)));
      console.log('[API] Found teacher IDs:', teacherIds);

      // Fetch teacher data separately
      if (teacherIds.length > 0) {
        const { data: teachers, error: teacherError } = await supabase
          .from('user_profiles')
          .select('*')
          .in('user_id', teacherIds);

        if (!teacherError && teachers) {
          console.log('[API] Found', teachers.length, 'teachers');

          // Create a map of teacher_id to teacher data
          const teacherMap = new Map();
          teachers.forEach(teacher => {
            teacherMap.set(teacher.user_id, teacher);
          });

          // Add teacher data to sessions
          sessions = sessions.map(session => ({
            ...session,
            teacher: teacherMap.get(session.teacher_id) || null
          }));

          console.log('[API] Sample session with teacher:', sessions[0]);
        } else {
          console.log('[API] Error fetching teachers:', teacherError);
        }
      }
    } else {
      // If no sessions found, return empty array
      sessions = null;
      error = result.error;
      console.log('[API] No sessions found in sessions table');
      return res.json([]);
    }

    if (error) {
      console.error('Error fetching sessions from Supabase:', error);

      // Try to get sessions from the database as a fallback
      try {
        console.log('[API] Falling back to database for sessions');
        const db = req.app.locals.db;
        const dbSessions = await db.query('SELECT * FROM sessions WHERE is_public = true LIMIT 20');

        if (dbSessions && dbSessions.length > 0) {
          console.log(`[API] Found ${dbSessions.length} sessions in database fallback`);
          return res.json(dbSessions);
        }
      } catch (dbError) {
        console.error('Database fallback failed for sessions:', dbError);
      }

      // Return empty array if all else fails
      console.log('[API] No sessions found, returning empty array');
      return res.json([]);
    }

    // Map sessions to our app's format
    const mappedSessions = sessions.map(mapSupabaseSession);

    console.log(`[API] Found ${mappedSessions.length} sessions in Supabase`);

    return res.json(mappedSessions);
  } catch (error) {
    console.error('Unhandled error in Supabase sessions API:', error);
    console.log('[API] Error fetching sessions, returning empty array');
    return res.json([]);
  }
});



// Mock teacher generation function removed - only using real database data

/**
 * @api {get} /api/supabase/teachers Get all teachers from Supabase
 */
router.get('/supabase/teachers', async (req: Request, res: Response) => {
  try {
    console.log('[API] Fetching teachers from Supabase');

    // Check if we have supabase instance
    if (!supabase) {
      console.log('[API] Supabase instance unavailable, returning empty array');
      return res.json([]);
    }

    // Query teachers from user_profiles table
    console.log('[API] Querying user_profiles table for teachers');
    const { data: teachers, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('is_teacher', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching teachers from Supabase:', error);
      console.log('[API] Returning empty array due to Supabase error');
      return res.json([]);
    }

    if (!teachers || teachers.length === 0) {
      console.log('[API] No teachers found in Supabase, returning empty array');
      return res.json([]);
    }

    // Map teachers to our app's format
    const mappedTeachers = teachers.map(mapSupabaseTeacher);
    console.log(`[API] Found ${mappedTeachers.length} teachers in Supabase`);
    console.log(`[API] Sample teacher:`, mappedTeachers[0]);
    return res.json(mappedTeachers);
  } catch (error) {
    console.error('Error fetching teachers from Supabase:', error);
    console.log('[API] Returning empty array due to error');
    return res.json([]);
  }
});

/**
 * @api {get} /api/supabase/sessions/:id Get session by ID from Supabase
 */
router.get('/supabase/sessions/:id', async (req: Request, res: Response) => {
  try {
    const id = req.params.id;

    console.log(`[API] Fetching session ${id} from Supabase`);

    // Query session from sessions table with teacher data
    const { data: session, error } = await supabase
      .from('sessions')
      .select(`
        *,
        teacher:user_profiles(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching session ${id} from Supabase:`, error);

      // Try to get the session from the database as a fallback
      try {
        console.log(`[API] Falling back to database for session ${id}`);
        const db = req.app.locals.db;
        const dbSession = await db.query('SELECT * FROM content.sessions WHERE id = $1', [id]);

        if (dbSession && dbSession.length > 0) {
          console.log(`[API] Found session ${id} in database fallback`);
          return res.json(dbSession[0]);
        }
      } catch (dbError) {
        console.error(`Database fallback failed for session ${id}:`, dbError);
      }

      return res.status(404).json({ error: 'Session not found' });
    }

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Map session to our app's format
    const mappedSession = mapSupabaseSession(session);

    return res.json(mappedSession);
  } catch (error) {
    console.error('Unhandled error in Supabase session API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @api {get} /api/supabase/teachers/:id Get teacher by ID from Supabase
 */
router.get('/supabase/teachers/:id', async (req: Request, res: Response) => {
  try {
    const id = req.params.id;

    console.log(`[API] Fetching teacher ${id} from Supabase`);

    // Query teacher from user_profiles table
    const { data: teacher, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', id)
      .single();

    if (error) {
      console.error(`Error fetching teacher ${id} from Supabase:`, error);

      // Try to get the teacher from the database as a fallback
      try {
        console.log(`[API] Falling back to database for teacher ${id}`);
        const db = req.app.locals.db;
        const dbTeacher = await db.query(`
          SELECT u.id, u.username, p.*
          FROM profiles.user_profiles p
          JOIN auth.users u ON p.user_id = u.id
          WHERE u.id = $1
        `, [id]);

        if (dbTeacher && dbTeacher.length > 0) {
          console.log(`[API] Found teacher ${id} in database fallback`);
          return res.json(dbTeacher[0]);
        }
      } catch (dbError) {
        console.error(`Database fallback failed for teacher ${id}:`, dbError);
      }

      // Return 404 if teacher not found
      console.log(`[API] Teacher ${id} not found`);
      return res.status(404).json({ error: 'Teacher not found' });
    }

    if (!teacher) {
      return res.status(404).json({ error: 'Teacher not found' });
    }

    // Map teacher to our app's format
    const mappedTeacher = mapSupabaseTeacher(teacher);

    return res.json(mappedTeacher);
  } catch (error) {
    console.error(`Error fetching teacher ${req.params.id} from Supabase:`, error);

    // Return error
    console.log(`[API] Error fetching teacher ${req.params.id}`);
    return res.status(500).json({ error: 'Failed to fetch teacher' });
  }
});

/**
 * @api {post} /api/supabase/setup-storage Set up Supabase storage buckets
 * @apiDescription Creates the necessary storage buckets in Supabase
 */
router.post('/supabase/setup-storage', async (req: Request, res: Response) => {
  try {
    console.log('[API] Setting up Supabase storage buckets');

    // Define the buckets we want to create
    const buckets = [
      { name: 'profiles', options: { public: false } },
      { name: 'sessions', options: { public: true } },
      { name: 'messages', options: { public: false } },
      { name: 'placeholders', options: { public: true } },
      { name: 'app-assets', options: { public: true } },
    ];

    const results = [];

    // Create each bucket
    for (const bucket of buckets) {
      try {
        // Check if bucket exists first
        const { data: existingBuckets, error: listError } = await supabase
          .storage
          .listBuckets();

        if (listError) {
          console.error(`Error listing buckets: ${listError.message}`);
          results.push({ bucket: bucket.name, status: 'error', message: listError.message });
          continue;
        }

        const bucketExists = existingBuckets.some(b => b.name === bucket.name);

        if (bucketExists) {
          console.log(`Bucket ${bucket.name} already exists`);
          results.push({ bucket: bucket.name, status: 'exists' });
          continue;
        }

        // Create the bucket
        const { data, error } = await supabase
          .storage
          .createBucket(bucket.name, bucket.options);

        if (error) {
          console.error(`Error creating bucket ${bucket.name}: ${error.message}`);
          results.push({ bucket: bucket.name, status: 'error', message: error.message });
        } else {
          console.log(`Created bucket ${bucket.name}`);
          results.push({ bucket: bucket.name, status: 'created' });
        }
      } catch (bucketError) {
        console.error(`Unexpected error creating bucket ${bucket.name}:`, bucketError);
        results.push({ bucket: bucket.name, status: 'error', message: String(bucketError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase storage setup API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/upload-placeholders Upload placeholder images to Supabase storage
 * @apiDescription Uploads default placeholder images to the placeholders bucket
 */
router.post('/supabase/upload-placeholders', async (req: Request, res: Response) => {
  try {
    console.log('[API] Uploading placeholder images to Supabase storage');

    // Define the placeholder images to upload
    const placeholders = [
      {
        name: 'profile-placeholder.svg',
        path: 'placeholders/profile-placeholder.svg',
        contentType: 'image/svg+xml',
        data: '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><circle cx="100" cy="100" r="100" fill="#e2e8f0"/><circle cx="100" cy="85" r="40" fill="#94a3b8"/><path d="M160 155c0 5.523-26.863 35-60 35s-60-29.477-60-35c0-33.137 26.863-25 60-25s60-8.137 60 25z" fill="#94a3b8"/></svg>'
      },
      {
        name: 'session-placeholder.svg',
        path: 'placeholders/session-placeholder.svg',
        contentType: 'image/svg+xml',
        data: '<svg xmlns="http://www.w3.org/2000/svg" width="300" height="200" viewBox="0 0 300 200"><rect width="300" height="200" fill="#e2e8f0"/><path d="M150 80c-13.807 0-25 11.193-25 25s11.193 25 25 25 25-11.193 25-25-11.193-25-25-25zm0 45c-11.046 0-20-8.954-20-20s8.954-20 20-20 20 8.954 20 20-8.954 20-20 20z" fill="#94a3b8"/><path d="M185 65h-70c-8.284 0-15 6.716-15 15v40c0 8.284 6.716 15 15 15h70c8.284 0 15-6.716 15-15V80c0-8.284-6.716-15-15-15zm10 55c0 5.523-4.477 10-10 10h-70c-5.523 0-10-4.477-10-10V80c0-5.523 4.477-10 10-10h70c5.523 0 10 4.477 10 10v40z" fill="#94a3b8"/></svg>'
      },
      {
        name: 'cover-placeholder.jpg',
        path: 'placeholders/cover-placeholder.jpg',
        contentType: 'image/jpeg',
        url: 'https://frksndjujrbjhlrcjvtf.supabase.co/storage/v1/object/public/placeholders/default-cover.jpg'
      }
    ];

    const results = [];

    // Check if the placeholders bucket exists
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();

    if (bucketsError) {
      console.error('Error listing buckets:', bucketsError);
      return res.status(500).json({ error: 'Failed to list buckets' });
    }

    const placeholdersBucket = buckets.find(b => b.name === 'placeholders');

    if (!placeholdersBucket) {
      console.error('Placeholders bucket does not exist');
      return res.status(400).json({ error: 'Placeholders bucket does not exist. Please run setup-storage first.' });
    }

    // Upload each placeholder image
    for (const placeholder of placeholders) {
      try {
        let uploadResult;

        if (placeholder.data) {
          // Convert SVG string to Uint8Array
          const encoder = new TextEncoder();
          const data = encoder.encode(placeholder.data);

          uploadResult = await supabase
            .storage
            .from('placeholders')
            .upload(placeholder.path, data, {
              contentType: placeholder.contentType,
              upsert: true
            });
        } else if (placeholder.url) {
          // For images with URLs, we need to fetch them first
          const response = await fetch(placeholder.url);
          const blob = await response.blob();

          uploadResult = await supabase
            .storage
            .from('placeholders')
            .upload(placeholder.path, blob, {
              contentType: placeholder.contentType,
              upsert: true
            });
        }

        if (uploadResult.error) {
          console.error(`Error uploading ${placeholder.name}:`, uploadResult.error);
          results.push({ file: placeholder.name, status: 'error', message: uploadResult.error.message });
        } else {
          console.log(`Uploaded ${placeholder.name}`);

          // Get the public URL
          const { data: publicUrl } = supabase
            .storage
            .from('placeholders')
            .getPublicUrl(placeholder.path);

          results.push({
            file: placeholder.name,
            status: 'uploaded',
            path: placeholder.path,
            url: publicUrl.publicUrl
          });
        }
      } catch (uploadError) {
        console.error(`Unexpected error uploading ${placeholder.name}:`, uploadError);
        results.push({ file: placeholder.name, status: 'error', message: String(uploadError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase placeholder upload API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/update-images Update session and teacher images
 * @apiDescription Updates session and teacher images to use Supabase storage URLs
 */
router.post('/supabase/update-images', async (req: Request, res: Response) => {
  try {
    console.log('[API] Updating session and teacher images to use Supabase storage');

    // Get the public URL for placeholder images
    const { data: profilePlaceholder } = supabase
      .storage
      .from('placeholders')
      .getPublicUrl('placeholders/profile-placeholder.svg');

    const { data: sessionPlaceholder } = supabase
      .storage
      .from('placeholders')
      .getPublicUrl('placeholders/session-placeholder.svg');

    const { data: coverPlaceholder } = supabase
      .storage
      .from('placeholders')
      .getPublicUrl('placeholders/cover-placeholder.jpg');

    // Update session images
    const { data: updatedSessions, error: sessionError } = await supabase
      .from('sessions')
      .update({ image_url: sessionPlaceholder.publicUrl })
      .eq('image_url', 'https://via.placeholder.com/300')
      .select('id');

    if (sessionError) {
      console.error('Error updating session images:', sessionError);
      return res.status(500).json({ error: 'Failed to update session images' });
    }

    // Update teacher profile images
    const { data: updatedTeacherAvatars, error: teacherAvatarError } = await supabase
      .from('user_profiles')
      .update({ avatar: profilePlaceholder.publicUrl })
      .is('avatar', null)
      .select('id');

    if (teacherAvatarError) {
      console.error('Error updating teacher avatars:', teacherAvatarError);
      return res.status(500).json({ error: 'Failed to update teacher avatars' });
    }

    // Update teacher cover photos
    const { data: updatedTeacherCovers, error: teacherCoverError } = await supabase
      .from('user_profiles')
      .update({ cover_photo: coverPlaceholder.publicUrl })
      .is('cover_photo', null)
      .select('id');

    if (teacherCoverError) {
      console.error('Error updating teacher cover photos:', teacherCoverError);
      return res.status(500).json({ error: 'Failed to update teacher cover photos' });
    }

    return res.json({
      success: true,
      results: {
        sessions: {
          updated: updatedSessions?.length || 0,
          placeholder: sessionPlaceholder.publicUrl
        },
        teacherAvatars: {
          updated: updatedTeacherAvatars?.length || 0,
          placeholder: profilePlaceholder.publicUrl
        },
        teacherCovers: {
          updated: updatedTeacherCovers?.length || 0,
          placeholder: coverPlaceholder.publicUrl
        }
      }
    });
  } catch (error) {
    console.error('Unhandled error in Supabase image update API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/setup-rls Set up Row Level Security policies for Supabase storage
 * @apiDescription Creates RLS policies for Supabase storage buckets
 */
router.post('/supabase/setup-rls', async (req: Request, res: Response) => {
  try {
    console.log('[API] Setting up Row Level Security policies for Supabase storage');

    const results = [];

    // Define RLS policies for each bucket
    const policies = [
      // Profiles bucket policies
      {
        bucket: 'profiles',
        name: 'Allow public read access to profile images',
        definition: `
          CREATE POLICY "Allow public read access to profile images"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'profiles' AND (storage.foldername(name))[1] = 'avatars');
        `,
        type: 'read'
      },
      {
        bucket: 'profiles',
        name: 'Allow public read access to cover photos',
        definition: `
          CREATE POLICY "Allow public read access to cover photos"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'profiles' AND (storage.foldername(name))[1] = 'covers');
        `,
        type: 'read'
      },
      {
        bucket: 'profiles',
        name: 'Allow authenticated users to upload their own profile images',
        definition: `
          CREATE POLICY "Allow authenticated users to upload their own profile images"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (bucket_id = 'profiles' AND (storage.foldername(name))[1] = 'avatars' AND (storage.foldername(name))[2] = auth.uid()::text);
        `,
        type: 'insert'
      },
      {
        bucket: 'profiles',
        name: 'Allow authenticated users to upload their own cover photos',
        definition: `
          CREATE POLICY "Allow authenticated users to upload their own cover photos"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (bucket_id = 'profiles' AND (storage.foldername(name))[1] = 'covers' AND (storage.foldername(name))[2] = auth.uid()::text);
        `,
        type: 'insert'
      },
      {
        bucket: 'profiles',
        name: 'Allow authenticated users to update their own profile images',
        definition: `
          CREATE POLICY "Allow authenticated users to update their own profile images"
          ON storage.objects FOR UPDATE
          TO authenticated
          USING (bucket_id = 'profiles' AND (storage.foldername(name))[1] IN ('avatars', 'covers') AND (storage.foldername(name))[2] = auth.uid()::text);
        `,
        type: 'update'
      },
      {
        bucket: 'profiles',
        name: 'Allow authenticated users to delete their own profile images',
        definition: `
          CREATE POLICY "Allow authenticated users to delete their own profile images"
          ON storage.objects FOR DELETE
          TO authenticated
          USING (bucket_id = 'profiles' AND (storage.foldername(name))[1] IN ('avatars', 'covers') AND (storage.foldername(name))[2] = auth.uid()::text);
        `,
        type: 'delete'
      },

      // Sessions bucket policies
      {
        bucket: 'sessions',
        name: 'Allow public read access to session images',
        definition: `
          CREATE POLICY "Allow public read access to session images"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'sessions' AND (storage.foldername(name))[1] = 'thumbnails');
        `,
        type: 'read'
      },
      {
        bucket: 'sessions',
        name: 'Allow authenticated users to upload session images they own',
        definition: `
          CREATE POLICY "Allow authenticated users to upload session images they own"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (bucket_id = 'sessions' AND EXISTS (
            SELECT 1 FROM sessions s
            WHERE s.teacher_id::text = auth.uid()::text
            AND (storage.foldername(name))[2] = s.id::text
          ));
        `,
        type: 'insert'
      },
      {
        bucket: 'sessions',
        name: 'Allow authenticated users to update session images they own',
        definition: `
          CREATE POLICY "Allow authenticated users to update session images they own"
          ON storage.objects FOR UPDATE
          TO authenticated
          USING (bucket_id = 'sessions' AND EXISTS (
            SELECT 1 FROM sessions s
            WHERE s.teacher_id::text = auth.uid()::text
            AND (storage.foldername(name))[2] = s.id::text
          ));
        `,
        type: 'update'
      },
      {
        bucket: 'sessions',
        name: 'Allow authenticated users to delete session images they own',
        definition: `
          CREATE POLICY "Allow authenticated users to delete session images they own"
          ON storage.objects FOR DELETE
          TO authenticated
          USING (bucket_id = 'sessions' AND EXISTS (
            SELECT 1 FROM sessions s
            WHERE s.teacher_id::text = auth.uid()::text
            AND (storage.foldername(name))[2] = s.id::text
          ));
        `,
        type: 'delete'
      },

      // Messages bucket policies
      {
        bucket: 'messages',
        name: 'Allow users to read message attachments in their conversations',
        definition: `
          CREATE POLICY "Allow users to read message attachments in their conversations"
          ON storage.objects FOR SELECT
          TO authenticated
          USING (bucket_id = 'messages' AND EXISTS (
            SELECT 1 FROM conversations c
            WHERE (c.user_id::text = auth.uid()::text OR c.teacher_id::text = auth.uid()::text)
            AND (storage.foldername(name))[1] = c.id::text
          ));
        `,
        type: 'read'
      },
      {
        bucket: 'messages',
        name: 'Allow users to upload message attachments to their conversations',
        definition: `
          CREATE POLICY "Allow users to upload message attachments to their conversations"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (bucket_id = 'messages' AND EXISTS (
            SELECT 1 FROM conversations c
            WHERE (c.user_id::text = auth.uid()::text OR c.teacher_id::text = auth.uid()::text)
            AND (storage.foldername(name))[1] = c.id::text
          ));
        `,
        type: 'insert'
      },
      {
        bucket: 'messages',
        name: 'Allow users to delete message attachments from their conversations',
        definition: `
          CREATE POLICY "Allow users to delete message attachments from their conversations"
          ON storage.objects FOR DELETE
          TO authenticated
          USING (bucket_id = 'messages' AND EXISTS (
            SELECT 1 FROM conversations c
            WHERE (c.user_id::text = auth.uid()::text OR c.teacher_id::text = auth.uid()::text)
            AND (storage.foldername(name))[1] = c.id::text
          ));
        `,
        type: 'delete'
      },

      // Placeholders bucket policies - public read-only
      {
        bucket: 'placeholders',
        name: 'Allow public read access to placeholder images',
        definition: `
          CREATE POLICY "Allow public read access to placeholder images"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'placeholders');
        `,
        type: 'read'
      },

      // App assets bucket policies - public read-only
      {
        bucket: 'app-assets',
        name: 'Allow public read access to app assets',
        definition: `
          CREATE POLICY "Allow public read access to app assets"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'app-assets');
        `,
        type: 'read'
      },

      // Session media bucket policies - for media gallery feature
      {
        bucket: 'session-media',
        name: 'Allow public read access to session media',
        definition: `
          CREATE POLICY "Allow public read access to session media"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'session-media');
        `,
        type: 'read'
      },
      {
        bucket: 'session-media',
        name: 'Allow authenticated users to upload their own session media',
        definition: `
          CREATE POLICY "Allow authenticated users to upload their own session media"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (bucket_id = 'session-media' AND (storage.foldername(name))[1] = auth.uid()::text);
        `,
        type: 'insert'
      },
      {
        bucket: 'session-media',
        name: 'Allow authenticated users to update their own session media',
        definition: `
          CREATE POLICY "Allow authenticated users to update their own session media"
          ON storage.objects FOR UPDATE
          TO authenticated
          USING (bucket_id = 'session-media' AND (storage.foldername(name))[1] = auth.uid()::text);
        `,
        type: 'update'
      },
      {
        bucket: 'session-media',
        name: 'Allow authenticated users to delete their own session media',
        definition: `
          CREATE POLICY "Allow authenticated users to delete their own session media"
          ON storage.objects FOR DELETE
          TO authenticated
          USING (bucket_id = 'session-media' AND (storage.foldername(name))[1] = auth.uid()::text);
        `,
        type: 'delete'
      }
    ];

    // Enable RLS on storage.objects
    try {
      await supabase.rpc('enable_storage_rls');
      results.push({ action: 'enable_storage_rls', status: 'success' });
    } catch (error) {
      console.error('Error enabling storage RLS:', error);
      results.push({ action: 'enable_storage_rls', status: 'error', message: String(error) });
    }

    // Apply each policy
    for (const policy of policies) {
      try {
        // Check if policy already exists
        const { data: existingPolicies, error: listError } = await supabase
          .from('storage.policies')
          .select('name')
          .eq('name', policy.name)
          .eq('bucket_id', policy.bucket);

        if (listError) {
          console.error(`Error checking if policy ${policy.name} exists:`, listError);
          results.push({ policy: policy.name, status: 'error', message: listError.message });
          continue;
        }

        if (existingPolicies && existingPolicies.length > 0) {
          console.log(`Policy ${policy.name} already exists`);
          results.push({ policy: policy.name, status: 'exists' });
          continue;
        }

        // Create the policy
        const { error } = await supabase.rpc('create_storage_policy', {
          bucket_id: policy.bucket,
          policy_name: policy.name,
          definition: policy.definition,
          policy_type: policy.type
        });

        if (error) {
          console.error(`Error creating policy ${policy.name}:`, error);
          results.push({ policy: policy.name, status: 'error', message: error.message });
        } else {
          console.log(`Created policy ${policy.name}`);
          results.push({ policy: policy.name, status: 'created' });
        }
      } catch (policyError) {
        console.error(`Unexpected error creating policy ${policy.name}:`, policyError);
        results.push({ policy: policy.name, status: 'error', message: String(policyError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase RLS setup API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/upload-mock-images Upload mock images for sessions and teachers
 * @apiDescription Uploads mock images for sessions and teachers and links them to the database
 */
router.post('/supabase/upload-mock-images', async (req: Request, res: Response) => {
  try {
    console.log('[API] Uploading mock images for sessions and teachers');

    // Define mock images for sessions - covering all types from schema
    const sessionImages = [
      // Yoga - Multiple different images
      {
        type: 'Yoga',
        url: 'https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?w=800&auto=format&fit=crop'
      },
      {
        type: 'Yoga',
        url: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?w=800&auto=format&fit=crop'
      },
      {
        type: 'Yoga',
        url: 'https://images.unsplash.com/photo-1506629905607-bb0e50540319?w=800&auto=format&fit=crop'
      },
      // Pilates
      {
        type: 'Pilates',
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&auto=format&fit=crop'
      },
      {
        type: 'Pilates',
        url: 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=800&auto=format&fit=crop'
      },
      // Language - Multiple different images
      {
        type: 'Language',
        url: 'https://images.unsplash.com/photo-1546410531-bb4caa6b424d?w=800&auto=format&fit=crop'
      },
      {
        type: 'Language',
        url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&auto=format&fit=crop'
      },
      {
        type: 'Language',
        url: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&auto=format&fit=crop'
      },
      // Music - Multiple different images
      {
        type: 'Music',
        url: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&auto=format&fit=crop'
      },
      {
        type: 'Music',
        url: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&auto=format&fit=crop'
      },
      {
        type: 'Music',
        url: 'https://images.unsplash.com/photo-1519892300165-cb5542fb47c7?w=800&auto=format&fit=crop'
      },
      // Dance - Multiple different images
      {
        type: 'Dance',
        url: 'https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?w=800&auto=format&fit=crop'
      },
      {
        type: 'Dance',
        url: 'https://images.unsplash.com/photo-1547153760-18fc86324498?w=800&auto=format&fit=crop'
      },
      {
        type: 'Dance',
        url: 'https://images.unsplash.com/photo-1594998893017-36147d457c04?w=800&auto=format&fit=crop'
      },
      // Cooking - Multiple different images
      {
        type: 'Cooking',
        url: 'https://images.unsplash.com/photo-1556910103-1c02745aae4d?w=800&auto=format&fit=crop'
      },
      {
        type: 'Cooking',
        url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&auto=format&fit=crop'
      },
      {
        type: 'Cooking',
        url: 'https://images.unsplash.com/photo-1542010589005-d1eacc3918f2?w=800&auto=format&fit=crop'
      },
      // Art - Multiple different images
      {
        type: 'Art',
        url: 'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=800&auto=format&fit=crop'
      },
      {
        type: 'Art',
        url: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&auto=format&fit=crop'
      },
      {
        type: 'Art',
        url: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800&auto=format&fit=crop'
      },
      // Academic - Multiple different images
      {
        type: 'Academic',
        url: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=800&auto=format&fit=crop'
      },
      {
        type: 'Academic',
        url: 'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=800&auto=format&fit=crop'
      },
      {
        type: 'Academic',
        url: 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?w=800&auto=format&fit=crop'
      },
      // Professional - Multiple different images
      {
        type: 'Professional',
        url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&auto=format&fit=crop'
      },
      {
        type: 'Professional',
        url: 'https://images.unsplash.com/photo-1521737711867-e3b97375f902?w=800&auto=format&fit=crop'
      },
      {
        type: 'Professional',
        url: 'https://images.unsplash.com/photo-**********-8e7e53415bb0?w=800&auto=format&fit=crop'
      },
      // Health - Multiple different images
      {
        type: 'Health',
        url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&auto=format&fit=crop'
      },
      {
        type: 'Health',
        url: 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=800&auto=format&fit=crop'
      },
      {
        type: 'Health',
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&auto=format&fit=crop'
      },
      // Fitness - Multiple different images
      {
        type: 'Fitness',
        url: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=800&auto=format&fit=crop'
      },
      {
        type: 'Fitness',
        url: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?w=800&auto=format&fit=crop'
      },
      {
        type: 'Fitness',
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&auto=format&fit=crop'
      },
      // DIY
      {
        type: 'DIY',
        url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&auto=format&fit=crop'
      },
      {
        type: 'DIY',
        url: 'https://images.unsplash.com/photo-1606760227091-3dd870d97f1d?w=800&auto=format&fit=crop'
      },
      {
        type: 'DIY',
        url: 'https://images.unsplash.com/photo-1493909367013-11f1a162266d?w=800&auto=format&fit=crop'
      },
      // Beauty
      {
        type: 'Beauty',
        url: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800&auto=format&fit=crop'
      },
      {
        type: 'Beauty',
        url: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=800&auto=format&fit=crop'
      },
      {
        type: 'Beauty',
        url: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&auto=format&fit=crop'
      },
      // Therapy
      {
        type: 'Therapy',
        url: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&auto=format&fit=crop'
      },
      {
        type: 'Therapy',
        url: 'https://images.unsplash.com/photo-1583195764036-6dc248ac07d9?w=800&auto=format&fit=crop'
      },
      {
        type: 'Therapy',
        url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&auto=format&fit=crop'
      },
      // Relationships
      {
        type: 'Relationships',
        url: 'https://images.unsplash.com/photo-1511988617509-a57c8a288659?w=800&auto=format&fit=crop'
      },
      {
        type: 'Relationships',
        url: 'https://images.unsplash.com/photo-1502301197179-d8f8a75c7221?w=800&auto=format&fit=crop'
      },
      {
        type: 'Relationships',
        url: 'https://images.unsplash.com/photo-1504973960431-1c467e159aa4?w=800&auto=format&fit=crop'
      },
      // Spirituality
      {
        type: 'Spirituality',
        url: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=800&auto=format&fit=crop'
      },
      {
        type: 'Spirituality',
        url: 'https://images.unsplash.com/photo-1593811167562-9cef47bfc4a7?w=800&auto=format&fit=crop'
      },
      {
        type: 'Spirituality',
        url: 'https://images.unsplash.com/photo-1536623975707-c4b3b2af565d?w=800&auto=format&fit=crop'
      }
    ];

    // Define diverse teacher avatar images - different people, ethnicities, ages, genders
    const teacherAvatars = [
      // Diverse women
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&auto=format&fit=crop', // Woman 1
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&auto=format&fit=crop', // Woman 2
      'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=400&auto=format&fit=crop', // Woman 3  
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&auto=format&fit=crop', // Woman 4
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&auto=format&fit=crop', // Woman 5
      'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&auto=format&fit=crop', // Woman 6
      'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&auto=format&fit=crop', // Woman 7
      'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=400&auto=format&fit=crop', // Woman 8

      // Diverse men  
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop', // Man 1
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&auto=format&fit=crop', // Man 2
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&auto=format&fit=crop', // Man 3
      'https://images.unsplash.com/photo-1545167622-3a6ac756afa4?w=400&auto=format&fit=crop', // Man 4
      'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=400&auto=format&fit=crop', // Man 5
      'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=400&auto=format&fit=crop', // Man 6
      'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400&auto=format&fit=crop', // Man 7
      'https://images.unsplash.com/photo-1542190891-2093d38760f2?w=400&auto=format&fit=crop', // Man 8

      // Additional diverse faces
      'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=400&auto=format&fit=crop', // Older man with glasses
      'https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=400&auto=format&fit=crop', // Young man
      'https://images.unsplash.com/photo-1484515991774-4d4ed0e0016e?w=400&auto=format&fit=crop', // Woman with headphones
      'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400&auto=format&fit=crop', // Professional woman
      'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&auto=format&fit=crop', // Man with beard
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&auto=format&fit=crop', // Smiling woman
      'https://images.unsplash.com/photo-1517070208541-6ddc4d3b8d81?w=400&auto=format&fit=crop', // Professional man
      'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?w=400&auto=format&fit=crop', // Diverse woman
      'https://images.unsplash.com/photo-1557862921-37829c790f19?w=400&auto=format&fit=crop', // Young woman
      'https://images.unsplash.com/photo-1558203728-00f45181dd84?w=400&auto=format&fit=crop'  // Man in casual wear
    ];

    // Define diverse teacher cover photos - different teaching/professional environments
    const teacherCovers = [
      'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop', // Yoga/meditation space
      'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop', // Modern classroom
      'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop', // Creative workspace
      'https://images.unsplash.com/photo-1559251606-c623743a6d76?w=1200&auto=format&fit=crop', // Music studio
      'https://images.unsplash.com/photo-1566041510639-8d95a2490bfb?w=1200&auto=format&fit=crop', // Art studio
      'https://images.unsplash.com/photo-1523580494863-6f3031224c94?w=1200&auto=format&fit=crop', // Library/academic
      'https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=1200&auto=format&fit=crop', // Professional office
      'https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=1200&auto=format&fit=crop', // Fitness studio
      'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=1200&auto=format&fit=crop', // Workshop/DIY space
      'https://images.unsplash.com/photo-**********-8e7e53415bb0?w=1200&auto=format&fit=crop', // Conference room
      'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200&auto=format&fit=crop', // Kitchen/cooking space
      'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200&auto=format&fit=crop', // Therapy/wellness room
      'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=1200&auto=format&fit=crop', // Spiritual/meditation
      'https://images.unsplash.com/photo-1531482615713-2afd69097998?w=1200&auto=format&fit=crop', // Dance studio
      'https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=1200&auto=format&fit=crop', // Study/tutoring space
      'https://images.unsplash.com/photo-1521791136064-7986c2920216?w=1200&auto=format&fit=crop'  // Beauty/wellness space
    ];

    const results = {
      sessions: [],
      teachers: []
    };

    // 1. Update session images
    // Get all sessions
    const { data: sessions, error: sessionsError } = await supabase
      .from('sessions')
      .select('id, type');

    if (sessionsError) {
      console.error('Error fetching sessions:', sessionsError);
      return res.status(500).json({ error: 'Failed to fetch sessions' });
    }

    // Update each session with an appropriate image based on type
    for (const session of sessions) {
      try {
        // Find all matching images for the session type
        const matchingImages = sessionImages.filter(img =>
          session.type && img.type.toLowerCase() === session.type.toLowerCase()
        );

        // If matching images exist, pick a random one; otherwise use the first available image
        let imageUrl;
        if (matchingImages.length > 0) {
          const randomIndex = Math.floor(Math.random() * matchingImages.length);
          imageUrl = matchingImages[randomIndex].url;
        } else {
          imageUrl = sessionImages[0].url;
        }

        // Update the session with the new image URL
        const { error: updateError } = await supabase
          .from('sessions')
          .update({ image_url: imageUrl })
          .eq('id', session.id);

        if (updateError) {
          console.error(`Error updating session ${session.id}:`, updateError);
          results.sessions.push({ id: session.id, status: 'error', message: updateError.message });
        } else {
          console.log(`Updated session ${session.id} with image for type ${session.type}`);
          results.sessions.push({ id: session.id, status: 'updated', type: session.type, imageUrl });
        }
      } catch (sessionError) {
        console.error(`Unexpected error updating session ${session.id}:`, sessionError);
        results.sessions.push({ id: session.id, status: 'error', message: String(sessionError) });
      }
    }

    // 2. Update teacher avatars and cover photos
    // Get all teachers
    const { data: teachers, error: teachersError } = await supabase
      .from('user_profiles')
      .select('id, user_id')
      .eq('is_teacher', true);

    if (teachersError) {
      console.error('Error fetching teachers:', teachersError);
      return res.status(500).json({ error: 'Failed to fetch teachers' });
    }

    // Update each teacher with random avatar and cover photo
    for (const [index, teacher] of teachers.entries()) {
      try {
        // Get random avatar and cover photo
        const avatarUrl = teacherAvatars[index % teacherAvatars.length];
        const coverUrl = teacherCovers[index % teacherCovers.length];

        // Update the teacher with the new image URLs
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            avatar: avatarUrl,
            cover_photo: coverUrl
          })
          .eq('id', teacher.id);

        if (updateError) {
          console.error(`Error updating teacher ${teacher.id}:`, updateError);
          results.teachers.push({ id: teacher.id, status: 'error', message: updateError.message });
        } else {
          console.log(`Updated teacher ${teacher.id} with avatar and cover photo`);
          results.teachers.push({
            id: teacher.id,
            status: 'updated',
            avatarUrl,
            coverUrl
          });
        }
      } catch (teacherError) {
        console.error(`Unexpected error updating teacher ${teacher.id}:`, teacherError);
        results.teachers.push({ id: teacher.id, status: 'error', message: String(teacherError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase mock image upload API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/upload-teacher-images Upload teacher images to Supabase buckets
 * @apiDescription Downloads high-quality teacher images and uploads them to Supabase buckets
 */
router.post('/supabase/upload-teacher-images', async (req: Request, res: Response) => {
  try {
    console.log('[API] Uploading teacher images to Supabase buckets');

    // Import the script dynamically
    const { uploadTeacherImages } = await import('../scripts/upload-teacher-images-real.cjs');

    // Run the script
    const results = await uploadTeacherImages();

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase teacher image upload API:', error);
    return res.status(500).json({ message: "Internal server error", error: error.message });
  }
});

/**
 * @api {post} /api/supabase/setup-additional-buckets Set up additional Supabase storage buckets
 * @apiDescription Creates additional storage buckets in Supabase based on the comprehensive schema
 */
router.post('/supabase/setup-additional-buckets', async (req: Request, res: Response) => {
  try {
    console.log('[API] Setting up additional Supabase storage buckets');

    // Define the additional buckets we want to create
    const buckets = [
      // User-related buckets
      { name: 'user-uploads', options: { public: false } },

      // Session-related buckets
      { name: 'session-types', options: { public: true } },
      { name: 'session-materials', options: { public: false } },
      { name: 'session-recordings', options: { public: false } },

      // Communication-related buckets
      { name: 'announcements', options: { public: false } },

      // Additional specialized buckets
      { name: 'testimonials', options: { public: true } },
      { name: 'blog', options: { public: true } },
      { name: 'events', options: { public: true } }
    ];

    const results = [];

    // Create each bucket
    for (const bucket of buckets) {
      try {
        // Check if bucket exists first
        const { data: existingBuckets, error: listError } = await supabase
          .storage
          .listBuckets();

        if (listError) {
          console.error(`Error listing buckets: ${listError.message}`);
          results.push({ bucket: bucket.name, status: 'error', message: listError.message });
          continue;
        }

        const bucketExists = existingBuckets.some(b => b.name === bucket.name);

        if (bucketExists) {
          console.log(`Bucket ${bucket.name} already exists`);
          results.push({ bucket: bucket.name, status: 'exists' });
          continue;
        }

        // Create the bucket
        const { data, error } = await supabase
          .storage
          .createBucket(bucket.name, bucket.options);

        if (error) {
          console.error(`Error creating bucket ${bucket.name}: ${error.message}`);
          results.push({ bucket: bucket.name, status: 'error', message: error.message });
        } else {
          console.log(`Created bucket ${bucket.name}`);
          results.push({ bucket: bucket.name, status: 'created' });
        }
      } catch (bucketError) {
        console.error(`Unexpected error creating bucket ${bucket.name}:`, bucketError);
        results.push({ bucket: bucket.name, status: 'error', message: String(bucketError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase additional buckets setup API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/create-folder-structure Create folder structure in Supabase buckets
 * @apiDescription Creates the folder structure in Supabase buckets according to the schema
 */
router.post('/supabase/create-folder-structure', async (req: Request, res: Response) => {
  try {
    console.log('[API] Creating folder structure in Supabase buckets');

    // Define the folder structure for each bucket
    const folderStructure = [
      // Profiles bucket folders
      { bucket: 'profiles', path: 'avatars/.keep' },
      { bucket: 'profiles', path: 'covers/.keep' },
      { bucket: 'profiles', path: 'documents/.keep' },
      { bucket: 'profiles', path: 'portfolios/.keep' },

      // User uploads bucket folders
      { bucket: 'user-uploads', path: 'general/.keep' },

      // Sessions bucket folders
      { bucket: 'sessions', path: 'thumbnails/.keep' },
      { bucket: 'sessions', path: 'materials/.keep' },
      { bucket: 'sessions', path: 'recordings/.keep' },

      // Session types bucket folders (for category images)
      { bucket: 'session-types', path: 'yoga/.keep' },
      { bucket: 'session-types', path: 'pilates/.keep' },
      { bucket: 'session-types', path: 'cooking/.keep' },
      { bucket: 'session-types', path: 'music/.keep' },
      { bucket: 'session-types', path: 'dance/.keep' },
      { bucket: 'session-types', path: 'language/.keep' },
      { bucket: 'session-types', path: 'art/.keep' },
      { bucket: 'session-types', path: 'fitness/.keep' },
      { bucket: 'session-types', path: 'academic/.keep' },
      { bucket: 'session-types', path: 'professional/.keep' },
      { bucket: 'session-types', path: 'health/.keep' },
      { bucket: 'session-types', path: 'diy/.keep' },
      { bucket: 'session-types', path: 'beauty/.keep' },
      { bucket: 'session-types', path: 'therapy/.keep' },
      { bucket: 'session-types', path: 'relationships/.keep' },
      { bucket: 'session-types', path: 'spirituality/.keep' },

      // Messages bucket folders
      { bucket: 'messages', path: 'attachments/.keep' },

      // Announcements bucket folders
      { bucket: 'announcements', path: 'system/.keep' },
      { bucket: 'announcements', path: 'teacher/.keep' },

      // Placeholders bucket folders
      { bucket: 'placeholders', path: 'avatars/.keep' },
      { bucket: 'placeholders', path: 'covers/.keep' },
      { bucket: 'placeholders', path: 'sessions/.keep' },

      // App assets bucket folders
      { bucket: 'app-assets', path: 'logos/.keep' },
      { bucket: 'app-assets', path: 'icons/.keep' },
      { bucket: 'app-assets', path: 'backgrounds/.keep' },

      // Testimonials bucket folders
      { bucket: 'testimonials', path: 'images/.keep' },
      { bucket: 'testimonials', path: 'videos/.keep' },

      // Blog bucket folders
      { bucket: 'blog', path: 'featured/.keep' },
      { bucket: 'blog', path: 'gallery/.keep' },

      // Events bucket folders
      { bucket: 'events', path: 'banners/.keep' },
      { bucket: 'events', path: 'gallery/.keep' }
    ];

    const results = [];

    // Create an empty file in each folder to establish the folder structure
    for (const folder of folderStructure) {
      try {
        // Check if the bucket exists
        const { data: buckets, error: bucketsError } = await supabase
          .storage
          .listBuckets();

        if (bucketsError) {
          console.error('Error listing buckets:', bucketsError);
          results.push({ folder: `${folder.bucket}/${folder.path}`, status: 'error', message: bucketsError.message });
          continue;
        }

        const bucketExists = buckets.some(b => b.name === folder.bucket);

        if (!bucketExists) {
          console.error(`Bucket ${folder.bucket} does not exist`);
          results.push({ folder: `${folder.bucket}/${folder.path}`, status: 'error', message: `Bucket ${folder.bucket} does not exist` });
          continue;
        }

        // Create an empty file to establish the folder
        const emptyFile = new Uint8Array(0);

        const { data, error } = await supabase
          .storage
          .from(folder.bucket)
          .upload(folder.path, emptyFile, {
            contentType: 'text/plain',
            upsert: true
          });

        if (error) {
          console.error(`Error creating folder ${folder.bucket}/${folder.path}:`, error);
          results.push({ folder: `${folder.bucket}/${folder.path}`, status: 'error', message: error.message });
        } else {
          console.log(`Created folder ${folder.bucket}/${folder.path}`);
          results.push({ folder: `${folder.bucket}/${folder.path}`, status: 'created' });
        }
      } catch (folderError) {
        console.error(`Unexpected error creating folder ${folder.bucket}/${folder.path}:`, folderError);
        results.push({ folder: `${folder.bucket}/${folder.path}`, status: 'error', message: String(folderError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase folder structure API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {get} /api/supabase/list-buckets List all Supabase storage buckets
 * @apiDescription Lists all storage buckets in Supabase
 */
router.get('/supabase/list-buckets', async (req: Request, res: Response) => {
  try {
    console.log('[API] Listing Supabase storage buckets');

    const { data: buckets, error } = await supabase
      .storage
      .listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      return res.status(500).json({ error: 'Failed to list buckets' });
    }

    // Get the contents of each bucket
    const bucketsWithContents = await Promise.all(
      buckets.map(async (bucket) => {
        try {
          const { data: contents, error: contentsError } = await supabase
            .storage
            .from(bucket.name)
            .list();

          if (contentsError) {
            console.error(`Error listing contents of bucket ${bucket.name}:`, contentsError);
            return {
              ...bucket,
              contents: [],
              error: contentsError.message
            };
          }

          return {
            ...bucket,
            contents: contents || []
          };
        } catch (error) {
          console.error(`Unexpected error listing contents of bucket ${bucket.name}:`, error);
          return {
            ...bucket,
            contents: [],
            error: String(error)
          };
        }
      })
    );

    return res.json({ success: true, buckets: bucketsWithContents });
  } catch (error) {
    console.error('Unhandled error in Supabase list buckets API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/upload-bucket-images Upload images to Supabase buckets
 * @apiDescription Downloads images from external URLs and uploads them to Supabase buckets
 */
router.post('/supabase/upload-bucket-images', async (req: Request, res: Response) => {
  try {
    console.log('[API] Uploading images to Supabase buckets');

    // Define session type images to download and upload
    const sessionTypeImages = [
      {
        type: 'Yoga',
        url: 'https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'yoga/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Pilates',
        url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'pilates/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Cooking',
        url: 'https://images.unsplash.com/photo-1556910103-1c02745aae4d?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'cooking/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Music',
        url: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'music/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Dance',
        url: 'https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'dance/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Language',
        url: 'https://images.unsplash.com/photo-1546410531-bb4caa6b424d?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'language/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Art',
        url: 'https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'art/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Fitness',
        url: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'fitness/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Academic',
        url: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'academic/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Professional',
        url: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'professional/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Health',
        url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'health/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'DIY',
        url: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'diy/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Beauty',
        url: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'beauty/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Therapy',
        url: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'therapy/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Relationships',
        url: 'https://images.unsplash.com/photo-1511988617509-a57c8a288659?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'relationships/cover.jpg',
        contentType: 'image/jpeg'
      },
      {
        type: 'Spirituality',
        url: 'https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=800&auto=format&fit=crop',
        bucket: 'session-types',
        path: 'spirituality/cover.jpg',
        contentType: 'image/jpeg'
      }
    ];

    // Define teacher avatar images to download and upload
    const teacherAvatarImages = [
      {
        url: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher1.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher2.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher3.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher4.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher5.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher6.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher7.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=400&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'avatars/teacher8.jpg',
        contentType: 'image/jpeg'
      }
    ];

    // Define teacher cover images to download and upload
    const teacherCoverImages = [
      {
        url: 'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'covers/cover1.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'covers/cover2.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'covers/cover3.jpg',
        contentType: 'image/jpeg'
      },
      {
        url: 'https://images.unsplash.com/photo-1579546929662-711aa81148cf?w=1200&auto=format&fit=crop',
        bucket: 'profiles',
        path: 'covers/cover4.jpg',
        contentType: 'image/jpeg'
      }
    ];

    // Combine all images
    const allImages = [
      ...sessionTypeImages,
      ...teacherAvatarImages,
      ...teacherCoverImages
    ];

    const results = [];

    // Download and upload each image
    for (const image of allImages) {
      try {
        // Download the image
        console.log(`Downloading image from ${image.url}`);
        const response = await fetch(image.url);

        if (!response.ok) {
          console.error(`Error downloading image from ${image.url}: ${response.statusText}`);
          results.push({
            image: image.path,
            status: 'error',
            message: `Failed to download: ${response.statusText}`
          });
          continue;
        }

        const blob = await response.blob();

        // Upload to Supabase
        console.log(`Uploading image to ${image.bucket}/${image.path}`);
        const { data, error } = await supabase
          .storage
          .from(image.bucket)
          .upload(image.path, blob, {
            contentType: image.contentType,
            upsert: true
          });

        if (error) {
          console.error(`Error uploading image to ${image.bucket}/${image.path}:`, error);
          results.push({
            image: image.path,
            status: 'error',
            message: error.message
          });
        } else {
          // Get the public URL
          const { data: publicUrlData } = supabase
            .storage
            .from(image.bucket)
            .getPublicUrl(image.path);

          console.log(`Uploaded image to ${image.bucket}/${image.path}`);
          results.push({
            image: image.path,
            status: 'uploaded',
            publicUrl: publicUrlData.publicUrl,
            type: image.type
          });
        }
      } catch (imageError) {
        console.error(`Unexpected error processing image ${image.path}:`, imageError);
        results.push({
          image: image.path,
          status: 'error',
          message: String(imageError)
        });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase image upload API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

/**
 * @api {post} /api/supabase/update-database-images Update database with Supabase bucket image URLs
 * @apiDescription Updates session and teacher records to use Supabase bucket image URLs
 */
router.post('/supabase/update-database-images', async (req: Request, res: Response) => {
  try {
    console.log('[API] Updating database with Supabase bucket image URLs');

    // Get all session type images
    const { data: sessionTypeImages, error: sessionTypeError } = await supabase
      .storage
      .from('session-types')
      .list('', {
        limit: 100,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (sessionTypeError) {
      console.error('Error listing session type images:', sessionTypeError);
      return res.status(500).json({ error: 'Failed to list session type images' });
    }

    // Get all profile avatars
    const { data: profileAvatars, error: avatarError } = await supabase
      .storage
      .from('profiles')
      .list('avatars', {
        limit: 100,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (avatarError) {
      console.error('Error listing profile avatars:', avatarError);
      return res.status(500).json({ error: 'Failed to list profile avatars' });
    }

    // Get all profile covers
    const { data: profileCovers, error: coverError } = await supabase
      .storage
      .from('profiles')
      .list('covers', {
        limit: 100,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (coverError) {
      console.error('Error listing profile covers:', coverError);
      return res.status(500).json({ error: 'Failed to list profile covers' });
    }

    // Map session types to their image URLs
    const sessionTypeMap: Record<string, string> = {};

    for (const folder of sessionTypeImages) {
      if (folder.name && !folder.id) { // It's a folder
        // Get the images in this folder
        const { data: folderImages, error: folderError } = await supabase
          .storage
          .from('session-types')
          .list(folder.name, {
            limit: 100,
            offset: 0,
            sortBy: { column: 'name', order: 'asc' }
          });

        if (folderError) {
          console.error(`Error listing images in folder ${folder.name}:`, folderError);
          continue;
        }

        if (folderImages && folderImages.length > 0) {
          // Find the cover image
          const coverImage = folderImages.find(img => img.name === 'cover.jpg');

          if (coverImage) {
            // Get the public URL
            const { data: publicUrlData } = supabase
              .storage
              .from('session-types')
              .getPublicUrl(`${folder.name}/cover.jpg`);

            sessionTypeMap[folder.name] = publicUrlData.publicUrl;
          }
        }
      }
    }

    // Get avatar and cover URLs
    const avatarUrls = profileAvatars
      .filter(avatar => avatar.name && avatar.name !== '.keep')
      .map(avatar => {
        const { data } = supabase
          .storage
          .from('profiles')
          .getPublicUrl(`avatars/${avatar.name}`);
        return data.publicUrl;
      });

    const coverUrls = profileCovers
      .filter(cover => cover.name && cover.name !== '.keep')
      .map(cover => {
        const { data } = supabase
          .storage
          .from('profiles')
          .getPublicUrl(`covers/${cover.name}`);
        return data.publicUrl;
      });

    const results = {
      sessions: [],
      teachers: []
    };

    // 1. Update session images
    // Get all sessions
    const { data: sessions, error: sessionsError } = await supabase
      .from('sessions')
      .select('id, type');

    if (sessionsError) {
      console.error('Error fetching sessions:', sessionsError);
      return res.status(500).json({ error: 'Failed to fetch sessions' });
    }

    // Update each session with an appropriate image based on type
    for (const session of sessions) {
      try {
        // Find a matching image for the session type
        const sessionType = session.type ? session.type.toLowerCase() : '';
        let imageUrl = null;

        // Try to find an exact match
        for (const [type, url] of Object.entries(sessionTypeMap)) {
          if (sessionType === type.toLowerCase()) {
            imageUrl = url;
            break;
          }
        }

        // If no exact match, try a partial match
        if (!imageUrl) {
          for (const [type, url] of Object.entries(sessionTypeMap)) {
            if (sessionType.includes(type.toLowerCase()) || type.toLowerCase().includes(sessionType)) {
              imageUrl = url;
              break;
            }
          }
        }

        // If still no match, use the first one
        if (!imageUrl && Object.values(sessionTypeMap).length > 0) {
          imageUrl = Object.values(sessionTypeMap)[0];
        }

        if (imageUrl) {
          // Update the session with the new image URL
          const { error: updateError } = await supabase
            .from('sessions')
            .update({ image_url: imageUrl })
            .eq('id', session.id);

          if (updateError) {
            console.error(`Error updating session ${session.id}:`, updateError);
            results.sessions.push({ id: session.id, status: 'error', message: updateError.message });
          } else {
            console.log(`Updated session ${session.id} with image for type ${session.type}`);
            results.sessions.push({ id: session.id, status: 'updated', type: session.type, imageUrl });
          }
        } else {
          console.log(`No matching image found for session type ${session.type}`);
          results.sessions.push({ id: session.id, status: 'skipped', message: 'No matching image found' });
        }
      } catch (sessionError) {
        console.error(`Unexpected error updating session ${session.id}:`, sessionError);
        results.sessions.push({ id: session.id, status: 'error', message: String(sessionError) });
      }
    }

    // 2. Update teacher avatars and cover photos
    // Get all teachers
    const { data: teachers, error: teachersError } = await supabase
      .from('user_profiles')
      .select('id, user_id')
      .eq('is_teacher', true);

    if (teachersError) {
      console.error('Error fetching teachers:', teachersError);
      return res.status(500).json({ error: 'Failed to fetch teachers' });
    }

    // Update each teacher with random avatar and cover photo
    for (const [index, teacher] of teachers.entries()) {
      try {
        // Get random avatar and cover photo
        const avatarUrl = avatarUrls[index % avatarUrls.length];
        const coverUrl = coverUrls[index % coverUrls.length];

        // Update the teacher with the new image URLs
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            avatar: avatarUrl,
            cover_photo: coverUrl
          })
          .eq('id', teacher.id);

        if (updateError) {
          console.error(`Error updating teacher ${teacher.id}:`, updateError);
          results.teachers.push({ id: teacher.id, status: 'error', message: updateError.message });
        } else {
          console.log(`Updated teacher ${teacher.id} with avatar and cover photo`);
          results.teachers.push({
            id: teacher.id,
            status: 'updated',
            avatarUrl,
            coverUrl
          });
        }
      } catch (teacherError) {
        console.error(`Unexpected error updating teacher ${teacher.id}:`, teacherError);
        results.teachers.push({ id: teacher.id, status: 'error', message: String(teacherError) });
      }
    }

    return res.json({ success: true, results });
  } catch (error) {
    console.error('Unhandled error in Supabase database update API:', error);
    return res.status(500).json({ message: "Internal server error" });
  }
});

// API route for creating test grouped sessions
router.post('/api/supabase/create-test-grouped-sessions', async (req, res) => {
  try {
    console.log('Creating test grouped sessions...');

    // Create sessions with same title but different dates
    const testSessions = [
      {
        title: 'Introduction to Photography',
        description: 'Learn the basics of photography including composition, lighting, and camera settings.',
        teacher_id: '2e22bbf5-c786-4f74-b647-f0a4903d9f7f', // Use existing teacher
        type: 'Art',
        price: 45,
        duration: 90,
        date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        max_participants: 8,
        format: 'Online',
        language: 'English',
        skill_level: 'Beginner',
        is_published: true,
      },
      {
        title: 'Introduction to Photography',
        description: 'Learn the basics of photography including composition, lighting, and camera settings.',
        teacher_id: '2e22bbf5-c786-4f74-b647-f0a4903d9f7f',
        type: 'Art',
        price: 45,
        duration: 90,
        date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
        max_participants: 8,
        format: 'Online',
        language: 'English',
        skill_level: 'Beginner',
        is_published: true,
      },
      {
        title: 'Introduction to Photography',
        description: 'Learn the basics of photography including composition, lighting, and camera settings.',
        teacher_id: '2e22bbf5-c786-4f74-b647-f0a4903d9f7f',
        type: 'Art',
        price: 45,
        duration: 90,
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
        max_participants: 8,
        format: 'Online',
        language: 'English',
        skill_level: 'Beginner',
        is_published: true,
      },
      {
        title: 'Yoga for Beginners',
        description: 'Start your yoga journey with gentle poses and breathing techniques.',
        teacher_id: '2e22bbf5-c786-4f74-b647-f0a4903d9f7f',
        type: 'Yoga',
        price: 25,
        duration: 60,
        date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days from now
        max_participants: 15,
        format: 'Online',
        language: 'English',
        skill_level: 'Beginner',
        is_published: true,
      },
      {
        title: 'Yoga for Beginners',
        description: 'Start your yoga journey with gentle poses and breathing techniques.',
        teacher_id: '2e22bbf5-c786-4f74-b647-f0a4903d9f7f',
        type: 'Yoga',
        price: 25,
        duration: 60,
        date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
        max_participants: 15,
        format: 'Online',
        language: 'English',
        skill_level: 'Beginner',
        is_published: true,
      }
    ];

    const createdSessions = [];

    for (const sessionData of testSessions) {
      const { data, error } = await supabase
        .from('sessions')
        .insert([sessionData])
        .select()
        .single();

      if (error) {
        console.error('Error creating test session:', error);
        continue;
      }

      createdSessions.push(data);
      console.log(`Created test session: ${data.title} on ${data.date}`);
    }

    res.json({
      success: true,
      message: `Created ${createdSessions.length} test sessions`,
      sessions: createdSessions
    });

  } catch (error) {
    console.error('Error creating test grouped sessions:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create test sessions'
    });
  }
});

export default router;
