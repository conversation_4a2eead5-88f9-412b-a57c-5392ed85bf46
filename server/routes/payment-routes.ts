import express from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import { storage } from '../storage';
import stripeService from '../services/stripe-service';
import paymentService from '../services/payment-service';
// Get environment variables
function getEnv(name: string): string {
  return process.env[name] || '';
}

const router = express.Router();

/**
 * Create a payment intent for a session booking
 */
router.post('/api/payments/create-intent', validateJWT, async (req, res) => {
  try {
    const { sessionId, amount } = req.body;

    if (!sessionId || !amount) {
      return res.status(400).json({ error: 'Session ID and amount are required' });
    }

    // Get the session to verify it exists and the price matches
    const session = await storage.getSession(sessionId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Verify the amount matches the session price
    if (session.price !== amount) {
      return res.status(400).json({
        error: 'Amount does not match session price',
        expected: session.price,
        received: amount
      });
    }

    // Create a payment intent
    const paymentIntent = await stripeService.createPaymentIntent(
      amount,
      'usd',
      {
        sessionId: sessionId.toString(),
        userId: req.user.id.toString()
      }
    );

    res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      id: paymentIntent.id
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({ error: 'Failed to create payment intent' });
  }
});

/**
 * Create a checkout session for a session booking
 */
router.post('/api/payments/create-checkout-session', validateJWT, async (req, res) => {
  try {
    console.log('[STRIPE] Create checkout session request:', req.body);
    console.log('[STRIPE] User:', req.user ? `ID: ${req.user.id}, Username: ${req.user.username}` : 'Not authenticated');

    const { sessionId } = req.body;

    if (!sessionId) {
      console.log('[STRIPE] Error: Session ID is required');
      return res.status(400).json({ error: 'Session ID is required' });
    }

    // Get the session to verify it exists
    console.log('[STRIPE] Fetching session:', sessionId);
    const session = await storage.getSession(sessionId);

    if (!session) {
      console.log('[STRIPE] Error: Session not found:', sessionId);
      return res.status(404).json({ error: 'Session not found' });
    }

    console.log('[STRIPE] Session found:', {
      id: session.id,
      title: session.title,
      price: session.price,
      teacherId: session.teacherId
    });

    // Check if the user is the teacher of the session
    if (session.teacherId === req.user.id) {
      console.log('[STRIPE] Error: User is the teacher of the session');
      return res.status(400).json({ error: 'You cannot book your own session' });
    }

    // Check if the session has a price
    if (!session.price || session.price <= 0) {
      console.log('[STRIPE] Error: Session has no price or price is zero');
      return res.status(400).json({ error: 'This session is free and does not require payment' });
    }

    console.log('[STRIPE] Creating checkout session with params:', {
      sessionId,
      userId: req.user.id,
      price: session.price,
      origin: req.headers.origin
    });

    // Create a checkout session
    const checkoutSession = await stripeService.createCheckoutSession(
      sessionId,
      req.user.id,
      session.price,
      `${req.headers.origin}/payment-success?session_id=${sessionId}`,
      `${req.headers.origin}/payment-cancel?session_id=${sessionId}`,
      session.teacherId // Pass the teacher ID for Connect payments
    );

    console.log('[STRIPE] Checkout session created:', {
      id: checkoutSession.id,
      url: checkoutSession.url ? checkoutSession.url.substring(0, 50) + '...' : 'No URL'
    });

    res.status(200).json({ url: checkoutSession.url });
  } catch (error) {
    console.error('[STRIPE] Error creating checkout session:', error);

    // Provide more detailed error message
    let errorMessage = 'Failed to create checkout session';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    res.status(500).json({ error: errorMessage });
  }
});

/**
 * Webhook endpoint for Stripe events
 */
router.post('/api/payments/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const signature = req.headers['stripe-signature'] as string;

  if (!signature) {
    return res.status(400).json({ error: 'Stripe signature is missing' });
  }

  try {
    // Verify the webhook signature
    const event = stripeService.verifyWebhookSignature(
      req.body.toString(),
      signature
    );

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as any;

        // Extract metadata
        const sessionId = parseInt(paymentIntent.metadata.sessionId);
        const userId = parseInt(paymentIntent.metadata.userId);

        if (!sessionId || !userId) {
          console.error('Missing metadata in payment intent:', paymentIntent.id);
          return res.status(400).json({ error: 'Missing metadata in payment intent' });
        }

        // Create a booking
        const booking = await storage.createBooking({
          userId,
          sessionId,
          status: 'confirmed',
          paymentId: paymentIntent.id,
          paymentStatus: 'paid',
          paymentAmount: paymentIntent.amount / 100, // Convert from cents to dollars
          paymentProcessor: 'stripe'
        });

        // Calculate platform fee
        const platformFee = await paymentService.calculatePlatformFee(booking.id);

        // Get the session to find the teacher
        const session = await storage.getSession(sessionId);
        if (session && session.teacherId) {
          // Create a pending payout for the teacher
          // The amount is the payment amount minus the platform fee
          const payoutAmount = (paymentIntent.amount / 100) - platformFee.amount;
          await paymentService.createPayout(
            session.teacherId,
            payoutAmount,
            'USD',
            'pending',
            `Payout for session #${sessionId}, booking #${booking.id}`
          );
        }

        console.log(`Booking created from webhook: ${booking.id}`);
        break;

      case 'checkout.session.completed':
        const checkoutSession = event.data.object as any;

        // Extract metadata
        const checkoutSessionId = parseInt(checkoutSession.metadata.sessionId);
        const checkoutUserId = parseInt(checkoutSession.metadata.userId);
        const checkoutTeacherId = checkoutSession.metadata.teacherId ? parseInt(checkoutSession.metadata.teacherId) : null;

        if (!checkoutSessionId || !checkoutUserId) {
          console.error('Missing metadata in checkout session:', checkoutSession.id);
          return res.status(400).json({ error: 'Missing metadata in checkout session' });
        }

        // Create a booking
        const checkoutBooking = await storage.createBooking({
          userId: checkoutUserId,
          sessionId: checkoutSessionId,
          status: 'confirmed',
          paymentId: checkoutSession.payment_intent,
          paymentStatus: 'paid',
          paymentAmount: checkoutSession.amount_total / 100, // Convert from cents to dollars
          paymentProcessor: 'stripe'
        });

        // Check if this was a Connect payment (direct to teacher)
        const isConnectPayment = checkoutSession.payment_intent_data &&
          checkoutSession.payment_intent_data.transfer_data &&
          checkoutSession.payment_intent_data.transfer_data.destination;

        // If this was a Connect payment, the platform fee was already taken
        // and the teacher was already paid via Stripe Connect
        if (!isConnectPayment) {
          console.log('Standard payment flow - creating manual payout record');

          // Calculate platform fee
          const checkoutPlatformFee = await paymentService.calculatePlatformFee(checkoutBooking.id);

          // Get the session to find the teacher
          const checkoutSessionData = await storage.getSession(checkoutSessionId);
          if (checkoutSessionData && checkoutSessionData.teacherId) {
            // Create a pending payout for the teacher
            // The amount is the payment amount minus the platform fee
            const checkoutPayoutAmount = (checkoutSession.amount_total / 100) - checkoutPlatformFee.amount;
            await paymentService.createPayout(
              checkoutSessionData.teacherId,
              checkoutPayoutAmount,
              'USD',
              'pending',
              `Payout for session #${checkoutSessionId}, booking #${checkoutBooking.id}`
            );
          }
        } else {
          console.log('Connect payment flow - teacher paid directly via Stripe Connect');

          // Record the platform fee for accounting purposes
          const platformFeeAmount = checkoutSession.payment_intent_data.application_fee_amount / 100;
          await paymentService.recordPlatformFee(checkoutBooking.id, platformFeeAmount, 'USD');

          // Record that the teacher was paid automatically via Stripe Connect
          if (checkoutTeacherId) {
            await paymentService.createPayout(
              checkoutTeacherId,
              (checkoutSession.amount_total / 100) - platformFeeAmount,
              'USD',
              'paid', // Mark as already paid
              `Automatic payout via Stripe Connect for session #${checkoutSessionId}, booking #${checkoutBooking.id}`
            );
          }
        }

        console.log(`Booking created from checkout session: ${checkoutBooking.id}`);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    res.status(400).json({ error: 'Webhook signature verification failed' });
  }
});

/**
 * Complete a booking after payment
 */
router.post('/api/payments/complete-booking', validateJWT, async (req, res) => {
  try {
    const { paymentIntentId, sessionId } = req.body;

    if (!paymentIntentId || !sessionId) {
      return res.status(400).json({ error: 'Payment intent ID and session ID are required' });
    }

    // Verify the payment intent
    const paymentIntent = await stripeService.retrievePaymentIntent(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        error: 'Payment has not been completed',
        status: paymentIntent.status
      });
    }

    // Create a booking
    const booking = await storage.createBooking({
      userId: req.user.id,
      sessionId,
      status: 'confirmed',
      paymentId: paymentIntentId,
      paymentStatus: 'paid',
      paymentAmount: paymentIntent.amount / 100, // Convert from cents to dollars
      paymentProcessor: 'stripe'
    });

    // Calculate platform fee
    const platformFee = await paymentService.calculatePlatformFee(booking.id);

    // Get the session to find the teacher
    const session = await storage.getSession(sessionId);
    if (session && session.teacherId) {
      // Create a pending payout for the teacher
      // The amount is the payment amount minus the platform fee
      const payoutAmount = (paymentIntent.amount / 100) - platformFee.amount;
      await paymentService.createPayout(
        session.teacherId,
        payoutAmount,
        'USD',
        'pending',
        `Payout for session #${sessionId}, booking #${booking.id}`
      );
    }

    res.status(200).json(booking);
  } catch (error) {
    console.error('Error completing booking:', error);
    res.status(500).json({ error: 'Failed to complete booking' });
  }
});

/**
 * Get Stripe publishable key
 */
router.get('/api/payments/config', (req, res) => {
  // Use hardcoded key as fallback
  const STRIPE_PUBLISHABLE_KEY = 'pk_live_51KHwbDEUyEi4Uqi62GdwWYpjfmEw6TMofatlJNKurHYM4MTE08Ffjysat1x4Exiohs2JMmD6yeNoZBHBYfpScdMl00pyo8BfNC';

  res.status(200).json({
    publishableKey: getEnv('STRIPE_PUBLISHABLE_KEY') || STRIPE_PUBLISHABLE_KEY
  });
});

export default router;
