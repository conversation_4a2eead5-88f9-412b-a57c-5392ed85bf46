import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { validateJWT } from '../middleware/supabase-auth';
import { mapAutomatedMessagesField } from '../utils/session-helpers';
import { createActivityMessage } from '../utils/activity-message';
import { invalidateCache, invalidateSessionCache, invalidateCachePattern } from '../middleware/cache';
import { insertSessionSchema, insertReviewSchema } from '@shared/schema';
import { Mutex } from 'async-mutex';
import { productionMessageScheduler } from '../services/ProductionMessageScheduler';
import { queryOptimizer, errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';
import serviceManager from '../services/ServiceManager';

// Schemas for validation
const sessionIdSchema = z.object({
  id: z.string().uuid()
});

const teacherIdSchema = z.object({
  id: z.string().uuid()
});

const reviewParamsSchema = z.object({
  sessionId: z.string().uuid()
});

const sessionVisibilitySchema = z.object({
  isPublic: z.boolean()
});

const simpleUpdateSessionSchema = z.object({
  id: z.string().uuid(),
  // All other fields are optional
}).passthrough();

const directUpdateQuerySchema = z.object({
  id: z.string().uuid(),
  // All other fields are optional
}).passthrough();

// Create a mutex for session updates to prevent race conditions
const sessionMutex = new Mutex();

// Helper to safely access user ID from request
function getUserId(req: Request): string {
  return (req.user as any)?.id;
}

export function registerSessionRoutes(app: Express) {
  // Get all sessions (public API)
  app.get("/api/sessions", async (req: Request, res: Response) => {
    try {
      console.log('[API] Getting sessions with query params:', req.query);

      // For now, redirect to the working Supabase endpoint
      // This is a temporary solution until we fix the SessionService
      const { supabaseAdmin } = await import('../lib/supabase');

      const { data: sessions, error } = await supabaseAdmin
        .from('sessions_view')
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) {
        console.error('[API] Error getting sessions from Supabase:', error);
        throw error;
      }

      // Transform the data to include teacher information
      const sessionsWithTeacher = sessions?.map(session => ({
        ...session,
        teacher: session.teacher_name ? {
          id: session.teacher_id,
          name: session.teacher_name,
          username: session.teacher_username,
          avatar: session.teacher_avatar,
          bio: session.teacher_bio
        } : null
      })) || [];

      console.log(`[API] Found ${sessionsWithTeacher.length} sessions in database`);

      // Log the first session's details for debugging
      if (sessionsWithTeacher.length > 0) {
        console.log(`[API] First session: id=${sessionsWithTeacher[0].id}, title=${sessionsWithTeacher[0].title}, teacher=${sessionsWithTeacher[0].teacher?.name || 'unknown'}`);
      }

      res.json(sessionsWithTeacher);
    } catch (error) {
      console.error('[API] Error getting sessions:', error);
      console.error('[API] Error details:', (error as Error).message, (error as Error).stack);
      res.status(500).json({
        message: 'Failed to fetch sessions',
        error: (error as Error).message,
        details: process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
      });
    }
  });

  // Get a specific session by ID
  app.get("/api/sessions/:id",
    asyncHandler(async (req: Request, res: Response) => {
      const { id } = req.params;

      try {
        // Check if this is a UUID (for Supabase) or numeric ID (for PostgreSQL)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

        if (uuidRegex.test(id)) {
          // Use our fixed SessionRepository for UUID-based sessions
          const sessionService = serviceManager.getSessionService();
          const session = await sessionService.getSessionWithTeacher(id);

          if (!session) {
            throw errorHandler.notFound("Session not found");
          }

          // Return the session with automated messages field mapped
          res.json(mapAutomatedMessagesField(session));
        } else {
          // Use the original PostgreSQL service for numeric IDs
          const numericId = parseInt(id);
          if (isNaN(numericId) || numericId <= 0) {
            throw errorHandler.badRequest("Invalid session ID format");
          }

          const sessionService = serviceManager.getSessionService();
          const session = await sessionService.getSessionWithTeacher(numericId.toString());

          if (!session) {
            throw errorHandler.notFound("Session not found");
          }

          res.json(mapAutomatedMessagesField(session));
        }
      } catch (error) {
        console.error(`Error fetching session ${id}:`, error);
        throw errorHandler.internal("Failed to fetch session");
      }
    })
  );

  // Create a new session
  app.post("/api/sessions",
    validateJWT,
    validate(insertSessionSchema),
    asyncHandler(async (req: Request, res: Response) => {
      // Only authenticated users can create sessions
      if (!req.user) {
        throw errorHandler.unauthorized("Authentication required");
      }

      const userId = (req.user as any).id;
      const validatedData = req.body;

      // Check if the session date is in the future
      if (validatedData.date) {
        const sessionDate = new Date(validatedData.date);
        const now = new Date();

        if (sessionDate < now) {
          throw errorHandler.badRequest("Session date must be in the future");
        }

        // TODO: Implement scheduling conflict check with Supabase
        // For now, we'll skip this check to get session creation working
      }

      // Prepare the session data for database insertion
      const dbFields = {
        ...validatedData,
        teacherId: validatedData.teacherId || userId, // Support both teacherId from frontend and fallback to userId
        // Convert automatedMessages to JSON string if present
        automatedMessagesJson: validatedData.automatedMessages
          ? JSON.stringify(validatedData.automatedMessages)
          : null
      };

      // Remove the original automatedMessages field as it's not in the database schema
      delete dbFields.automatedMessages;

      // Use the Supabase service directly for UUID-based operations
      const { createSession } = await import('../services/supabase/sessionService');

      // Map the fields to match the Supabase schema - only include fields that exist
      const supabaseSessionData = {
        title: dbFields.title,
        description: dbFields.description,
        teacher_id: dbFields.teacherId,
        type: dbFields.type,
        price: dbFields.price,
        duration: dbFields.duration,
        date: dbFields.date,
        language: dbFields.language,
        skill_level: dbFields.skillLevel,
        format: dbFields.format,
        max_participants: dbFields.maxParticipants,
        zoom_link: dbFields.zoomLink,
        learning_outcomes: dbFields.learningOutcomes,
        requirements: dbFields.requirements,
        agreement_type: dbFields.agreementType,
        automated_messages_json: dbFields.automatedMessagesJson,
        is_public: true, // Default to public
        // Note: is_featured and is_private columns do NOT exist in Supabase schema
        // cancellation policy fields
        cancellation_policy: dbFields.cancellationPolicy,
        cancellation_timeframe: dbFields.cancellationTimeframe,
        custom_cancellation_hours: dbFields.customCancellationHours,
        cancellation_fee_percentage: dbFields.cancellationFeePercentage,
        no_show_fee_percentage: dbFields.noShowFeePercentage,
        legal_agreement: dbFields.legalAgreement,
        custom_agreement: dbFields.customAgreement,
        scheduling_mode: dbFields.schedulingMode,
        // automated message fields
        welcome_message: dbFields.welcomeMessage,
        reminder_message: dbFields.reminderMessage,
        preparation_notes: dbFields.preparationNotes,
        follow_up_message: dbFields.followUpMessage,
        // recurring session fields
        recurring_pattern: dbFields.recurringPattern,
        recurring_day: dbFields.recurringDay,
        num_occurrences: dbFields.numOccurrences,
        // image
        image_url: dbFields.imageUrl,
        // location and hosting fields
        location_type: dbFields.locationType,
        location_details: dbFields.locationDetails,
        online_hosting_service: dbFields.onlineHostingService,
        is_featured: dbFields.isFeatured || false,
      };

      const session = await createSession(supabaseSessionData);

      if (!session) {
        throw errorHandler.internal("Failed to create session");
      }

      // Return the created session
      res.status(201).json(mapAutomatedMessagesField(session));
    })
  );

  // Update a session
  app.put("/api/sessions/:id",
    validateJWT,
    validate(sessionIdSchema, 'params'),
    validate(insertSessionSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: sessionId } = req.params as unknown as z.infer<typeof sessionIdSchema>;
      const teacherId = (req.user as any)!.id;

      console.log(`[PUT Session] Request to update session ${sessionId} by user ${teacherId}`);

      // Use the working Supabase service instead of the problematic SessionService
      const { getSession, updateSession } = await import('../services/supabase/sessionService');

      // Get the original session to track changes
      const originalSession = await getSession(sessionId);

      console.log(`[PUT Session] Original session found:`, {
        sessionId,
        exists: !!originalSession,
        teacherId: originalSession?.teacher_id,
        requestingUserId: teacherId,
        teacherIdType: typeof originalSession?.teacher_id,
        requestingUserIdType: typeof teacherId
      });

      if (!originalSession) {
        console.log(`[PUT Session] Session ${sessionId} not found`);
        throw errorHandler.notFound("Session not found");
      }

      // Check if the user is the teacher of the session 
      // The Supabase session service returns both teacher_id and teacherId fields
      const sessionTeacherId = originalSession.teacher_id || originalSession.teacherId;
      if (sessionTeacherId !== teacherId) {
        console.log(`[PUT Session] Ownership check failed:`, {
          sessionTeacherId: sessionTeacherId,
          requestingUserId: teacherId,
          originalSession_teacher_id: originalSession.teacher_id,
          originalSession_teacherId: originalSession.teacherId,
          strictEqual: sessionTeacherId === teacherId,
          looseEqual: sessionTeacherId == teacherId
        });
        throw errorHandler.forbidden("You can only update your own sessions");
      }

      console.log(`[PUT Session] Ownership check passed for session ${sessionId}`);

      // Map the frontend fields to Supabase database fields
      const supabaseUpdateData = {
        title: req.body.title,
        description: req.body.description,
        type: req.body.type,
        price: req.body.price,
        duration: req.body.duration,
        date: req.body.date,
        language: req.body.language,
        skill_level: req.body.skillLevel,
        format: req.body.format,
        max_participants: req.body.maxParticipants,
        image_url: req.body.imageUrl || req.body.image_url, // Handle both frontend formats
        is_public: req.body.isPublic,
        automated_messages_json: req.body.automatedMessages
          ? JSON.stringify(req.body.automatedMessages)
          : originalSession.automated_messages_json,
        welcome_message: req.body.welcomeMessage,
        reminder_message: req.body.reminderMessage,
        preparation_notes: req.body.preparationNotes,
        follow_up_message: req.body.followUpMessage,
        location_type: req.body.locationType,
        location_details: req.body.locationDetails,
        online_hosting_service: req.body.onlineHostingService,
        is_featured: req.body.isFeatured,
        cancellation_policy: req.body.cancellationPolicy,
        cancellation_timeframe: req.body.cancellationTimeframe,
        cancellation_fee_percentage: req.body.cancellationFeePercentage,
        no_show_fee_percentage: req.body.noShowFeePercentage,
        legal_agreement: req.body.legalAgreement,
        agreement_type: req.body.agreementType,
        custom_agreement: req.body.customAgreement,
        scheduling_mode: req.body.schedulingMode,
        recurring_pattern: req.body.recurringPattern,
        recurring_day: req.body.recurringDay,
        num_occurrences: req.body.numOccurrences,
        media_gallery: req.body.mediaGallery || originalSession.media_gallery || []
      };

      console.log(`[PUT Session] Image URL mapping:`, {
        fromBody_imageUrl: req.body.imageUrl,
        fromBody_image_url: req.body.image_url,
        mapped_image_url: supabaseUpdateData.image_url
      });

      console.log(`[PUT Session] About to update session ${sessionId} with Supabase service`);

      // Update the session using the working Supabase service
      const updatedSession = await updateSession(sessionId, supabaseUpdateData);

      if (!updatedSession) {
        console.error(`[PUT Session] Failed to update session ${sessionId}. Supabase update returned undefined.`);
        throw errorHandler.internal("Failed to update session");
      }

      console.log(`[PUT Session] Session ${sessionId} successfully updated`);

      // Set strong no-cache headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // Return the updated session with automated messages field mapped
      res.status(200).json(mapAutomatedMessagesField(updatedSession));
    })
  );

  // Patch a session (for visibility toggle)
  app.patch("/api/sessions/:id",
    validateJWT,
    validate(sessionIdSchema, 'params'),
    validate(sessionVisibilitySchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: sessionId } = req.params as unknown as z.infer<typeof sessionIdSchema>;
      const requestId = Math.random().toString(36).substring(2, 8);
      let mutexReleaser: (() => void) | null = null;

      console.log(`[Session Update] [${requestId}] PATCH request received for session ${sessionId}:`, {
        body: req.body,
        isAuthenticated: !!req.user,
        user: req.user ? { id: (req.user as any).id, email: (req.user as any).email } : null
      });

      // Verify the user is authenticated (should already be checked by middleware)
      if (!req.user) {
        throw errorHandler.unauthorized("Authentication required");
      }

      // Acquire a mutex lock to prevent race conditions
      console.log(`[Session Update] [${requestId}] Acquiring lock for session ${sessionId}`);
      mutexReleaser = await sessionMutex.acquire(sessionId);
      console.log(`[Session Update] [${requestId}] Lock acquired for session ${sessionId}`);

      try {
        // Critical: Don't use a cached version of the session - forced database lookup
        console.log(`[Session Update] [${requestId}] Fetching session ${sessionId} directly from database`);

        // Get the session service
        const sessionService = serviceManager.getSessionService();

        // Get the session with teacher
        const session = await sessionService.getSessionWithTeacher(sessionId);

        if (!session) {
          console.log(`[Session Update] [${requestId}] Session ${sessionId} not found`);
          throw errorHandler.notFound("Session not found");
        }

        // Check the boolean value explicitly
        console.log(`[Session Update] [${requestId}] Session found:`, {
          id: session.id,
          teacherId: session.teacherId,
          isPublic: session.isPublic,
          isPublicType: typeof session.isPublic
        });

        // Check if the user is authorized to update this session
        const userId = (req.user as any).id;
        if (session.teacherId !== userId) {
          console.log(`[Session Update] [${requestId}] User ${userId} not authorized to update session ${sessionId} (owned by ${session.teacherId})`);
          throw errorHandler.forbidden("Not authorized to update this session");
        }

        // Double encode the boolean to ensure consistency
        const newVisibility = !!req.body.isPublic;
        console.log(`[Session Update] [${requestId}] Fast-path visibility toggle for session ${sessionId} from ${session.isPublic} to ${newVisibility}`);

        // If the value is already what we want, return early to avoid unnecessary updates
        if (session.isPublic === newVisibility) {
          console.log(`[Session Update] [${requestId}] Session ${sessionId} is already ${newVisibility ? 'public' : 'private'}, no update needed`);

          // Return the current session state
          return res.json({
            success: true,
            timestamp: Date.now(),
            id: sessionId,
            isPublic: newVisibility,
            noChangeRequired: true,
            session: mapAutomatedMessagesField(session)
          });
        }

        // Update session visibility
        const updatedSession = await sessionService.updateSession(sessionId, { isPublic: newVisibility });

        if (!updatedSession) {
          console.error(`[Session Update] [${requestId}] Failed to update session visibility`);
          throw errorHandler.internal("Failed to update session visibility");
        }

        console.log(`[Session Update] [${requestId}] Updated session ${sessionId} visibility to ${newVisibility}`);
        console.log(`[Session Update] [${requestId}] Database value is now: ${updatedSession.isPublic} (${typeof updatedSession.isPublic})`);

        // Return the updated session state
        return res.json({
          success: true,
          timestamp: Date.now(),
          id: sessionId,
          isPublic: newVisibility,
          session: mapAutomatedMessagesField({ ...updatedSession, isPublic: newVisibility })
        });
      } finally {
        // Release the mutex lock
        if (mutexReleaser) {
          await sessionMutex.release(sessionId);
          console.log(`[Session Update] [${requestId}] Released lock for session ${sessionId}`);
        }
      }
    })
  );

  // Add a review for a session
  app.post("/api/sessions/:sessionId/reviews",
    validateJWT,
    validate(reviewParamsSchema, 'params'),
    validate(insertReviewSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { sessionId } = req.params as unknown as z.infer<typeof reviewParamsSchema>;
      const userId = req.user!.id;

      // Create the review with the validated data
      const reviewData = {
        ...req.body,
        sessionId,
        userId
      };

      // Get the review service
      const reviewService = serviceManager.getReviewService();

      // Create the review
      const review = await reviewService.createReview(reviewData);

      // Invalidate any caches for session detail pages
      invalidateSessionCache(sessionId);

      res.status(201).json(review);
    })
  );

  // Schedule messages for existing bookings of a session
  app.post("/api/sessions/:sessionId/schedule-messages",
    validateJWT,
    validate(reviewParamsSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { sessionId } = req.params as unknown as z.infer<typeof reviewParamsSchema>;

      // Get the session service
      const sessionService = serviceManager.getSessionService();

      // Get the session
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        throw errorHandler.notFound("Session not found");
      }

      // Check if the user is the teacher of the session
      if (req.user?.id !== session.teacherId) {
        throw errorHandler.forbidden("You are not authorized to schedule messages for this session");
      }

      // Get the booking service
      const bookingService = serviceManager.getBookingService();

      // Get all bookings for this session
      const bookings = await bookingService.getBookingsBySessionId(sessionId);
      if (!bookings || bookings.length === 0) {
        return res.status(200).json({ message: "No bookings found for this session", bookingsProcessed: 0 });
      }

      // Use the production message scheduler
      const scheduler = productionMessageScheduler;

      // Parse automatedMessages from session.automatedMessagesJson
      let automatedMessages = [];
      if (session.automatedMessagesJson) {
        try {
          automatedMessages = JSON.parse(session.automatedMessagesJson);
        } catch (e) {
          console.error('[API] Error parsing automatedMessagesJson:', e);
        }
      }

      if (!automatedMessages || automatedMessages.length === 0) {
        return res.status(200).json({ message: "No automated messages found for this session", bookingsProcessed: 0 });
      }

      // Schedule messages for each booking
      let bookingsProcessed = 0;
      for (const booking of bookings) {
        try {
          // Get the user service
          const userService = serviceManager.getUserService();

          // Get the student and teacher
          const student = await userService.getUser(booking.userId);
          const teacher = await userService.getUser(session.teacherId);

          if (student && teacher) {
            await scheduler.scheduleMessagesForBooking(
              booking,
              { ...session, automatedMessages },
              student,
              teacher
            );
            bookingsProcessed++;
          }
        } catch (error) {
          console.error(`[API] Error scheduling messages for booking ${booking.id}:`, error);
        }
      }

      return res.status(200).json({
        success: true,
        message: `Scheduled messages for ${bookingsProcessed} bookings`,
        bookingsProcessed
      });
    })
  );

  // Add a direct endpoint for teacher sessions that bypasses middleware
  app.get("/api/direct/sessions/teacher/:id",
    validate(teacherIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id } = req.params as unknown as z.infer<typeof teacherIdSchema>;

      console.log(`[DIRECT API] Fetching sessions for teacher ${id}`);

      // Get the user service
      const userService = serviceManager.getUserService();

      // Verify teacher exists
      const teacher = await userService.getUser(id);
      if (!teacher) {
        throw errorHandler.notFound("Teacher not found");
      }

      // Get the session service
      const sessionService = serviceManager.getSessionService();

      // Get all sessions for this teacher
      const sessions = await sessionService.getSessions({ teacherId: id });
      console.log(`[DIRECT API] Found ${sessions.length} sessions for teacher ${id}`);

      // Set cache headers to prevent caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');

      return res.status(200).json(mapAutomatedMessagesField(sessions));
    })
  );

  // Enhanced debug endpoint for session updates with improved reliability
  app.post("/api/simple-update-session",
    validateJWT,
    validate(simpleUpdateSessionSchema),
    asyncHandler(async (req: Request, res: Response) => {
      // Only the session's teacher can update
      const { id, ...fields } = req.body;

      // Get the session service
      const sessionService = serviceManager.getSessionService();

      // Get the session
      const session = await sessionService.getSession(Number(id));
      if (!session) {
        throw errorHandler.notFound("Session not found");
      }

      if (!req.user || session.teacherId !== req.user.id) {
        throw errorHandler.forbidden("You can only update your own sessions");
      }

      const requestId = req.body.debug_id || `debug-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;

      // Only allow fields that exist in the sessions table (timezone is NOT one of them)
      const allowedFields = [
        'title', 'description', 'type', 'skillLevel', 'format', 'location', 'date',
        'price', 'duration', 'maxParticipants', 'isPublic', 'schedulingMode', 'language', 'imageUrl',
        'cancellationPolicy', 'cancellationTimeframe', 'cancellationFeePercentage', 'noShowFeePercentage',
        'legalAgreement', 'otherFields', 'numOccurrences', 'recurringPattern', 'recurringDay', 'customCancellationHours', 'category', 'welcomeMessage', 'reminderMessage', 'preparationNotes', 'followUpMessage', 'automatedMessagesJson'
      ];

      function camelToSnake(str) {
        return str.replace(/([A-Z])/g, '_$1').toLowerCase();
      }

      const filteredFields = {};
      for (const key of allowedFields) {
        if (fields[key] !== undefined) {
          filteredFields[camelToSnake(key)] = fields[key];
        }
      }

      // --- PATCH: Always handle automatedMessages ---
      if (fields.automatedMessages) {
        filteredFields['automated_messages_json'] = JSON.stringify(fields.automatedMessages);
      }

      // Normalize date if present
      if (filteredFields.date) {
        try {
          const parsedDate = new Date(filteredFields.date);
          if (!isNaN(parsedDate.getTime())) filteredFields.date = parsedDate.toISOString();
        } catch { }
      }

      // Use sessionService.updateSession for all fields
      console.log(`[DEBUG API] About to update session ${id} with fields:`, filteredFields);
      const updatedSession = await sessionService.updateSession(Number(id), filteredFields);

      if (!updatedSession) {
        throw errorHandler.internal("Failed to update session");
      }

      // Invalidate cache
      try {
        invalidateSessionCache(id);
        invalidateCachePattern(`sessions:${id}`);
        invalidateCachePattern(`/api/sessions/${id}`);
        invalidateCachePattern(`/api/sessions`);
      } catch (cacheError) {
        console.error(`[DEBUG API ${requestId}] Cache invalidation error (non-fatal):`, cacheError);
      }

      return res.status(200).json({
        success: true,
        message: "Session updated successfully (all fields)",
        id,
        updated_at: updatedSession.updatedAt || updatedSession.updated_at,
        timestamp: Date.now(),
        session: mapAutomatedMessagesField(updatedSession)
      });
    })
  );

  // Direct update endpoint via query params
  app.get("/api/direct-update",
    validate(directUpdateQuerySchema, 'query'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id, ...fields } = req.query as unknown as z.infer<typeof directUpdateQuerySchema>;

      // Normalize date if present
      if (fields.date) {
        try {
          const parsedDate = new Date(fields.date);
          if (!isNaN(parsedDate.getTime())) fields.date = parsedDate.toISOString();
        } catch { }
      }

      // Get the session service
      const sessionService = serviceManager.getSessionService();

      // Use sessionService.updateSession for all fields
      const updatedSession = await sessionService.updateSession(id, fields);

      if (!updatedSession) {
        throw errorHandler.internal("Failed to update session");
      }

      // Invalidate cache
      try {
        invalidateSessionCache(id);
        invalidateCachePattern(`sessions:${id}`);
        invalidateCachePattern(`/api/sessions/${id}`);
        invalidateCachePattern(`/api/sessions`);
      } catch (cacheError) {
        console.error(`[DIRECT API] Cache invalidation error (non-fatal):`, cacheError);
      }

      return res.status(200).json({
        success: true,
        message: "Session updated successfully (direct update)",
        id,
        updated_at: updatedSession.updatedAt || updatedSession.updated_at,
        timestamp: Date.now(),
        session: mapAutomatedMessagesField(updatedSession)
      });
    })
  );

  // Delete a session
  app.delete("/api/sessions/:id",
    validateJWT,
    validate(sessionIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: sessionId } = req.params as unknown as z.infer<typeof sessionIdSchema>;

      console.log(`[DELETE Session] Request to delete session ${sessionId} by user ${(req.user as any)?.id}`);

      // Only authenticated users can delete sessions
      if (!req.user) {
        throw errorHandler.unauthorized("Authentication required");
      }

      const userId = (req.user as any).id;

      // Use the Supabase service for UUID-based operations
      const { getSession, deleteSession } = await import('../services/supabase/sessionService');

      // Get the session to verify ownership
      const session = await getSession(sessionId);

      if (!session) {
        console.log(`[DELETE Session] Session ${sessionId} not found`);
        throw errorHandler.notFound("Session not found");
      }

      // Check if the user is the teacher of the session
      const sessionTeacherId = session.teacher_id || session.teacherId;
      if (sessionTeacherId !== userId) {
        console.log(`[DELETE Session] User ${userId} not authorized to delete session ${sessionId} (owned by ${sessionTeacherId})`);
        throw errorHandler.forbidden("You can only delete your own sessions");
      }

      console.log(`[DELETE Session] Ownership check passed for session ${sessionId}`);

      // Delete the session
      const result = await deleteSession(sessionId);

      if (!result.success) {
        throw errorHandler.internal("Failed to delete session");
      }

      console.log(`[DELETE Session] Successfully deleted session ${sessionId}`);

      // Invalidate cache using the enhanced invalidateSessionCache function
      try {
        invalidateSessionCache(sessionId, userId);
        invalidateCachePattern(`/api/sessions`);
        invalidateCachePattern(`/api/teachers/${userId}/sessions`);
      } catch (cacheError) {
        console.error(`[DELETE Session] Cache invalidation error (non-fatal):`, cacheError);
      }

      return res.status(200).json({
        success: true,
        message: "Session deleted successfully"
      });
    })
  );
}
