/**
 * API routes for scheduled messages
 */

import express, { Request, Response } from 'express';
import { IStorage } from '../storage';
import { MessageScheduler } from '../services/message-scheduler';
import serviceManager from '../services/ServiceManager';
import { validate } from '../middleware';
import { InsertScheduledMessageSchema } from '@shared/schema';

export function setupScheduledMessageRoutes(app: express.Application, storage: IStorage, messageScheduler: MessageScheduler) {
  // Get scheduled messages for a conversation
  app.get("/api/conversations/:conversationId/scheduled-messages", async (req: Request, res: Response) => {
    try {
      const conversationId = parseInt(req.params.conversationId);

      if (isNaN(conversationId)) {
        return res.status(400).json({ message: "Invalid conversation ID" });
      }

      // Check if conversation exists and user is a participant
      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: "Conversation not found" });
      }

      // Check if user is a participant
      if (!conversation.participantIds.includes(req.user.id)) {
        return res.status(403).json({ message: "You are not a participant in this conversation" });
      }

      // Get scheduled messages
      const scheduledMessages = await storage.getScheduledMessagesByConversation(conversationId);

      return res.status(200).json(scheduledMessages);
    } catch (error) {
      console.error('[API] Error getting scheduled messages:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get scheduled messages for a booking
  app.get("/api/bookings/:bookingId/scheduled-messages", async (req: Request, res: Response) => {
    try {
      const bookingId = parseInt(req.params.bookingId);

      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }

      // Check if booking exists and user is authorized
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Check if user is the student or teacher
      const session = await storage.getSession(booking.sessionId);
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }

      if (booking.userId !== req.user.id && session.teacherId !== req.user.id) {
        return res.status(403).json({ message: "You are not authorized to view these scheduled messages" });
      }

      // Get scheduled messages
      const scheduledMessages = await storage.getScheduledMessagesByBooking(bookingId);

      return res.status(200).json(scheduledMessages);
    } catch (error) {
      console.error('[API] Error getting scheduled messages for booking:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Create a scheduled message
  app.post("/api/scheduled-messages", async (req: Request, res: Response) => {
    try {
      const { conversationId, content, scheduledTime, bookingId, sessionId, metadata } = req.body;

      if (!conversationId || !content || !scheduledTime) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Check if conversation exists and user is a participant
      const conversation = await storage.getConversation(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: "Conversation not found" });
      }

      // Check if user is a participant
      if (!conversation.participantIds.includes(req.user.id)) {
        return res.status(403).json({ message: "You are not a participant in this conversation" });
      }

      // Create scheduled message
      const scheduledMessage = await storage.createScheduledMessage({
        conversationId,
        senderId: req.user.id,
        content,
        scheduledTime: new Date(scheduledTime),
        status: 'pending',
        bookingId: bookingId || null,
        sessionId: sessionId || null,
        metadata: metadata || null
      });

      return res.status(201).json(scheduledMessage);
    } catch (error) {
      console.error('[API] Error creating scheduled message:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Update a scheduled message
  app.put("/api/scheduled-messages/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid message ID" });
      }

      // Check if message exists
      const message = await storage.getScheduledMessage(id);
      if (!message) {
        return res.status(404).json({ message: "Scheduled message not found" });
      }

      // Check if user is the sender
      if (message.senderId !== req.user.id) {
        return res.status(403).json({ message: "You are not authorized to update this message" });
      }

      // Check if message has already been sent
      if (message.status === 'sent') {
        return res.status(400).json({ message: "Cannot update a message that has already been sent" });
      }

      // Update message status
      const { status } = req.body;
      if (status && status !== message.status) {
        await storage.updateScheduledMessageStatus(id, status);
      }

      return res.status(200).json({ message: "Scheduled message updated successfully" });
    } catch (error) {
      console.error('[API] Error updating scheduled message:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Delete a scheduled message
  app.delete("/api/scheduled-messages/:id", async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid message ID" });
      }

      // Check if message exists
      const message = await storage.getScheduledMessage(id);
      if (!message) {
        return res.status(404).json({ message: "Scheduled message not found" });
      }

      // Check if user is the sender
      if (message.senderId !== req.user.id) {
        return res.status(403).json({ message: "You are not authorized to delete this message" });
      }

      // Check if message has already been sent
      if (message.status === 'sent') {
        return res.status(400).json({ message: "Cannot delete a message that has already been sent" });
      }

      // Delete message
      await storage.deleteScheduledMessage(id);

      return res.status(200).json({ message: "Scheduled message deleted successfully" });
    } catch (error) {
      console.error('[API] Error deleting scheduled message:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });

  // Process pending scheduled messages (admin only)
  app.post("/api/scheduled-messages/process", async (req: Request, res: Response) => {
    try {
      // Check if user is an admin (you'll need to implement this check)
      if (!req.user.isAdmin) {
        return res.status(403).json({ message: "You are not authorized to perform this action" });
      }

      // Process pending messages
      const processedCount = await messageScheduler.deliverDueMessages();

      return res.status(200).json({
        message: "Processed pending scheduled messages",
        count: processedCount
      });
    } catch (error) {
      console.error('[API] Error processing scheduled messages:', error);
      return res.status(500).json({ message: "Internal server error" });
    }
  });
}
