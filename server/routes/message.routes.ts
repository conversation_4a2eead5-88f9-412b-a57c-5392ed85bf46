import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { validateJWT } from '../middleware/supabase-auth';
import { errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';

// Validation schemas
const conversationIdSchema = z.object({
  id: z.string().refine(val => !isNaN(parseInt(val)), {
    message: "Conversation ID must be a valid number"
  }).transform(val => parseInt(val))
});

const createMessageSchema = z.object({
  content: z.string().min(1, "Message content is required"),
  attachments: z.array(z.string()).optional()
});

const markAsReadSchema = z.object({
  messageIds: z.array(z.number()).optional()
});

/**
 * Register message-related routes
 * @param app Express application
 */
export function registerMessageRoutes(app: Express): void {
  console.log('[Routes] Registering message routes');

  // Get messages for a conversation
  app.get('/api/conversations/:id/messages',
    validateJWT,
    validate(conversationIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: conversationId } = req.params as unknown as z.infer<typeof conversationIdSchema>;

      // Get the conversation to check permissions
      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        throw errorHandler.notFound('Conversation not found');
      }

      // Check if user is a participant in the conversation
      const isParticipant = conversation.participants.some(p => p.id === req.user.id);

      if (!isParticipant) {
        throw errorHandler.forbidden('You are not a participant in this conversation');
      }

      // Get messages
      const messages = await storage.getConversationMessages(conversationId);

      // Mark messages as read for the current user
      await storage.markMessagesAsRead(conversationId, req.user.id);

      res.json(messages);
    })
  );

  // Send a message
  app.post('/api/conversations/:id/messages',
    validateJWT,
    validate(conversationIdSchema, 'params'),
    validate(createMessageSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: conversationId } = req.params as unknown as z.infer<typeof conversationIdSchema>;
      const validatedData = req.body;

      // Get the conversation to check permissions
      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        throw errorHandler.notFound('Conversation not found');
      }

      // Check if user is a participant in the conversation
      const isParticipant = conversation.participants.some(p => p.id === req.user.id);

      if (!isParticipant) {
        throw errorHandler.forbidden('You are not a participant in this conversation');
      }

      // Create the message
      const message = await storage.createMessage({
        conversationId,
        senderId: req.user.id,
        content: validatedData.content,
        attachments: validatedData.attachments,
        isRead: false
      });

      // Update conversation last activity
      await storage.updateConversation(conversationId, {
        lastActivityAt: new Date()
      });

      res.status(201).json(message);
    })
  );

  // Mark messages as read
  app.post('/api/conversations/:id/read',
    validateJWT,
    validate(conversationIdSchema, 'params'),
    validate(markAsReadSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: conversationId } = req.params as unknown as z.infer<typeof conversationIdSchema>;

      // Get the conversation to check permissions
      const conversation = await storage.getConversation(conversationId);

      if (!conversation) {
        throw errorHandler.notFound('Conversation not found');
      }

      // Check if user is a participant in the conversation
      const isParticipant = conversation.participants.some(p => p.id === req.user.id);

      if (!isParticipant) {
        throw errorHandler.forbidden('You are not a participant in this conversation');
      }

      // Mark messages as read
      await storage.markMessagesAsRead(conversationId, req.user.id);

      res.json({ message: 'Messages marked as read' });
    })
  );
}
