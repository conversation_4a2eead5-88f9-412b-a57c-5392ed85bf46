import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { createActivityMessage } from '../utils/activity-message';
import { invalidateCache } from '../middleware/cache';
import { productionMessageScheduler } from '../services/ProductionMessageScheduler';
import { errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';
import { validateJWT } from '../middleware/supabase-auth';

// Validation schemas
const createBookingSchema = z.object({
  sessionId: z.number(),
  date: z.string().or(z.date()),
  notes: z.string().optional(),
  paymentIntentId: z.string().optional(),
  paymentMethodId: z.string().optional()
});

const bookingIdSchema = z.object({
  id: z.string().refine(val => !isNaN(parseInt(val)), {
    message: "Booking ID must be a valid number"
  }).transform(val => parseInt(val))
});

const userIdSchema = z.object({
  id: z.string().uuid({
    message: "User ID must be a valid UUID"
  })
});

const bookingQuerySchema = z.object({
  status: z.string().optional()
});

const cancelBookingSchema = z.object({
  reason: z.string().optional()
});

/**
 * Register booking-related routes
 * @param app Express application
 */
export function registerBookingRoutes(app: Express): void {
  console.log('[Routes] Registering booking routes');

  // Create a booking
  app.post('/api/bookings',
    validateJWT,
    validate(createBookingSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const validatedData = req.body;
      const userId = req.user.id;

      // Get the session
      const session = await storage.getSessionWithTeacher(validatedData.sessionId);

      if (!session) {
        throw errorHandler.notFound('Session not found');
      }

      // Check if user is trying to book their own session
      if (session.teacherId === userId) {
        throw errorHandler.badRequest('You cannot book your own session');
      }

      // Check if session is at capacity
      const bookings = await storage.getSessionBookings(validatedData.sessionId);
      const activeBookings = bookings.filter(b => b.status !== 'canceled');

      if (session.maxParticipants && activeBookings.length >= session.maxParticipants) {
        throw errorHandler.conflict('Session is at full capacity');
      }

      // Check if user already has a booking for this session
      const existingBooking = activeBookings.find(b => b.userId === userId);

      if (existingBooking) {
        throw errorHandler.conflict('You already have a booking for this session');
      }

      // Create the booking
      const booking = await storage.createBooking({
        sessionId: validatedData.sessionId,
        userId,
      });

      console.log(`[API] Created booking ${booking.id} for session ${validatedData.sessionId} by user ${userId}`);

      // Send activity message to conversation
      await createActivityMessage(
        'booking',
        userId,
        Number(session.teacherId),
        validatedData.sessionId,
        {
          sessionTitle: session.title,
          createdAt: booking.createdAt,
          duration: session.duration,
          price: session.price,
          paymentStatus: booking.paymentStatus || 'Pending'
        }
      );

      // Schedule automated messages if available
      try {
        const scheduler = productionMessageScheduler;

        if (scheduler && session.automatedMessagesJson) {
          const automatedMessages = JSON.parse(session.automatedMessagesJson);

          if (automatedMessages && automatedMessages.length > 0) {
            const student = await storage.getUser(Number(userId));
            const teacher = await storage.getUser(Number(session.teacherId));

            if (student && teacher) {
              await scheduler.scheduleMessagesForBooking(
                booking,
                { ...session, automatedMessages },
                student,
                teacher
              );

              console.log(`[API] Scheduled automated messages for booking ${booking.id}`);
            }
          }
        }
      } catch (error) {
        console.error(`[API] Error scheduling messages for booking ${booking.id}:`, error);
        // Non-fatal error, continue
      }

      // Invalidate caches
      invalidateCache([
        `/api/sessions/${validatedData.sessionId}`,
        `/api/users/${userId}/bookings`,
      ]);

      res.status(201).json(booking);
    })
  );

  // Get user's bookings
  app.get('/api/users/bookings',
    validateJWT,
    validate(bookingQuerySchema, 'query'),
    asyncHandler(async (req: Request, res: Response) => {
      const userId = req.user.id;
      const status = req.query.status as string;

      // Get user's bookings
      const bookings = await storage.getUserBookings(Number(userId));

      // Filter by status if provided
      let filteredBookings = bookings;
      if (status) {
        filteredBookings = bookings.filter(b => b.status === status);
      }

      // Sort bookings by date (most recent first)
      filteredBookings.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      res.json(filteredBookings);
    })
  );

  // Get user's bookings by user ID (for frontend compatibility)
  app.get('/api/users/:id/bookings',
    validate(userIdSchema, 'params'),
    validate(bookingQuerySchema, 'query'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: userId } = req.params as unknown as z.infer<typeof userIdSchema>;
      const status = req.query.status as string;

      // Check if user is authorized to view these bookings
      if (req.user && req.user.id !== userId) {
        throw errorHandler.forbidden('You are not authorized to view these bookings');
      }

      // Use the Supabase booking service for UUID compatibility
      try {
        const { getUserBookings } = await import('../services/supabase/bookingService');
        const bookings = await getUserBookings(userId);

        // Filter by status if provided
        let filteredBookings = bookings;
        if (status) {
          filteredBookings = bookings.filter(b => b.status === status);
        }

        // Sort bookings by date (most recent first)
        filteredBookings.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        res.json(filteredBookings);
      } catch (error) {
        console.error(`Error fetching bookings for user ${userId}:`, error);
        console.error('Full error details:', JSON.stringify(error, null, 2));
        res.status(500).json({
          error: 'Failed to fetch user bookings',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error instanceof Error ? error.stack : JSON.stringify(error)
        });
      }
    })
  );

  // Direct endpoint for user bookings (for frontend compatibility)
  app.get('/api/direct/bookings/:id',
    validate(userIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: userId } = req.params as unknown as z.infer<typeof userIdSchema>;

      // Use the Supabase booking service for UUID compatibility
      try {
        const { getUserBookings } = await import('../services/supabase/bookingService');
        const bookings = await getUserBookings(userId);

        // Sort bookings by date (most recent first)
        bookings.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        res.json(bookings);
      } catch (error) {
        console.error(`Error fetching bookings for user ${userId}:`, error);
        res.status(500).json({
          error: 'Failed to fetch user bookings',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    })
  );

  // Get booking by ID
  app.get('/api/bookings/:id',
    validateJWT,
    validate(bookingIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: bookingId } = req.params as unknown as z.infer<typeof bookingIdSchema>;

      // Get the booking
      const booking = await storage.getBooking(bookingId);

      if (!booking) {
        throw errorHandler.notFound('Booking not found');
      }

      // Check if user is authorized to view this booking
      const session = await storage.getSession(booking.sessionId);
      if (!session) {
        throw errorHandler.notFound('Session not found');
      }
      if (booking.userId !== req.user.id && session.teacherId !== req.user.id) {
        throw errorHandler.forbidden('You are not authorized to view this booking');
      }

      res.json(booking);
    })
  );

  // Cancel booking
  app.post('/api/bookings/:id/cancel',
    validateJWT,
    validate(bookingIdSchema, 'params'),
    validate(cancelBookingSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: bookingId } = req.params as unknown as z.infer<typeof bookingIdSchema>;
      const { reason } = req.body;

      // Get the booking
      const booking = await storage.getBooking(bookingId);

      if (!booking) {
        throw errorHandler.notFound('Booking not found');
      }

      // Check if user is authorized to cancel this booking
      const session = await storage.getSession(booking.sessionId);
      if (!session) {
        throw errorHandler.notFound('Session not found');
      }
      if (booking.userId !== req.user.id && session.teacherId !== req.user.id) {
        throw errorHandler.forbidden('You are not authorized to cancel this booking');
      }

      // Check if booking is already canceled
      if (booking.status === 'canceled') {
        throw errorHandler.badRequest('Booking is already canceled');
      }

      // Cancel the booking
      const canceledBooking = await storage.updateBooking(bookingId, {
        status: 'canceled',
      });

      // Send activity message to conversation
      await createActivityMessage(
        'cancellation',
        req.user.id,
        booking.userId === req.user.id ? Number(session.teacherId) : booking.userId,
        booking.sessionId,
        {
          sessionTitle: session.title,
          createdAt: booking.createdAt,
          reason: reason || 'No reason provided'
        }
      );

      // Invalidate caches
      invalidateCache([
        `/api/sessions/${booking.sessionId}`,
        `/api/users/${booking.userId}/bookings`,
      ]);

      res.json(canceledBooking);
    })
  );
}
