import { Router, Request, Response } from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import serviceManager from '../services/ServiceManager';
import { invalidateCache } from '../middleware/cache';
import { z } from 'zod';
import { insertConversationSchema, insertMessageSchema } from '@shared/schema';

const router = Router();
const messageService = serviceManager.getMessageService();

/**
 * @api {get} /api/conversations Get all conversations
 * @apiDescription Get all conversations for the authenticated user
 */
router.get('/conversations', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    const conversations = await messageService.getUserConversations(userId);

    return res.json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return res.status(500).json({
      message: "Failed to fetch conversations",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {get} /api/conversations/:id Get conversation by ID
 * @apiDescription Get a conversation by ID
 * @apiParam {number} id Conversation ID
 */
router.get('/conversations/:id', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid conversation ID' });
    }

    const conversation = await messageService.getConversationWithMessages(id);

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    // Check if the user is a participant in this conversation
    const isParticipant = conversation.participants.some(p => p.id === req.user.id);

    if (!isParticipant) {
      return res.status(403).json({ error: 'You are not a participant in this conversation' });
    }

    return res.json(conversation);
  } catch (error) {
    console.error('Error getting conversation:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/conversations Create a new conversation
 * @apiDescription Create a new conversation
 * @apiBody {string} title Conversation title
 * @apiBody {number[]} participantIds Participant IDs
 */
router.post('/conversations', validateJWT, async (req: Request, res: Response) => {
  try {
    // Validate conversation data
    const conversationData = insertConversationSchema.parse(req.body);

    // Ensure the current user is a participant
    if (!conversationData.participantIds.includes(req.user.id)) {
      conversationData.participantIds.push(req.user.id);
    }

    // Create the conversation
    const conversation = await messageService.createConversation(conversationData);

    // Invalidate related caches
    invalidateCache([
      '/api/conversations',
      `/api/conversations/${conversation.id}`
    ]);

    return res.status(201).json(conversation);
  } catch (error) {
    console.error('Error creating conversation:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/conversations/:id/messages Send a message
 * @apiDescription Send a message to a conversation
 * @apiParam {number} id Conversation ID
 * @apiBody {string} content Message content
 */
router.post('/conversations/:id/messages', validateJWT, async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.id, 10);

    if (isNaN(conversationId)) {
      return res.status(400).json({ error: 'Invalid conversation ID' });
    }

    // Check if the conversation exists
    const conversation = await messageService.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    // Check if the user is a participant in this conversation
    const isParticipant = conversation.participants.some(p => p.id === req.user.id);

    if (!isParticipant) {
      return res.status(403).json({ error: 'You are not a participant in this conversation' });
    }

    // Validate message data
    const messageData = insertMessageSchema.parse({
      conversationId,
      senderId: req.user.id,
      content: req.body.content,
      isRead: false
    });

    // Create the message
    const message = await messageService.createMessage(messageData);

    // Invalidate related caches
    invalidateCache([
      `/api/conversations/${conversationId}`,
      `/api/conversations/${conversationId}/messages`
    ]);

    return res.status(201).json(message);
  } catch (error) {
    console.error('Error sending message:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/conversations/:id/messages Get conversation messages
 * @apiDescription Get messages for a conversation
 * @apiParam {number} id Conversation ID
 * @apiQuery {number} [limit] Limit the number of results
 * @apiQuery {number} [offset] Offset the results
 */
router.get('/conversations/:id/messages', validateJWT, async (req: Request, res: Response) => {
  try {
    const conversationId = parseInt(req.params.id, 10);

    if (isNaN(conversationId)) {
      return res.status(400).json({ error: 'Invalid conversation ID' });
    }

    // Check if the conversation exists
    const conversation = await messageService.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    // Check if the user is a participant in this conversation
    const isParticipant = conversation.participants.some(p => p.id === req.user.id);

    if (!isParticipant) {
      return res.status(403).json({ error: 'You are not a participant in this conversation' });
    }

    // Parse query parameters
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 50;
    const offset = req.query.offset ? parseInt(req.query.offset as string, 10) : 0;

    // Get the messages
    const messages = await messageService.getConversationMessages(conversationId, limit, offset);

    return res.json(messages);
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/messages/:id/read Mark message as read
 * @apiDescription Mark a message as read
 * @apiParam {number} id Message ID
 */
router.put('/messages/:id/read', validateJWT, async (req: Request, res: Response) => {
  try {
    const messageId = parseInt(req.params.id, 10);

    if (isNaN(messageId)) {
      return res.status(400).json({ error: 'Invalid message ID' });
    }

    // Check if the message exists
    const message = await messageService.getMessage(messageId);

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    // Check if the user is a participant in the conversation
    const conversation = await messageService.getConversation(message.conversationId);
    const isParticipant = conversation.participants.some(p => p.id === req.user.id);

    if (!isParticipant) {
      return res.status(403).json({ error: 'You are not a participant in this conversation' });
    }

    // Mark the message as read
    const updatedMessage = await messageService.markMessageAsRead(messageId);

    // Invalidate related caches
    invalidateCache([
      `/api/conversations/${message.conversationId}`,
      `/api/conversations/${message.conversationId}/messages`
    ]);

    return res.json(updatedMessage);
  } catch (error) {
    console.error('Error marking message as read:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/unread-messages Get unread message count
 * @apiDescription Get the number of unread messages for a user
 * @apiParam {number} id User ID
 */
router.get('/users/:id/unread-messages', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = parseInt(req.params.id, 10);

    if (isNaN(userId)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Ensure the user has permission to view this information
    if (req.user.id !== userId) {
      return res.status(403).json({ error: 'You do not have permission to view this information' });
    }

    // Get the unread message count
    const unreadCount = await messageService.getUnreadMessageCount(userId);

    return res.json({ unreadCount });
  } catch (error) {
    console.error('Error getting unread message count:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

export default router;
