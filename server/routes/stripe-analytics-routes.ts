import express from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import stripeAnalyticsService from '../services/stripe-analytics-service';

const router = express.Router();

/**
 * Test Stripe wrapper connection
 */
router.get('/api/stripe-analytics/test', validateJWT, async (req, res) => {
    try {
        const connectionTest = await stripeAnalyticsService.testConnection();
        res.json(connectionTest);
    } catch (error) {
        console.error('Error testing Stripe analytics connection:', error);
        res.status(500).json({ error: 'Failed to test connection' });
    }
});

/**
 * Get data summary
 */
router.get('/api/stripe-analytics/summary', validateJWT, async (req, res) => {
    try {
        const summary = await stripeAnalyticsService.getDataSummary();
        res.json(summary);
    } catch (error) {
        console.error('Error fetching data summary:', error);
        res.status(500).json({ error: 'Failed to fetch data summary' });
    }
});

/**
 * Get booking analytics
 */
router.get('/api/stripe-analytics/bookings', validateJWT, async (req, res) => {
    try {
        const limit = parseInt(req.query.limit as string) || 100;
        const offset = parseInt(req.query.offset as string) || 0;

        const bookings = await stripeAnalyticsService.getBookingAnalytics(limit, offset);
        res.json(bookings);
    } catch (error) {
        console.error('Error fetching booking analytics:', error);
        res.status(500).json({ error: 'Failed to fetch booking analytics' });
    }
});

/**
 * Get revenue summary
 */
router.get('/api/stripe-analytics/revenue', validateJWT, async (req, res) => {
    try {
        const startDate = req.query.startDate as string;
        const endDate = req.query.endDate as string;
        const limit = parseInt(req.query.limit as string) || 30;

        const revenue = await stripeAnalyticsService.getRevenueSummary(startDate, endDate, limit);
        res.json(revenue);
    } catch (error) {
        console.error('Error fetching revenue summary:', error);
        res.status(500).json({ error: 'Failed to fetch revenue summary' });
    }
});

/**
 * Get teacher earnings
 */
router.get('/api/stripe-analytics/teachers', validateJWT, async (req, res) => {
    try {
        const teacherId = req.query.teacherId as string;

        const earnings = await stripeAnalyticsService.getTeacherEarnings(teacherId);
        res.json(earnings);
    } catch (error) {
        console.error('Error fetching teacher earnings:', error);
        res.status(500).json({ error: 'Failed to fetch teacher earnings' });
    }
});

/**
 * Get teacher performance metrics for a specific teacher
 */
router.get('/api/stripe-analytics/teachers/:teacherId/metrics', validateJWT, async (req, res) => {
    try {
        const { teacherId } = req.params;

        const metrics = await stripeAnalyticsService.getTeacherPerformanceMetrics(teacherId);
        res.json(metrics);
    } catch (error) {
        console.error('Error fetching teacher performance metrics:', error);
        res.status(500).json({ error: 'Failed to fetch teacher performance metrics' });
    }
});

/**
 * Get payment reconciliation issues
 */
router.get('/api/stripe-analytics/reconciliation', validateJWT, async (req, res) => {
    try {
        const reconciliation = await stripeAnalyticsService.getPaymentReconciliation();
        res.json(reconciliation);
    } catch (error) {
        console.error('Error fetching payment reconciliation:', error);
        res.status(500).json({ error: 'Failed to fetch payment reconciliation' });
    }
});

/**
 * Get total revenue metrics
 */
router.get('/api/stripe-analytics/metrics/revenue', validateJWT, async (req, res) => {
    try {
        const metrics = await stripeAnalyticsService.getTotalRevenueMetrics();
        res.json(metrics);
    } catch (error) {
        console.error('Error fetching total revenue metrics:', error);
        res.status(500).json({ error: 'Failed to fetch total revenue metrics' });
    }
});

export default router; 