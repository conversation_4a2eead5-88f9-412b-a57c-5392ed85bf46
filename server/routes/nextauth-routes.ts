import express from 'express';
import { storage } from '../storage';
import { hashPassword, comparePasswords } from '../utils/passwordHasher';

const router = express.Router();

// NextAuth session endpoint
router.get('/api/auth/session', (req, res) => {
  if (req.user) {
    const { password, ...userWithoutPassword } = req.user;
    res.status(200).json({
      user: userWithoutPassword,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
    });
  } else {
    res.status(200).json({});
  }
});

// NextAuth CSRF token endpoint
router.get('/api/auth/csrf', (req, res) => {
  res.status(200).json({
    csrfToken: req.csrfToken?.() || 'mock-csrf-token'
  });
});

// NextAuth providers endpoint
router.get('/api/auth/providers', (req, res) => {
  res.status(200).json({
    google: {
      id: 'google',
      name: 'Google',
      type: 'oauth',
      signinUrl: '/api/auth/signin/google',
      callbackUrl: '/api/auth/callback/google'
    }
  });
});

// NextAuth signin endpoint for Google
router.get('/api/auth/signin/google', (req, res) => {
  const clientId = process.env.GOOGLE_CLIENT_ID;
  const redirectUri = `${process.env.NEXTAUTH_URL}/api/auth/callback/google`;
  const scope = 'openid email profile';
  const state = Math.random().toString(36).substring(2, 15);

  // Store state in session for verification
  req.session.oauthState = state;

  const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${state}`;

  res.redirect(authUrl);
});

// NextAuth callback endpoint for Google
router.get('/api/auth/callback/google', async (req, res) => {
  const { code, state } = req.query;

  // Verify state to prevent CSRF
  if (state !== req.session.oauthState) {
    return res.status(400).json({ error: 'Invalid state parameter' });
  }

  try {
    // Exchange code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        code: code as string,
        client_id: process.env.GOOGLE_CLIENT_ID || '',
        client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
        redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/callback/google`,
        grant_type: 'authorization_code'
      })
    });

    const tokens = await tokenResponse.json();

    // Get user info
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
      headers: {
        Authorization: `Bearer ${tokens.access_token}`
      }
    });

    const profile = await userInfoResponse.json();

    // Check if user exists by email
    let user = await storage.getUserByEmail(profile.email);

    if (!user) {
      // Create new user
      const username = profile.email.split('@')[0];
      user = await storage.createUser({
        name: profile.name,
        email: profile.email,
        username: username,
        password: await hashPassword(Math.random().toString(36).substring(2, 15)),
        role: 'Learner',
        avatar: profile.picture
      });

      // Link social account
      await storage.linkSocialAccount(user.id, {
        provider: 'google',
        providerId: profile.sub,
        username: username,
        email: profile.email,
        profileUrl: profile.picture
      });
    } else {
      // Link social account if not already linked
      const socialAccounts = await storage.getSocialAccounts(user.id);
      const googleAccount = socialAccounts.find(account => account.provider === 'google' && account.providerId === profile.sub);

      if (!googleAccount) {
        await storage.linkSocialAccount(user.id, {
          provider: 'google',
          providerId: profile.sub,
          username: user.username,
          email: profile.email,
          profileUrl: profile.picture
        });
      }
    }

    // Log the user in
    req.login(user, (err) => {
      if (err) {
        return res.redirect('/auth/error?error=LoginFailed');
      }

      // Redirect to the callback URL or home page
      const callbackUrl = req.session.callbackUrl || '/';
      delete req.session.callbackUrl;

      res.redirect(callbackUrl);
    });
  } catch (error) {
    console.error('OAuth error:', error);
    res.redirect('/auth/error?error=OAuthSignin');
  }
});

// NextAuth signout endpoint
router.post('/api/auth/signout', (req, res) => {
  req.logout((err) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to sign out' });
    }

    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ error: 'Failed to destroy session' });
      }

      res.status(200).json({ success: true });
    });
  });
});

// NextAuth error logging endpoint
router.post('/api/auth/_log', (req, res) => {
  // Just log the error and return success
  console.log('NextAuth error:', req.body);
  res.status(200).json({ success: true });
});

export default router;
