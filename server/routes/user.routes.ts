import { Request, Response, Router, RequestHandler } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { invalidateCache } from '../middleware/cache';
import { queryOptimizer } from '../services';
import { validateJWT, SupabaseUser } from '../middleware/supabase-auth';

const router = Router();

// Helper function to handle errors
const handleError = (res: Response, error: unknown, defaultMessage: string) => {
  console.error(defaultMessage, error);
  if (error instanceof z.ZodError) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.errors
    });
  }
  res.status(500).json({ message: defaultMessage });
};

// Type guard to check if user is authenticated with Supabase
const hasSupabaseUser = (req: Request): req is Request & { user: SupabaseUser } => {
  return !!(req as any).user && typeof (req as any).user.id === 'string';
};

// Get current user's profile (authenticated endpoint) - MUST come before /users/:id
router.get('/users/profile', validateJWT, async (req: Request, res: Response) => {
  try {
    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const userId = req.user.id;
    console.log(`[UserRoutes] Fetching authenticated user profile for ID: ${userId}`);

    const profile = await queryOptimizer.getUserProfile(userId);
    console.log(`[UserRoutes] Profile fetched:`, profile);

    if (!profile) {
      console.log(`[UserRoutes] User profile not found for authenticated user ID: ${userId}`);
      return res.status(404).json({ message: 'User profile not found' });
    }

    res.json(profile);
  } catch (error) {
    console.error('Error fetching current user profile:', error);
    res.status(500).json({ message: 'Failed to fetch user profile' });
  }
});

// Get user by ID (for frontend compatibility) - MUST come after specific routes
router.get('/users/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.params.id;
    console.log(`[UserRoutes] Fetching user profile for ID: ${userId}`);

    const profile = await queryOptimizer.getUserProfile(userId);
    console.log(`[UserRoutes] Profile fetched:`, profile);

    if (!profile) {
      console.log(`[UserRoutes] User profile not found for ID: ${userId}`);
      return res.status(404).json({ error: 'User profile not found' });
    }

    // Create a sanitized profile object
    const sanitizedProfile = { ...profile };

    // Check if the requesting user is the owner of this profile
    // This handles both direct ID matches and user_id/id mismatches
    const isOwner = hasSupabaseUser(req) && (
      String(req.user.id) === String(userId) ||
      String(req.user.id) === String(profile.user_id) ||
      (profile.id && String(req.user.id) === String(profile.id))
    );

    console.log(`[UserRoutes] Auth user ID: ${hasSupabaseUser(req) ? req.user.id : 'none'}, Profile ID: ${profile.id}, Profile user_id: ${profile.user_id}, isOwner: ${isOwner}`);

    // Remove sensitive information if not the profile owner
    if (!isOwner) {
      delete sanitizedProfile.email;
      delete sanitizedProfile.phone;
    }

    // Add ownership flag to the response
    sanitizedProfile.isOwnProfile = isOwner;

    // Get teacher sessions
    // Use the profile.id value if it exists, otherwise use the requested userId
    const teacherId = profile.id || userId;
    const teacherSessions = await storage.getRepositories()
      .getSessionRepository()
      .getSessionsByTeacher(teacherId);

    if (teacherSessions && teacherSessions.length > 0) {
      // Only include public sessions for non-owners
      sanitizedProfile.teachingSessions = isOwner
        ? teacherSessions
        : teacherSessions.filter((session: { isPublic?: boolean }) => session.isPublic);
    }

    res.json(sanitizedProfile);
  } catch (error) {
    console.log('Failed to fetch user profile:', error);
    handleError(res, error, 'Failed to fetch user profile');
  }
});

// Get user profile (legacy endpoint)
router.get('/users/:id/profile', async (req: Request, res: Response) => {
  try {
    const userId = req.params.id;

    // Use the optimized query for better performance
    const profile = await queryOptimizer.getUserProfile(userId);

    if (!profile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    // Check if the requesting user is the owner of this profile
    const isOwner = hasSupabaseUser(req) && (
      String(req.user.id) === String(userId) ||
      String(req.user.id) === String(profile.user_id) ||
      (profile.id && String(req.user.id) === String(profile.id))
    );

    // Remove sensitive information if not the profile owner
    if (!isOwner) {
      delete profile.email;
      delete profile.phone;

      // Only return public sessions if not the profile owner
      // Assuming teachingSessions is a property of the profile object
      if (profile.teachingSessions && Array.isArray(profile.teachingSessions)) {
        profile.teachingSessions = profile.teachingSessions.filter((session: { isPublic?: boolean }) => session.isPublic);
      }
    }

    // Add ownership flag to the response
    profile.isOwnProfile = isOwner;

    res.json(profile);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ message: 'Failed to fetch user profile' });
  }
});

// Update user profile - ADD validateJWT middleware
router.put('/users/profile', validateJWT, async (req: Request, res: Response) => {
  try {
    console.log('[API] Profile update request received');

    if (!hasSupabaseUser(req)) {
      console.log('[API] Authentication failed - no user found');
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const userId = req.user.id;
    console.log('[API] Authenticated user ID:', userId, 'Type:', typeof userId);

    // Validate profile data - Updated to include email and social media fields
    const profileSchema = z.object({
      displayName: z.string().optional(),
      bio: z.string().optional(),
      location: z.string().optional(),
      website: z.string().optional(),
      phone: z.string().optional(),
      timezone: z.string().optional(),
      profileImageUrl: z.string().optional(),
      coverImageUrl: z.string().optional(),
      isTeacher: z.boolean().optional(),
      socialLinks: z.record(z.string()).optional(),
      username: z.string().optional(),
      // Privacy settings
      showProfile: z.boolean().optional(),
      showTeachingSessions: z.boolean().optional(),
      showLearningSessions: z.boolean().optional(),
      showSocialLinks: z.boolean().optional(),
      showContact: z.boolean().optional(),
      // Social media URLs (handle null values properly)
      instagramUrl: z.string().nullable().optional(),
      facebookUrl: z.string().nullable().optional(),
      twitterUrl: z.string().nullable().optional(),
      linkedinUrl: z.string().nullable().optional(),
      youtubeUrl: z.string().nullable().optional(),
      tiktokUrl: z.string().nullable().optional(),
    });

    const validatedData = profileSchema.parse(req.body);

    // Check if username is taken if provided
    if (validatedData.username) {
      const existingUser = await storage.getUserByUsername(validatedData.username);
      if (existingUser && String(existingUser.id) !== String(userId)) {
        return res.status(409).json({ message: 'Username already taken' });
      }
    }

    // Since we're dealing with Supabase UUIDs, use Supabase directly
    console.log('[API] Using Supabase directly for UUID user:', userId);

    // Import supabase admin client
    const { supabaseAdmin } = await import('../lib/supabase-admin');

    // Map frontend fields to database fields
    const profileUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    // Add fields only if they're provided
    if (validatedData.displayName !== undefined) {
      profileUpdateData.name = validatedData.displayName;
    }
    if (validatedData.bio !== undefined) {
      profileUpdateData.bio = validatedData.bio;
    }
    if (validatedData.location !== undefined) {
      profileUpdateData.location = validatedData.location;
    }
    if (validatedData.website !== undefined) {
      profileUpdateData.website = validatedData.website;
    }
    if (validatedData.phone !== undefined) {
      profileUpdateData.phone = validatedData.phone;
    }
    if (validatedData.timezone !== undefined) {
      profileUpdateData.timezone = validatedData.timezone;
    }
    if (validatedData.profileImageUrl !== undefined) {
      profileUpdateData.avatar = validatedData.profileImageUrl;
    }
    if (validatedData.coverImageUrl !== undefined) {
      profileUpdateData.cover_photo = validatedData.coverImageUrl;
    }
    if (validatedData.isTeacher !== undefined) {
      profileUpdateData.is_teacher = validatedData.isTeacher;
    }
    if (validatedData.username !== undefined) {
      profileUpdateData.username = validatedData.username;
    }
    if (validatedData.showProfile !== undefined) {
      profileUpdateData.show_profile = validatedData.showProfile;
    }
    if (validatedData.showTeachingSessions !== undefined) {
      profileUpdateData.show_teaching_sessions = validatedData.showTeachingSessions;
    }
    if (validatedData.showLearningSessions !== undefined) {
      profileUpdateData.show_learning_sessions = validatedData.showLearningSessions;
    }
    if (validatedData.showSocialLinks !== undefined) {
      profileUpdateData.show_social_links = validatedData.showSocialLinks;
    }
    if (validatedData.showContact !== undefined) {
      profileUpdateData.show_contact = validatedData.showContact;
    }

    // Handle social links from socialLinks object
    if (validatedData.socialLinks) {
      // Map social links to individual fields
      if (validatedData.socialLinks.facebook) {
        profileUpdateData.facebook_url = validatedData.socialLinks.facebook;
      }
      if (validatedData.socialLinks.twitter) {
        profileUpdateData.twitter_url = validatedData.socialLinks.twitter;
      }
      if (validatedData.socialLinks.instagram) {
        profileUpdateData.instagram_url = validatedData.socialLinks.instagram;
      }
      if (validatedData.socialLinks.linkedin) {
        profileUpdateData.linkedin_url = validatedData.socialLinks.linkedin;
      }
      if (validatedData.socialLinks.youtube) {
        profileUpdateData.youtube_url = validatedData.socialLinks.youtube;
      }
      if (validatedData.socialLinks.tiktok) {
        profileUpdateData.tiktok_url = validatedData.socialLinks.tiktok;
      }
    }

    // Handle social media URLs from direct fields (frontend sends these)
    if (validatedData.instagramUrl !== undefined) {
      profileUpdateData.instagram_url = validatedData.instagramUrl;
    }
    if (validatedData.facebookUrl !== undefined) {
      profileUpdateData.facebook_url = validatedData.facebookUrl;
    }
    if (validatedData.twitterUrl !== undefined) {
      profileUpdateData.twitter_url = validatedData.twitterUrl;
    }
    if (validatedData.linkedinUrl !== undefined) {
      profileUpdateData.linkedin_url = validatedData.linkedinUrl;
    }
    if (validatedData.youtubeUrl !== undefined) {
      profileUpdateData.youtube_url = validatedData.youtubeUrl;
    }
    if (validatedData.tiktokUrl !== undefined) {
      profileUpdateData.tiktok_url = validatedData.tiktokUrl;
    }

    console.log('[API] Profile update data:', JSON.stringify(profileUpdateData, null, 2));

    // Remove email field if it exists (user_profiles table doesn't have email column)
    if ('email' in profileUpdateData) {
      delete profileUpdateData.email;
      console.log('[API] Removed email field from profile update data');
    }

    // Use upsert to handle both create and update cases
    const { data: updatedProfile, error } = await supabaseAdmin
      .from('user_profiles')
      .upsert({
        user_id: userId,
        ...profileUpdateData
      }, { onConflict: 'user_id' })
      .select()
      .single();

    if (error) {
      console.error('[API] Supabase update error:', error);
      return res.status(500).json({
        message: 'Failed to update user profile',
        error: error.message
      });
    }

    if (!updatedProfile) {
      return res.status(500).json({ message: 'No profile returned after update' });
    }

    // Invalidate cache for this user's profile
    invalidateCache([
      `/api/users/${userId}/profile`,
      `/api/users/profile`
    ]);

    console.log('[API] Profile updated successfully:', updatedProfile);
    res.json(updatedProfile);
  } catch (error) {
    console.error('Error updating user profile:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Invalid profile data',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update user profile' });
  }
});

// Search users
router.get('/search', async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string;
    const limit = parseInt(String(req.query.limit || '10'), 10);

    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    // Search users by username
    const users = await storage.searchUsersByUsername(query, limit);

    // Remove sensitive information
    const sanitizedUsers = users.map((user: any) => ({
      id: user.id,
      name: user.name || '',
      displayName: user.name || '',
      username: user.username,
      profileImageUrl: user.avatar || '',
      isTeacher: user.isTeacher || false
    }));

    res.json(sanitizedUsers);
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({ message: 'Failed to search users' });
  }
});

// Get user by username
router.get('/username/:username', async (req: Request, res: Response) => {
  try {
    const { username } = req.params;

    if (!username) {
      return res.status(400).json({ message: 'Username is required' });
    }

    // Get user by username
    const user = await storage.getUserByUsername(username);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Use the optimized query for better performance
    // Ensure user.id is treated as a string UUID here
    const profile = await queryOptimizer.getUserProfile(String(user.id));

    if (!profile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    // Create a sanitized profile object
    const sanitizedProfile = { ...profile };

    // Check if the requesting user is the owner of this profile
    const isOwner = hasSupabaseUser(req) && (
      String(req.user.id) === String(user.id) ||
      String(req.user.id) === String(profile.user_id) ||
      (profile.id && String(req.user.id) === String(profile.id))
    );

    // Remove sensitive information if not the profile owner
    if (!isOwner) {
      // Remove sensitive information
      if (sanitizedProfile.email) {
        delete sanitizedProfile.email;
      }

      if (sanitizedProfile.phone) {
        delete sanitizedProfile.phone;
      }

      // Only return public sessions if not the profile owner
      if (sanitizedProfile.teachingSessions && Array.isArray(sanitizedProfile.teachingSessions)) {
        sanitizedProfile.teachingSessions = sanitizedProfile.teachingSessions.filter(
          (session: { isPublic?: boolean }) => session.isPublic
        );
      }
    }

    // Add ownership flag to the response
    sanitizedProfile.isOwnProfile = isOwner;

    res.json(sanitizedProfile);
  } catch (error) {
    handleError(res, error, 'Failed to fetch user by username');
  }
});

// Update user teacher status
router.put('/teacher-status', async (req: Request, res: Response) => {
  try {
    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Validate teacher data
    const teacherSchema = z.object({
      isTeacher: z.boolean(),
      isTeacher: z.boolean()
    });

    const validatedData = teacherSchema.parse(req.body);
    console.log(`[API] Updating teacher status for user ${req.user.id} with data:`, validatedData);

    // For Supabase UUIDs, we need to handle this differently since the storage layer expects numeric IDs
    // Use Supabase directly for UUID users
    const { supabaseAdmin } = await import('../lib/supabase-admin');

    const { data: updatedProfile, error } = await supabaseAdmin
      .from('user_profiles')
      .update({
        is_teacher: validatedData.isTeacher,
        is_teacher: validatedData.isTeacher,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', req.user.id)
      .select()
      .single();

    if (error) {
      console.error('[API] Error updating teacher status:', error);
      return res.status(500).json({ message: 'Failed to update teacher status' });
    }

    if (!updatedProfile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    // Invalidate cache for this user's profile
    invalidateCache([
      `/api/users/${req.user.id}`,
      `/api/users/${req.user.id}/profile`,
      `/api/users/profile`
    ]);

    console.log(`[API] User ${req.user.id} teacher status updated successfully:`, {
      isTeacher: updatedProfile.is_teacher,
      isTeacher: updatedProfile.is_teacher
    });

    res.json(updatedProfile);
  } catch (error) {
    console.error('Error updating teacher status:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Invalid teacher data',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update teacher status' });
  }
});

// Get user's saved sessions
router.get('/:id/saved-sessions', async (req: Request, res: Response) => {
  try {
    const userId = req.params.id;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    if (!hasSupabaseUser(req) || String(req.user.id) !== userId) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    // For now, return an empty array since we don't have saved sessions implemented yet
    // TODO: Implement saved sessions functionality
    res.json([]);
  } catch (error) {
    console.error('Error fetching saved sessions:', error);
    res.status(500).json({ message: 'Failed to fetch saved sessions' });
  }
});

// Get current user's profile (duplicate route - keeping for compatibility)
router.get('/profile', async (req: Request, res: Response) => {
  try {
    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const userId = String(req.user.id);
    const profile = await queryOptimizer.getUserProfile(userId);

    if (!profile) {
      return res.status(404).json({ message: 'User profile not found' });
    }

    res.json(profile);
  } catch (error) {
    handleError(res, error, 'Failed to fetch current user profile');
  }
});

// Search users by username (duplicate route - keeping for compatibility)
router.get('/search', async (req: Request, res: Response) => {
  try {
    const query = req.query.q as string;
    const limit = parseInt(String(req.query.limit || '10'), 10);

    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    const users = await storage.searchUsersByUsername(query, limit);
    const sanitizedUsers = users.map((user: any) => ({
      id: user.id,
      name: user.name || '',
      displayName: user.name || '',
      username: user.username,
      profileImageUrl: user.avatar || '',
      isTeacher: user.isTeacher || false
    }));

    res.json(sanitizedUsers);
  } catch (error) {
    handleError(res, error, 'Failed to search users');
  }
});

// Get user's saved sessions (legacy route)
router.get('/saved-sessions', async (req: Request, res: Response) => {
  try {
    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const userId = String(req.user.id);
    // TODO: Implement saved sessions functionality
    res.json([]);
  } catch (error) {
    handleError(res, error, 'Failed to fetch saved sessions');
  }
});

// Add user conversations route
router.get('/users/:id/conversations', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.params.id;

    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // Ensure the user has permission to view these conversations
    if (String(req.user.id) !== String(userId)) {
      return res.status(403).json({ error: 'You do not have permission to view these conversations' });
    }

    // For now, return empty array as this feature is not fully implemented
    res.json([]);
  } catch (error) {
    console.error('Error getting user conversations:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Add unread messages count route
router.get('/users/:id/unread-messages-count', validateJWT, async (req: Request, res: Response) => {
  try {
    const userId = req.params.id;

    if (!hasSupabaseUser(req)) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // Ensure the user has permission to view this information
    if (String(req.user.id) !== String(userId)) {
      return res.status(403).json({ error: 'You do not have permission to view this information' });
    }

    // For now, return 0 as this feature is not fully implemented
    res.json({ unreadCount: 0 });
  } catch (error) {
    console.error('Error getting unread message count:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

export default router;
