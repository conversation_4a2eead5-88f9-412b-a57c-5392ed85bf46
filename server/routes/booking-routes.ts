import { Router, Request, Response } from 'express';
import serviceManager from '../services/ServiceManager';
import { invalidateCache } from '../middleware/cache';
import { z } from 'zod';
import { insertBookingSchema } from '@shared/schema';

const router = Router();
const bookingService = serviceManager.getBookingService();
const sessionService = serviceManager.getSessionService();

/**
 * @api {get} /api/bookings Get all bookings
 * @apiDescription Get all bookings, optionally filtered
 * @apiQuery {number} [userId] Filter by user ID
 * @apiQuery {number} [sessionId] Filter by session ID
 * @apiQuery {string} [status] Filter by status
 * @apiQuery {number} [limit] Limit the number of results
 * @apiQuery {number} [offset] Offset the results
 */
router.get('/bookings', async (req: Request, res: Response) => {
  try {
    const filters: any = {};

    // Convert query parameters to filters if provided
    if (req.query.userId) filters.userId = parseInt(req.query.userId as string, 10);
    if (req.query.sessionId) filters.sessionId = parseInt(req.query.sessionId as string, 10);
    if (req.query.status) filters.status = req.query.status;

    // Parse numeric filters
    if (req.query.limit) filters.limit = parseInt(req.query.limit as string, 10);
    if (req.query.offset) filters.offset = parseInt(req.query.offset as string, 10);

    // Check if the user is an admin
    const isAdmin = req.user && (req.user as any).role === 'admin';

    // If not admin, restrict to user's own bookings
    if (!isAdmin) {
      filters.userId = req.user.id;
    }

    const bookings = await bookingService.getBookings(filters);

    return res.json(bookings);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return res.status(500).json({
      message: "Failed to fetch bookings",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {get} /api/bookings/:id Get booking by ID
 * @apiDescription Get a booking by ID
 * @apiParam {number} id Booking ID
 */
router.get('/bookings/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid booking ID' });
    }

    const booking = await bookingService.getBookingWithSession(id);

    if (!booking) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Check if the user has permission to view this booking
    const isAdmin = req.user && (req.user as any).role === 'admin';
    const isTeacher = req.user && booking.session && booking.session.teacherId === req.user.id;
    const isBooker = req.user && booking.userId === req.user.id;

    if (!isAdmin && !isTeacher && !isBooker) {
      return res.status(403).json({ error: 'You do not have permission to view this booking' });
    }

    return res.json(booking);
  } catch (error) {
    console.error('Error getting booking:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/bookings Create a new booking
 * @apiDescription Create a new booking
 * @apiBody {number} sessionId Session ID
 * @apiBody {number} userId User ID
 * @apiBody {string} [status] Booking status
 * @apiBody {string} [notes] Booking notes
 * @apiBody {string} [paymentStatus] Payment status
 * @apiBody {number} [paymentAmount] Payment amount
 */
router.post('/bookings', async (req: Request, res: Response) => {
  try {
    console.log('[API] Creating booking with data:', req.body);

    // Validate booking data
    const bookingData = insertBookingSchema.parse(req.body);

    // Check if the session exists
    const session = await sessionService.getSession(bookingData.sessionId);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Check if the session is available
    if (session.maxParticipants !== null) {
      const bookingCount = await bookingService.countSessionBookings(bookingData.sessionId);

      if (bookingCount >= session.maxParticipants) {
        return res.status(409).json({ error: 'Session is fully booked' });
      }
    }

    // Check if the user already has a booking for this session
    const existingBooking = await bookingService.getUserSessionBooking(bookingData.userId, bookingData.sessionId);

    if (existingBooking) {
      return res.status(409).json({ error: 'User already has a booking for this session' });
    }

    // Create the booking
    const booking = await bookingService.createBooking(bookingData);

    // Invalidate related caches
    invalidateCache([
      '/api/bookings',
      `/api/bookings/${booking.id}`,
      `/api/sessions/${booking.sessionId}`,
      `/api/users/${booking.userId}/bookings`
    ]);

    return res.status(201).json(booking);
  } catch (error) {
    console.error('Error creating booking:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/bookings/:id Update a booking
 * @apiDescription Update a booking
 * @apiParam {number} id Booking ID
 * @apiBody {string} [status] Booking status
 * @apiBody {string} [notes] Booking notes
 * @apiBody {string} [paymentStatus] Payment status
 * @apiBody {number} [paymentAmount] Payment amount
 */
router.put('/bookings/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid booking ID' });
    }

    // Check if the booking exists
    const booking = await bookingService.getBooking(id);

    if (!booking) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Check if the user has permission to update this booking
    const isAdmin = req.user && (req.user as any).role === 'admin';
    const session = await sessionService.getSession(booking.sessionId);
    const isTeacher = req.user && session && session.teacherId === req.user.id;
    const isBooker = req.user && booking.userId === req.user.id;

    if (!isAdmin && !isTeacher && !isBooker) {
      return res.status(403).json({ error: 'You do not have permission to update this booking' });
    }

    // Update the booking
    const updatedBooking = await bookingService.updateBooking(id, req.body);

    if (!updatedBooking) {
      return res.status(500).json({ error: 'Failed to update booking' });
    }

    // Invalidate related caches
    invalidateCache([
      '/api/bookings',
      `/api/bookings/${id}`,
      `/api/sessions/${booking.sessionId}`,
      `/api/users/${booking.userId}/bookings`
    ]);

    return res.json(updatedBooking);
  } catch (error) {
    console.error('Error updating booking:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {delete} /api/bookings/:id Cancel a booking
 * @apiDescription Cancel a booking
 * @apiParam {number} id Booking ID
 */
router.delete('/bookings/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid booking ID' });
    }

    // Check if the booking exists
    const booking = await bookingService.getBooking(id);

    if (!booking) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    // Check if the user has permission to cancel this booking
    const isAdmin = req.user && (req.user as any).role === 'admin';
    const session = await sessionService.getSession(booking.sessionId);
    const isTeacher = req.user && session && session.teacherId === req.user.id;
    const isBooker = req.user && booking.userId === req.user.id;

    if (!isAdmin && !isTeacher && !isBooker) {
      return res.status(403).json({ error: 'You do not have permission to cancel this booking' });
    }

    // Cancel the booking
    const cancelled = await bookingService.cancelBooking(id);

    if (!cancelled) {
      return res.status(500).json({ error: 'Failed to cancel booking' });
    }

    // Invalidate related caches
    invalidateCache([
      '/api/bookings',
      `/api/bookings/${id}`,
      `/api/sessions/${booking.sessionId}`,
      `/api/users/${booking.userId}/bookings`
    ]);

    return res.json({ message: 'Booking cancelled successfully' });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/sessions/:id/bookings Get session bookings
 * @apiDescription Get bookings for a session
 * @apiParam {number} id Session ID
 */
router.get('/sessions/:id/bookings', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid session ID' });
    }

    // Check if the session exists
    const session = await sessionService.getSession(id);

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Check if the user has permission to view these bookings
    const isAdmin = req.user && (req.user as any).role === 'admin';
    const isTeacher = req.user && session.teacherId === req.user.id;

    if (!isAdmin && !isTeacher) {
      return res.status(403).json({ error: 'You do not have permission to view these bookings' });
    }

    const bookings = await bookingService.getSessionBookings(id);

    return res.json(bookings);
  } catch (error) {
    console.error('Error getting session bookings:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

export default router;
