/**
 * Supabase Routes Registration
 *
 * This file registers all Supabase-related routes.
 */

import { Express } from 'express';
import { registerSupabaseAuthRoutes } from './supabase-auth.routes';
import supabaseProfileRoutes from './supabase-profile.routes';
import supabaseBookingRoutes from './supabase-booking.routes';
import supabaseAdminRoutes from './supabase-admin.routes';
import supabaseTeacherRoutes from './supabase-teacher.routes';

/**
 * Register all Supabase routes
 * @param app Express application
 */
export function registerSupabaseRoutes(app: Express): void {
  console.log('[Routes] Registering Supabase routes');

  // Register auth routes
  registerSupabaseAuthRoutes(app);

  // Register profile routes
  app.use('/api/supabase/profiles', supabaseProfileRoutes);

  // Register booking routes
  app.use('/api/supabase/bookings', supabaseBookingRoutes);

  // Register teacher routes
  app.use('/api/supabase/teachers', supabaseTeacherRoutes);

  // Register admin routes
  app.use('/api/supabase/admin', supabaseAdminRoutes);
}
