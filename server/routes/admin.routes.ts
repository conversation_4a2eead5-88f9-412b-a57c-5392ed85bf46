import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { isAdmin } from '../middleware/auth.middleware';
import { storage } from '../storage';
import { monitoring } from '../services/monitoring';
import { logger } from '../services/logger';
import { validate, asyncHandler } from '../middleware';
import os from 'os';

/**
 * Register admin routes
 * @param app Express application
 */
export function registerAdminRoutes(app: Express): void {
  console.log('[Routes] Registering admin routes');

  // Admin dashboard
  app.get('/admin', (req: Request, res: Response) => {
    res.redirect('/admin/dashboard');
  });

  // Admin dashboard
  app.get('/admin/dashboard', (req: Request, res: Response) => {
    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>SessionHub Admin Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { padding-top: 20px; }
          .card { margin-bottom: 20px; }
          .nav-link.active { font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="mb-4">SessionHub Admin Dashboard</h1>

          <div class="row mb-4">
            <div class="col-md-3">
              <div class="list-group">
                <a href="/admin/dashboard" class="list-group-item list-group-item-action active">Dashboard</a>
                <a href="/admin/status" class="list-group-item list-group-item-action">Server Status</a>
                <a href="/admin/monitoring" class="list-group-item list-group-item-action">Error Monitoring</a>
                <a href="/admin/logs" class="list-group-item list-group-item-action">Application Logs</a>
                <a href="/admin/users" class="list-group-item list-group-item-action">User Management</a>
                <a href="/admin/sessions" class="list-group-item list-group-item-action">Session Management</a>
                <a href="/" class="list-group-item list-group-item-action text-primary">Back to Site</a>
              </div>
            </div>

            <div class="col-md-9">
              <div class="row">
                <div class="col-md-4">
                  <div class="card">
                    <div class="card-body">
                      <h5 class="card-title">System Status</h5>
                      <p class="card-text">Server is running normally</p>
                      <a href="/admin/status" class="btn btn-primary">View Details</a>
                    </div>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="card">
                    <div class="card-body">
                      <h5 class="card-title">Error Monitoring</h5>
                      <p class="card-text">View application errors and performance issues</p>
                      <a href="/admin/monitoring" class="btn btn-primary">View Details</a>
                    </div>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="card">
                    <div class="card-body">
                      <h5 class="card-title">User Management</h5>
                      <p class="card-text">Manage users and permissions</p>
                      <a href="/admin/users" class="btn btn-primary">View Details</a>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row mt-4">
                <div class="col-md-12">
                  <div class="card">
                    <div class="card-header">
                      Quick Stats
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-3">
                          <h5>Server Uptime</h5>
                          <p id="uptime">${formatUptime(process.uptime())}</p>
                        </div>
                        <div class="col-md-3">
                          <h5>Memory Usage</h5>
                          <p id="memory">${formatMemory(process.memoryUsage().heapUsed)}</p>
                        </div>
                        <div class="col-md-3">
                          <h5>CPU Load</h5>
                          <p id="cpu">${os.loadavg()[0].toFixed(2)}%</p>
                        </div>
                        <div class="col-md-3">
                          <h5>Environment</h5>
                          <p id="env">${process.env.NODE_ENV || 'development'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
      </body>
      </html>
    `);
  });

  // Error monitoring dashboard
  app.get('/admin/monitoring', (req: Request, res: Response) => {
    const errorStats = monitoring.getErrorStats();
    const requestStats = monitoring.getRequestStats();

    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error Monitoring - SessionHub Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { padding-top: 20px; }
          .card { margin-bottom: 20px; }
          .nav-link.active { font-weight: bold; }
          .table-responsive { margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="mb-4">Error Monitoring</h1>

          <div class="row mb-4">
            <div class="col-md-3">
              <div class="list-group">
                <a href="/admin/dashboard" class="list-group-item list-group-item-action">Dashboard</a>
                <a href="/admin/status" class="list-group-item list-group-item-action">Server Status</a>
                <a href="/admin/monitoring" class="list-group-item list-group-item-action active">Error Monitoring</a>
                <a href="/admin/logs" class="list-group-item list-group-item-action">Application Logs</a>
                <a href="/admin/users" class="list-group-item list-group-item-action">User Management</a>
                <a href="/admin/sessions" class="list-group-item list-group-item-action">Session Management</a>
                <a href="/" class="list-group-item list-group-item-action text-primary">Back to Site</a>
              </div>

              <div class="mt-4">
                <button id="resetStats" class="btn btn-warning">Reset Statistics</button>
              </div>
            </div>

            <div class="col-md-9">
              <div class="card">
                <div class="card-header">
                  Error Statistics
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Error Type</th>
                          <th>Count</th>
                        </tr>
                      </thead>
                      <tbody id="errorStats">
                        ${Object.entries(errorStats).map(([type, count]) => `
                          <tr>
                            <td>${type}</td>
                            <td>${count}</td>
                          </tr>
                        `).join('') || '<tr><td colspan="2">No errors recorded</td></tr>'}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div class="card">
                <div class="card-header">
                  Slow Requests
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Route</th>
                          <th>Count</th>
                        </tr>
                      </thead>
                      <tbody id="slowRequests">
                        ${Object.entries(requestStats.slowRequests).map(([route, count]) => `
                          <tr>
                            <td>${route}</td>
                            <td>${count}</td>
                          </tr>
                        `).join('') || '<tr><td colspan="2">No slow requests recorded</td></tr>'}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div class="card">
                <div class="card-header">
                  Request Statistics
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Route</th>
                          <th>Count</th>
                        </tr>
                      </thead>
                      <tbody id="requestStats">
                        ${Object.entries(requestStats.requests).map(([route, count]) => `
                          <tr>
                            <td>${route}</td>
                            <td>${count}</td>
                          </tr>
                        `).join('') || '<tr><td colspan="2">No requests recorded</td></tr>'}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
        <script>
          document.getElementById('resetStats').addEventListener('click', async () => {
            try {
              const response = await fetch('/api/dev/monitoring/reset', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                credentials: 'include'
              });

              if (response.ok) {
                alert('Statistics reset successfully');
                window.location.reload();
              } else {
                alert('Failed to reset statistics');
              }
            } catch (error) {
              console.error('Error:', error);
              alert('An error occurred');
            }
          });
        </script>
      </body>
      </html>
    `);
  });

  // User management
  app.get('/admin/users', asyncHandler(async (req: Request, res: Response) => {
    const users = await storage.getAllUsers();

    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>User Management - SessionHub Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { padding-top: 20px; }
          .card { margin-bottom: 20px; }
          .nav-link.active { font-weight: bold; }
          .table-responsive { margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="mb-4">User Management</h1>

          <div class="row mb-4">
            <div class="col-md-3">
              <div class="list-group">
                <a href="/admin/dashboard" class="list-group-item list-group-item-action">Dashboard</a>
                <a href="/admin/status" class="list-group-item list-group-item-action">Server Status</a>
                <a href="/admin/monitoring" class="list-group-item list-group-item-action">Error Monitoring</a>
                <a href="/admin/logs" class="list-group-item list-group-item-action">Application Logs</a>
                <a href="/admin/users" class="list-group-item list-group-item-action active">User Management</a>
                <a href="/admin/sessions" class="list-group-item list-group-item-action">Session Management</a>
                <a href="/" class="list-group-item list-group-item-action text-primary">Back to Site</a>
              </div>
            </div>

            <div class="col-md-9">
              <div class="card">
                <div class="card-header">
                  Users
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Name</th>
                          <th>Email</th>
                          <th>Role</th>
                          <th>Created At</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${users.map(user => `
                          <tr>
                            <td>${user.id}</td>
                            <td>${user.name || '-'}</td>
                            <td>${user.email}</td>
                            <td>${user.role || 'user'}</td>
                            <td>${new Date(user.createdAt).toLocaleString()}</td>
                            <td>
                              <a href="/admin/users/${user.id}" class="btn btn-sm btn-primary">View</a>
                              <form method="post" action="/api/admin/users/${user.id}/role" style="display:inline-block;">
                                <input type="hidden" name="role" value="${user.role === 'admin' ? 'user' : 'admin'}" />
                                <button type="submit" class="btn btn-sm btn-${user.role === 'admin' ? 'warning' : 'success'}">
                                  ${user.role === 'admin' ? 'Remove Admin' : 'Make Admin'}
                                </button>
                              </form>
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
      </body>
      </html>
    `);
  }));

  // Session management
  app.get('/admin/sessions', asyncHandler(async (req: Request, res: Response) => {
    const sessions = await storage.getSessions({});

    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Session Management - SessionHub Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { padding-top: 20px; }
          .card { margin-bottom: 20px; }
          .nav-link.active { font-weight: bold; }
          .table-responsive { margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="mb-4">Session Management</h1>

          <div class="row mb-4">
            <div class="col-md-3">
              <div class="list-group">
                <a href="/admin/dashboard" class="list-group-item list-group-item-action">Dashboard</a>
                <a href="/admin/status" class="list-group-item list-group-item-action">Server Status</a>
                <a href="/admin/monitoring" class="list-group-item list-group-item-action">Error Monitoring</a>
                <a href="/admin/logs" class="list-group-item list-group-item-action">Application Logs</a>
                <a href="/admin/users" class="list-group-item list-group-item-action">User Management</a>
                <a href="/admin/sessions" class="list-group-item list-group-item-action active">Session Management</a>
                <a href="/" class="list-group-item list-group-item-action text-primary">Back to Site</a>
              </div>
            </div>

            <div class="col-md-9">
              <div class="card">
                <div class="card-header">
                  Sessions
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>ID</th>
                          <th>Title</th>
                          <th>Teacher</th>
                          <th>Price</th>
                          <th>Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${sessions.map(session => `
                          <tr>
                            <td>${session.id}</td>
                            <td>${session.title}</td>
                            <td>${session.teacher?.name || 'Unknown'}</td>
                            <td>$${session.price}</td>
                            <td>${session.date ? new Date(session.date).toLocaleString() : 'N/A'}</td>
                            <td>
                              <a href="/admin/sessions/${session.id}" class="btn btn-sm btn-primary">View</a>
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
      </body>
      </html>
    `);
  }));

  // Application logs
  app.get('/admin/logs', (req: Request, res: Response) => {
    // In a real application, you would fetch logs from a file or database
    // For now, we'll just show a placeholder

    res.send(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Application Logs - SessionHub Admin</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
          body { padding-top: 20px; }
          .card { margin-bottom: 20px; }
          .nav-link.active { font-weight: bold; }
          .log-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
          }
          .log-entry { margin-bottom: 5px; }
          .log-error { color: #dc3545; }
          .log-warn { color: #ffc107; }
          .log-info { color: #0d6efd; }
          .log-debug { color: #6c757d; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="mb-4">Application Logs</h1>

          <div class="row mb-4">
            <div class="col-md-3">
              <div class="list-group">
                <a href="/admin/dashboard" class="list-group-item list-group-item-action">Dashboard</a>
                <a href="/admin/status" class="list-group-item list-group-item-action">Server Status</a>
                <a href="/admin/monitoring" class="list-group-item list-group-item-action">Error Monitoring</a>
                <a href="/admin/logs" class="list-group-item list-group-item-action active">Application Logs</a>
                <a href="/admin/users" class="list-group-item list-group-item-action">User Management</a>
                <a href="/admin/sessions" class="list-group-item list-group-item-action">Session Management</a>
                <a href="/" class="list-group-item list-group-item-action text-primary">Back to Site</a>
              </div>

              <div class="mt-4">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="" id="showErrors" checked>
                  <label class="form-check-label" for="showErrors">
                    Show Errors
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="" id="showWarnings" checked>
                  <label class="form-check-label" for="showWarnings">
                    Show Warnings
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="" id="showInfo" checked>
                  <label class="form-check-label" for="showInfo">
                    Show Info
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="" id="showDebug">
                  <label class="form-check-label" for="showDebug">
                    Show Debug
                  </label>
                </div>
              </div>
            </div>

            <div class="col-md-9">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <span>Application Logs</span>
                  <button id="refreshLogs" class="btn btn-sm btn-primary">Refresh</button>
                </div>
                <div class="card-body">
                  <div class="log-container" id="logContainer">
                    <div class="log-entry log-info">[2023-05-01T12:00:00.000Z] [INFO] Server started</div>
                    <div class="log-entry log-info">[2023-05-01T12:00:01.000Z] [INFO] Database connected</div>
                    <div class="log-entry log-warn">[2023-05-01T12:05:00.000Z] [WARN] Slow database query detected</div>
                    <div class="log-entry log-error">[2023-05-01T12:10:00.000Z] [ERROR] Failed to process payment</div>
                    <div class="log-entry log-debug">[2023-05-01T12:15:00.000Z] [DEBUG] Processing request: GET /api/sessions</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
        <script>
          // Filter logs based on checkboxes
          function filterLogs() {
            const showErrors = document.getElementById('showErrors').checked;
            const showWarnings = document.getElementById('showWarnings').checked;
            const showInfo = document.getElementById('showInfo').checked;
            const showDebug = document.getElementById('showDebug').checked;

            document.querySelectorAll('.log-error').forEach(el => {
              el.style.display = showErrors ? 'block' : 'none';
            });

            document.querySelectorAll('.log-warn').forEach(el => {
              el.style.display = showWarnings ? 'block' : 'none';
            });

            document.querySelectorAll('.log-info').forEach(el => {
              el.style.display = showInfo ? 'block' : 'none';
            });

            document.querySelectorAll('.log-debug').forEach(el => {
              el.style.display = showDebug ? 'block' : 'none';
            });
          }

          // Add event listeners to checkboxes
          document.getElementById('showErrors').addEventListener('change', filterLogs);
          document.getElementById('showWarnings').addEventListener('change', filterLogs);
          document.getElementById('showInfo').addEventListener('change', filterLogs);
          document.getElementById('showDebug').addEventListener('change', filterLogs);

          // Initial filter
          filterLogs();

          // Refresh logs
          document.getElementById('refreshLogs').addEventListener('click', () => {
            // In a real application, you would fetch logs from the server
            alert('Logs refreshed');
          });
        </script>
      </body>
      </html>
    `);
  });
}

// Helper functions
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

function formatMemory(bytes: number): string {
  const mb = bytes / 1024 / 1024;
  return `${mb.toFixed(2)} MB`;
}
