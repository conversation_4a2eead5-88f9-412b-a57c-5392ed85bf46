import { Express, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';

// Validation schemas
const createPaymentIntentSchema = z.object({
  sessionId: z.number(),
  bookingId: z.number().optional()
});

const confirmPaymentSchema = z.object({
  paymentIntentId: z.string(),
  bookingId: z.number()
});

/**
 * Register payment-related routes
 * @param app Express application
 */
export function registerPaymentRoutes(app: Express): void {
  console.log('[Routes] Registering payment routes');

  // Create a payment intent
  app.post('/api/payments/create-intent',
    validate(createPaymentIntentSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { sessionId, bookingId } = req.body;

      // Get the session
      const session = await storage.getSession(sessionId);

      if (!session) {
        throw errorHandler.notFound('Session not found');
      }

      // Create a payment intent (placeholder for Stripe integration)
      const paymentIntent = {
        id: `pi_${Math.random().toString(36).substring(2, 15)}`,
        amount: session.price * 100, // Convert to cents
        currency: 'usd',
        status: 'requires_payment_method'
      };

      res.json(paymentIntent);
    })
  );

  // Confirm a payment
  app.post('/api/payments/confirm',
    validate(confirmPaymentSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const { paymentIntentId, bookingId } = req.body;

      // Update booking with payment information
      const updatedBooking = await storage.updateBooking(bookingId, {
        paymentIntentId,
        paymentStatus: 'paid',
        paidAt: new Date()
      });

      if (!updatedBooking) {
        throw errorHandler.notFound('Booking not found');
      }

      res.json(updatedBooking);
    })
  );

  // Get payment history
  app.get('/api/payments/history',
    asyncHandler(async (req: Request, res: Response) => {
      const userId = req.user.id;

      // Get user's payment history
      const payments = await storage.getUserPayments(userId);

      res.json(payments);
    })
  );
}
