import { Express } from 'express';
import { Server } from 'http';
import { Server as SocketIOServer } from 'socket.io';

// Import route modules
import userRoutes from './user.routes';
import { registerSessionRoutes } from './session.routes';
import { registerBookingRoutes } from './booking.routes';
import { registerMessageRoutes } from './message.routes';
import { registerConversationRoutes } from './conversation.routes';
import { registerTeacherRoutes } from './teacher.routes';
import { registerPaymentRoutes } from './payment.routes';
import { registerDevRoutes } from './dev.routes';
import { registerAdminRoutes } from './admin.routes';
import { registerSupabaseAuthRoutes } from './supabase-auth.routes';
import { registerSupabaseRoutes } from './supabase.routes';

// Import existing route modules
// AWS routes removed
import emailVerificationRoutes from './email-verification-routes';
import passwordResetRoutes from './password-reset-routes';
// uploadRoutes removed - using Supabase storage instead
import reviewRoutes from './review-routes';
import devToolsRoutes from './dev-tools-routes';
import paymentRoutes from './payment-routes';
// import teacherPaymentRoutes from './teacher-payment-routes'; // File doesn't exist - commented out
// import teacherRoutes from './teacher-routes'; // Temporarily disabled due to import issues
// TODO: Rename teacher-payment-routes.ts to teacher-payment-routes.ts after migration
import stripeConnectRoutes from './stripe-connect-routes';
import stripeAnalyticsRoutes from './stripe-analytics-routes';
import supabaseRoutes from './supabase-routes';
import supabaseAdminRoutes from './supabase-admin.routes';

// Import middleware
import { setupCorsMiddleware } from '../middleware/cors';
import { setupApiMiddleware } from '../middleware/api';

/**
 * Register all routes for the application
 * @param app Express application
 * @param io Socket.IO server instance
 */
export async function registerRoutes(app: Express, io: SocketIOServer): Promise<void> {
  // Setup middleware
  setupCorsMiddleware(app);
  setupApiMiddleware(app);

  // Mount existing route modules
  // AWS routes removed
  app.use('/api/email-verification', emailVerificationRoutes);
  app.use('/api/password-reset', passwordResetRoutes);
  app.use('/api', reviewRoutes);
  app.use(paymentRoutes);
  // app.use(teacherPaymentRoutes); // Commented out - file doesn't exist
  // app.use(teacherRoutes); // Temporarily disabled due to import issues
  app.use(stripeConnectRoutes);
  app.use(stripeAnalyticsRoutes);
  app.use('/api', supabaseRoutes);

  // Mount the user routes with the /api base path
  app.use('/api', userRoutes);

  // Mount dev tools routes in development only
  if (process.env.NODE_ENV === 'development') {
    app.use('/api/dev-tools', devToolsRoutes);
    console.log('[API] Dev Tools routes mounted (development only)');
  }

  // Health check endpoint
  app.get('/api/health-check', (_req, res) => {
    res.status(200).json({ status: 'ok', timestamp: Date.now() });
  });

  // Register new modular routes
  registerSessionRoutes(app);
  registerBookingRoutes(app);
  registerMessageRoutes(app);
  registerConversationRoutes(app);
  registerTeacherRoutes(app);
  registerPaymentRoutes(app);
  // Cognito auth routes removed
  registerSupabaseAuthRoutes(app);
  registerSupabaseRoutes(app);
  registerDevRoutes(app);
  registerAdminRoutes(app);
  app.use('/api/supabase-admin', supabaseAdminRoutes);

  console.log(`Server initialized. Routes registered.`);
}
