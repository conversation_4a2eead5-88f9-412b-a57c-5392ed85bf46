import { Router, Request, Response } from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import serviceManager from '../services/ServiceManager';
import { invalidateCache } from '../middleware/cache';
import { z } from 'zod';

const router = Router();
const userService = serviceManager.getUserService();

/**
 * @api {get} /api/users Get all users
 * @apiDescription Get all users, optionally filtered
 * @apiQuery {string} [username] Filter by username
 * @apiQuery {string} [email] Filter by email
 * @apiQuery {boolean} [isTeacher] Filter by teacher status
 * @apiQuery {number} [limit] Limit the number of results
 * @apiQuery {number} [offset] Offset the results
 */
router.get('/users', async (req: Request, res: Response) => {
  try {
    const filters: any = {};

    // Convert query parameters to filters if provided
    if (req.query.username) filters.username = req.query.username;
    if (req.query.email) filters.email = req.query.email;
    if (req.query.isTeacher === 'true') filters.isTeacher = true;
    if (req.query.isTeacher === 'false') filters.isTeacher = false;

    // Parse numeric filters
    if (req.query.limit) filters.limit = parseInt(req.query.limit as string, 10);
    if (req.query.offset) filters.offset = parseInt(req.query.offset as string, 10);

    const users = await userService.getUsers(filters);

    return res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return res.status(500).json({
      message: "Failed to fetch users",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * @api {get} /api/users/:id Get user by ID
 * @apiDescription Get a user by ID
 * @apiParam {number} id User ID
 */
router.get('/users/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const user = await userService.getUserWithProfile(id);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    return res.json(user);
  } catch (error) {
    console.error('Error getting user:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/profile Get user profile
 * @apiDescription Get a user's profile
 * @apiParam {number} id User ID
 */
router.get('/users/:id/profile', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const profile = await userService.getUserProfile(id);

    if (!profile) {
      return res.status(404).json({ error: 'Profile not found' });
    }

    return res.json(profile);
  } catch (error) {
    console.error('Error getting user profile:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/users/:id/profile Update user profile
 * @apiDescription Update a user's profile
 * @apiParam {number} id User ID
 * @apiBody {string} [bio] User bio
 * @apiBody {string} [location] User location
 * @apiBody {string} [website] User website
 * @apiBody {string} [avatar] User avatar URL
 * @apiBody {string} [coverPhoto] User cover photo URL
 * @apiBody {string} [phone] User phone number
 * @apiBody {string} [timezone] User timezone
 * @apiBody {string} [facebookUrl] User Facebook URL
 * @apiBody {string} [twitterUrl] User Twitter URL
 * @apiBody {string} [instagramUrl] User Instagram URL
 * @apiBody {string} [linkedinUrl] User LinkedIn URL
 * @apiBody {string} [youtubeUrl] User YouTube URL
 * @apiBody {string} [tiktokUrl] User TikTok URL
 */
router.put('/users/:id/profile', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Check if the user exists
    const user = await userService.getUser(id);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Ensure the user has permission to update this profile
    if (req.user && req.user.id !== id) {
      return res.status(403).json({ error: 'You do not have permission to update this profile' });
    }

    // Log the request body for debugging
    console.log('[API] Profile update request body:', JSON.stringify(req.body, null, 2));

    // Update the profile
    const updatedProfile = await userService.updateUserProfile(id, req.body);

    if (!updatedProfile) {
      return res.status(500).json({ error: 'Failed to update profile' });
    }

    // Invalidate related caches
    invalidateCache([
      `/api/users/${id}`,
      `/api/users/${id}/profile`
    ]);

    return res.json(updatedProfile);
  } catch (error) {
    console.error('Error updating user profile:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/sessions Get user sessions
 * @apiDescription Get sessions created by a user
 * @apiParam {number} id User ID
 */
router.get('/users/:id/sessions', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const sessions = await userService.getUserSessions(id);

    return res.json(sessions);
  } catch (error) {
    console.error('Error getting user sessions:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/bookings Get user bookings
 * @apiDescription Get bookings made by a user
 * @apiParam {number} id User ID
 */
router.get('/users/:id/bookings', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    const bookings = await userService.getUserBookings(id);

    return res.json(bookings);
  } catch (error) {
    console.error('Error getting user bookings:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/conversations Get user conversations
 * @apiDescription Get conversations involving a user
 * @apiParam {number} id User ID
 */
router.get('/users/:id/conversations', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Ensure the user has permission to view these conversations
    if (req.user && req.user.id !== id) {
      return res.status(403).json({ error: 'You do not have permission to view these conversations' });
    }

    const conversations = await userService.getUserConversations(id);

    return res.json(conversations);
  } catch (error) {
    console.error('Error getting user conversations:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {get} /api/users/:id/saved-sessions Get user saved sessions
 * @apiDescription Get sessions saved by a user
 * @apiParam {number} id User ID
 */
router.get('/api/users/:id/saved-sessions', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // For now, return an empty array since we don't have saved sessions implemented yet
    // This prevents the 404 error
    return res.json([]);

    // TODO: Implement saved sessions functionality
    // const savedSessions = await userService.getUserSavedSessions(id);
    // return res.json(savedSessions);
  } catch (error) {
    console.error('Error getting user saved sessions:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {put} /api/users/:id Update user
 * @apiDescription Update a user's information
 * @apiParam {number} id User ID
 * @apiBody {string} [name] User name
 * @apiBody {string} [email] User email
 * @apiBody {boolean} [isTeacher] Whether the user is a teacher
 */
router.put('/users/:id', validateJWT, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    // Check if the user exists
    const user = await userService.getUser(id);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Ensure the user has permission to update this user
    if (req.user && req.user.id !== id) {
      return res.status(403).json({ error: 'You do not have permission to update this user' });
    }

    // Update the user
    const updatedUser = await userService.updateUser(id, req.body);

    if (!updatedUser) {
      return res.status(500).json({ error: 'Failed to update user' });
    }

    // Invalidate related caches
    invalidateCache([
      `/api/users/${id}`,
      `/api/users/${id}/profile`
    ]);

    return res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @api {post} /api/users Create user
 * @apiDescription Create a new user
 * @apiBody {string} username Username
 * @apiBody {string} email Email
 * @apiBody {string} password Password
 * @apiBody {string} name Full name
 */
router.post('/users', async (req: Request, res: Response) => {
  try {
    // Define validation schema
    const userSchema = z.object({
      username: z.string().min(3).max(50),
      email: z.string().email(),
      password: z.string().min(8),
      name: z.string().min(1)
    });

    // Validate request body
    const userData = userSchema.parse(req.body);

    // Check if username or email already exists
    const existingUser = await userService.getUserByUsernameOrEmail(userData.username, userData.email);

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        field: existingUser.username === userData.username ? 'username' : 'email'
      });
    }

    // Create the user
    const user = await userService.createUser(userData);

    return res.status(201).json(user);
  } catch (error) {
    console.error('Error creating user:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Invalid input data',
        details: error.errors
      });
    }

    return res.status(500).json({ error: 'Server error' });
  }
});

export default router;
