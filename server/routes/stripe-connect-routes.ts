import express from 'express';
import { validateJWT } from '../middleware/supabase-auth';
import stripeService from '../services/stripe-service';
import { storage } from '../storage';

const router = express.Router();

/**
 * Create a Stripe Connect account for an teacher
 * POST /api/stripe/connect/accounts
 */
router.post('/api/stripe/connect/accounts', validateJWT, async (req, res) => {
  try {
    // Check if the user is an teacher
    if (!req.user.isTeacher) {
      return res.status(403).json({ error: 'Only teachers can create Connect accounts' });
    }

    // Check if the user already has a Connect account
    if (req.user.stripe_connect_id) {
      return res.status(400).json({
        error: 'You already have a Connect account',
        accountId: req.user.stripe_connect_id,
        onboardingComplete: req.user.stripe_connect_onboarding_complete
      });
    }

    // Create a Connect account
    const account = await stripeService.createConnectAccount(req.user.id, req.user.email);

    // Update the user record with the Connect account ID
    await storage.updateUser(req.user.id, {
      stripe_connect_id: account.id,
      stripe_connect_onboarding_complete: false
    });

    res.status(201).json({
      accountId: account.id,
      onboardingComplete: false
    });
  } catch (error) {
    console.error('Error creating Connect account:', error);
    res.status(500).json({ error: 'Failed to create Connect account' });
  }
});

/**
 * Create an onboarding link for a Connect account
 * POST /api/stripe/connect/account-links
 */
router.post('/api/stripe/connect/account-links', validateJWT, async (req, res) => {
  try {
    // Check if the user is an teacher
    if (!req.user.isTeacher) {
      return res.status(403).json({ error: 'Only teachers can access Connect account links' });
    }

    // Check if the user has a Connect account
    if (!req.user.stripe_connect_id) {
      return res.status(400).json({ error: 'You do not have a Connect account yet' });
    }

    const { refreshUrl, returnUrl } = req.body;

    if (!refreshUrl || !returnUrl) {
      return res.status(400).json({ error: 'refreshUrl and returnUrl are required' });
    }

    // Create an account link
    const accountLink = await stripeService.createAccountLink(
      req.user.stripe_connect_id,
      refreshUrl,
      returnUrl
    );

    res.status(200).json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating account link:', error);
    res.status(500).json({ error: 'Failed to create account link' });
  }
});

/**
 * Get the status of a Connect account
 * GET /api/stripe/connect/accounts/status
 */
router.get('/api/stripe/connect/accounts/status', validateJWT, async (req, res) => {
  try {
    // Check if the user is an teacher
    if (!req.user.isTeacher) {
      return res.status(403).json({ error: 'Only teachers can access Connect account status' });
    }

    // Check if the user has a Connect account
    if (!req.user.stripe_connect_id) {
      return res.status(200).json({
        hasAccount: false,
        onboardingComplete: false,
        accountId: null
      });
    }

    // Get the Connect account
    const account = await stripeService.retrieveConnectAccount(req.user.stripe_connect_id);

    // Check if the account is fully onboarded
    const isOnboardingComplete =
      account.details_submitted &&
      account.payouts_enabled &&
      account.capabilities?.card_payments === 'active' &&
      account.capabilities?.transfers === 'active';

    // Update the user record if the onboarding status has changed
    if (isOnboardingComplete !== req.user.stripe_connect_onboarding_complete) {
      await storage.updateUser(req.user.id, {
        stripe_connect_onboarding_complete: isOnboardingComplete
      });
    }

    res.status(200).json({
      hasAccount: true,
      onboardingComplete: isOnboardingComplete,
      accountId: req.user.stripe_connect_id,
      accountDetails: {
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        detailsSubmitted: account.details_submitted,
        requirements: account.requirements
      }
    });
  } catch (error) {
    console.error('Error getting Connect account status:', error);
    res.status(500).json({ error: 'Failed to get Connect account status' });
  }
});

/**
 * Create a Stripe dashboard login link for a Connect account
 * POST /api/stripe/connect/dashboard-links
 */
router.post('/api/stripe/connect/dashboard-links', validateJWT, async (req, res) => {
  try {
    // Check if the user is an teacher
    if (!req.user.isTeacher) {
      return res.status(403).json({ error: 'Only teachers can access Connect dashboard' });
    }

    // Check if the user has a Connect account
    if (!req.user.stripe_connect_id) {
      return res.status(400).json({ error: 'You do not have a Connect account yet' });
    }

    // Create a login link
    const loginLink = await stripeService.createDashboardLoginLink(req.user.stripe_connect_id);

    res.status(200).json({ url: loginLink.url });
  } catch (error) {
    console.error('Error creating dashboard link:', error);
    res.status(500).json({ error: 'Failed to create dashboard link' });
  }
});

export default router;
