import { Express, Request, Response } from 'express';
import { storage } from '../storage';
import { validateJWT } from '../middleware/supabase-auth';
import { queryOptimizer, errorHandler } from '../services';
import { validate, validateId, asyncHandler } from '../middleware';
import { z } from 'zod';
import { getUserProfile, UserProfile } from '../services/supabase/profileService';

// Validation schemas
const topTeachersSchema = z.object({
  limit: z.string().optional().transform(val => parseInt(val || '5'))
});

const teacherIdSchema = z.object({
  id: z.string().transform(val => {
    // Check if this looks like a UUID (8-4-4-4-12 format)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(val)) {
      console.log(`[TeacherRoutes] Identified UUID format for teacher ID: ${val}`);
      return val; // Return as is if it's a UUID
    }

    // Check if it might be a partial UUID (like the "57" error case)
    if (val.length < 36) {
      console.error(`[TeacherRoutes] Invalid ID format: ${val} - should be a full UUID`);
      throw new Error(`Invalid ID format: ${val}`);
    }

    // If it's not a valid UUID or number, just return the string
    console.log(`[TeacherRoutes] Using teacher ID as string: ${val}`);
    return val;
  })
});

const teacherProfileSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").optional(),
  bio: z.string().optional(),
  avatar: z.string().optional(),
  coverPhoto: z.string().optional(),
  timezone: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  facebookUrl: z.string().optional(),
  twitterUrl: z.string().optional(),
  instagramUrl: z.string().optional(),
  linkedinUrl: z.string().optional(),
  youtubeUrl: z.string().optional(),
  tiktokUrl: z.string().optional(),
  specializations: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  education: z.string().optional(),
  experience: z.string().optional(),
  isTeacher: z.boolean().optional(),
  showTeachingSessions: z.boolean().optional(),
  showLearningSessions: z.boolean().optional(),
  showProfile: z.boolean().optional(),
  showSocialLinks: z.boolean().optional(),
  showContact: z.boolean().optional()
});

/**
 * Register teacher-related routes
 * @param app Express application
 */
export function registerTeacherRoutes(app: Express): void {
  console.log('[Routes] Registering teacher routes');

  // Get top teachers
  app.get('/api/teachers/top',
    validate(topTeachersSchema, 'query'),
    asyncHandler(async (req: Request, res: Response) => {
      const parsedQuery = topTeachersSchema.parse(req.query);
      const limit = parsedQuery.limit;

      // Use the optimized query for better performance
      const teachers = await queryOptimizer.getTopTeachers(limit);
      res.json(teachers);
    })
  );

  // Get all teachers
  app.get('/api/teachers',
    asyncHandler(async (_req: Request, res: Response) => {
      // Use the storage to get all teachers
      const teachers = await storage.getAllTeachers();
      res.json(teachers);
    })
  );

  // Get teacher by ID
  app.get('/api/teachers/:id',
    validate(teacherIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: teacherId } = req.params as unknown as z.infer<typeof teacherIdSchema>;

      try {
        // Get the user profile from Supabase (UUID compatible)
        const profile: UserProfile = await getUserProfile(teacherId);

        if (!profile) {
          throw errorHandler.notFound('Teacher not found');
        }

        // Check if the user is a teacher
        if (!profile.is_teacher) {
          throw errorHandler.notFound('Teacher not found');
        }

        // Format the response to match the expected UserWithProfile structure
        const teacher = {
          id: profile.user_id,
          name: profile.name,
          username: profile.username,
          avatar: profile.avatar,
          bio: profile.bio,
          timezone: profile.timezone,
          isTeacher: profile.is_teacher,
          profile: {
            id: profile.id || profile.user_id,
            user_id: profile.user_id,
            name: profile.name,
            avatar: profile.avatar,
            coverPhoto: profile.cover_photo,
            bio: profile.bio,
            location: profile.location,
            timezone: profile.timezone,
            website: profile.website,
            specializations: profile.specializations || [],
            skills: profile.skills || [],
            certifications: profile.certifications || [],
            experience: profile.experience,
            education: profile.education,
            rating: 0, // TODO: Calculate from reviews
            reviewCount: 0, // TODO: Calculate from reviews
            facebookUrl: profile.facebook_url,
            twitterUrl: profile.twitter_url,
            instagramUrl: profile.instagram_url,
            linkedinUrl: profile.linkedin_url,
            youtubeUrl: profile.youtube_url,
            tiktokUrl: profile.tiktok_url,
            isTeacher: profile.is_teacher,
            showTeachingSessions: true, // Default values
            showLearningSessions: true,
            showProfile: true,
            showSocialLinks: true,
            showContact: true
          }
        };

        res.json(teacher);
      } catch (error) {
        console.error(`Error fetching teacher profile for ID ${teacherId}:`, error);
        throw errorHandler.notFound('Teacher not found');
      }
    })
  );

  // Update teacher profile
  app.put('/api/teachers/profile',
    validateJWT,
    validate(teacherProfileSchema),
    asyncHandler(async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw errorHandler.unauthorized('Unauthorized');
      }

      const teacherData = req.body;

      // Update teacher profile
      const updatedTeacher = await storage.updateTeacherProfile(userId, teacherData);

      if (!updatedTeacher) {
        throw errorHandler.notFound('Teacher profile not found');
      }

      res.json(updatedTeacher);
    })
  );

  // Get teacher's sessions
  app.get('/api/teachers/:id/sessions',
    validate(teacherIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: teacherId } = req.params as unknown as z.infer<typeof teacherIdSchema>;

      try {
        // Parse query parameters for pagination
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 20;
        const offset = (page - 1) * limit;

        console.log(`[TeacherRoutes] Fetching sessions for teacher ${teacherId} - page: ${page}, limit: ${limit}, offset: ${offset}`);

        // Use the Supabase session service directly for teacher sessions
        const { getSessionsByTeacher } = await import('../services/supabase/sessionService');
        const sessions = await getSessionsByTeacher(teacherId, limit, offset);

        console.log(`[TeacherRoutes] Found ${sessions.length} sessions for teacher ${teacherId}`);

        // Return paginated response
        res.json({
          sessions,
          pagination: {
            page,
            limit,
            total: sessions.length,
            hasMore: sessions.length === limit
          }
        });
      } catch (error) {
        console.error(`Error fetching sessions for teacher ${teacherId}:`, error);
        res.status(500).json({ error: 'Failed to fetch teacher sessions' });
      }
    })
  );

  // Get teacher's session templates (NEW PROFESSIONAL ARCHITECTURE)
  app.get('/api/teachers/:id/templates',
    validate(teacherIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: teacherId } = req.params as unknown as z.infer<typeof teacherIdSchema>;

      try {
        // First, get the actual user profile ID from the user_id
        const { getUserProfile } = await import('../services/supabase/profileService');
        const profile = await getUserProfile(teacherId);

        if (!profile) {
          throw errorHandler.notFound('Teacher not found');
        }

        // Import the new SessionTemplateService
        const { SessionTemplateService } = await import('../services/SessionTemplateService');
        const { DatabaseService } = await import('../database/DatabaseService');

        const db = DatabaseService.getInstance();
        const sessionService = new SessionTemplateService(db);

        // Get templates for this teacher
        const templates = await sessionService.getTemplatesByTeacher(profile.id);

        console.log(`[TeacherRoutes] Found ${templates.length} session templates for teacher ${teacherId}`);

        res.json({
          templates,
          count: templates.length
        });
      } catch (error) {
        console.error(`Error fetching session templates for teacher ${teacherId}:`, error);
        res.status(500).json({ error: 'Failed to fetch teacher session templates' });
      }
    })
  );

  // Get specific template with all occurrences
  app.get('/api/teachers/:id/templates/:templateId',
    validate(teacherIdSchema, 'params'),
    asyncHandler(async (req: Request, res: Response) => {
      const { id: teacherId } = req.params as unknown as z.infer<typeof teacherIdSchema>;
      const { templateId } = req.params;

      try {
        // Import the new SessionTemplateService
        const { SessionTemplateService } = await import('../services/SessionTemplateService');
        const { DatabaseService } = await import('../database/DatabaseService');

        const db = DatabaseService.getInstance();
        const sessionService = new SessionTemplateService(db);

        // Get template with all occurrences
        const templateWithOccurrences = await sessionService.getTemplateWithOccurrences(templateId);

        if (!templateWithOccurrences) {
          throw errorHandler.notFound('Session template not found');
        }

        // Verify this template belongs to the teacher
        const { getUserProfile } = await import('../services/supabase/profileService');
        const profile = await getUserProfile(teacherId);

        if (!profile || templateWithOccurrences.teacher_id !== profile.id) {
          throw errorHandler.forbidden('Access denied to this template');
        }

        console.log(`[TeacherRoutes] Found template ${templateId} with ${templateWithOccurrences.occurrences.length} occurrences`);

        res.json(templateWithOccurrences);
      } catch (error) {
        console.error(`Error fetching template ${templateId} for teacher ${teacherId}:`, error);
        res.status(500).json({ error: 'Failed to fetch session template' });
      }
    })
  );
}
