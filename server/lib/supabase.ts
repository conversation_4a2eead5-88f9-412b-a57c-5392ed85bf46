/**
 * Backend Supabase Client
 * 
 * This file provides the Supabase client for backend use with the service role key.
 * It should NEVER be exposed to the frontend.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables. Backend operations will not work properly.');
  console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Not set');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set');
  
  // Log all environment variables for debugging
  console.log('Available environment variables:', Object.keys(process.env).join(', '));
}

// Create a Supabase client with the service role key
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Get a Supabase client with the service role key
 * This should ONLY be used for operations that require elevated privileges
 * @returns Supabase client with service role key
 */
export function getServiceClient() {
  return supabaseAdmin;
}

/**
 * Get a Supabase client with the anon key
 * This simulates the client-side experience for testing
 * @returns Supabase client with anon key
 */
export function getAnonClient() {
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';
  return createClient(supabaseUrl, supabaseAnonKey);
}

export default {
  supabaseAdmin,
  getServiceClient,
  getAnonClient
};
