/**
 * Authentication utilities for server routes
 * 
 * This file provides authentication middleware and utility functions
 */

import { Request, Response, NextFunction } from 'express';
import passport from 'passport';

// Basic authentication middleware
export function isAuthenticated(req: Request, res: Response, next: NextFunction) {
    if (req.isAuthenticated()) {
        return next();
    }
    res.status(401).json({ message: "Unauthorized - Authentication required" });
}

// Authentication middleware with redirect to login
export function isAuthenticatedWithRedirect(req: Request, res: Response, next: NextFunction) {
    if (req.isAuthenticated()) {
        return next();
    }
    // Redirect to login page with the return URL
    res.redirect(`/auth/login?returnTo=${encodeURIComponent(req.originalUrl)}`);
}

// Configure passport - empty implementation since we're using Supabase
export function configurePassport() {
    // No implementation needed - using Supabase auth
    console.log('Passport configuration skipped - using Supabase auth');
}

// Setup authentication routes - empty implementation since we're using Supabase
export function setupAuth(app: any) {
    // No implementation needed - using Supabase auth
    console.log('Auth setup skipped - using Supabase auth');
}

// Cache invalidation helper
export function invalidateUserCache(userId: string | number) {
    console.log(`Invalidating cache for user ${userId}`);
    // Implementation would go here
}