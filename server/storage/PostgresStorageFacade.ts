import session from 'express-session';
import connectPg from 'connect-pg-simple';
import createMemoryStore from 'memorystore';
import { Pool } from 'pg';
import { IStorage, DeviceToken, NotificationPreference } from '../storage';
import { RepositoryFactory } from '../repositories';
import {
  User, InsertUser, Profile, InsertProfile, UserProfile, InsertUserProfile,
  Session, InsertSession, Booking, InsertBooking, Review, InsertReview,
  Conversation, InsertConversation, Message, InsertMessage,
  ScheduledMessage, InsertScheduledMessage, SessionWithTeacher, BookingWithSession,
  MessageWithSender, ConversationWithMessages, UserWithProfile
} from '@shared/schema';
import { errorHandler } from '../services';
import { createAdminMethods } from './admin-methods';
// Import the database and cache modules
import { ConnectionManager, db } from '../database';
import cacheManager, { CacheManager } from '../cache';

/**
 * PostgreSQL-based implementation of the storage interface using repositories
 */
export class PostgresStorageFacade implements IStorage {
  sessionStore: session.Store;
  private pool: Pool;
  private repositoryFactory: RepositoryFactory;
  private adminMethods: ReturnType<typeof createAdminMethods>;

  /**
   * Constructor
   * @param pool - Database connection pool (optional, will use the default pool if not provided)
   */
  constructor(pool?: Pool) {
    // Use the provided pool or get the default pool from ConnectionManager
    this.pool = pool || db;

    // Initialize the repository factory
    this.repositoryFactory = new RepositoryFactory(this.pool);

    // Initialize admin methods
    this.adminMethods = createAdminMethods(this.pool);

    // Initialize the session store
    this.initializeSessionStore();

    // Log initialization
    console.log('[Storage] PostgresStorageFacade initialized');
  }

  /**
   * Initialize the session store
   */
  private initializeSessionStore(): void {
    try {
      // Use PostgreSQL session store
      this.createPgSessionStore();
      console.log('[SessionStore] Session store created with PostgreSQL');
    } catch (error) {
      console.error('[SessionStore] Unhandled error in session store initialization:', error);
      // Fall back to memory store as a last resort
      const MemoryStore = createMemoryStore(session);
      this.sessionStore = new MemoryStore({
        checkPeriod: 86400000, // 24 hours
        stale: false // Don't serve stale data
      });
      console.log('[SessionStore] Session store created with memory fallback due to unhandled error');
    }
  }

  /**
   * Create a PostgreSQL session store
   */
  private createPgSessionStore(): void {
    try {
      const PgSession = connectPg(session);
      this.sessionStore = new PgSession({
        pool: this.pool,
        tableName: 'session',
        createTableIfMissing: true
      });
      console.log('[SessionStore] PostgreSQL session store created successfully');
    } catch (error) {
      console.error('[SessionStore] Error creating PostgreSQL session store:', error);
      // Fall back to memory store
      const MemoryStore = createMemoryStore(session);
      this.sessionStore = new MemoryStore({
        checkPeriod: 86400000, // 24 hours
        stale: false // Don't serve stale data
      });
      console.log('[SessionStore] Session store created with memory fallback due to PostgreSQL error');
    }
  }

  /**
   * Initialize the database
   */
  async initializeDatabase(): Promise<void> {
    try {
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.repositoryFactory.getUserRepository().getUser(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.repositoryFactory.getUserRepository().getUserByUsername(username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return this.repositoryFactory.getUserRepository().getUserByEmail(email);
  }

  async getUserBySocialProvider(provider: string, providerId: string): Promise<User | undefined> {
    return this.repositoryFactory.getUserRepository().getUserBySocialProvider(provider, providerId);
  }

  async getUserWithProfile(id: number): Promise<UserWithProfile | undefined> {
    return this.repositoryFactory.getUserRepository().getUserWithProfile(id);
  }

  async getUserBySessionId(sessionId: string): Promise<User | null> {
    const user = await this.repositoryFactory.getUserRepository().getUserBySessionId(sessionId);
    return user || null;
  }

  async createUser(user: InsertUser): Promise<User> {
    return this.repositoryFactory.getUserRepository().createUser(user);
  }

  async createUserFromSocial(socialData: {
    name: string;
    email: string;
    provider: string;
    providerId: string;
    username?: string;
    profileUrl?: string;
    avatarUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<User> {
    return this.repositoryFactory.getUserRepository().createUserFromSocial(socialData);
  }

  async updateUser(id: number, user: Partial<User>): Promise<User | undefined> {
    return this.repositoryFactory.getUserRepository().updateUser(id, user);
  }

  async getAllTeachers(): Promise<User[]> {
    return this.repositoryFactory.getUserRepository().getAllTeachers();
  }

  async getAllTeachers(): Promise<UserWithProfile[]> {
    return this.repositoryFactory.getUserRepository().getAllTeachers();
  }

  async getTopTeachers(limit: number): Promise<User[]> {
    return this.repositoryFactory.getUserRepository().getTopTeachers(limit);
  }

  async searchUsersByUsername(partialUsername: string, limit?: number): Promise<User[]> {
    return this.repositoryFactory.getUserRepository().searchUsersByUsername(partialUsername, limit);
  }

  // Profile operations
  async getProfile(userId: number): Promise<Profile | undefined> {
    // This is a legacy method, use getUserProfile instead
    return this.getUserProfile(userId) as unknown as Profile;
  }

  async updateProfile(userId: number, profile: Partial<Profile>): Promise<Profile | undefined> {
    // This is a legacy method, use updateUserProfile instead
    return this.updateUserProfile(userId, profile as unknown as Partial<UserProfile>) as unknown as Profile;
  }

  // User profile operations
  async getUserProfile(userId: number): Promise<UserProfile | undefined> {
    return this.repositoryFactory.getUserRepository().getUserProfile(userId);
  }

  async updateUserProfile(userId: number, profile: Partial<UserProfile>): Promise<UserProfile | undefined> {
    return this.repositoryFactory.getUserRepository().updateUserProfile(userId, profile);
  }

  // This is a legacy method, use updateUserProfile instead
  async updateUserProfileData(userId: number, profile: Partial<UserProfile>): Promise<UserProfile | undefined> {
    return this.updateUserProfile(userId, profile);
  }

  // Session operations
  async getSession(id: number): Promise<Session | undefined> {
    return this.repositoryFactory.getSessionRepository().getSession(id);
  }

  async getSessionWithTeacher(id: number, skipCache?: boolean): Promise<SessionWithTeacher | undefined> {
    return this.repositoryFactory.getSessionRepository().getSessionWithTeacher(id, skipCache);
  }

  async createSession(session: InsertSession): Promise<Session> {
    return this.repositoryFactory.getSessionRepository().createSession(session);
  }

  async updateSession(id: number, session: Partial<Session>): Promise<Session | undefined> {
    return this.repositoryFactory.getSessionRepository().updateSession(id, session);
  }

  async deleteSession(id: number): Promise<boolean> {
    return this.repositoryFactory.getSessionRepository().deleteSession(id);
  }

  async getSessions(filters?: any, includePrivate?: boolean): Promise<SessionWithTeacher[]> {
    return this.repositoryFactory.getSessionRepository().getSessions(filters, includePrivate);
  }

  async getSessionsByTeacher(teacherId: number): Promise<SessionWithTeacher[]> {
    return this.repositoryFactory.getSessionRepository().getSessionsByTeacher(teacherId);
  }

  async countSessions(filters?: any): Promise<number> {
    return this.repositoryFactory.getSessionRepository().countSessions(filters);
  }

  async checkSessionSchedulingConflict(
    userId: number,
    date: Date,
    duration: number,
    sessionIdToExclude?: number
  ): Promise<{ hasConflict: boolean, conflictingSession?: Session }> {
    return this.repositoryFactory.getSessionRepository().checkSessionSchedulingConflict(
      userId, date, duration, sessionIdToExclude
    );
  }

  // Booking operations
  async getBooking(id: number): Promise<Booking | undefined> {
    return this.repositoryFactory.getBookingRepository().getBooking(id);
  }

  async getBookingWithSession(id: number): Promise<BookingWithSession | undefined> {
    return this.repositoryFactory.getBookingRepository().getBookingWithSession(id);
  }

  async createBooking(booking: InsertBooking): Promise<Booking> {
    return this.repositoryFactory.getBookingRepository().createBooking(booking);
  }

  async updateBooking(id: number, booking: Partial<Booking>): Promise<Booking | undefined> {
    return this.repositoryFactory.getBookingRepository().updateBooking(id, booking);
  }

  async getUserBookings(userId: number): Promise<BookingWithSession[]> {
    return this.repositoryFactory.getBookingRepository().getUserBookings(userId);
  }

  async getSessionBookings(sessionId: number): Promise<Booking[]> {
    return this.repositoryFactory.getBookingRepository().getSessionBookings(sessionId);
  }

  async getBookingsBySessionId(sessionId: number): Promise<any[]> {
    return this.repositoryFactory.getBookingRepository().getBookingsBySessionId(sessionId);
  }

  // Review operations
  async getReview(id: number): Promise<Review | undefined> {
    return this.repositoryFactory.getReviewRepository().getReview(id);
  }

  async createReview(review: InsertReview): Promise<Review> {
    return this.repositoryFactory.getReviewRepository().createReview(review);
  }

  async getSessionReviews(sessionId: number): Promise<Review[]> {
    return this.repositoryFactory.getReviewRepository().getSessionReviews(sessionId);
  }

  async getUserReviews(userId: number): Promise<Review[]> {
    return this.repositoryFactory.getReviewRepository().getUserReviews(userId);
  }

  async updateSessionRatings(sessionId: number): Promise<void> {
    return this.repositoryFactory.getReviewRepository().updateSessionRatings(sessionId);
  }

  async updateUserRatings(userId: number): Promise<void> {
    return this.repositoryFactory.getReviewRepository().updateUserRatings(userId);
  }

  // Chat operations
  async createConversation(conversation: InsertConversation): Promise<Conversation> {
    return this.repositoryFactory.getConversationRepository().createConversation(conversation);
  }

  async getConversation(id: number): Promise<Conversation | undefined> {
    return this.repositoryFactory.getConversationRepository().getConversation(id);
  }

  async getConversationWithMessages(id: number): Promise<ConversationWithMessages | undefined> {
    return this.repositoryFactory.getConversationRepository().getConversationWithMessages(id);
  }

  async getUserConversations(userId: number): Promise<ConversationWithMessages[]> {
    return this.repositoryFactory.getConversationRepository().getUserConversations(userId);
  }

  async createMessage(message: InsertMessage): Promise<Message> {
    return this.repositoryFactory.getMessageRepository().createMessage(message);
  }

  async getMessage(id: number): Promise<Message | undefined> {
    return this.repositoryFactory.getMessageRepository().getMessage(id);
  }

  async getMessageWithSender(id: number): Promise<MessageWithSender | undefined> {
    return this.repositoryFactory.getMessageRepository().getMessageWithSender(id);
  }

  async getConversationMessages(conversationId: number): Promise<MessageWithSender[]> {
    return this.repositoryFactory.getMessageRepository().getConversationMessages(conversationId);
  }

  async markMessagesAsRead(conversationId: number, userId: number): Promise<void> {
    return this.repositoryFactory.getMessageRepository().markMessagesAsRead(conversationId, userId);
  }

  async getUnreadMessageCount(userId: number): Promise<number> {
    return this.repositoryFactory.getMessageRepository().getUnreadMessageCount(userId);
  }

  // Device token operations
  async registerDeviceToken(userId: number, token: string, platform: string): Promise<DeviceToken> {
    return this.repositoryFactory.getNotificationRepository().registerDeviceToken(userId, token, platform);
  }

  async getUserDevices(userId: number): Promise<DeviceToken[]> {
    return this.repositoryFactory.getNotificationRepository().getUserDevices(userId);
  }

  async removeDeviceToken(token: string): Promise<boolean> {
    return this.repositoryFactory.getNotificationRepository().removeDeviceToken(token);
  }

  // Notification preferences operations
  async getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined> {
    return this.repositoryFactory.getNotificationRepository().getNotificationPreferences(userId);
  }

  async updateNotificationPreferences(userId: number, preferences: Partial<NotificationPreference>): Promise<NotificationPreference> {
    return this.repositoryFactory.getNotificationRepository().updateNotificationPreferences(userId, preferences);
  }

  // Password reset methods
  async storePasswordResetToken(userId: number, token: string, expiry: Date): Promise<boolean> {
    return this.repositoryFactory.getAuthRepository().storePasswordResetToken(userId, token, expiry);
  }

  async getPasswordResetToken(token: string): Promise<{ userId: number, expiry: Date } | null> {
    const result = await this.repositoryFactory.getAuthRepository().getPasswordResetToken(token);
    return result || null;
  }

  async deletePasswordResetToken(token: string): Promise<void> {
    return this.repositoryFactory.getAuthRepository().deletePasswordResetToken(token);
  }

  // Email verification methods
  async storeEmailVerificationToken(userId: number, token: string, expiry: Date): Promise<boolean> {
    return this.repositoryFactory.getAuthRepository().storeEmailVerificationToken(userId, token, expiry);
  }

  async getEmailVerificationToken(token: string): Promise<{ userId: number, expiry: Date } | null> {
    const result = await this.repositoryFactory.getAuthRepository().getEmailVerificationToken(token);
    return result || null;
  }

  async deleteEmailVerificationToken(token: string): Promise<void> {
    return this.repositoryFactory.getAuthRepository().deleteEmailVerificationToken(token);
  }

  async verifyUserEmail(userId: number): Promise<boolean> {
    return this.repositoryFactory.getUserRepository().verifyUserEmail(userId);
  }

  // Conversation management
  async deleteConversation(conversationId: number, userId: number): Promise<boolean> {
    return this.repositoryFactory.getConversationRepository().deleteConversation(conversationId, userId);
  }

  // Scheduled message operations
  async createScheduledMessage(message: InsertScheduledMessage): Promise<ScheduledMessage> {
    return this.repositoryFactory.getMessageRepository().createScheduledMessage(message);
  }

  async getScheduledMessage(id: number): Promise<ScheduledMessage | undefined> {
    return this.repositoryFactory.getMessageRepository().getScheduledMessage(id);
  }

  async getScheduledMessagesByConversation(conversationId: number): Promise<ScheduledMessage[]> {
    return this.repositoryFactory.getMessageRepository().getScheduledMessagesByConversation(conversationId);
  }

  async getScheduledMessagesByBooking(bookingId: number): Promise<ScheduledMessage[]> {
    return this.repositoryFactory.getMessageRepository().getScheduledMessagesByBooking(bookingId);
  }

  async getPendingScheduledMessages(limit?: number): Promise<ScheduledMessage[]> {
    return this.repositoryFactory.getMessageRepository().getPendingScheduledMessages(limit);
  }

  async updateScheduledMessageStatus(id: number, status: string, sentAt?: Date): Promise<boolean> {
    return this.repositoryFactory.getMessageRepository().updateScheduledMessageStatus(id, status, sentAt);
  }

  async deleteScheduledMessage(id: number): Promise<boolean> {
    return this.repositoryFactory.getMessageRepository().deleteScheduledMessage(id);
  }

  /**
   * Get the repository factory
   * @returns Repository factory
   */
  getRepositories(): RepositoryFactory {
    return this.repositoryFactory;
  }

  // Admin methods
  async getAllUsers(): Promise<any[]> {
    return this.adminMethods.getAllUsers();
  }

  async getUserCount(): Promise<number> {
    return this.adminMethods.getUserCount();
  }

  async getSessionCount(): Promise<number> {
    return this.adminMethods.getSessionCount();
  }

  async getBookingCount(): Promise<number> {
    return this.adminMethods.getBookingCount();
  }

  async getReviewCount(): Promise<number> {
    return this.adminMethods.getReviewCount();
  }
}

export default PostgresStorageFacade;
