import { Pool } from 'pg';
import { errorHandler } from '../services/error-handler';

/**
 * Admin-related storage methods
 * @param pool Database connection pool
 * @returns Admin storage methods
 */
export function createAdminMethods(pool: Pool) {
  /**
   * Get all users
   * @returns Array of users
   */
  async function getAllUsers() {
    try {
      const query = `
        SELECT
          u.id,
          u.username,
          u.email,
          u.email_verified,
          u.role,
          u.created_at,
          u.last_login_at,
          p.name,
          p.avatar,
          p.is_teacher,
          p.is_teacher
        FROM
          auth.users u
        LEFT JOIN
          profiles.user_profiles p ON u.id = p.user_id
        ORDER BY
          u.id DESC
      `;

      const result = await pool.query(query);
      return result.rows.map(row => ({
        id: row.id,
        username: row.username,
        email: row.email,
        name: row.name || row.username,
        emailVerified: row.email_verified,
        role: row.role || 'user',
        isTeacher: row.is_teacher || false,
        isTeacher: row.is_teacher || false,
        avatar: row.avatar,
        createdAt: row.created_at,
        lastLoginAt: row.last_login_at
      }));
    } catch (error) {
      throw errorHandler.database('Error getting all users', error);
    }
  }

  /**
   * Get user count
   * @returns Number of users
   */
  async function getUserCount() {
    try {
      const query = 'SELECT COUNT(*) as count FROM auth.users';
      const result = await pool.query(query);
      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      throw errorHandler.database('Error getting user count', error);
    }
  }

  /**
   * Get session count
   * @returns Number of sessions
   */
  async function getSessionCount() {
    try {
      const query = 'SELECT COUNT(*) as count FROM content.sessions';
      const result = await pool.query(query);
      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      throw errorHandler.database('Error getting session count', error);
      return 0;
    }
  }

  /**
   * Get booking count
   * @returns Number of bookings
   */
  async function getBookingCount() {
    try {
      const query = 'SELECT COUNT(*) as count FROM bookings.bookings';
      const result = await pool.query(query);
      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      console.error('Error getting booking count:', error);
      return 0;
    }
  }

  /**
   * Get review count
   * @returns Number of reviews
   */
  async function getReviewCount() {
    try {
      const query = 'SELECT COUNT(*) as count FROM content.reviews';
      const result = await pool.query(query);
      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      console.error('Error getting review count:', error);
      return 0;
    }
  }

  return {
    getAllUsers,
    getUserCount,
    getSessionCount,
    getBookingCount,
    getReviewCount
  };
}
