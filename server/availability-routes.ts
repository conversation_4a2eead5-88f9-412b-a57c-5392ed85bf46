import { Router } from 'express';
import { storage } from './storage';
import AvailabilityManager from './availability-manager';

const createAvailabilityRoutes = () => {
  const router = Router();
  const availabilityManager = new AvailabilityManager(storage);

  // Middleware to check if user is authenticated
  const isAuthenticated = (req: any, res: any, next: any) => {
    if (req.isAuthenticated && req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ error: 'Unauthorized' });
  };

  // Get global availability for an teacher
  router.get('/global-availability/:teacherId', async (req, res) => {
    try {
      const teacherId = parseInt(req.params.teacherId);

      if (isNaN(teacherId)) {
        return res.status(400).json({ error: 'Invalid teacher ID' });
      }

      const availability = await availabilityManager.getGlobalAvailability(teacherId);
      res.json(availability);
    } catch (error) {
      console.error('Error fetching global availability:', error);
      res.status(500).json({ error: 'Failed to fetch global availability' });
    }
  });

  // Create global availability
  router.post('/global-availability', isAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;

      if (!user || !user.isTeacher) {
        return res.status(403).json({ error: 'Only teachers can set global availability' });
      }

      const { dayOfWeek, startTime, endTime } = req.body;

      if (!dayOfWeek || !startTime || !endTime) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const newAvailability = await availabilityManager.setGlobalAvailability(
        user.id,
        dayOfWeek,
        startTime,
        endTime
      );

      res.status(201).json(newAvailability);
    } catch (error) {
      console.error('Error creating global availability:', error);
      res.status(500).json({ error: 'Failed to create global availability' });
    }
  });

  // Delete global availability
  router.delete('/global-availability/:id', isAuthenticated, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req.user as any)?.id;

      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      if (!userId) {
        return res.status(401).json({ error: 'User ID is required' });
      }

      const result = await availabilityManager.deleteGlobalAvailability(id, userId);

      if (result) {
        res.json({ success: true });
      } else {
        res.status(404).json({ error: 'Availability not found or you do not have permission to delete it' });
      }
    } catch (error) {
      console.error('Error deleting global availability:', error);
      res.status(500).json({ error: 'Failed to delete global availability' });
    }
  });

  // Apply global availability to a session
  router.post('/global-availability/apply/:sessionId', isAuthenticated, async (req, res) => {
    try {
      const user = req.user as any;
      const sessionId = parseInt(req.params.sessionId);

      if (isNaN(sessionId)) {
        return res.status(400).json({ error: 'Invalid session ID' });
      }

      // Check if user owns this session
      const session = await storage.getSession(sessionId);

      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      // Check if the user is the teacher of this session
      // The session object's structure might vary, adjust the property name as needed
      if (session.teacherId !== user.id) {
        return res.status(403).json({ error: 'You can only apply availability to your own sessions' });
      }

      const availabilities = await availabilityManager.applyGlobalAvailabilityToSession(
        user.id,
        sessionId
      );

      res.json({ success: true, availabilities });
    } catch (error) {
      console.error('Error applying global availability to session:', error);
      res.status(500).json({ error: 'Failed to apply global availability to session' });
    }
  });

  return router;
};

export default createAvailabilityRoutes;