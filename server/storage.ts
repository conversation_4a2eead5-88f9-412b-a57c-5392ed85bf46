import {
  User, InsertUser,
  Profile, InsertProfile,
  UserProfile, InsertUserProfile,
  Session, InsertSession,
  Booking, InsertBooking,
  Review, InsertReview,
  Conversation, InsertConversation,
  Message, InsertMessage,
  ScheduledMessage, InsertScheduledMessage,
  SessionWithTeacher, BookingWithSession,
  MessageWithSender, ConversationWithMessages,
  UserWithProfile
} from '@shared/schema';
import session from 'express-session';
import { PostgresStorageFacade } from './storage/PostgresStorageFacade';

// Global type declaration
declare global {
  var passwordResetTokens: Map<string, { userId: number, expiry: Date }>;
  var emailVerificationTokens: Map<string, { userId: number, expiry: Date }>;
}

// Device token interface
export interface DeviceToken {
  id: number;
  user_id: number;
  token: string;
  platform: 'ios' | 'android' | 'web';
  created_at: Date;
  last_active: Date;
}

// Notification preferences interface
export interface NotificationPreference {
  user_id: number;
  new_message: boolean;
  new_booking: boolean;
  booking_reminder: boolean;
  booking_changes: boolean;
  created_at: Date;
  updated_at: Date;
}

// Define storage interface
export interface IStorage {
  // Session store for authentication
  sessionStore: session.Store;

  // Database initialization
  initializeDatabase(): Promise<void>;

  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserBySocialProvider(provider: string, providerId: string): Promise<User | undefined>;
  getUserWithProfile(id: number): Promise<UserWithProfile | undefined>;
  getUserBySessionId(sessionId: string): Promise<User | null>;
  createUser(user: InsertUser): Promise<User>;
  createUserFromSocial(socialData: {
    name: string;
    email: string;
    provider: string;
    providerId: string;
    username?: string;
    profileUrl?: string;
    avatarUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<User>;
  updateUser(id: number, user: Partial<User>): Promise<User | undefined>;
  getAllTeachers(): Promise<User[]>;
  getAllTeachers(): Promise<UserWithProfile[]>;
  getTopTeachers(limit: number): Promise<User[]>;
  searchUsersByUsername(partialUsername: string, limit?: number): Promise<User[]>;

  // Profile operations
  getProfile(userId: number): Promise<Profile | undefined>;
  updateProfile(userId: number, profile: Partial<Profile>): Promise<Profile | undefined>;

  // User profile operations
  getUserProfile(userId: number): Promise<UserProfile | undefined>;
  updateUserProfile(userId: number, profile: Partial<UserProfile>): Promise<UserProfile | undefined>;
  updateUserProfileData(userId: number, profile: Partial<UserProfile>): Promise<UserProfile | undefined>;

  // Teacher profile operations
  getTeacherProfile(userId: number): Promise<Profile | undefined>;
  updateTeacherProfile(userId: number, profile: Partial<Profile>): Promise<Profile | undefined>;

  // Social account operations
  linkSocialAccount(userId: number, socialAccount: {
    provider: string;
    providerId: string;
    username?: string;
    email?: string;
    profileUrl?: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: Date;
  }): Promise<boolean>;
  getSocialAccounts(userId: number): Promise<Array<{
    provider: string;
    providerId: string;
    username: string;
    profileUrl: string;
  }>>;
  deleteSocialAccount(userId: number, provider: string, providerId: string): Promise<boolean>;

  // Session operations
  getSession(id: number): Promise<Session | undefined>;
  getSessionWithTeacher(id: number, skipCache?: boolean): Promise<SessionWithTeacher | undefined>;
  createSession(session: InsertSession): Promise<Session>;
  updateSession(id: number, session: Partial<Session>): Promise<Session | undefined>;
  deleteSession(id: number): Promise<boolean>;
  getSessions(filters?: any, includePrivate?: boolean): Promise<SessionWithTeacher[]>;
  getSessionsByTeacher(teacherId: number | string): Promise<SessionWithTeacher[]>;
  countSessions(filters?: any): Promise<number>;
  checkSessionSchedulingConflict(
    userId: number,
    date: Date,
    duration: number,
    sessionIdToExclude?: number
  ): Promise<{ hasConflict: boolean, conflictingSession?: Session }>;

  // Booking operations
  getBooking(id: number): Promise<Booking | undefined>;
  getBookingWithSession(id: number): Promise<BookingWithSession | undefined>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBooking(id: number, booking: Partial<Booking>): Promise<Booking | undefined>;
  getUserBookings(userId: number): Promise<BookingWithSession[]>;
  getSessionBookings(sessionId: number): Promise<Booking[]>;
  getBookingsBySessionId(sessionId: number): Promise<Booking[]>;

  // Review operations
  getReview(id: number): Promise<Review | undefined>;
  createReview(review: InsertReview): Promise<Review>;
  getSessionReviews(sessionId: number): Promise<Review[]>;
  getUserReviews(userId: number): Promise<Review[]>;
  updateSessionRatings(sessionId: number): Promise<void>;
  updateUserRatings(userId: number): Promise<void>;

  // Chat operations
  createConversation(conversation: InsertConversation): Promise<Conversation>;
  getConversation(id: number): Promise<Conversation | undefined>;
  getConversationWithMessages(id: number): Promise<ConversationWithMessages | undefined>;
  getUserConversations(userId: number): Promise<ConversationWithMessages[]>;
  createMessage(message: InsertMessage): Promise<Message>;
  getMessage(id: number): Promise<Message | undefined>;
  getMessageWithSender(id: number): Promise<MessageWithSender | undefined>;
  getConversationMessages(conversationId: number): Promise<MessageWithSender[]>;
  markMessagesAsRead(conversationId: number, userId: number): Promise<void>;
  getUnreadMessageCount(userId: number): Promise<number>;

  // Device token operations
  registerDeviceToken(userId: number, token: string, platform: string): Promise<DeviceToken>;
  getUserDevices(userId: number): Promise<DeviceToken[]>;
  removeDeviceToken(token: string): Promise<boolean>;

  // Notification preferences operations
  getNotificationPreferences(userId: number): Promise<NotificationPreference | undefined>;
  updateNotificationPreferences(userId: number, preferences: Partial<NotificationPreference>): Promise<NotificationPreference>;

  // Password reset methods
  storePasswordResetToken(userId: number, token: string, expiry: Date): Promise<boolean>;
  getPasswordResetToken(token: string): Promise<{ userId: number, expiry: Date } | null>;
  deletePasswordResetToken(token: string): Promise<void>;

  // Email verification methods
  storeEmailVerificationToken(userId: number, token: string, expiry: Date): Promise<boolean>;
  getEmailVerificationToken(token: string): Promise<{ userId: number, expiry: Date } | null>;
  deleteEmailVerificationToken(token: string): Promise<void>;
  verifyUserEmail(userId: number): Promise<boolean>;

  // Conversation management
  deleteConversation(conversationId: number, userId: number): Promise<boolean>;

  // Scheduled message operations
  createScheduledMessage(message: InsertScheduledMessage): Promise<ScheduledMessage>;
  getScheduledMessage(id: number): Promise<ScheduledMessage | undefined>;
  getScheduledMessagesByConversation(conversationId: number): Promise<ScheduledMessage[]>;
  getScheduledMessagesByBooking(bookingId: number): Promise<ScheduledMessage[]>;
  getPendingScheduledMessages(limit?: number): Promise<ScheduledMessage[]>;
  updateScheduledMessageStatus(id: number, status: string, sentAt?: Date): Promise<boolean>;
  deleteScheduledMessage(id: number): Promise<boolean>;

  // Admin operations
  getAllUsers(): Promise<any[]>;
  getUserCount(): Promise<number>;
  getSessionCount(): Promise<number>;
  getBookingCount(): Promise<number>;
  getReviewCount(): Promise<number>;

  // Repository access
  getRepositories(): any;
}

// Storage facade that can be used across the application
// It creates the appropriate storage implementation based on configuration
console.log('Initializing database connection...');
// Create storage based on configuration
import { db } from './db';
export const storage: IStorage = new PostgresStorageFacade(db);