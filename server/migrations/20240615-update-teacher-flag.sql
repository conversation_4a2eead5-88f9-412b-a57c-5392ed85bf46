-- Migration to update is_teacher flag for existing teachers
-- This ensures backward compatibility with the new terminology

-- First, add the is_teacher column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'is_teacher'
    ) THEN
        ALTER TABLE users ADD COLUMN is_teacher BOOLEAN NOT NULL DEFAULT FALSE;
    END IF;
END $$;

-- Set is_teacher to true for all existing teachers
UPDATE users SET is_teacher = TRUE WHERE is_teacher = TRUE;

-- Create the user_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bio TEXT,
    timezone TEXT,
    phone TEXT,
    location TEXT,
    website TEXT,
    facebook_url TEXT,
    twitter_url TEXT,
    instagram_url TEXT,
    linkedin_url TEXT,
    cover_photo TEXT,
    specializations TEXT[],
    skills TEXT[],
    certifications TEXT[],
    education TEXT,
    experience TEXT,
    availability TEXT,
    rating DOUBLE PRECISION DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    show_teaching_sessions BOOLEAN DEFAULT TRUE,
    show_learning_sessions BOOLEAN DEFAULT FALSE,
    show_profile BOOLEAN DEFAULT TRUE,
    show_social_links BOOLEAN DEFAULT TRUE,
    show_contact BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index on user_id to ensure one profile per user
CREATE UNIQUE INDEX IF NOT EXISTS user_profiles_user_id_idx ON user_profiles(user_id);

-- Migrate data from teacher_profiles to user_profiles
INSERT INTO user_profiles (
    user_id, specializations, skills, certifications, education, experience,
    website, facebook_url, twitter_url, instagram_url, linkedin_url,
    rating, review_count, created_at, updated_at
)
SELECT
    user_id, specializations, skills, certifications, education, experience,
    website, facebook_url, twitter_url, instagram_url, linkedin_url,
    rating, review_count, created_at, updated_at
FROM teacher_profiles
ON CONFLICT (user_id) DO NOTHING;

-- Migrate data from profiles to user_profiles
INSERT INTO user_profiles (
    user_id, bio, timezone, phone, location, website, facebook_url, twitter_url,
    instagram_url, linkedin_url, cover_photo, specializations, skills, certifications,
    education, experience, availability, show_teaching_sessions, show_learning_sessions,
    show_profile, show_social_links, show_contact
)
SELECT
    user_id, bio, timezone, phone, location, website, facebook_url, twitter_url,
    instagram_url, linkedin_url, cover_photo, specializations, skills, certifications,
    education, experience, availability, show_teaching_sessions, show_learning_sessions,
    show_profile, show_social_links, show_contact
FROM profiles
ON CONFLICT (user_id) DO UPDATE SET
    bio = COALESCE(EXCLUDED.bio, user_profiles.bio),
    timezone = COALESCE(EXCLUDED.timezone, user_profiles.timezone),
    phone = COALESCE(EXCLUDED.phone, user_profiles.phone),
    location = COALESCE(EXCLUDED.location, user_profiles.location),
    cover_photo = COALESCE(EXCLUDED.cover_photo, user_profiles.cover_photo),
    availability = COALESCE(EXCLUDED.availability, user_profiles.availability),
    show_teaching_sessions = COALESCE(EXCLUDED.show_teaching_sessions, user_profiles.show_teaching_sessions),
    show_learning_sessions = COALESCE(EXCLUDED.show_learning_sessions, user_profiles.show_learning_sessions),
    show_profile = COALESCE(EXCLUDED.show_profile, user_profiles.show_profile),
    show_social_links = COALESCE(EXCLUDED.show_social_links, user_profiles.show_social_links),
    show_contact = COALESCE(EXCLUDED.show_contact, user_profiles.show_contact),
    updated_at = NOW();
