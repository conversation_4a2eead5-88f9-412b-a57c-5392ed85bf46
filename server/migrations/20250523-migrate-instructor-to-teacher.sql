-- Migration to update teacher terminology to teacher
-- This migration updates column names and API routes to use teacher instead of teacher

-- First, ensure we have the teacher_id column in sessions table
-- (This should already exist from previous migrations, but we'll make sure)
DO $$
BEGIN
    -- Add teacher_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'content' 
        AND table_name = 'sessions' 
        AND column_name = 'teacher_id'
    ) THEN
        ALTER TABLE content.sessions ADD COLUMN teacher_id INTEGER REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Copy data from teacher_id to teacher_id if teacher_id is null
UPDATE content.sessions 
SET teacher_id = teacher_id 
WHERE teacher_id IS NULL AND teacher_id IS NOT NULL;

-- Make teacher_id NOT NULL after copying data
ALTER TABLE content.sessions ALTER COLUMN teacher_id SET NOT NULL;

-- Update RLS policies to use teacher_id instead of teacher_id
DROP POLICY IF EXISTS "Teachers can insert their own sessions" ON content.sessions;
DROP POLICY IF EXISTS "Teachers can update their own sessions" ON content.sessions;
DROP POLICY IF EXISTS "Teachers can delete their own sessions" ON content.sessions;
DROP POLICY IF EXISTS "Teachers can view bookings for their sessions" ON bookings.bookings;

-- Create new policies with teacher terminology
CREATE POLICY "Teachers can insert their own sessions"
ON content.sessions FOR INSERT
WITH CHECK (teacher_id = auth.uid());

CREATE POLICY "Teachers can update their own sessions"
ON content.sessions FOR UPDATE
USING (teacher_id = auth.uid());

CREATE POLICY "Teachers can delete their own sessions"
ON content.sessions FOR DELETE
USING (teacher_id = auth.uid());

CREATE POLICY "Teachers can view bookings for their sessions"
ON bookings.bookings FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM content.sessions s 
        WHERE s.id = session_id AND s.teacher_id = auth.uid()
    )
);

-- Update indexes to use teacher_id
DROP INDEX IF EXISTS content.idx_sessions_teacher_id;
CREATE INDEX IF NOT EXISTS idx_sessions_teacher_id ON content.sessions(teacher_id);

-- Add comment to document the migration
COMMENT ON COLUMN content.sessions.teacher_id IS 'Primary teacher ID field (replaces teacher_id)';
COMMENT ON COLUMN content.sessions.teacher_id IS 'Legacy teacher ID field (kept for backward compatibility)';

-- Update any views that might reference teacher_id
-- (Add view updates here if needed)

-- Log the migration
INSERT INTO public.migration_log (migration_name, applied_at, description) 
VALUES (
    '20250523-migrate-teacher-to-teacher', 
    NOW(), 
    'Updated teacher terminology to teacher, added teacher_id column, updated RLS policies'
) ON CONFLICT (migration_name) DO NOTHING; 