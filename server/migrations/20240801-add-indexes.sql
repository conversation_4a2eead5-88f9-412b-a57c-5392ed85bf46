-- Migration to add indexes for improved query performance
-- This will significantly speed up common queries

-- Sessions indexes
-- Index on teacher_id for faster teacher session lookups
CREATE INDEX IF NOT EXISTS idx_sessions_teacher_id ON content.sessions(teacher_id);

-- Index on date for faster date-based queries
CREATE INDEX IF NOT EXISTS idx_sessions_date ON content.sessions(date);

-- Index on type for faster filtering by session type
CREATE INDEX IF NOT EXISTS idx_sessions_type ON content.sessions(type);

-- Index on skill_level for faster filtering by skill level
CREATE INDEX IF NOT EXISTS idx_sessions_skill_level ON content.sessions(skill_level);

-- Composite index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_sessions_type_skill_level ON content.sessions(type, skill_level);

-- Bookings indexes
-- Index on user_id for faster user booking lookups
CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON bookings.bookings(user_id);

-- Index on session_id for faster session booking lookups
CREATE INDEX IF NOT EXISTS idx_bookings_session_id ON bookings.bookings(session_id);

-- Index on status for faster filtering by booking status
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings.bookings(status);

-- Composite index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_bookings_user_session ON bookings.bookings(user_id, session_id);

-- Composite index for status-based queries
CREATE INDEX IF NOT EXISTS idx_bookings_user_status ON bookings.bookings(user_id, status);

-- Reviews indexes
-- Index on session_id for faster session review lookups
CREATE INDEX IF NOT EXISTS idx_reviews_session_id ON content.reviews(session_id);

-- Index on user_id for faster user review lookups
CREATE INDEX IF NOT EXISTS idx_reviews_user_id ON content.reviews(user_id);

-- Composite index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_reviews_session_user ON content.reviews(session_id, user_id);

-- Messages indexes
-- Index on conversation_id for faster conversation message lookups
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messaging.messages(conversation_id);

-- Index on sender_id for faster sender message lookups
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messaging.messages(sender_id);

-- Index on created_at for faster time-based queries
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messaging.messages(created_at);

-- Index on read status for faster unread message queries
CREATE INDEX IF NOT EXISTS idx_messages_read ON messaging.messages(read);

-- Composite index for common filtering patterns
CREATE INDEX IF NOT EXISTS idx_messages_conversation_created ON messaging.messages(conversation_id, created_at);

-- Conversations indexes
-- Index on participant_ids for faster participant conversation lookups
CREATE INDEX IF NOT EXISTS idx_conversations_participant_ids ON messaging.conversations USING GIN (participant_ids);

-- Index on last_message_at for faster sorting by recent conversations
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON messaging.conversations(last_message_at);

-- User profiles indexes
-- Index on user_id for faster user profile lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON profiles.user_profiles(user_id);

-- Index on is_teacher for faster teacher lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_is_teacher ON profiles.user_profiles(is_teacher);

-- Index on timezone for faster timezone-based queries
CREATE INDEX IF NOT EXISTS idx_user_profiles_timezone ON profiles.user_profiles(timezone);

-- Index on name for faster name-based searches
CREATE INDEX IF NOT EXISTS idx_user_profiles_name ON profiles.user_profiles(name);

-- Composite index for teacher name searches
CREATE INDEX IF NOT EXISTS idx_user_profiles_teacher_name ON profiles.user_profiles(is_teacher, name) WHERE is_teacher = true;

-- Scheduled messages indexes
-- Index on scheduled_time for faster time-based queries
CREATE INDEX IF NOT EXISTS idx_scheduled_messages_scheduled_time ON messaging.scheduled_messages(scheduled_time);

-- Index on status for faster filtering by message status
CREATE INDEX IF NOT EXISTS idx_scheduled_messages_status ON messaging.scheduled_messages(status);

-- Index on booking_id for faster booking message lookups
CREATE INDEX IF NOT EXISTS idx_scheduled_messages_booking_id ON messaging.scheduled_messages(booking_id);

-- Index on session_id for faster session message lookups
CREATE INDEX IF NOT EXISTS idx_scheduled_messages_session_id ON messaging.scheduled_messages(session_id);

-- Record the migration in the schema_migrations table
INSERT INTO schema_migrations (version)
VALUES ('20240801-add-indexes')
ON CONFLICT (version) DO NOTHING;
