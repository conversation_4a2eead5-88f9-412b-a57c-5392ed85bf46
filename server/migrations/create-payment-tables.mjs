import pg from 'pg';
const { Pool } = pg;

// Create a connection to the database
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL',
  ssl: { rejectUnauthorized: false }
});

async function createPaymentTables() {
  try {
    console.log('Creating payment-related tables...');
    
    // Create instructor_payment_methods table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS instructor_payment_methods (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        payment_type VARCHAR(50) NOT NULL,
        is_default BOOLEAN DEFAULT true,
        details JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    console.log('Created instructor_payment_methods table');
    
    // Create payouts table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS payouts (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        status VARCHAR(50) NOT NULL,
        payment_method_id INTEGER REFERENCES instructor_payment_methods(id),
        external_reference VARCHAR(255),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE,
        notes TEXT
      );
    `);
    console.log('Created payouts table');
    
    // Create platform_fees table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS platform_fees (
        id SERIAL PRIMARY KEY,
        booking_id INTEGER NOT NULL REFERENCES bookings(id),
        amount DECIMAL(10,2) NOT NULL,
        percentage DECIMAL(5,2) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    console.log('Created platform_fees table');
    
    // Add payment_status column to bookings table if it doesn't exist
    const bookingsResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'bookings' AND column_name = 'payment_status';
    `);
    
    if (bookingsResult.rows.length === 0) {
      await pool.query(`
        ALTER TABLE bookings 
        ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50),
        ADD COLUMN IF NOT EXISTS payment_id VARCHAR(255),
        ADD COLUMN IF NOT EXISTS payment_amount DECIMAL(10,2),
        ADD COLUMN IF NOT EXISTS payment_processor VARCHAR(50);
      `);
      console.log('Added payment columns to bookings table');
    } else {
      console.log('Payment columns already exist in bookings table');
    }
    
    console.log('All payment tables created successfully');
  } catch (error) {
    console.error('Error creating payment tables:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the migration
createPaymentTables()
  .then(() => {
    console.log('Migration completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
