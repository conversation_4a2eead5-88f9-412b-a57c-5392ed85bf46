import { Pool, PoolClient, QueryResult, PoolConfig } from 'pg';
import { promises as dns } from 'dns';

console.log('[DB] Initializing database connection...');
console.log(`[DB] Environment: ${process.env.NODE_ENV || 'development'}`);

// Debug: Log all environment variables for troubleshooting
console.log('[DB] Environment variables:', {
  NODE_ENV: process.env.NODE_ENV,
  HAS_DB_URL: !!process.env.DATABASE_URL,
  HAS_SUPABASE_URL: !!process.env.SUPABASE_URL,
});

// Parse and validate connection string
let connectionString = process.env.DATABASE_URL?.trim().replace(/^['"]|['"]$/g, '') || '';

if (!connectionString) {
  console.error('[DB] ERROR: DATABASE_URL environment variable is not set');
  console.error('[DB] Current working directory:', process.cwd());
  console.error('[DB] Environment keys:', Object.keys(process.env).join(', '));
  console.error('[DB] Make sure your .env file is in the server/ directory and contains DATABASE_URL');
  process.exit(1);
}

// Fix hostname if it contains the db. prefix that doesn't resolve
if (connectionString.includes('db.akcknfwrkbzazrytpkng.supabase.co')) {
  connectionString = connectionString.replace('db.akcknfwrkbzazrytpkng.supabase.co', 'db.frksndjujrbjhlrcjvtf.supabase.co');
  console.log('[DB] Updated connection string to new Supabase project');
}

// Try alternate ports if standard port is used
if (connectionString.includes('akcknfwrkbzazrytpkng.supabase.co:5432')) {
  console.log('[DB] Using new Supabase project instead of old one');
  connectionString = connectionString.replace('akcknfwrkbzazrytpkng.supabase.co:5432', 'db.frksndjujrbjhlrcjvtf.supabase.co:5432');
}

console.log('[DB] Using connection string:',
  connectionString.includes('@')
    ? connectionString.replace(/:([^:]+)@/, ':[REDACTED]@')
    : '[connection string available]'
);

// Parse connection details for logging
try {
  const dbName = connectionString.split('/').pop()?.split('?')[0] || 'unknown';
  const hostMatch = connectionString.match(/@([^:@/]+)/);
  const userMatch = connectionString.match(/\/\/([^:@]+):/);
  const portMatch = connectionString.match(/:(\d+)\//);

  if (hostMatch && userMatch) {
    const host = hostMatch[1];
    const user = userMatch[1];
    const port = portMatch ? portMatch[1] : 'default';
    console.log(`[DB] Connecting to: ${host} (port: ${port}) as ${user} (database: ${dbName})`);

    // Test hostname resolution
    console.log(`[DB] Testing DNS resolution for: ${host}`);
    dns.lookup(host, (err: NodeJS.ErrnoException | null, address: string, family: number) => {
      if (err) {
        console.error(`[DB] DNS resolution failed: ${err.message}`);
      } else {
        console.log(`[DB] DNS resolution successful: ${host} -> ${address} (IPv${family})`);
      }
    });
  } else {
    console.log('[DB] Connecting to database with provided connection string');
  }
} catch (error) {
  console.error('[DB] Error parsing connection string:', error);
  // Continue anyway as the connection string might still be valid
}

// Database configuration
const dbConfig: PoolConfig = {
  connectionString,
  max: 10,      // Reduced to prevent too many connections
  min: 1,       // Minimum connections in the pool
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 15000,  // Increased timeout
  allowExitOnIdle: true,
  // For development, we'll disable SSL certificate validation
  // In production, you should use proper SSL certificates
  ssl: {
    rejectUnauthorized: false
  },
  application_name: `sessionhub-${process.env.NODE_ENV || 'development'}`,
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000,
};

console.log('[DB] Database configuration:', {
  ...dbConfig,
  connectionString: connectionString ? '***' : 'Not set',
  ssl: 'Configured',
  application_name: dbConfig.application_name,
});

// Create a new database pool
const pool = new Pool(dbConfig);

// Track if the pool has been ended
let isPoolEnded = false;

// Add error handler for the pool
pool.on('error', (err: Error) => {
  console.error('[DB] Unexpected error on idle client:', err);
  // Don't exit in production to allow for auto-recovery
  if (process.env.NODE_ENV !== 'production') {
    console.error('[DB] Pool error detected, attempting to recover...');
    setTimeout(reconnectPool, 5000); // Try to reconnect after 5 seconds
  }
});

// Interface for query log entries
interface QueryLogEntry {
  query: string;
  params: unknown[];
  startTime: number;
  endTime?: number;
  duration?: number;
  error?: string;
}

// Track query performance
const queryLog: QueryLogEntry[] = [];

// Type for before query handlers
type BeforeQueryHandler = (query: string, params: unknown[]) => void;
const beforeQueryHandlers: BeforeQueryHandler[] = [];

// Function to test the database connection
const testConnection = async (): Promise<boolean> => {
  let client: PoolClient | null = null;
  try {
    client = await pool.connect();
    console.log('[DB] Successfully connected to database');

    // Test query
    const res = await client.query('SELECT NOW() as current_time');
    console.log('[DB] Database time:', res.rows[0].current_time);

    // Check PostgreSQL version
    const versionRes = await client.query('SELECT version()');
    console.log('[DB] PostgreSQL version:', versionRes.rows[0].version.split(' ').slice(0, 2).join(' '));

    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Database connection test failed:', errorMessage);
    return false;
  } finally {
    if (client) {
      client.release();
    }
  }
};

// Database client wrapper with error handling and logging
const db = {
  // Execute a query with parameters
  async query<T extends object = any>(text: string, params: unknown[] = []): Promise<QueryResult<T>> {
    const start = Date.now();

    // Call all before query handlers
    for (const handler of beforeQueryHandlers) {
      try {
        handler(text, params);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Error in beforeQuery handler:', errorMessage);
      }
    }

    try {
      const res = await pool.query(text, params);
      const duration = Date.now() - start;

      // Only log queries that take longer than 100ms
      if (duration > 100) {
        // Store slow queries in memory for diagnostics
        if (queryLog.length >= 100) {
          queryLog.shift(); // Remove oldest entry
        }

        const logEntry: QueryLogEntry = {
          query: text,
          params,
          startTime: start,
          endTime: Date.now(),
          duration,
        };

        queryLog.push(logEntry);

        console.log('Slow query:', {
          query: text.slice(0, 100) + (text.length > 100 ? '...' : ''),
          duration: `${duration}ms`,
          rows: res.rowCount,
        });
      }

      return res;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Database query failed:', {
        query: text.slice(0, 100),
        error: errorMessage
      });

      // Check if this is a connection error that needs reconnection
      if (errorMessage && (
        errorMessage.includes('pool is draining') ||
        errorMessage.includes('Cannot use a pool after calling end') ||
        errorMessage.includes('Connection terminated'))) {
        await reconnectPool();
        throw new Error(`Database connection error: ${errorMessage}. Pool reconnected, please retry your request.`);
      }

      throw error;
    }
  },

  // Get a client from the pool
  async connect(): Promise<PoolClient> {
    try {
      const client = await pool.connect();
      return client;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to connect to database:', errorMessage);
      throw error;
    }
  },

  // End the pool
  async end(): Promise<void> {
    if (isPoolEnded) {
      console.log('Database pool already ended, skipping...');
      return;
    }

    try {
      isPoolEnded = true;
      await pool.end();
      console.log('Database pool ended successfully');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error ending database connection:', errorMessage);
      isPoolEnded = false;
      throw error;
    }
  },

  // Add a before query hook
  onBeforeQuery(handler: BeforeQueryHandler): number {
    beforeQueryHandlers.push(handler);
    return beforeQueryHandlers.length - 1; // Return handler ID for removal
  },

  // Remove a before query hook
  offBeforeQuery(index: number): void {
    if (index >= 0 && index < beforeQueryHandlers.length) {
      beforeQueryHandlers.splice(index, 1);
    }
  },

  // Get query statistics
  getQueryStats() {
    return {
      totalQueries: queryLog.length,
      averageDuration: queryLog.length > 0
        ? queryLog.reduce((sum, q) => sum + (q.duration || 0), 0) / queryLog.length
        : 0,
      lastQuery: queryLog[queryLog.length - 1],
      recentQueries: queryLog.slice(-10).reverse(),
    };
  },

  // Get current connection status
  getStatus() {
    return {
      isPoolEnded,
      totalIdle: pool.idleCount,
      totalClients: pool.totalCount,
      waitingCount: pool.waitingCount,
    };
  },

  // Test the database connection
  testConnection,
};

// Reconnect to the database pool
async function reconnectPool(): Promise<boolean> {
  if (isPoolEnded) {
    try {
      console.log('[DB] Attempting to reconnect to the database...');
      // Recreate the pool
      const newPool = new Pool(dbConfig);

      // Test the connection
      const client = await newPool.connect();
      await client.query('SELECT 1');
      client.release();

      // Replace the old pool
      await pool.end();
      Object.assign(pool, newPool);
      isPoolEnded = false;

      console.log('[DB] Successfully reconnected to the database');
      return true;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[DB] Failed to reconnect to database:', errorMessage);
      // Schedule another reconnection attempt
      setTimeout(reconnectPool, 10000); // Try again in 10 seconds
      return false;
    }
  }
  return true;
}

// Initialize the database connection
async function initDatabase(): Promise<boolean> {
  try {
    console.log('[DB] Initializing database connection...');

    // Test the connection
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('[DB] Failed to connect to database');
      setTimeout(reconnectPool, 5000); // Try to reconnect after 5 seconds
      return false;
    }

    // Check and repair schema if needed
    await checkAndRepairSchema();

    console.log('[DB] Database initialization complete');
    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Database initialization failed:', errorMessage);
    return false;
  }
}

// Check and repair database schema
async function checkAndRepairSchema(): Promise<void> {
  try {
    console.log('[DB] Checking database schema...');

    // Check if users table exists
    const { rows: tables } = await db.query<{ table_name: string }>(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'users';
    `);

    if (tables.length === 0) {
      console.log('[DB] Users table not found, creating...');
      await db.query(`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          email VARCHAR(255) UNIQUE NOT NULL,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
      `);

      // Create indexes
      await db.query('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
      console.log('[DB] Users table and indexes created');
    }

    // Check if search path is set correctly
    const { rows: searchPath } = await db.query<{ search_path: string }>('SHOW search_path');
    if (!searchPath[0].search_path.includes('public')) {
      console.log('[DB] Updating search_path to include public schema');
      await db.query('SET search_path TO public, "$user", public');
    }

    console.log('[DB] Database schema check complete');
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Error checking/repairing schema:', errorMessage);
    throw error;
  }
}

// Start the database connection
export async function start() {
  try {
    console.log('[DB] Starting database...');

    // Initialize the database connection
    const isInitialized = await initDatabase();
    if (!isInitialized) {
      console.warn('[DB] Database initialization failed, will continue with limited functionality');
    }

    // Set up periodic health check
    setInterval(async () => {
      try {
        await db.query('SELECT 1');
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('[DB] Health check failed:', errorMessage);

        // Try to reconnect if health check fails
        if (!isPoolEnded) {
          await reconnectPool();
        }
      }
    }, 30000); // Check every 30 seconds

    console.log('[DB] Database started successfully');
    return db;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('[DB] Failed to start database:', errorMessage);
    console.warn('[DB] Continuing with limited functionality');
    return db; // Return db anyway to allow app to function with limited capabilities
  }
}

// Note: Graceful shutdown is handled by the main server process
// to avoid duplicate database pool closing attempts

export { db };
export default db;
