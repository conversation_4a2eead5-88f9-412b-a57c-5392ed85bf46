import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/database';
import User from './User';
import Session from './Session';

interface ReviewAttributes {
  id: string;
  userId: string;
  teacherId: string;
  sessionId?: string;
  rating: number;
  content: string;
  type: 'text' | 'audio' | 'video';
  mediaUrl?: string;
  helpful: number;
  createdAt: Date;
  updatedAt: Date;
}

interface ReviewCreationAttributes extends Optional<ReviewAttributes, 'id' | 'helpful' | 'createdAt' | 'updatedAt'> {}

class Review extends Model<ReviewAttributes, ReviewCreationAttributes> implements ReviewAttributes {
  public id!: string;
  public userId!: string;
  public teacherId!: string;
  public sessionId?: string;
  public rating!: number;
  public content!: string;
  public type!: 'text' | 'audio' | 'video';
  public mediaUrl?: string;
  public helpful!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Review.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    teacherId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    sessionId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'sessions',
        key: 'id',
      },
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5,
      },
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    type: {
      type: DataTypes.ENUM('text', 'audio', 'video'),
      allowNull: false,
      defaultValue: 'text',
    },
    mediaUrl: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    helpful: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Review',
    tableName: 'reviews',
    timestamps: true,
  }
);

// Define associations
Review.belongsTo(User, { foreignKey: 'userId', as: 'user' });
Review.belongsTo(User, { foreignKey: 'teacherId', as: 'teacher' });
Review.belongsTo(Session, { foreignKey: 'sessionId', as: 'session' });

export default Review;
