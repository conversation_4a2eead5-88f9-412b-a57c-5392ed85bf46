/**
 * <PERSON><PERSON><PERSON> to check the database for redundant or legacy tables
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function checkDatabase() {
  try {
    console.log('Checking database for redundant or legacy tables...');

    // Get all tables
    const allTables = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);

    console.log('\nAll tables in database:');
    allTables.rows.forEach(row => console.log(row.table_name));

    // Check for backup or old tables
    const backupTables = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND (table_name LIKE '%backup%' OR table_name LIKE '%old%')
      ORDER BY table_name
    `);

    console.log('\nBackup or old tables:');
    if (backupTables.rows.length === 0) {
      console.log('No backup or old tables found');
    } else {
      backupTables.rows.forEach(row => console.log(row.table_name));
    }

    // Check for empty tables
    console.log('\nChecking for empty tables:');
    for (const row of allTables.rows) {
      const tableName = row.table_name;
      const countResult = await pool.query(`SELECT COUNT(*) FROM "${tableName}"`);
      const count = parseInt(countResult.rows[0].count);

      if (count === 0) {
        console.log(`${tableName}: EMPTY (0 rows)`);
      } else {
        console.log(`${tableName}: ${count} rows`);
      }
    }

    // Check for potentially redundant tables
    console.log('\nPotentially redundant tables:');

    // Check profiles_backup vs user_profiles
    if (backupTables.rows.some(row => row.table_name === 'profiles_backup')) {
      const profilesBackupColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles_backup'
        ORDER BY ordinal_position
      `);

      const userProfilesColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'user_profiles'
        ORDER BY ordinal_position
      `);

      console.log('\nComparing profiles_backup and user_profiles:');
      console.log('profiles_backup columns:');
      profilesBackupColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));

      console.log('user_profiles columns:');
      userProfilesColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
    }

    // Check teacher_profiles_backup vs user_profiles
    if (backupTables.rows.some(row => row.table_name === 'teacher_profiles_backup')) {
      const teacherProfilesBackupColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'teacher_profiles_backup'
        ORDER BY ordinal_position
      `);

      console.log('\nComparing teacher_profiles_backup and user_profiles:');
      console.log('teacher_profiles_backup columns:');
      teacherProfilesBackupColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
    }

    // Check scheduled_messages_old vs scheduled_messages
    if (backupTables.rows.some(row => row.table_name === 'scheduled_messages_old')) {
      const scheduledMessagesOldColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'scheduled_messages_old'
        ORDER BY ordinal_position
      `);

      const scheduledMessagesColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'scheduled_messages'
        ORDER BY ordinal_position
      `);

      console.log('\nComparing scheduled_messages_old and scheduled_messages:');
      console.log('scheduled_messages_old columns:');
      scheduledMessagesOldColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));

      console.log('scheduled_messages columns:');
      scheduledMessagesColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
    }

    // Check sessions_complete vs sessions
    const sessionsCompleteExists = allTables.rows.some(row => row.table_name === 'sessions_complete');
    const sessionsExists = allTables.rows.some(row => row.table_name === 'sessions');

    if (sessionsCompleteExists && sessionsExists) {
      const sessionsCompleteColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'sessions_complete'
        ORDER BY ordinal_position
      `);

      const sessionsColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'sessions'
        ORDER BY ordinal_position
      `);

      console.log('\nComparing sessions_complete and sessions:');
      console.log('sessions_complete columns:');
      sessionsCompleteColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));

      console.log('sessions columns:');
      sessionsColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
    }

    // Check session vs sessions
    const sessionExists = allTables.rows.some(row => row.table_name === 'session');

    if (sessionExists && sessionsExists) {
      const sessionColumns = await pool.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'session'
        ORDER BY ordinal_position
      `);

      console.log('\nComparing session and sessions:');
      console.log('session columns:');
      sessionColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
    }

    console.log('\nDatabase check completed.');
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    await pool.end();
  }
}

// Run the script
checkDatabase()
  .then(() => {
    console.log('Database check completed successfully');
  })
  .catch(err => {
    console.error('Failed to check database:', err);
  });
