import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a new pool with the DATABASE_URL from environment
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkUserProfiles() {
  console.log('Connecting to database...');
  console.log('Using connection string: From environment');

  try {
    // Test the connection
    await pool.query('SELECT NOW()');
    console.log('Connection successful!');

    // Get all users
    const usersResult = await pool.query(`
      SELECT id, username, email FROM auth.users LIMIT 10
    `);

    console.log(`Found ${usersResult.rows.length} users in auth.users table`);

    for (const user of usersResult.rows) {
      console.log(`\nChecking user: ${user.username} (${user.email}), ID: ${user.id}`);

      // Check if user has a profile
      const profileResult = await pool.query(`
        SELECT * FROM profiles.user_profiles WHERE user_id = $1
      `, [user.id]);

      if (profileResult.rows.length > 0) {
        const profile = profileResult.rows[0];
        console.log(`  Profile found: ID ${profile.id}`);
        console.log(`  Avatar: ${profile.avatar || 'None'}`);
        console.log(`  Cover Photo: ${profile.cover_photo || 'None'}`);
      } else {
        console.log(`  No profile found for user ${user.id}`);
      }
    }

    // Check for any users with avatar in the profiles table
    const avatarResult = await pool.query(`
      SELECT user_id, avatar FROM profiles.user_profiles 
      WHERE avatar IS NOT NULL AND avatar != ''
      LIMIT 5
    `);

    console.log(`\nFound ${avatarResult.rows.length} users with avatars in profiles.user_profiles table`);
    for (const row of avatarResult.rows) {
      console.log(`  User ID: ${row.user_id}, Avatar: ${row.avatar}`);
    }

  } catch (error) {
    console.error('Error checking user profiles:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('\nDatabase connection closed');
  }
}

checkUserProfiles();
