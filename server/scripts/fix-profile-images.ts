import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function fixProfileImages() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Check auth.users table structure
    const userColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'auth' AND table_name = 'users'
      ORDER BY ordinal_position;
    `);

    console.log(`\nColumns in auth.users table:`, userColumns.rows.map(r => r.column_name));

    // Check profiles.user_profiles table structure
    const profileColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
      ORDER BY ordinal_position;
    `);

    console.log(`\nColumns in profiles.user_profiles table:`, profileColumns.rows.map(r => r.column_name));

    // Get all users with their profiles
    const usersWithProfiles = await pool.query(`
      SELECT u.id, u.username, u.email, p.avatar, p.cover_photo
      FROM auth.users u
      LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
      ORDER BY u.id;
    `);

    console.log(`\nFound ${usersWithProfiles.rows.length} users in the database`);

    // Check for users without profiles
    const usersWithoutProfiles = usersWithProfiles.rows.filter(user => !user.avatar && !user.cover_photo);
    console.log(`\nUsers without profile images: ${usersWithoutProfiles.length}`);

    // Check for users with profile images
    const usersWithImages = usersWithProfiles.rows.filter(user => user.avatar || user.cover_photo);
    console.log(`\nUsers with profile images: ${usersWithImages.length}`);

    // Print sample users with images
    console.log(`\nSample users with images:`);
    usersWithImages.slice(0, 5).forEach(user => {
      console.log(`  User ${user.id} (${user.username}):`);
      console.log(`    Avatar: ${user.avatar || 'NULL'}`);
      console.log(`    Cover photo: ${user.cover_photo || 'NULL'}`);
    });

    // Check if there are any users in backup.users_backup with avatar/cover_photo
    try {
      const backupUsers = await pool.query(`
        SELECT id, username, avatar, cover_photo
        FROM backup.users_backup
        WHERE avatar IS NOT NULL OR cover_photo IS NOT NULL
        LIMIT 5;
      `);

      if (backupUsers.rows.length > 0) {
        console.log(`\nFound users in backup.users_backup with images:`);
        backupUsers.rows.forEach(user => {
          console.log(`  User ${user.id} (${user.username}):`);
          console.log(`    Avatar: ${user.avatar || 'NULL'}`);
          console.log(`    Cover photo: ${user.cover_photo || 'NULL'}`);
        });
      } else {
        console.log(`\nNo users found in backup.users_backup with images`);
      }
    } catch (error) {
      console.error(`Error checking backup.users_backup: ${error.message}`);
    }

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error fixing profile images:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

fixProfileImages();
