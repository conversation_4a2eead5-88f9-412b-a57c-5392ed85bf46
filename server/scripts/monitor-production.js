#!/usr/bin/env node
/**
 * Production monitoring script
 * Performs health checks and sends alerts for critical issues
 */

const https = require('https');
const http = require('http');
const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const config = {
    healthCheckUrl: process.env.HEALTH_CHECK_URL || 'http://localhost:4004/api/health',
    logFile: path.join(__dirname, '../logs/monitor.log'),
    checkInterval: parseInt(process.env.CHECK_INTERVAL || '60000', 10), // Default: 1 minute
    alertThreshold: 3, // Number of consecutive failures before alerting
    healthyThreshold: 2, // Number of consecutive successes to consider recovered
    alertMethod: process.env.ALERT_METHOD || 'log', // log, email, slack, or webhook
    alertEndpoint: process.env.ALERT_ENDPOINT || '',
    alertCredentials: process.env.ALERT_CREDENTIALS || '',
};

// State tracking
let consecutiveFailures = 0;
let consecutiveSuccesses = 0;
let alertSent = false;
let lastError = null;

// Logging function
async function log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;

    console.log(logMessage.trim());

    try {
        await fs.appendFile(config.logFile, logMessage);
    } catch (err) {
        console.error(`Failed to write to log file: ${err.message}`);
    }
}

// Alert function
async function sendAlert(message) {
    await log(`ALERT: ${message}`, 'critical');

    if (alertSent) {
        return; // Prevent alert spam
    }

    try {
        alertSent = true;

        switch (config.alertMethod) {
            case 'email':
                // Implement email alerts here
                break;
            case 'slack':
                await sendSlackAlert(message);
                break;
            case 'webhook':
                await sendWebhookAlert(message);
                break;
            case 'log':
            default:
                // Just log to console and file (already done)
                break;
        }
    } catch (err) {
        await log(`Failed to send alert: ${err.message}`, 'error');
    }
}

// Slack alert implementation
async function sendSlackAlert(message) {
    if (!config.alertEndpoint) {
        await log('Slack webhook URL not configured', 'error');
        return;
    }

    const payload = JSON.stringify({
        text: `🚨 *SESSION HUB ALERT*: ${message}`,
    });

    return new Promise((resolve, reject) => {
        const req = https.request(config.alertEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': payload.length,
            }
        }, (res) => {
            if (res.statusCode === 200) {
                resolve();
            } else {
                reject(new Error(`Slack returned status: ${res.statusCode}`));
            }
        });

        req.on('error', reject);
        req.write(payload);
        req.end();
    });
}

// Webhook alert implementation
async function sendWebhookAlert(message) {
    if (!config.alertEndpoint) {
        await log('Webhook URL not configured', 'error');
        return;
    }

    const payload = JSON.stringify({
        event: 'server_health_alert',
        severity: 'critical',
        message: message,
        timestamp: new Date().toISOString(),
        details: lastError ? String(lastError) : 'Unknown error'
    });

    const url = new URL(config.alertEndpoint);
    const options = {
        hostname: url.hostname,
        port: url.port || (url.protocol === 'https:' ? 443 : 80),
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': payload.length
        }
    };

    if (config.alertCredentials) {
        options.headers['Authorization'] = `Bearer ${config.alertCredentials}`;
    }

    const requestModule = url.protocol === 'https:' ? https : http;

    return new Promise((resolve, reject) => {
        const req = requestModule.request(options, (res) => {
            if (res.statusCode >= 200 && res.statusCode < 300) {
                resolve();
            } else {
                reject(new Error(`Webhook returned status: ${res.statusCode}`));
            }
        });

        req.on('error', reject);
        req.write(payload);
        req.end();
    });
}

// Server health recovery notification
async function notifyRecovery() {
    await log('Server health recovered!', 'info');

    if (alertSent) {
        alertSent = false;

        if (config.alertMethod !== 'log') {
            try {
                // Send recovery notification
                switch (config.alertMethod) {
                    case 'slack':
                        await sendSlackAlert('✅ Server health recovered');
                        break;
                    case 'webhook':
                        await sendWebhookAlert('Server health recovered');
                        break;
                    case 'email':
                        // Implement email recovery notification
                        break;
                }
            } catch (err) {
                await log(`Failed to send recovery notification: ${err.message}`, 'error');
            }
        }
    }
}

// Check server memory usage
async function checkMemoryUsage() {
    return new Promise((resolve, reject) => {
        exec('pm2 jlist', (err, stdout) => {
            if (err) {
                reject(err);
                return;
            }

            try {
                const processes = JSON.parse(stdout);
                const sessionhub = processes.find(p => p.name === 'sessionhub');

                if (sessionhub) {
                    const memoryUsage = Math.round(sessionhub.monit.memory / (1024 * 1024));
                    resolve(memoryUsage);
                } else {
                    reject(new Error('Sessionhub process not found'));
                }
            } catch (parseErr) {
                reject(parseErr);
            }
        });
    });
}

// Perform health check
async function performHealthCheck() {
    try {
        // Basic health check
        const response = await new Promise((resolve, reject) => {
            const url = new URL(config.healthCheckUrl);
            const requestModule = url.protocol === 'https:' ? https : http;

            const req = requestModule.get(url, res => {
                let data = '';
                res.on('data', chunk => { data += chunk; });
                res.on('end', () => {
                    if (res.statusCode === 200) {
                        try {
                            const responseData = JSON.parse(data);
                            resolve(responseData);
                        } catch (err) {
                            reject(new Error(`Invalid JSON response: ${data}`));
                        }
                    } else {
                        reject(new Error(`Health check failed with status: ${res.statusCode}`));
                    }
                });
            });

            req.on('error', reject);
            req.setTimeout(10000, () => reject(new Error('Health check timed out')));
            req.end();
        });

        // Check memory usage
        try {
            const memoryUsage = await checkMemoryUsage();
            await log(`Memory usage: ${memoryUsage} MB`);

            if (memoryUsage > 450) { // 90% of 500MB restart limit
                await log(`High memory usage: ${memoryUsage} MB`, 'warning');
            }
        } catch (memErr) {
            await log(`Failed to check memory usage: ${memErr.message}`, 'warning');
        }

        // Reset failure counter and increment success counter
        consecutiveFailures = 0;
        consecutiveSuccesses++;

        if (alertSent && consecutiveSuccesses >= config.healthyThreshold) {
            await notifyRecovery();
        }

        await log(`Health check passed: ${JSON.stringify(response)}`);
    } catch (err) {
        lastError = err;
        consecutiveFailures++;
        consecutiveSuccesses = 0;

        await log(`Health check failed: ${err.message}`, 'error');

        if (consecutiveFailures >= config.alertThreshold) {
            await sendAlert(`Server health check failed ${consecutiveFailures} times: ${err.message}`);

            // If server is completely down, try to restart it
            if (consecutiveFailures >= config.alertThreshold * 2) {
                await log('Attempting to restart server...', 'warning');
                exec('pm2 restart sessionhub', (execErr) => {
                    if (execErr) {
                        log(`Failed to restart server: ${execErr.message}`, 'error');
                    } else {
                        log('Server restart initiated', 'info');
                    }
                });
            }
        }
    }
}

// Create log directory if it doesn't exist
async function init() {
    try {
        const logDir = path.dirname(config.logFile);
        await fs.mkdir(logDir, { recursive: true });
        await log('Monitor started');

        // Perform initial health check
        await performHealthCheck();

        // Set up interval for regular health checks
        setInterval(performHealthCheck, config.checkInterval);
    } catch (err) {
        console.error(`Initialization error: ${err.message}`);
        process.exit(1);
    }
}

// Start the monitor
init().catch(err => {
    console.error(`Failed to start monitor: ${err.message}`);
    process.exit(1);
}); 