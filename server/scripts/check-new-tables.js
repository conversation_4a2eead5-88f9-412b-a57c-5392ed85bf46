/**
 * <PERSON><PERSON><PERSON> to check the new user tables structure
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function checkNewTables() {
  try {
    console.log('Checking new user tables structure...');
    
    // Check users table
    const usersColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nUsers table columns (Authentication):');
    usersColumns.rows.forEach(col => console.log(`${col.column_name} (${col.data_type})`));
    
    // Check user_profiles table
    const profilesColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_profiles' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nUser_profiles table columns (Profile Data):');
    profilesColumns.rows.forEach(col => console.log(`${col.column_name} (${col.data_type})`));
    
    // Check user_payment_info table
    const paymentColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_payment_info' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nUser_payment_info table columns (Payment Data):');
    paymentColumns.rows.forEach(col => console.log(`${col.column_name} (${col.data_type})`));
    
    // Check row counts
    const userCount = await pool.query('SELECT COUNT(*) FROM users');
    const profileCount = await pool.query('SELECT COUNT(*) FROM user_profiles');
    const paymentCount = await pool.query('SELECT COUNT(*) FROM user_payment_info');
    
    console.log(`\nUsers table has ${userCount.rows[0].count} rows`);
    console.log(`User_profiles table has ${profileCount.rows[0].count} rows`);
    console.log(`User_payment_info table has ${paymentCount.rows[0].count} rows`);
    
    // Check for users without profiles
    const usersWithoutProfiles = await pool.query(`
      SELECT u.id, u.username 
      FROM users u 
      LEFT JOIN user_profiles up ON u.id = up.user_id 
      WHERE up.id IS NULL
    `);
    
    console.log(`\nUsers without profiles: ${usersWithoutProfiles.rows.length}`);
    if (usersWithoutProfiles.rows.length > 0) {
      console.log('Sample users without profiles:');
      usersWithoutProfiles.rows.slice(0, 5).forEach(user => 
        console.log(`ID: ${user.id}, Username: ${user.username}`)
      );
    }
    
    // Check for users without payment info
    const usersWithoutPayment = await pool.query(`
      SELECT u.id, u.username 
      FROM users u 
      LEFT JOIN user_payment_info upi ON u.id = upi.user_id 
      WHERE upi.id IS NULL
    `);
    
    console.log(`\nUsers without payment info: ${usersWithoutPayment.rows.length}`);
    if (usersWithoutPayment.rows.length > 0) {
      console.log('Sample users without payment info:');
      usersWithoutPayment.rows.slice(0, 5).forEach(user => 
        console.log(`ID: ${user.id}, Username: ${user.username}`)
      );
    }
    
    // Check for payment records with Stripe data
    const paymentWithStripe = await pool.query(`
      SELECT user_id, stripe_connect_id
      FROM user_payment_info
      WHERE stripe_connect_id IS NOT NULL
    `);
    
    console.log(`\nPayment records with Stripe data: ${paymentWithStripe.rows.length}`);
    if (paymentWithStripe.rows.length > 0) {
      console.log('Sample payment records with Stripe data:');
      paymentWithStripe.rows.slice(0, 5).forEach(record => 
        console.log(`User ID: ${record.user_id}, Stripe Connect ID: ${record.stripe_connect_id}`)
      );
    }
    
    console.log('\nNew tables structure check completed.');
  } catch (error) {
    console.error('Error checking new tables:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
checkNewTables()
  .then(() => {
    console.log('Tables check completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to check tables:', err);
    process.exit(1);
  });
