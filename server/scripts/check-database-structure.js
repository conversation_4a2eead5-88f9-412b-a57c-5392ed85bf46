/**
 * <PERSON><PERSON><PERSON> to check the database structure after cleanup
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function checkDatabaseStructure() {
  try {
    console.log('Checking database structure after cleanup...');
    
    // Get all tables
    const allTables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    console.log('\nAll tables in database:');
    allTables.rows.forEach(row => console.log(row.table_name));
    
    // Check row counts for each table
    console.log('\nTable row counts:');
    for (const row of allTables.rows) {
      const tableName = row.table_name;
      const countResult = await pool.query(`SELECT COUNT(*) FROM "${tableName}"`);
      const count = parseInt(countResult.rows[0].count);
      
      console.log(`${tableName}: ${count} rows`);
    }
    
    // Check for foreign key relationships
    console.log('\nForeign key relationships:');
    for (const row of allTables.rows) {
      const tableName = row.table_name;
      
      // Check foreign keys from this table to other tables
      const foreignKeysFrom = await pool.query(`
        SELECT tc.constraint_name, kcu.column_name, 
               ccu.table_name AS foreign_table_name,
               ccu.column_name AS foreign_column_name 
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = $1
      `, [tableName]);
      
      if (foreignKeysFrom.rows.length > 0) {
        console.log(`\n${tableName} references:`);
        foreignKeysFrom.rows.forEach(fk => {
          console.log(`  - ${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        });
      }
      
      // Check foreign keys to this table from other tables
      const foreignKeysTo = await pool.query(`
        SELECT tc.table_name AS referencing_table, kcu.column_name AS referencing_column,
               tc.constraint_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = $1
      `, [tableName]);
      
      if (foreignKeysTo.rows.length > 0) {
        console.log(`\n${tableName} is referenced by:`);
        foreignKeysTo.rows.forEach(fk => {
          console.log(`  - ${fk.referencing_table}.${fk.referencing_column}`);
        });
      }
    }
    
    // Check for any remaining issues
    console.log('\nChecking for potential issues:');
    
    // Check for tables with similar names
    const similarNameTables = allTables.rows.filter(row => 
      row.table_name === 'session' || 
      row.table_name === 'sessions'
    );
    
    if (similarNameTables.length > 1) {
      console.log('\nTables with similar names:');
      similarNameTables.forEach(row => console.log(row.table_name));
    }
    
    // Check for the session table (which might be for Express sessions)
    const sessionTable = allTables.rows.find(row => row.table_name === 'session');
    if (sessionTable) {
      const sessionColumns = await pool.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'session'
        ORDER BY ordinal_position
      `);
      
      console.log('\nsession table columns:');
      sessionColumns.rows.forEach(col => console.log(`  ${col.column_name} (${col.data_type})`));
      
      // Check if this is an Express session table
      const isExpressSessionTable = sessionColumns.rows.some(col => 
        col.column_name === 'sid' && 
        sessionColumns.rows.some(c => c.column_name === 'sess')
      );
      
      if (isExpressSessionTable) {
        console.log('\nThe "session" table appears to be for Express sessions and should be kept.');
      }
    }
    
    console.log('\nDatabase structure check completed.');
  } catch (error) {
    console.error('Error checking database structure:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
checkDatabaseStructure()
  .then(() => {
    console.log('Database structure check completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to check database structure:', err);
    process.exit(1);
  });
