import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function inspectDatabase() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Get all schemas
    const schemas = await pool.query(`
      SELECT schema_name
      FROM information_schema.schemata
      WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
      ORDER BY schema_name;
    `);

    console.log(`\n=== DATABASE SCHEMAS ===`);
    schemas.rows.forEach(schema => {
      console.log(`- ${schema.schema_name}`);
    });

    // For each schema, get all tables
    for (const schema of schemas.rows) {
      const schemaName = schema.schema_name;

      const tables = await pool.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = $1
        ORDER BY table_name;
      `, [schemaName]);

      console.log(`\n=== TABLES IN SCHEMA: ${schemaName} ===`);

      for (const table of tables.rows) {
        const tableName = table.table_name;
        console.log(`\n- ${tableName}`);

        // Get columns for this table
        const columns = await pool.query(`
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns
          WHERE table_schema = $1 AND table_name = $2
          ORDER BY ordinal_position;
        `, [schemaName, tableName]);

        console.log(`  Columns:`);
        columns.rows.forEach(column => {
          console.log(`    ${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not nullable'})`);
        });

        // Get sample data (first row) for this table
        try {
          const sampleData = await pool.query(`
            SELECT *
            FROM ${schemaName}.${tableName}
            LIMIT 1;
          `);

          if (sampleData.rows.length > 0) {
            console.log(`  Sample data (first row):`);
            const row = sampleData.rows[0];
            for (const [key, value] of Object.entries(row)) {
              // Truncate long values
              const displayValue = value === null ? 'NULL' :
                (typeof value === 'string' && value.length > 100) ?
                  `${value.substring(0, 100)}...` :
                  value;
              console.log(`    ${key}: ${displayValue}`);
            }
          } else {
            console.log(`  No data in table`);
          }
        } catch (error) {
          console.error(`  Error getting sample data: ${error.message}`);
        }
      }
    }

    // Specifically examine user and profile tables
    console.log(`\n\n=== USER AND PROFILE TABLES DETAILED INSPECTION ===`);

    // Check auth.users table
    console.log(`\n=== auth.users TABLE ===`);
    try {
      const usersCount = await pool.query(`SELECT COUNT(*) FROM auth.users`);
      console.log(`Total users: ${usersCount.rows[0].count}`);

      // Check for avatar and cover_photo columns
      const userColumns = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'auth' AND table_name = 'users'
        AND column_name IN ('avatar', 'cover_photo');
      `);

      console.log(`Avatar/cover_photo columns in auth.users:`, userColumns.rows.map(r => r.column_name));

      // Get sample users with avatar/cover_photo
      const usersWithImages = await pool.query(`
        SELECT id, username, email, avatar, cover_photo
        FROM auth.users
        WHERE avatar IS NOT NULL OR cover_photo IS NOT NULL
        LIMIT 5;
      `);

      console.log(`Sample users with images:`);
      usersWithImages.rows.forEach(user => {
        console.log(`  User ${user.id} (${user.username}):`);
        console.log(`    Avatar: ${user.avatar || 'NULL'}`);
        console.log(`    Cover photo: ${user.cover_photo || 'NULL'}`);
      });
    } catch (error) {
      console.error(`Error inspecting auth.users: ${error.message}`);
    }

    // Check profiles.user_profiles table
    console.log(`\n=== profiles.user_profiles TABLE ===`);
    try {
      const profilesCount = await pool.query(`SELECT COUNT(*) FROM profiles.user_profiles`);
      console.log(`Total profiles: ${profilesCount.rows[0].count}`);

      // Check for avatar and cover_photo columns
      const profileColumns = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
        AND column_name IN ('avatar', 'cover_photo');
      `);

      console.log(`Avatar/cover_photo columns in profiles.user_profiles:`, profileColumns.rows.map(r => r.column_name));

      // Get sample profiles with avatar/cover_photo
      const profilesWithImages = await pool.query(`
        SELECT user_id, avatar, cover_photo
        FROM profiles.user_profiles
        WHERE avatar IS NOT NULL OR cover_photo IS NOT NULL
        LIMIT 5;
      `);

      console.log(`Sample profiles with images:`);
      profilesWithImages.rows.forEach(profile => {
        console.log(`  Profile for user ${profile.user_id}:`);
        console.log(`    Avatar: ${profile.avatar || 'NULL'}`);
        console.log(`    Cover photo: ${profile.cover_photo || 'NULL'}`);
      });

      // Check for mismatches between users and profiles
      console.log(`\n=== CHECKING FOR MISMATCHES BETWEEN USERS AND PROFILES ===`);

      const mismatches = await pool.query(`
        SELECT u.id, u.username, u.avatar as user_avatar, p.avatar as profile_avatar,
               u.cover_photo as user_cover, p.cover_photo as profile_cover
        FROM auth.users u
        JOIN profiles.user_profiles p ON u.id = p.user_id
        WHERE (u.avatar IS DISTINCT FROM p.avatar) OR (u.cover_photo IS DISTINCT FROM p.cover_photo)
        LIMIT 10;
      `);

      if (mismatches.rows.length > 0) {
        console.log(`Found ${mismatches.rows.length} users with mismatched avatar/cover_photo:`);
        mismatches.rows.forEach(row => {
          console.log(`  User ${row.id} (${row.username}):`);
          console.log(`    User avatar: ${row.user_avatar || 'NULL'}`);
          console.log(`    Profile avatar: ${row.profile_avatar || 'NULL'}`);
          console.log(`    User cover: ${row.user_cover || 'NULL'}`);
          console.log(`    Profile cover: ${row.profile_cover || 'NULL'}`);
        });
      } else {
        console.log(`No mismatches found between users and profiles.`);
      }
    } catch (error) {
      console.error(`Error inspecting profiles.user_profiles: ${error.message}`);
    }

    // Save the output to a file
    const outputPath = path.join(__dirname, 'database-inspection.txt');
    fs.writeFileSync(outputPath, `Database Inspection Report\nGenerated: ${new Date().toISOString()}\n\n`, 'utf8');

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');
    console.log(`Report saved to ${outputPath}`);
  } catch (error) {
    console.error('Error inspecting database:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

inspectDatabase();
