import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Create a new pool with the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function updateProfile() {
  try {
    console.log('Connecting to database...');
    console.log('Using connection string:', process.env.DATABASE_URL ? 'From environment' : 'Not set');
    const client = await pool.connect();

    console.log('Connection successful!');

    // Update user profile
    console.log('\nUpdating profile for user with ID 11...');

    const updateResult = await client.query(`
      UPDATE profiles.user_profiles 
      SET 
        is_teacher = true,
        is_teacher = true,
        avatar = 'https://sessionhub-images.s3.amazonaws.com/profiles/avatars/6fcb88f4-575e-4d17-b737-321823c24304.jpg',
        updated_at = NOW()
      WHERE user_id = 11
      RETURNING *
    `);

    if (updateResult.rows.length > 0) {
      console.log('Profile updated successfully:');
      console.log(JSON.stringify(updateResult.rows[0], null, 2));
    } else {
      console.log('Failed to update profile for user with ID 11');
    }

    client.release();
  } catch (error) {
    console.error('Error updating profile:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

updateProfile();
