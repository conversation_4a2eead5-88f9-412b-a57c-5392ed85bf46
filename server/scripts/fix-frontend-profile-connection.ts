import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';

// Load environment variables
dotenv.config();

async function fixFrontendProfileConnection() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Check profiles.user_profiles table structure
    console.log('Checking profiles.user_profiles table structure...');
    const profileColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
      ORDER BY column_name;
    `);

    console.log('profiles.user_profiles columns:');
    profileColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    // Check if avatar and cover_photo columns exist in profiles.user_profiles
    const hasAvatarInProfiles = profileColumns.rows.some(col => col.column_name === 'avatar');
    const hasCoverPhotoInProfiles = profileColumns.rows.some(col => col.column_name === 'cover_photo');

    console.log(`\nAvatar column in profiles.user_profiles: ${hasAvatarInProfiles ? 'EXISTS' : 'MISSING'}`);
    console.log(`Cover photo column in profiles.user_profiles: ${hasCoverPhotoInProfiles ? 'EXISTS' : 'MISSING'}`);

    // Check user 11 in profiles.user_profiles table
    console.log('\nChecking user 11 in profiles.user_profiles table...');
    const profileResult = await pool.query(`
      SELECT * 
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (profileResult.rows.length === 0) {
      console.log('User 11 not found in profiles.user_profiles table');
    } else {
      const profile = profileResult.rows[0];
      console.log('User 11 profile data from profiles.user_profiles:');
      console.log(JSON.stringify(profile, null, 2));
    }

    // Check the server code to ensure it's correctly fetching from profiles.user_profiles
    console.log('\nChecking server code for profile data handling...');

    // Check postgresql-storage.ts
    const storageFilePath = path.resolve(__dirname, '../postgresql-storage.ts');
    if (fs.existsSync(storageFilePath)) {
      const storageFileContent = fs.readFileSync(storageFilePath, 'utf8');

      // Check if the file correctly fetches avatar and cover_photo from profiles.user_profiles
      const avatarFetchPattern = /avatar.*?profiles\.user_profiles/;
      const coverPhotoFetchPattern = /cover_photo.*?profiles\.user_profiles/;

      const avatarFetchMatch = storageFileContent.match(avatarFetchPattern);
      const coverPhotoFetchMatch = storageFileContent.match(coverPhotoFetchPattern);

      console.log(`Storage file correctly fetches avatar from profiles.user_profiles: ${!!avatarFetchMatch}`);
      console.log(`Storage file correctly fetches cover_photo from profiles.user_profiles: ${!!coverPhotoFetchMatch}`);
    } else {
      console.log('postgresql-storage.ts file not found');
    }

    // Check user-profile-storage.ts
    const profileStorageFilePath = path.resolve(__dirname, '../user-profile-storage.ts');
    if (fs.existsSync(profileStorageFilePath)) {
      const profileStorageFileContent = fs.readFileSync(profileStorageFilePath, 'utf8');

      // Check if the file correctly handles avatar and cover_photo
      const avatarHandlingPattern = /avatar.*?user_profiles/;
      const coverPhotoHandlingPattern = /cover_photo.*?user_profiles/;

      const avatarHandlingMatch = profileStorageFileContent.match(avatarHandlingPattern);
      const coverPhotoHandlingMatch = profileStorageFileContent.match(coverPhotoHandlingPattern);

      console.log(`Profile storage file correctly handles avatar: ${!!avatarHandlingMatch}`);
      console.log(`Profile storage file correctly handles cover_photo: ${!!coverPhotoHandlingMatch}`);
    } else {
      console.log('user-profile-storage.ts file not found');
    }

    // Check routes.ts
    const routesFilePath = path.resolve(__dirname, '../routes.ts');
    if (fs.existsSync(routesFilePath)) {
      const routesFileContent = fs.readFileSync(routesFilePath, 'utf8');

      // Check if the file correctly handles avatar and cover_photo in profile updates
      const avatarUpdatePattern = /avatar.*?updateUserProfile/;
      const coverPhotoUpdatePattern = /coverPhoto.*?updateUserProfile/;

      const avatarUpdateMatch = routesFileContent.match(avatarUpdatePattern);
      const coverPhotoUpdateMatch = routesFileContent.match(coverPhotoUpdatePattern);

      console.log(`Routes file correctly handles avatar updates: ${!!avatarUpdateMatch}`);
      console.log(`Routes file correctly handles coverPhoto updates: ${!!coverPhotoUpdateMatch}`);
    } else {
      console.log('routes.ts file not found');
    }

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

    console.log('\nRecommendations:');
    console.log('1. Ensure the frontend is fetching user profile data from /api/users/:id/profile endpoint');
    console.log('2. Make sure the server is returning avatar and cover_photo from profiles.user_profiles table');
    console.log('3. Verify that profile updates are correctly saving to profiles.user_profiles table');

  } catch (error) {
    console.error('Error fixing frontend profile connection:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

fixFrontendProfileConnection();
