/**
 * <PERSON><PERSON>t to restore necessary tables that were removed
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function restoreTables() {
  try {
    console.log('Starting table restoration...');
    
    // Begin transaction
    await pool.query('BEGIN');
    
    // 1. Restore email_verification_tokens table
    console.log('\nRestoring email_verification_tokens table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS email_verification_tokens (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token VARCHAR(255) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        expires_at TIMESTAMP NOT NULL,
        UNIQUE(token)
      )
    `);
    console.log('email_verification_tokens table restored');
    
    // 2. Restore global_availability table
    console.log('\nRestoring global_availability table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS global_availability (
        id SERIAL PRIMARY KEY,
        teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        day_of_week INTEGER NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        timezone VARCHAR(100) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `);
    console.log('global_availability table restored');
    
    // 3. Restore notification_preferences table
    console.log('\nRestoring notification_preferences table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS notification_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        email_notifications BOOLEAN NOT NULL DEFAULT TRUE,
        sms_notifications BOOLEAN NOT NULL DEFAULT FALSE,
        push_notifications BOOLEAN NOT NULL DEFAULT FALSE,
        booking_reminders BOOLEAN NOT NULL DEFAULT TRUE,
        session_updates BOOLEAN NOT NULL DEFAULT TRUE,
        marketing_emails BOOLEAN NOT NULL DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(user_id)
      )
    `);
    console.log('notification_preferences table restored');
    
    // 4. Restore payouts table
    console.log('\nRestoring payouts table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS payouts (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        payment_method_id INTEGER REFERENCES teacher_payment_methods(id) ON DELETE SET NULL,
        amount DECIMAL(10, 2) NOT NULL,
        currency VARCHAR(3) NOT NULL DEFAULT 'USD',
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        stripe_payout_id VARCHAR(255),
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        processed_at TIMESTAMP,
        notes TEXT
      )
    `);
    console.log('payouts table restored');
    
    // 5. Restore platform_fees table
    console.log('\nRestoring platform_fees table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS platform_fees (
        id SERIAL PRIMARY KEY,
        booking_id INTEGER NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
        fee_amount DECIMAL(10, 2) NOT NULL,
        fee_percentage DECIMAL(5, 2) NOT NULL,
        currency VARCHAR(3) NOT NULL DEFAULT 'USD',
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        stripe_fee_id VARCHAR(255),
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        processed_at TIMESTAMP
      )
    `);
    console.log('platform_fees table restored');
    
    // 6. Restore reviews table
    console.log('\nRestoring reviews table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS reviews (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        session_id INTEGER NOT NULL REFERENCES sessions(id) ON DELETE CASCADE,
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        content TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        audio_url TEXT,
        video_url TEXT,
        is_public BOOLEAN NOT NULL DEFAULT TRUE,
        UNIQUE(user_id, session_id)
      )
    `);
    console.log('reviews table restored');
    
    // 7. Restore user_devices table
    console.log('\nRestoring user_devices table...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_devices (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        device_token VARCHAR(255) NOT NULL,
        device_type VARCHAR(50) NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        last_used_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(user_id, device_token)
      )
    `);
    console.log('user_devices table restored');
    
    // Commit transaction
    await pool.query('COMMIT');
    
    console.log('\nTable restoration completed successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await pool.query('ROLLBACK');
    console.error('Error restoring tables:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
restoreTables()
  .then(() => {
    console.log('Table restoration script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to restore tables:', err);
    process.exit(1);
  });
