/**
 * Script to drop legacy tables after migration is complete
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function dropLegacyTables() {
  const client = await pool.connect();
  
  try {
    // Start a transaction
    await client.query('BEGIN');
    
    console.log('Dropping legacy tables...');
    
    // Drop legacy tables
    await client.query(`
      DROP TABLE IF EXISTS profiles;
    `);
    
    await client.query(`
      DROP TABLE IF EXISTS teacher_profiles;
    `);
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Legacy tables dropped successfully!');
    
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error dropping legacy tables:', error);
    throw error;
  } finally {
    // Release the client back to the pool
    client.release();
  }
}

// Run the script
dropLegacyTables()
  .then(() => {
    console.log('Tables dropped successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to drop tables:', err);
    process.exit(1);
  });
