import pg from 'pg';
const { Pool } = pg;

// Create a connection to the database
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function checkPaymentMethods() {
  try {
    // Get the teacher's payment methods
    const result = await pool.query(`
      SELECT * FROM teacher_payment_methods WHERE user_id = $1
    `, [6]);
    
    console.log('Payment methods for teacher (ID: 6):', result.rows);
    
    // Get the teacher's details
    const teacherResult = await pool.query(`
      SELECT id, username, email, name, is_teacher FROM users WHERE id = $1
    `, [6]);
    
    console.log('Teacher details:', teacherResult.rows[0]);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

checkPaymentMethods();
