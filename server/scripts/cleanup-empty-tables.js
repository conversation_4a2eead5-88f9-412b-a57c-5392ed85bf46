/**
 * <PERSON><PERSON><PERSON> to clean up empty tables in the database
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function cleanupEmptyTables() {
  try {
    console.log('Starting empty tables cleanup...');
    
    // Begin transaction
    await pool.query('BEGIN');
    
    // List of empty tables to check
    const tablesToCheck = [
      'email_verification_tokens',
      'global_availability',
      'teacher_availability',
      'notification_preferences',
      'payouts',
      'platform_fees',
      'reviews',
      'time_slots',
      'user_devices'
    ];
    
    // Check each table
    for (const tableName of tablesToCheck) {
      const countResult = await pool.query(`SELECT COUNT(*) FROM "${tableName}"`);
      const count = parseInt(countResult.rows[0].count);
      
      if (count === 0) {
        console.log(`Table ${tableName} is empty`);
        
        // Check if the table is referenced by foreign keys
        const foreignKeyCheck = await pool.query(`
          SELECT tc.table_schema, tc.constraint_name, tc.table_name, kcu.column_name, 
                 ccu.table_schema AS foreign_table_schema,
                 ccu.table_name AS foreign_table_name,
                 ccu.column_name AS foreign_column_name 
          FROM information_schema.table_constraints AS tc 
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY' 
          AND ccu.table_name = $1
        `, [tableName]);
        
        if (foreignKeyCheck.rows.length > 0) {
          console.log(`Table ${tableName} is referenced by foreign keys and cannot be safely dropped:`);
          foreignKeyCheck.rows.forEach(row => {
            console.log(`  - Referenced by ${row.table_name}.${row.column_name}`);
          });
        } else {
          // Check if the table has foreign keys to other tables
          const hasForeignKeys = await pool.query(`
            SELECT tc.table_schema, tc.constraint_name, tc.table_name, kcu.column_name, 
                   ccu.table_schema AS foreign_table_schema,
                   ccu.table_name AS foreign_table_name,
                   ccu.column_name AS foreign_column_name 
            FROM information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND tc.table_name = $1
          `, [tableName]);
          
          if (hasForeignKeys.rows.length > 0) {
            console.log(`Table ${tableName} has foreign keys to other tables:`);
            hasForeignKeys.rows.forEach(row => {
              console.log(`  - References ${row.foreign_table_name}.${row.foreign_column_name}`);
            });
            
            // Drop foreign keys first
            for (const fk of hasForeignKeys.rows) {
              await pool.query(`ALTER TABLE "${tableName}" DROP CONSTRAINT "${fk.constraint_name}"`);
              console.log(`  - Dropped foreign key constraint ${fk.constraint_name}`);
            }
          }
          
          // Now we can safely drop the table
          await pool.query(`DROP TABLE IF EXISTS "${tableName}"`);
          console.log(`Dropped empty table ${tableName}`);
        }
      } else {
        console.log(`Table ${tableName} has ${count} rows and will not be dropped`);
      }
    }
    
    // Commit transaction
    await pool.query('COMMIT');
    
    console.log('\nEmpty tables cleanup completed successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await pool.query('ROLLBACK');
    console.error('Error cleaning up empty tables:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
cleanupEmptyTables()
  .then(() => {
    console.log('Empty tables cleanup script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to clean up empty tables:', err);
    process.exit(1);
  });
