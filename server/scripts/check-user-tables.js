/**
 * <PERSON>ript to check users and user_profiles tables
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function checkUserTables() {
  try {
    console.log('Checking users and user_profiles tables...');
    
    // Get users table structure
    const usersColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'users' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nUsers table columns:');
    usersColumns.rows.forEach(col => console.log(`${col.column_name} (${col.data_type})`));
    
    // Get user_profiles table structure
    const profilesColumns = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_profiles' 
      ORDER BY ordinal_position
    `);
    
    console.log('\nUser_profiles table columns:');
    profilesColumns.rows.forEach(col => console.log(`${col.column_name} (${col.data_type})`));
    
    // Check foreign key relationship
    const foreignKeys = await pool.query(`
      SELECT tc.constraint_name, kcu.column_name, 
             ccu.table_name AS foreign_table_name,
             ccu.column_name AS foreign_column_name 
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
      AND tc.table_name = 'user_profiles'
    `);
    
    console.log('\nForeign key relationships:');
    foreignKeys.rows.forEach(fk => 
      console.log(`${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`)
    );
    
    // Check for references to user_profiles
    const references = await pool.query(`
      SELECT tc.table_name, kcu.column_name 
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
      AND ccu.table_name = 'user_profiles'
    `);
    
    console.log('\nTables referencing user_profiles:');
    if (references.rows.length === 0) {
      console.log('No tables reference user_profiles');
    } else {
      references.rows.forEach(ref => 
        console.log(`${ref.table_name}.${ref.column_name}`)
      );
    }
    
    // Check row counts
    const userCount = await pool.query('SELECT COUNT(*) FROM users');
    const profileCount = await pool.query('SELECT COUNT(*) FROM user_profiles');
    
    console.log(`\nUsers table has ${userCount.rows[0].count} rows`);
    console.log(`User_profiles table has ${profileCount.rows[0].count} rows`);
    
    // Check for users without profiles
    const usersWithoutProfiles = await pool.query(`
      SELECT u.id, u.username 
      FROM users u 
      LEFT JOIN user_profiles up ON u.id = up.user_id 
      WHERE up.id IS NULL
    `);
    
    console.log(`\nUsers without profiles: ${usersWithoutProfiles.rows.length}`);
    if (usersWithoutProfiles.rows.length > 0) {
      console.log('Sample users without profiles:');
      usersWithoutProfiles.rows.slice(0, 5).forEach(user => 
        console.log(`ID: ${user.id}, Username: ${user.username}`)
      );
    }
    
    // Check for profiles without users
    const profilesWithoutUsers = await pool.query(`
      SELECT up.id, up.user_id 
      FROM user_profiles up 
      LEFT JOIN users u ON up.user_id = u.id 
      WHERE u.id IS NULL
    `);
    
    console.log(`\nProfiles without users: ${profilesWithoutUsers.rows.length}`);
    if (profilesWithoutUsers.rows.length > 0) {
      console.log('Sample profiles without users:');
      profilesWithoutUsers.rows.slice(0, 5).forEach(profile => 
        console.log(`ID: ${profile.id}, User ID: ${profile.user_id}`)
      );
    }
    
    // Check how the tables are used in the codebase
    console.log('\nAnalysis of table usage:');
    console.log('1. The users table contains core user account information (credentials, email, etc.)');
    console.log('2. The user_profiles table contains extended profile information (social media, bio, etc.)');
    console.log('3. They are linked by a one-to-one relationship via user_id');
    
    console.log('\nConsolidation considerations:');
    console.log('- Pros of consolidation: Simpler schema, fewer joins, easier maintenance');
    console.log('- Cons of consolidation: Larger table, mixing authentication with profile data, potential performance impact');
    
  } catch (error) {
    console.error('Error checking user tables:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
checkUserTables()
  .then(() => {
    console.log('\nUser tables check completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to check user tables:', err);
    process.exit(1);
  });
