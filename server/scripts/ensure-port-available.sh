#!/bin/bash

# This script ensures that the specified port is available for use
# It will check if the port is in use and offer to kill the process

PORT=$1

if [ -z "$PORT" ]; then
  echo "Please specify a port number"
  exit 1
fi

echo "Checking if port $PORT is in use..."

# Find processes using the port
PID=$(lsof -ti :$PORT)

if [ -z "$PID" ]; then
  echo "Port $PORT is available."
  exit 0
fi

# Get the name of the process
PROCESS_NAME=$(ps -p $PID -o comm=)
PROCESS_COMMAND=$(ps -p $PID -o command=)

echo "Port $PORT is in use by process $PID"
echo "Process name: $PROCESS_NAME"

# If it's a nodemon process from a previous run, kill it automatically
if [[ "$PROCESS_COMMAND" == *"nodemon"* ]] || [[ "$PROCESS_COMMAND" == *"node"* && "$PROCESS_COMMAND" == *"server"* ]]; then
  echo "Detected a Node.js/nodemon process. Killing automatically for clean restart..."
  kill -9 $PID
  sleep 1
  
  # Verify it was killed
  if lsof -ti :$PORT > /dev/null; then
    echo "Process is still running. Attempting force kill..."
    kill -9 $(lsof -ti :$PORT)
    sleep 1
    
    if lsof -ti :$PORT > /dev/null; then
      echo "Failed to kill process. Please check manually."
      exit 1
    fi
  fi
  
  echo "Process successfully killed. Port $PORT is now available."
  exit 0
fi

# For other processes, ask the user
echo "Do you want to kill this process? (y/n) "
read -r RESPONSE

if [[ "$RESPONSE" =~ ^[Yy]$ ]]; then
  echo "Killing process $PID..."
  kill -9 $PID
  sleep 1
  
  # Verify it was killed
  if lsof -ti :$PORT > /dev/null; then
    echo "Process is still running. Attempting force kill..."
    kill -9 $(lsof -ti :$PORT)
    sleep 1
    
    if lsof -ti :$PORT > /dev/null; then
      echo "Failed to kill process. Please check manually."
      exit 1
    fi
  fi
  
  echo "Process successfully killed. Port $PORT is now available."
else
  echo "Process not killed. Port $PORT is still in use."
  exit 1
fi 