import { db } from '../db';

async function checkIsPublicField() {
  try {
    console.log('Connecting to database...');
    
    // Check sessions and their is_public field
    const sessionsResult = await db.query(`
      SELECT id, title, is_public, teacher_id
      FROM content.sessions
      ORDER BY id
      LIMIT 20
    `);
    
    console.log(`Found ${sessionsResult.rows.length} sessions in the database`);
    
    // Print the sessions with their is_public field
    console.log('Sessions with is_public field:');
    sessionsResult.rows.forEach(session => {
      console.log(`ID: ${session.id}, Title: ${session.title}, is_public: ${session.is_public} (type: ${typeof session.is_public}), Teacher: ${session.teacher_id}`);
    });
    
    // Count public vs private sessions
    const publicSessions = sessionsResult.rows.filter(s => s.is_public === true);
    const privateSessions = sessionsResult.rows.filter(s => s.is_public === false);
    const nullPublicSessions = sessionsResult.rows.filter(s => s.is_public === null);
    
    console.log(`\nSummary:`);
    console.log(`Public sessions (is_public === true): ${publicSessions.length}`);
    console.log(`Private sessions (is_public === false): ${privateSessions.length}`);
    console.log(`Sessions with null is_public: ${nullPublicSessions.length}`);
    
    // Check for other values
    const otherPublicSessions = sessionsResult.rows.filter(s => 
      s.is_public !== true && 
      s.is_public !== false && 
      s.is_public !== null
    );
    
    if (otherPublicSessions.length > 0) {
      console.log(`\nSessions with other is_public values:`);
      otherPublicSessions.forEach(session => {
        console.log(`ID: ${session.id}, Title: ${session.title}, is_public: ${session.is_public} (type: ${typeof session.is_public})`);
      });
    }
    
    // Exit the process
    process.exit(0);
  } catch (error) {
    console.error('Error checking sessions:', error);
    process.exit(1);
  }
}

checkIsPublicField();
