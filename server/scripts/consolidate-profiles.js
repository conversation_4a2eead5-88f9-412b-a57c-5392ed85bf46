/**
 * <PERSON><PERSON>t to consolidate profile data from legacy tables (profiles and teacher_profiles)
 * into the unified user_profiles table.
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function consolidateProfiles() {
  const client = await pool.connect();

  try {
    // Start a transaction
    await client.query('BEGIN');

    console.log('Starting profile consolidation...');

    // 1. Get all users with profiles in any of the tables
    const { rows: allUsers } = await client.query(`
      SELECT DISTINCT user_id
      FROM (
        SELECT user_id FROM user_profiles
        UNION
        SELECT user_id FROM profiles
        UNION
        SELECT user_id FROM teacher_profiles
      ) AS all_users
      ORDER BY user_id;
    `);

    console.log(`Found ${allUsers.length} users with profiles to consolidate`);

    // 2. Process each user
    for (const { user_id } of allUsers) {
      console.log(`Processing user ${user_id}...`);

      // Get profile data from all tables
      const { rows: userProfiles } = await client.query(
        'SELECT * FROM user_profiles WHERE user_id = $1',
        [user_id]
      );

      const { rows: profiles } = await client.query(
        'SELECT * FROM profiles WHERE user_id = $1',
        [user_id]
      );

      const { rows: teacherProfiles } = await client.query(
        'SELECT * FROM teacher_profiles WHERE user_id = $1',
        [user_id]
      );

      const userProfile = userProfiles[0] || null;
      const profile = profiles[0] || null;
      const teacherProfile = teacherProfiles[0] || null;

      // If user already has a user_profile, update it with any missing data
      if (userProfile) {
        console.log(`User ${user_id} has a user_profile, updating with any missing data...`);

        // Prepare update data by merging from legacy profiles
        const updateData = {};

        // Helper function to merge fields
        const mergeField = (field, snakeCase) => {
          // Only update if the field is empty in user_profiles but has data in legacy tables
          if (!userProfile[snakeCase] || userProfile[snakeCase] === '') {
            if (profile && profile[snakeCase] && profile[snakeCase] !== '') {
              updateData[field] = profile[snakeCase];
            } else if (teacherProfile && teacherProfile[snakeCase] && teacherProfile[snakeCase] !== '') {
              updateData[field] = teacherProfile[snakeCase];
            }
          }
        };

        // Merge text fields
        mergeField('bio', 'bio');
        mergeField('timezone', 'timezone');
        mergeField('phone', 'phone');
        mergeField('location', 'location');
        mergeField('website', 'website');
        mergeField('facebook_url', 'facebook_url');
        mergeField('twitter_url', 'twitter_url');
        mergeField('instagram_url', 'instagram_url');
        mergeField('linkedin_url', 'linkedin_url');
        mergeField('cover_photo', 'cover_photo');
        mergeField('education', 'education');
        mergeField('experience', 'experience');
        mergeField('availability', 'availability');

        // Merge array fields (specializations, skills, certifications)
        // Only update if the arrays are empty in user_profiles
        if ((!userProfile.specializations || userProfile.specializations.length === 0) &&
            ((profile && profile.specializations && profile.specializations.length > 0) ||
             (teacherProfile && teacherProfile.specializations && teacherProfile.specializations.length > 0))) {
          updateData.specializations = (profile && profile.specializations) ||
                                      (teacherProfile && teacherProfile.specializations) ||
                                      [];
        }

        if ((!userProfile.skills || userProfile.skills.length === 0) &&
            ((profile && profile.skills && profile.skills.length > 0) ||
             (teacherProfile && teacherProfile.skills && teacherProfile.skills.length > 0))) {
          updateData.skills = (profile && profile.skills) ||
                             (teacherProfile && teacherProfile.skills) ||
                             [];
        }

        if ((!userProfile.certifications || userProfile.certifications.length === 0) &&
            ((profile && profile.certifications && profile.certifications.length > 0) ||
             (teacherProfile && teacherProfile.certifications && teacherProfile.certifications.length > 0))) {
          updateData.certifications = (profile && profile.certifications) ||
                                     (teacherProfile && teacherProfile.certifications) ||
                                     [];
        }

        // Only update if there are fields to update
        if (Object.keys(updateData).length > 0) {
          // Build the update query
          const fields = Object.keys(updateData);
          const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');
          const values = fields.map(field => updateData[field]);

          const updateQuery = `
            UPDATE user_profiles
            SET ${setClause}, updated_at = NOW()
            WHERE user_id = $1
          `;

          await client.query(updateQuery, [user_id, ...values]);
          console.log(`Updated user_profile for user ${user_id} with data:`, updateData);
        } else {
          console.log(`No updates needed for user ${user_id}`);
        }
      }
      // If user doesn't have a user_profile but has legacy profiles, create a new one
      else if (profile || teacherProfile) {
        console.log(`User ${user_id} has no user_profile but has legacy profiles, creating new user_profile...`);

        // Merge data from both legacy profiles
        const mergedProfile = {
          user_id: user_id,
          bio: (profile && profile.bio) || (teacherProfile && teacherProfile.bio) || null,
          timezone: (profile && profile.timezone) || (teacherProfile && teacherProfile.timezone) || null,
          phone: (profile && profile.phone) || (teacherProfile && teacherProfile.phone) || null,
          location: (profile && profile.location) || (teacherProfile && teacherProfile.location) || null,
          website: (profile && profile.website) || (teacherProfile && teacherProfile.website) || null,
          facebook_url: (profile && profile.facebook_url) || (teacherProfile && teacherProfile.facebook_url) || null,
          twitter_url: (profile && profile.twitter_url) || (teacherProfile && teacherProfile.twitter_url) || null,
          instagram_url: (profile && profile.instagram_url) || (teacherProfile && teacherProfile.instagram_url) || null,
          linkedin_url: (profile && profile.linkedin_url) || (teacherProfile && teacherProfile.linkedin_url) || null,
          cover_photo: (profile && profile.cover_photo) || null,
          specializations: (profile && profile.specializations) || (teacherProfile && teacherProfile.specializations) || [],
          skills: (profile && profile.skills) || (teacherProfile && teacherProfile.skills) || [],
          certifications: (profile && profile.certifications) || (teacherProfile && teacherProfile.certifications) || [],
          education: (profile && profile.education) || (teacherProfile && teacherProfile.education) || null,
          experience: (profile && profile.experience) || (teacherProfile && teacherProfile.experience) || null,
          availability: (profile && profile.availability) || null,
          rating: (profile && profile.rating) || (teacherProfile && teacherProfile.rating) || 0,
          review_count: (profile && profile.review_count) || (teacherProfile && teacherProfile.review_count) || 0,
          show_teaching_sessions: (profile && profile.show_teaching_sessions !== undefined) ? profile.show_teaching_sessions : true,
          show_learning_sessions: (profile && profile.show_learning_sessions !== undefined) ? profile.show_learning_sessions : false,
          show_profile: (profile && profile.show_profile !== undefined) ? profile.show_profile : true,
          show_social_links: (profile && profile.show_social_links !== undefined) ? profile.show_social_links : true,
          show_contact: (profile && profile.show_contact !== undefined) ? profile.show_contact : false
        };

        // Build the insert query
        const fields = Object.keys(mergedProfile).filter(key => mergedProfile[key] !== undefined);
        const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
        const values = fields.map(field => mergedProfile[field]);

        const insertQuery = `
          INSERT INTO user_profiles (${fields.join(', ')}, created_at, updated_at)
          VALUES (${placeholders}, NOW(), NOW())
          RETURNING id
        `;

        const result = await client.query(insertQuery, values);
        console.log(`Created new user_profile for user ${user_id} with ID ${result.rows[0].id}`);
      }
    }

    // 3. Verify all data has been migrated
    const { rows: missingUsers } = await client.query(`
      SELECT p.user_id
      FROM (
        SELECT user_id FROM profiles
        UNION
        SELECT user_id FROM teacher_profiles
      ) p
      LEFT JOIN user_profiles up ON p.user_id = up.user_id
      WHERE up.user_id IS NULL
    `);

    if (missingUsers.length > 0) {
      console.error(`Warning: ${missingUsers.length} users still missing from user_profiles:`, missingUsers);
      throw new Error('Migration incomplete: Some users are still missing from user_profiles');
    }

    // 4. Commit the transaction
    await client.query('COMMIT');
    console.log('Profile consolidation completed successfully!');

    // 5. Print summary
    const { rows: finalCounts } = await client.query(`
      SELECT
        (SELECT COUNT(*) FROM user_profiles) AS user_profiles_count,
        (SELECT COUNT(*) FROM profiles) AS profiles_count,
        (SELECT COUNT(*) FROM teacher_profiles) AS teacher_profiles_count
    `);

    console.log('Final counts:', finalCounts[0]);

  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error during profile consolidation:', error);
    throw error;
  } finally {
    // Release the client back to the pool
    client.release();
  }
}

// Run the consolidation
consolidateProfiles()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
