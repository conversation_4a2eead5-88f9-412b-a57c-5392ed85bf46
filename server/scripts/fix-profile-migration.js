/**
 * <PERSON><PERSON>t to fix profile migration issues
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function fixProfileMigration() {
  const client = await pool.connect();
  
  try {
    // Start a transaction
    await client.query('BEGIN');
    
    console.log('Fixing profile migration issues...');
    
    // 1. Check if there are any users without profiles in the user_profiles table
    const usersWithoutProfiles = await client.query(`
      SELECT u.id, u.username, u.email
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE up.id IS NULL
    `);
    
    console.log(`Found ${usersWithoutProfiles.rows.length} users without profiles in the user_profiles table`);
    
    // 2. For each user without a profile, check if they have a profile in the backup tables
    for (const user of usersWithoutProfiles.rows) {
      console.log(`Checking backup tables for user ${user.id} (${user.username})`);
      
      // Check profiles_backup table
      const profileBackup = await client.query(`
        SELECT * FROM profiles_backup WHERE user_id = $1
      `, [user.id]);
      
      // Check teacher_profiles_backup table
      const teacherProfileBackup = await client.query(`
        SELECT * FROM teacher_profiles_backup WHERE user_id = $1
      `, [user.id]);
      
      if (profileBackup.rows.length > 0 || teacherProfileBackup.rows.length > 0) {
        console.log(`Found backup profile for user ${user.id}`);
        
        // Combine data from both backup tables
        const profile = profileBackup.rows[0] || {};
        const teacherProfile = teacherProfileBackup.rows[0] || {};
        
        // Insert into user_profiles table
        await client.query(`
          INSERT INTO user_profiles (
            user_id, 
            specializations, 
            skills, 
            certifications, 
            experience, 
            education, 
            website, 
            facebook_url, 
            twitter_url, 
            instagram_url, 
            linkedin_url,
            youtube_url,
            tiktok_url,
            show_profile,
            show_social_links,
            show_contact,
            show_teaching_sessions,
            show_learning_sessions,
            created_at,
            updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
          )
        `, [
          user.id,
          teacherProfile.specializations || profile.specializations || [],
          teacherProfile.skills || profile.skills || [],
          teacherProfile.certifications || profile.certifications || [],
          teacherProfile.experience || profile.experience || '',
          teacherProfile.education || profile.education || '',
          teacherProfile.website || profile.website || '',
          teacherProfile.facebook_url || profile.facebook_url || '',
          teacherProfile.twitter_url || profile.twitter_url || '',
          teacherProfile.instagram_url || profile.instagram_url || '',
          teacherProfile.linkedin_url || profile.linkedin_url || '',
          teacherProfile.youtube_url || profile.youtube_url || '',
          teacherProfile.tiktok_url || profile.tiktok_url || '',
          teacherProfile.show_profile !== undefined ? teacherProfile.show_profile : true,
          teacherProfile.show_social_links !== undefined ? teacherProfile.show_social_links : true,
          teacherProfile.show_contact !== undefined ? teacherProfile.show_contact : false,
          teacherProfile.show_teaching_sessions !== undefined ? teacherProfile.show_teaching_sessions : true,
          teacherProfile.show_learning_sessions !== undefined ? teacherProfile.show_learning_sessions : false
        ]);
        
        console.log(`Created profile for user ${user.id}`);
      } else {
        console.log(`No backup profile found for user ${user.id}, creating empty profile`);
        
        // Create empty profile
        await client.query(`
          INSERT INTO user_profiles (
            user_id, 
            specializations, 
            skills, 
            certifications, 
            experience, 
            education, 
            website, 
            facebook_url, 
            twitter_url, 
            instagram_url, 
            linkedin_url,
            youtube_url,
            tiktok_url,
            show_profile,
            show_social_links,
            show_contact,
            show_teaching_sessions,
            show_learning_sessions,
            created_at,
            updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
          )
        `, [
          user.id,
          [],
          [],
          [],
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          true,
          true,
          false,
          true,
          false
        ]);
        
        console.log(`Created empty profile for user ${user.id}`);
      }
    }
    
    // 3. Update the users table to ensure all users have isTeacher and isTeacher flags set correctly
    await client.query(`
      UPDATE users
      SET is_teacher = true, is_teacher = true
      WHERE is_teacher IS NULL OR is_teacher IS NULL
    `);
    
    console.log('Updated user flags');
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Profile migration fixes applied successfully!');
    
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error fixing profile migration:', error);
    throw error;
  } finally {
    // Release the client back to the pool
    client.release();
  }
}

// Run the script
fixProfileMigration()
  .then(() => {
    console.log('Profile migration fixes completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to fix profile migration:', err);
    process.exit(1);
  });
