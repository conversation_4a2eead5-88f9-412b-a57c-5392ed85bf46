#!/bin/bash

# Production startup script for Session Hub server
# Uses PM2 for process management, logging, and automatic restarts

# Change to the project root directory
cd "$(dirname "$0")/.."

# Ensure PM2 is installed
if ! command -v pm2 &> /dev/null; then
  echo "PM2 is not installed. Installing globally..."
  npm install -g pm2
fi

# Set environment to production
export NODE_ENV=production

# Ensure DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
  if [ -f ".env" ]; then
    echo "Loading DATABASE_URL from .env file"
    export DATABASE_URL=$(grep DATABASE_URL .env | cut -d '=' -f2)
  else
    echo "ERROR: DATABASE_URL is not set and .env file not found!"
    exit 1
  fi
fi

# Build the application (if using TypeScript)
echo "Building the application..."
npm run build

# Start the application with PM2
echo "Starting application with PM2..."
pm2 start dist/index.js \
  --name "sessionhub" \
  --max-memory-restart 500M \
  --exp-backoff-restart-delay=1000 \
  --restart-delay=3000 \
  --log ./logs/app.log \
  --merge-logs \
  --time \
  --watch false \
  --max-restarts 10 \
  --wait-ready \
  --listen-timeout 5000 \
  --kill-timeout 15000 \
  --env production

# Save the PM2 configuration
pm2 save

echo "Application started successfully with PM2"
echo "To monitor: pm2 monit"
echo "To view logs: pm2 logs sessionhub"
echo "To stop: pm2 stop sessionhub" 