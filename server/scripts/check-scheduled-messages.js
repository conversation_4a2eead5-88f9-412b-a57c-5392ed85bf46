import pg from 'pg';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const { Pool } = pg;

const dbUrl = process.env.DATABASE_URL;
if (!dbUrl) {
  console.error('DATABASE_URL not found in environment variables');
  process.exit(1);
}

const pool = new Pool({
  connectionString: dbUrl,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkScheduledMessages() {
  try {
    // Get all scheduled messages
    const result = await pool.query(`
      SELECT 
        sm.*,
        s.title as session_title,
        u.name as student_name,
        i.name as teacher_name
      FROM 
        scheduled_messages sm
      JOIN 
        bookings b ON sm.booking_id = b.id
      JOIN 
        sessions s ON b.session_id = s.id
      JOIN 
        users u ON b.user_id = u.id
      JOIN 
        users i ON s.teacher_id = i.id
      ORDER BY 
        sm.send_at DESC
    `);

    console.log(`Found ${result.rows.length} scheduled messages:`);
    
    if (result.rows.length === 0) {
      console.log('No scheduled messages found.');
    } else {
      result.rows.forEach((row, index) => {
        console.log(`\n--- Message ${index + 1} ---`);
        console.log(`ID: ${row.id}`);
        console.log(`Session: ${row.session_title}`);
        console.log(`Student: ${row.student_name}`);
        console.log(`Teacher: ${row.teacher_name}`);
        console.log(`Content: ${row.content}`);
        console.log(`Send at: ${row.send_at}`);
        console.log(`Sent: ${row.sent ? 'Yes' : 'No'}`);
        console.log(`Created at: ${row.created_at}`);
      });
    }
  } catch (error) {
    console.error('Error checking scheduled messages:', error);
  } finally {
    await pool.end();
  }
}

checkScheduledMessages();
