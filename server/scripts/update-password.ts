import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import bcrypt from 'bcrypt';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Create a new pool with the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function updatePassword() {
  try {
    console.log('Connecting to database...');
    console.log('Using connection string:', process.env.DATABASE_URL ? 'From environment' : 'Not set');
    const client = await pool.connect();

    console.log('Connection successful!');

    // Update password for user 'ynachum'
    const username = 'ynachum';
    const newPassword = 'password123';

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update the user
    const updateResult = await client.query(`
      UPDATE auth.users
      SET password = $1, updated_at = NOW()
      WHERE username = $2
      RETURNING id, username, email
    `, [hashedPassword, username]);

    if (updateResult.rows.length > 0) {
      const user = updateResult.rows[0];
      console.log('Password updated successfully for user:');
      console.log(JSON.stringify(user, null, 2));
    } else {
      console.log('Failed to update password for user:', username);
    }

    client.release();
  } catch (error) {
    console.error('Error updating password:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

updatePassword();
