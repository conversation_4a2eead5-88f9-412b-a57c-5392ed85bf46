import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function removeProfileColumnsFromAuth() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Check if avatar and cover_photo columns exist in auth.users
    console.log('Checking auth.users table structure...');
    const userColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'auth' AND table_name = 'users'
      ORDER BY column_name;
    `);

    console.log('auth.users columns:');
    userColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    // Check if avatar column exists in auth.users
    const hasAvatarInUsers = userColumns.rows.some(col => col.column_name === 'avatar');
    const hasCoverPhotoInUsers = userColumns.rows.some(col => col.column_name === 'cover_photo');

    console.log(`\nAvatar column in auth.users: ${hasAvatarInUsers ? 'EXISTS' : 'MISSING'}`);
    console.log(`Cover photo column in auth.users: ${hasCoverPhotoInUsers ? 'EXISTS' : 'MISSING'}`);

    // Remove avatar column from auth.users if it exists
    if (hasAvatarInUsers) {
      console.log('\nRemoving avatar column from auth.users table...');
      await pool.query(`
        ALTER TABLE auth.users 
        DROP COLUMN avatar;
      `);
      console.log('Removed avatar column from auth.users table');
    }

    // Remove cover_photo column from auth.users if it exists
    if (hasCoverPhotoInUsers) {
      console.log('\nRemoving cover_photo column from auth.users table...');
      await pool.query(`
        ALTER TABLE auth.users 
        DROP COLUMN cover_photo;
      `);
      console.log('Removed cover_photo column from auth.users table');
    }

    // Verify the changes
    console.log('\nVerifying changes...');

    // Check auth.users table structure again
    const updatedUserColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'auth' AND table_name = 'users'
      ORDER BY column_name;
    `);

    console.log('Updated auth.users columns:');
    updatedUserColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    // Check profiles.user_profiles table structure
    console.log('\nChecking profiles.user_profiles table structure...');
    const profileColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
      ORDER BY column_name;
    `);

    console.log('profiles.user_profiles columns:');
    profileColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    // Check if avatar and cover_photo columns exist in profiles.user_profiles
    const hasAvatarInProfiles = profileColumns.rows.some(col => col.column_name === 'avatar');
    const hasCoverPhotoInProfiles = profileColumns.rows.some(col => col.column_name === 'cover_photo');

    console.log(`\nAvatar column in profiles.user_profiles: ${hasAvatarInProfiles ? 'EXISTS' : 'MISSING'}`);
    console.log(`Cover photo column in profiles.user_profiles: ${hasCoverPhotoInProfiles ? 'EXISTS' : 'MISSING'}`);

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

  } catch (error) {
    console.error('Error removing profile columns from auth:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

removeProfileColumnsFromAuth();
