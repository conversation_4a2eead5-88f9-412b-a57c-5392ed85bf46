/**
 * <PERSON><PERSON>t to restructure user tables into three tables:
 * 1. users - Authentication data only
 * 2. user_profiles - Profile information
 * 3. user_payment_info - Payment-related data
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function restructureUserTables() {
  try {
    console.log('Starting user tables restructuring...');

    // Begin transaction
    await pool.query('BEGIN');

    // 1. Create backup tables
    console.log('Creating backup tables...');

    // Backup users table
    await pool.query(`
      CREATE TABLE users_backup AS
      SELECT * FROM users
    `);
    console.log('Created users_backup table');

    // Backup user_profiles table
    await pool.query(`
      CREATE TABLE user_profiles_backup AS
      SELECT * FROM user_profiles
    `);
    console.log('Created user_profiles_backup table');

    // 2. Restructure users table to contain only authentication data
    console.log('Restructuring users table...');

    // Drop the users table
    await pool.query(`DROP TABLE users CASCADE`);
    console.log('Dropped users table');

    // Create new users table with only authentication fields
    await pool.query(`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        last_login_at TIMESTAMP
      )
    `);
    console.log('Created new users table with authentication fields only');

    // 3. Restructure user_profiles table to contain all profile data
    console.log('Restructuring user_profiles table...');

    // Drop the user_profiles table
    await pool.query(`DROP TABLE user_profiles CASCADE`);
    console.log('Dropped user_profiles table');

    // Create new user_profiles table with profile fields (no payment data)
    await pool.query(`
      CREATE TABLE user_profiles (
        id SERIAL PRIMARY KEY,
        user_id INTEGER UNIQUE NOT NULL,
        name TEXT,
        bio TEXT,
        avatar TEXT,
        cover_photo TEXT,
        cover_photo_position CHARACTER VARYING,
        timezone TEXT,
        phone TEXT,
        location TEXT,
        website TEXT,
        facebook_url TEXT,
        twitter_url TEXT,
        instagram_url TEXT,
        linkedin_url TEXT,
        youtube_url TEXT,
        tiktok_url TEXT,
        specializations TEXT[],
        skills TEXT[],
        certifications TEXT[],
        education TEXT,
        experience TEXT,
        rating DOUBLE PRECISION,
        review_count INTEGER,
        teacher_since TIMESTAMP,
        is_teacher BOOLEAN DEFAULT FALSE,
        is_teacher BOOLEAN DEFAULT FALSE,
        show_teaching_sessions BOOLEAN DEFAULT TRUE,
        show_learning_sessions BOOLEAN DEFAULT FALSE,
        show_profile BOOLEAN DEFAULT TRUE,
        show_social_links BOOLEAN DEFAULT TRUE,
        show_contact BOOLEAN DEFAULT FALSE,
        privacy_settings JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('Created new user_profiles table with profile fields');

    // 4. Create user_payment_info table for payment data
    console.log('Creating user_payment_info table...');

    await pool.query(`
      CREATE TABLE user_payment_info (
        id SERIAL PRIMARY KEY,
        user_id INTEGER UNIQUE NOT NULL,
        stripe_connect_id CHARACTER VARYING,
        stripe_connect_onboarding_complete BOOLEAN DEFAULT FALSE,
        default_payment_method TEXT,
        payout_preferences JSONB,
        tax_information_submitted BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('Created user_payment_info table');

    // 5. Migrate data from backup tables to new tables
    console.log('Migrating data to new tables...');

    // Migrate authentication data to users table
    await pool.query(`
      INSERT INTO users (id, username, password, email, email_verified, created_at, updated_at)
      SELECT id, username, password, email, email_verified, created_at, updated_at
      FROM users_backup
    `);
    console.log('Migrated authentication data to users table');

    // Migrate profile data to user_profiles table (without payment data)
    await pool.query(`
      INSERT INTO user_profiles (
        user_id, name, bio, avatar, cover_photo, cover_photo_position, timezone, phone, location,
        website, facebook_url, twitter_url, instagram_url, linkedin_url, youtube_url, tiktok_url,
        specializations, skills, certifications, education, experience, rating, review_count,
        teacher_since, is_teacher, is_teacher,
        show_teaching_sessions, show_learning_sessions, show_profile, show_social_links, show_contact,
        privacy_settings, created_at, updated_at
      )
      SELECT
        ub.id,
        ub.name,
        COALESCE(up.bio, ub.bio),
        ub.avatar,
        COALESCE(up.cover_photo, ub.cover_photo),
        up.cover_photo_position,
        COALESCE(up.timezone, ub.timezone),
        COALESCE(up.phone, ub.phone),
        up.location,
        up.website,
        up.facebook_url,
        up.twitter_url,
        up.instagram_url,
        up.linkedin_url,
        up.youtube_url,
        up.tiktok_url,
        COALESCE(up.specializations, ub.specializations),
        COALESCE(up.skills, ub.skills),
        COALESCE(up.certifications, ub.certifications),
        up.education,
        COALESCE(up.experience, ub.experience),
        COALESCE(up.rating, ub.rating),
        COALESCE(up.review_count, ub.review_count),
        ub.teacher_since,
        ub.is_teacher,
        ub.is_teacher,
        up.show_teaching_sessions,
        up.show_learning_sessions,
        up.show_profile,
        up.show_social_links,
        up.show_contact,
        ub.privacy_settings,
        COALESCE(up.created_at, ub.created_at),
        COALESCE(up.updated_at, ub.updated_at)
      FROM users_backup ub
      LEFT JOIN user_profiles_backup up ON ub.id = up.user_id
    `);
    console.log('Migrated profile data to user_profiles table');

    // Migrate payment data to user_payment_info table
    await pool.query(`
      INSERT INTO user_payment_info (
        user_id, stripe_connect_id, stripe_connect_onboarding_complete, created_at, updated_at
      )
      SELECT
        id,
        stripe_connect_id,
        stripe_connect_onboarding_complete,
        created_at,
        updated_at
      FROM users_backup
      WHERE stripe_connect_id IS NOT NULL OR stripe_connect_onboarding_complete = true
    `);
    console.log('Migrated payment data to user_payment_info table');

    // For users without payment info, create empty records
    await pool.query(`
      INSERT INTO user_payment_info (user_id, created_at, updated_at)
      SELECT
        ub.id,
        ub.created_at,
        ub.updated_at
      FROM users_backup ub
      LEFT JOIN user_payment_info upi ON ub.id = upi.user_id
      WHERE upi.id IS NULL
    `);
    console.log('Created empty payment records for users without payment info');

    // 6. Update sequences
    await pool.query(`SELECT setval('users_id_seq', (SELECT MAX(id) FROM users))`);
    await pool.query(`SELECT setval('user_profiles_id_seq', (SELECT MAX(id) FROM user_profiles))`);
    await pool.query(`SELECT setval('user_payment_info_id_seq', (SELECT MAX(id) FROM user_payment_info))`);
    console.log('Updated sequences');

    // 7. Recreate foreign keys for other tables that referenced users
    console.log('Recreating foreign keys...');

    // Get all foreign keys that referenced users
    const foreignKeys = await pool.query(`
      SELECT tc.table_name, kcu.column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
      AND ccu.table_name = 'users_backup'
    `);

    // Recreate foreign keys
    for (const fk of foreignKeys.rows) {
      const tableName = fk.table_name;
      const columnName = fk.column_name;

      if (tableName !== 'user_profiles' && tableName !== 'user_payment_info') {
        console.log(`Recreating foreign key for ${tableName}.${columnName}`);

        // Drop existing constraint if any
        try {
          await pool.query(`
            ALTER TABLE ${tableName}
            DROP CONSTRAINT IF EXISTS ${tableName}_${columnName}_fkey
          `);
        } catch (error) {
          console.warn(`Warning: Could not drop constraint for ${tableName}.${columnName}:`, error.message);
        }

        // Add new constraint
        try {
          await pool.query(`
            ALTER TABLE ${tableName}
            ADD CONSTRAINT ${tableName}_${columnName}_fkey
            FOREIGN KEY (${columnName}) REFERENCES users(id)
          `);
        } catch (error) {
          console.error(`Error: Could not add constraint for ${tableName}.${columnName}:`, error.message);
          throw error;
        }
      }
    }

    // Commit transaction
    await pool.query('COMMIT');

    console.log('User tables restructuring completed successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await pool.query('ROLLBACK');
    console.error('Error restructuring user tables:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
restructureUserTables()
  .then(() => {
    console.log('User tables restructuring script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to restructure user tables:', err);
    process.exit(1);
  });
