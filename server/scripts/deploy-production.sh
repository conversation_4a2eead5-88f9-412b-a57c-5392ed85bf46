#!/bin/bash
set -e

# Production deployment script for Session Hub server
# Includes health checks and rollback capabilities

# Change to the project root directory
cd "$(dirname "$0")/.."

# Configuration
APP_NAME="sessionhub"
HEALTH_CHECK_URL="http://localhost:4004/api/health"
HEALTH_CHECK_RETRIES=20
HEALTH_CHECK_WAIT=5
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Create backup directory
mkdir -p $BACKUP_DIR

# Function to perform health check
check_health() {
  echo "Performing health check..."
  for i in $(seq 1 $HEALTH_CHECK_RETRIES); do
    echo "Health check attempt $i of $HEALTH_CHECK_RETRIES..."
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_CHECK_URL || echo "failed")
    
    if [ "$HEALTH_STATUS" == "200" ]; then
      echo "Health check passed!"
      return 0
    else
      echo "Health check failed with status: $HEALTH_STATUS. Retrying in $HEALTH_CHECK_WAIT seconds..."
      sleep $HEALTH_CHECK_WAIT
    fi
  done
  
  echo "Health check failed after $HEALTH_CHECK_RETRIES attempts."
  return 1
}

# Function to perform rollback
rollback() {
  echo "Deployment failed! Rolling back..."
  
  if [ -d "$BACKUP_DIR/dist" ]; then
    echo "Restoring from backup..."
    rm -rf ./dist
    cp -r $BACKUP_DIR/dist ./
    
    echo "Restarting with previous version..."
    pm2 restart $APP_NAME
    
    # Check health after rollback
    if check_health; then
      echo "Rollback successful!"
    else
      echo "WARNING: Rollback completed but health check failed. Manual intervention required!"
      exit 2
    fi
  else
    echo "ERROR: No backup found for rollback. Manual intervention required!"
    exit 3
  fi
}

# Backup current deployment
echo "Creating backup of current deployment..."
if [ -d "./dist" ]; then
  cp -r ./dist $BACKUP_DIR/
  cp package.json $BACKUP_DIR/
  cp .env $BACKUP_DIR/ 2>/dev/null || true
fi

# Install dependencies
echo "Installing dependencies..."
npm ci --production

# Build application
echo "Building application..."
npm run build

# Run database migrations if needed
if [ -f "./scripts/run-migrations.js" ]; then
  echo "Running database migrations..."
  node ./scripts/run-migrations.js
fi

# Restart application with PM2
echo "Restarting application..."
if pm2 list | grep -q "$APP_NAME"; then
  pm2 restart $APP_NAME
else
  bash ./scripts/production-startup.sh
fi

# Perform health check
if check_health; then
  echo "Deployment successful!"
else
  rollback
  exit 1
fi

# Clean up old backups (keep last 5)
echo "Cleaning up old backups..."
cd ./backups && ls -t | tail -n +6 | xargs rm -rf 2>/dev/null || true

echo "Deployment completed successfully!" 