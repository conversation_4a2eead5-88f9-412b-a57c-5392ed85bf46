/**
 * <PERSON><PERSON><PERSON> to run the teacher image upload process
 *
 * This script:
 * 1. Makes a request to the API endpoint to upload teacher images
 * 2. Logs the results
 */

import fetch from 'node-fetch';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: resolve(__dirname, '../../.env') });

// API endpoint
const API_URL = process.env.API_URL || 'http://localhost:4005';

async function runTeacherImageUpload() {
  try {
    console.log('Starting teacher image upload process...');

    // Make a request to the API endpoint
    const response = await fetch(`${API_URL}/api/supabase/upload-teacher-images`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const result = await response.json();

    console.log('Teacher image upload process completed');
    console.log('Results:', JSON.stringify(result, null, 2));

    return result;
  } catch (error) {
    console.error('Error running teacher image upload:', error);
    throw error;
  }
}

// Run the script
runTeacherImageUpload()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });

export { runTeacherImageUpload };
