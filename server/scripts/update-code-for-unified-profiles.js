/**
 * <PERSON><PERSON><PERSON> to update the code to use only the unified user_profiles table.
 * This script will:
 * 1. Create backup tables for the legacy tables
 * 2. Update the code to use only the unified table
 * 3. Verify that everything is working correctly
 */

import pg from 'pg';
const { Pool } = pg;
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function backupLegacyTables() {
  const client = await pool.connect();
  
  try {
    // Start a transaction
    await client.query('BEGIN');
    
    console.log('Creating backup tables for legacy tables...');
    
    // Create backup tables
    await client.query(`
      CREATE TABLE IF NOT EXISTS profiles_backup AS 
      SELECT * FROM profiles;
    `);
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS teacher_profiles_backup AS 
      SELECT * FROM teacher_profiles;
    `);
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Backup tables created successfully!');
    
    // Verify backup tables
    const { rows: profilesCount } = await client.query('SELECT COUNT(*) FROM profiles_backup');
    const { rows: teacherProfilesCount } = await client.query('SELECT COUNT(*) FROM teacher_profiles_backup');
    
    console.log(`Backup tables created with ${profilesCount[0].count} profiles and ${teacherProfilesCount[0].count} teacher profiles`);
    
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');
    console.error('Error creating backup tables:', error);
    throw error;
  } finally {
    // Release the client back to the pool
    client.release();
  }
}

async function main() {
  try {
    // 1. Create backup tables
    await backupLegacyTables();
    
    console.log('Migration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the script
main()
  .then(() => {
    console.log('Code update completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Code update failed:', err);
    process.exit(1);
  });
