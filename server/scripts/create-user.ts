import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import bcrypt from 'bcrypt';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Create a new pool with the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function createUser() {
  try {
    console.log('Connecting to database...');
    console.log('Using connection string:', process.env.DATABASE_URL ? 'From environment' : 'Not set');
    const client = await pool.connect();

    console.log('Connection successful!');

    // Create a new user
    const username = 'testuser';
    const email = '<EMAIL>';
    const password = 'password123';

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Insert the user
    const insertResult = await client.query(`
      INSERT INTO auth.users (username, email, password, email_verified, created_at, updated_at)
      VALUES ($1, $2, $3, true, NOW(), NOW())
      RETURNING id, username, email
    `, [username, email, hashedPassword]);

    if (insertResult.rows.length > 0) {
      const user = insertResult.rows[0];
      console.log('User created successfully:');
      console.log(JSON.stringify(user, null, 2));

      // Create a profile for the user
      const profileResult = await client.query(`
        INSERT INTO profiles.user_profiles 
        (user_id, bio, timezone, location, is_teacher, is_teacher, created_at, updated_at)
        VALUES ($1, 'Test user bio', 'UTC', 'Test Location', true, true, NOW(), NOW())
        RETURNING *
      `, [user.id]);

      if (profileResult.rows.length > 0) {
        console.log('Profile created successfully:');
        console.log(JSON.stringify(profileResult.rows[0], null, 2));
      } else {
        console.log('Failed to create profile');
      }
    } else {
      console.log('Failed to create user');
    }

    client.release();
  } catch (error) {
    console.error('Error creating user:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

createUser();
