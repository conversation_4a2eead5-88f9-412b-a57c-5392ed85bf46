// Script to list all sessions in the database

const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

async function listSessions() {
  console.log("Connecting to the database...");
  
  const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'sessionhub',
    password: process.env.DB_PASSWORD,
    port: parseInt(process.env.DB_PORT || '5432', 10),
  });
  
  try {
    console.log("Executing query to fetch sessions...");
    
    // Query to get basic session info
    const result = await pool.query(`
      SELECT 
        s.id, s.title, s.teacher_id, s.price, s.duration, s.date,
        s.is_public, s.created_at, s.updated_at,
        u.name as teacher_name
      FROM 
        sessions s
      LEFT JOIN 
        users u ON s.teacher_id = u.id
      ORDER BY 
        s.updated_at DESC
      LIMIT 10
    `);
    
    if (result.rows.length === 0) {
      console.log("No sessions found in the database.");
      return;
    }
    
    console.log(`Found ${result.rows.length} sessions:`);
    console.log("=".repeat(80));
    
    // Display each session with formatting
    result.rows.forEach((session, index) => {
      console.log(`Session #${index+1}:`);
      console.log(`- ID: ${session.id}`);
      console.log(`- Title: ${session.title}`);
      console.log(`- Teacher: ${session.teacher_name} (ID: ${session.teacher_id})`);
      console.log(`- Price: $${session.price}`);
      console.log(`- Duration: ${session.duration} minutes`);
      console.log(`- Date: ${new Date(session.date).toLocaleString()}`);
      console.log(`- Public: ${session.is_public}`);
      console.log(`- Created: ${new Date(session.created_at).toLocaleString()}`);
      console.log(`- Updated: ${new Date(session.updated_at).toLocaleString()}`);
      console.log("-".repeat(80));
    });
    
    // Additional query to check table structure
    console.log("Checking database schema for sessions table...");
    const schemaResult = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'sessions'
      ORDER BY ordinal_position
    `);
    
    console.log("Sessions table schema:");
    schemaResult.rows.forEach(col => {
      console.log(`${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    console.log("=".repeat(80));
    
  } catch (error) {
    console.error("Error fetching sessions:", error);
  } finally {
    console.log("Closing database connection...");
    await pool.end();
  }
}

// Run the function
listSessions().catch(err => {
  console.error("Script failed with error:", err);
  process.exit(1);
}); 