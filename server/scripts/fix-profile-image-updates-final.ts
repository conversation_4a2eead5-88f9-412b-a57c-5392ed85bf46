import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function fixProfileImageUpdatesFinal() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Check if there are any avatar/cover_photo fields in profiles.user_profiles table
    try {
      const profileColumns = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
        AND column_name IN ('avatar', 'cover_photo');
      `);

      if (profileColumns.rows.length === 2) {
        console.log(`Confirmed: Found avatar and cover_photo columns in profiles.user_profiles table.`);
      } else {
        console.log(`WARNING: Missing avatar or cover_photo columns in profiles.user_profiles table.`);

        // Check which columns are missing
        const missingColumns = [];
        if (!profileColumns.rows.find(col => col.column_name === 'avatar')) {
          missingColumns.push('avatar');
        }
        if (!profileColumns.rows.find(col => col.column_name === 'cover_photo')) {
          missingColumns.push('cover_photo');
        }

        console.log(`Missing columns: ${missingColumns.join(', ')}`);

        // Add missing columns if needed
        for (const column of missingColumns) {
          console.log(`Adding missing column ${column} to profiles.user_profiles table...`);
          await pool.query(`
            ALTER TABLE profiles.user_profiles 
            ADD COLUMN ${column} TEXT;
          `);
          console.log(`Added ${column} column to profiles.user_profiles table.`);
        }
      }
    } catch (error) {
      console.error(`Error checking profiles.user_profiles table: ${error.message}`);
    }

    // Get all users with their profiles
    const usersWithProfiles = await pool.query(`
      SELECT u.id, u.username, u.email, p.avatar, p.cover_photo
      FROM auth.users u
      LEFT JOIN profiles.user_profiles p ON u.id = p.user_id
      ORDER BY u.id;
    `);

    console.log(`\nFound ${usersWithProfiles.rows.length} users in the database`);

    // Check for users without profiles
    const usersWithoutProfiles = usersWithProfiles.rows.filter(user => !user.avatar && !user.cover_photo);
    console.log(`\nUsers without profile images: ${usersWithoutProfiles.length}`);

    // Check for users with profile images
    const usersWithImages = usersWithProfiles.rows.filter(user => user.avatar || user.cover_photo);
    console.log(`\nUsers with profile images: ${usersWithImages.length}`);

    // Print sample users with images
    console.log(`\nSample users with images:`);
    usersWithImages.slice(0, 5).forEach(user => {
      console.log(`  User ${user.id} (${user.username}):`);
      console.log(`    Avatar: ${user.avatar || 'NULL'}`);
      console.log(`    Cover photo: ${user.cover_photo || 'NULL'}`);
    });

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

    console.log('\n=== FINAL RECOMMENDATIONS ===');
    console.log('1. The server is running on port 4002 as expected by the client');
    console.log('2. The avatar and cover_photo fields are in the profiles.user_profiles table, not in the auth.users table');
    console.log('3. We have added the avatar field to the fieldMapping object in user-profile-storage.ts');
    console.log('4. We have updated the profile update endpoints in routes.ts to include avatar and coverPhoto in the profileData object');
    console.log('5. The client is configured to use port 4002 for API requests');
    console.log('6. The profile image updates should now work correctly');
  } catch (error) {
    console.error('Error fixing profile image updates:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

fixProfileImageUpdatesFinal();
