import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function checkUserProfile11() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // First, check the table structure
    console.log('Checking auth.users table structure...');
    const userColumns = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'auth' AND table_name = 'users'
      ORDER BY column_name;
    `);

    console.log('auth.users columns:');
    userColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    console.log('\nChecking profiles.user_profiles table structure...');
    const profileColumns = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'profiles' AND table_name = 'user_profiles'
      ORDER BY column_name;
    `);

    console.log('profiles.user_profiles columns:');
    profileColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}`);
    });

    // Check user 11 in auth.users table
    console.log('\nChecking user 11 in auth.users table...');
    const userResult = await pool.query(`
      SELECT *
      FROM auth.users
      WHERE id = 11;
    `);

    if (userResult.rows.length === 0) {
      console.log('User 11 not found in auth.users table');
      return;
    }

    const user = userResult.rows[0];
    console.log('User 11 data from auth.users:');
    console.log(JSON.stringify(user, null, 2));

    // Check user 11 in profiles.user_profiles table
    console.log('\nChecking user 11 in profiles.user_profiles table...');
    const profileResult = await pool.query(`
      SELECT *
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (profileResult.rows.length === 0) {
      console.log('User 11 not found in profiles.user_profiles table');
    } else {
      const profile = profileResult.rows[0];
      console.log('User 11 profile data from profiles.user_profiles:');
      console.log(JSON.stringify(profile, null, 2));
    }

    // Check if avatar and cover_photo columns exist in auth.users
    const hasAvatarInUsers = userColumns.rows.some(col => col.column_name === 'avatar');
    const hasCoverPhotoInUsers = userColumns.rows.some(col => col.column_name === 'cover_photo');

    // Check if avatar and cover_photo columns exist in profiles.user_profiles
    const hasAvatarInProfiles = profileColumns.rows.some(col => col.column_name === 'avatar');
    const hasCoverPhotoInProfiles = profileColumns.rows.some(col => col.column_name === 'cover_photo');

    console.log('\nImage columns status:');
    console.log(`- Avatar column in auth.users: ${hasAvatarInUsers ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Cover photo column in auth.users: ${hasCoverPhotoInUsers ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Avatar column in profiles.user_profiles: ${hasAvatarInProfiles ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Cover photo column in profiles.user_profiles: ${hasCoverPhotoInProfiles ? 'EXISTS' : 'MISSING'}`);

    // Add missing columns if needed
    if (!hasAvatarInUsers) {
      console.log('\nAdding avatar column to auth.users table...');
      await pool.query(`
        ALTER TABLE auth.users
        ADD COLUMN avatar TEXT;
      `);
      console.log('Added avatar column to auth.users table');
    }

    if (!hasCoverPhotoInUsers) {
      console.log('\nAdding cover_photo column to auth.users table...');
      await pool.query(`
        ALTER TABLE auth.users
        ADD COLUMN cover_photo TEXT;
      `);
      console.log('Added cover_photo column to auth.users table');
    }

    if (!hasAvatarInProfiles) {
      console.log('\nAdding avatar column to profiles.user_profiles table...');
      await pool.query(`
        ALTER TABLE profiles.user_profiles
        ADD COLUMN avatar TEXT;
      `);
      console.log('Added avatar column to profiles.user_profiles table');
    }

    if (!hasCoverPhotoInProfiles) {
      console.log('\nAdding cover_photo column to profiles.user_profiles table...');
      await pool.query(`
        ALTER TABLE profiles.user_profiles
        ADD COLUMN cover_photo TEXT;
      `);
      console.log('Added cover_photo column to profiles.user_profiles table');
    }

    // Fix missing avatar in auth.users table
    if (hasAvatarInUsers) {
      const avatarValue = user.avatar;

      if (!avatarValue || avatarValue === '') {
        console.log('\nUser 11 has no avatar in auth.users table. Adding a default avatar...');

        // Generate a default avatar URL
        const defaultAvatarUrl = 'https://sessionhub-images.s3.amazonaws.com/profiles/avatars/default-11.jpg';

        // Update the user record
        await pool.query(`
          UPDATE auth.users
          SET avatar = $1
          WHERE id = 11;
        `, [defaultAvatarUrl]);

        console.log(`Updated user 11 with default avatar URL: ${defaultAvatarUrl}`);
      }
    }

    // Fix missing avatar in profiles.user_profiles table
    if (hasAvatarInProfiles && profileResult.rows.length > 0) {
      const profile = profileResult.rows[0];
      const avatarValue = profile.avatar;

      if (!avatarValue || avatarValue === '') {
        console.log('\nUser 11 has no avatar in profiles.user_profiles table. Adding a default avatar...');

        // Get the avatar from auth.users if it exists
        let userAvatar = null;
        if (hasAvatarInUsers) {
          const userAvatarResult = await pool.query(`
            SELECT avatar FROM auth.users WHERE id = 11;
          `);
          userAvatar = userAvatarResult.rows[0]?.avatar;
        }

        const defaultAvatarUrl = userAvatar || 'https://sessionhub-images.s3.amazonaws.com/profiles/avatars/default-11.jpg';

        // Update the profile record
        await pool.query(`
          UPDATE profiles.user_profiles
          SET avatar = $1
          WHERE user_id = 11;
        `, [defaultAvatarUrl]);

        console.log(`Updated profile for user 11 with avatar URL: ${defaultAvatarUrl}`);
      }
    } else if (hasAvatarInProfiles && profileResult.rows.length === 0) {
      // Create a new profile for user 11
      console.log('\nCreating a new profile for user 11...');

      // Get the avatar from auth.users if it exists
      let userAvatar = null;
      let userCoverPhoto = null;

      if (hasAvatarInUsers) {
        const userImageResult = await pool.query(`
          SELECT avatar FROM auth.users WHERE id = 11;
        `);
        userAvatar = userImageResult.rows[0]?.avatar;
      }

      if (hasCoverPhotoInUsers) {
        const userCoverResult = await pool.query(`
          SELECT cover_photo FROM auth.users WHERE id = 11;
        `);
        userCoverPhoto = userCoverResult.rows[0]?.cover_photo;
      }

      const defaultAvatarUrl = userAvatar || 'https://sessionhub-images.s3.amazonaws.com/profiles/avatars/default-11.jpg';

      // Create the profile record
      await pool.query(`
        INSERT INTO profiles.user_profiles (user_id, avatar, cover_photo)
        VALUES (11, $1, $2);
      `, [defaultAvatarUrl, userCoverPhoto]);

      console.log(`Created new profile for user 11 with avatar URL: ${defaultAvatarUrl}`);
    }

    // Verify the changes
    console.log('\nVerifying changes...');

    // Check user 11 in auth.users table again
    const updatedUserResult = await pool.query(`
      SELECT *
      FROM auth.users
      WHERE id = 11;
    `);

    const updatedUser = updatedUserResult.rows[0];
    console.log('Updated user 11 data from auth.users:');
    console.log(JSON.stringify(updatedUser, null, 2));

    // Check user 11 in profiles.user_profiles table again
    const updatedProfileResult = await pool.query(`
      SELECT *
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (updatedProfileResult.rows.length > 0) {
      const updatedProfile = updatedProfileResult.rows[0];
      console.log('Updated user 11 profile data from profiles.user_profiles:');
      console.log(JSON.stringify(updatedProfile, null, 2));
    }

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

  } catch (error) {
    console.error('Error checking user profile:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

checkUserProfile11();
