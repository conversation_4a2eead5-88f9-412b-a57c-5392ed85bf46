import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Create a new pool with the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function listUsers() {
  try {
    console.log('Connecting to database...');
    console.log('Using connection string:', process.env.DATABASE_URL ? 'From environment' : 'Not set');
    const client = await pool.connect();

    console.log('Fetching users...');
    const result = await client.query('SELECT * FROM auth.users');

    console.log('\nUsers in database:');
    console.log('------------------');
    result.rows.forEach(user => {
      console.log(`ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);
    });

    client.release();
  } catch (error) {
    console.error('Error listing users:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

listUsers();
