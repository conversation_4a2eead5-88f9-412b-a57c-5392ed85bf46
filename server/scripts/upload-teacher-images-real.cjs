const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Initialize Supabase with service role key
const supabaseUrl = process.env.SUPABASE_URL || 'https://frksndjujrbjhlrcjvtf.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
    console.error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
    console.log('Available env vars:', Object.keys(process.env).filter(k => k.includes('SUPABASE')));
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// High-quality teacher avatar images from Unsplash
const teacherAvatars = [
    'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1554151228-14d9def656e4?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&auto=format&fit=crop&q=80'
];

// High-quality teacher cover photos from Unsplash
const teacherCovers = [
    'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1579546929662-711aa81148cf?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1559251606-c623743a6d76?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1566041510639-8d95a2490bfb?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1579548122080-c35fd6820ecb?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1523580494863-6f3031224c94?w=1200&auto=format&fit=crop&q=80',
    'https://images.unsplash.com/photo-1545558014-8692077e9b5c?w=1200&auto=format&fit=crop&q=80'
];

// Main function to upload images and update teacher records
async function uploadTeacherImages() {
    try {
        console.log('Starting teacher image upload process...');

        // 1. Check if the profiles bucket exists
        const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

        if (bucketsError) {
            console.error('Error listing buckets:', bucketsError);
            return;
        }

        const profilesBucket = buckets.find(b => b.name === 'profiles');
        if (!profilesBucket) {
            console.error('Profiles bucket does not exist. Creating it...');

            // Create the profiles bucket with public access
            const { error: createError } = await supabase.storage.createBucket('profiles', {
                public: true,
                allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
                fileSizeLimit: 5242880 // 5MB
            });

            if (createError) {
                console.error('Error creating profiles bucket:', createError);
                return;
            }

            console.log('Profiles bucket created successfully');
        }

        // 2. Create the folder structure if it doesn't exist
        const folders = ['avatars', 'covers'];
        for (const folder of folders) {
            const emptyFile = new Uint8Array(0);
            const { error: folderError } = await supabase.storage
                .from('profiles')
                .upload(`${folder}/.keep`, emptyFile, {
                    contentType: 'text/plain',
                    upsert: true
                });

            if (folderError && !folderError.message.includes('already exists')) {
                console.log(`Folder profiles/${folder} setup:`, folderError.message);
            } else {
                console.log(`Folder profiles/${folder} is ready`);
            }
        }

        // 3. Get all teachers from the database
        const { data: teachers, error: teachersError } = await supabase
            .from('user_profiles')
            .select('id, user_id, name, avatar, cover_photo')
            .eq('is_teacher', true);

        if (teachersError) {
            console.error('Error fetching teachers:', teachersError);
            return;
        }

        console.log(`Found ${teachers.length} teachers to update`);

        // 4. Upload images and update teacher records
        const results = {
            success: 0,
            failed: 0,
            details: []
        };

        for (const [index, teacher] of teachers.entries()) {
            try {
                console.log(`\nProcessing teacher ${index + 1}/${teachers.length}: ${teacher.name || teacher.id}`);

                // Select avatar and cover images for this teacher
                const avatarImageUrl = teacherAvatars[index % teacherAvatars.length];
                const coverImageUrl = teacherCovers[index % teacherCovers.length];

                // Generate unique filenames
                const timestamp = Date.now();
                const avatarFilename = `avatar_${teacher.id}_${timestamp}.jpg`;
                const coverFilename = `cover_${teacher.id}_${timestamp}.jpg`;

                // Download and upload avatar image
                console.log(`  Downloading avatar from: ${avatarImageUrl}`);
                const avatarResponse = await fetch(avatarImageUrl);
                if (!avatarResponse.ok) {
                    throw new Error(`Failed to download avatar: ${avatarResponse.statusText}`);
                }
                const avatarBuffer = await avatarResponse.buffer();

                console.log(`  Uploading avatar as: ${avatarFilename}`);
                const { data: avatarData, error: avatarError } = await supabase.storage
                    .from('profiles')
                    .upload(`avatars/${avatarFilename}`, avatarBuffer, {
                        contentType: 'image/jpeg',
                        upsert: true
                    });

                if (avatarError) {
                    throw new Error(`Avatar upload failed: ${avatarError.message}`);
                }

                // Get the public URL for the avatar
                const { data: avatarUrl } = supabase.storage
                    .from('profiles')
                    .getPublicUrl(`avatars/${avatarFilename}`);

                // Download and upload cover image
                console.log(`  Downloading cover from: ${coverImageUrl}`);
                const coverResponse = await fetch(coverImageUrl);
                if (!coverResponse.ok) {
                    throw new Error(`Failed to download cover: ${coverResponse.statusText}`);
                }
                const coverBuffer = await coverResponse.buffer();

                console.log(`  Uploading cover as: ${coverFilename}`);
                const { data: coverData, error: coverError } = await supabase.storage
                    .from('profiles')
                    .upload(`covers/${coverFilename}`, coverBuffer, {
                        contentType: 'image/jpeg',
                        upsert: true
                    });

                if (coverError) {
                    throw new Error(`Cover upload failed: ${coverError.message}`);
                }

                // Get the public URL for the cover
                const { data: coverUrl } = supabase.storage
                    .from('profiles')
                    .getPublicUrl(`covers/${coverFilename}`);

                // Update the teacher record with the new image URLs
                console.log(`  Updating teacher profile with new image URLs...`);
                const { error: updateError } = await supabase
                    .from('user_profiles')
                    .update({
                        avatar: avatarUrl.publicUrl,
                        cover_photo: coverUrl.publicUrl,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', teacher.id);

                if (updateError) {
                    throw new Error(`Database update failed: ${updateError.message}`);
                }

                console.log(`  ✅ Successfully updated teacher ${teacher.name || teacher.id}`);
                results.success++;
                results.details.push({
                    teacherId: teacher.id,
                    teacherName: teacher.name,
                    status: 'success',
                    avatarUrl: avatarUrl.publicUrl,
                    coverUrl: coverUrl.publicUrl
                });

            } catch (error) {
                console.error(`  ❌ Error updating teacher ${teacher.name || teacher.id}:`, error.message);
                results.failed++;
                results.details.push({
                    teacherId: teacher.id,
                    teacherName: teacher.name,
                    status: 'error',
                    message: error.message
                });
                continue;
            }
        }

        console.log('\n=== Teacher Image Upload Results ===');
        console.log(`Total teachers processed: ${teachers.length}`);
        console.log(`Successful updates: ${results.success}`);
        console.log(`Failed updates: ${results.failed}`);

        if (results.failed > 0) {
            console.log('\nFailed teachers:');
            results.details.filter(d => d.status === 'error').forEach(detail => {
                console.log(`  - ${detail.teacherName || detail.teacherId}: ${detail.message}`);
            });
        }

        return results;
    } catch (error) {
        console.error('Unhandled error in uploadTeacherImages:', error);
        throw error;
    }
}

// Export for use in other scripts
module.exports = { uploadTeacherImages };

// Run the script if called directly
if (require.main === module) {
    uploadTeacherImages()
        .then(results => {
            console.log('\nUpload completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('Upload failed:', error);
            process.exit(1);
        });
} 