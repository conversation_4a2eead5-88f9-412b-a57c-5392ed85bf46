/**
 * <PERSON><PERSON>t to clean up redundant and legacy tables in the database
 */

import pg from 'pg';
const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function cleanupDatabase() {
  try {
    console.log('Starting database cleanup...');

    // Begin transaction
    await pool.query('BEGIN');

    // 1. First, let's check if there's any data in the backup tables that's not in the main tables
    console.log('\nChecking for unique data in backup tables...');

    // Check profiles_backup
    const profilesBackupCheck = await pool.query(`
      SELECT pb.*
      FROM profiles_backup pb
      LEFT JOIN user_profiles up ON pb.user_id = up.user_id
      WHERE up.id IS NULL
    `);

    if (profilesBackupCheck.rows.length > 0) {
      console.log(`Found ${profilesBackupCheck.rows.length} profiles in profiles_backup that are not in user_profiles`);

      // Migrate missing profiles to user_profiles
      for (const profile of profilesBackupCheck.rows) {
        console.log(`Migrating profile for user ${profile.user_id} to user_profiles`);

        await pool.query(`
          INSERT INTO user_profiles (
            user_id, bio, timezone, phone, location, website, facebook_url, twitter_url, instagram_url, linkedin_url,
            cover_photo, specializations, skills, certifications, education, experience, availability, rating, review_count,
            show_teaching_sessions, show_learning_sessions, show_profile, show_social_links, show_contact, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
            $11, $12, $13, $14, $15, $16, $17, $18, $19,
            $20, $21, $22, $23, $24, NOW(), NOW()
          )
        `, [
          profile.user_id, profile.bio, profile.timezone, profile.phone, profile.location, profile.website,
          profile.facebook_url, profile.twitter_url, profile.instagram_url, profile.linkedin_url,
          profile.cover_photo, profile.specializations, profile.skills, profile.certifications,
          profile.education, profile.experience, profile.availability, profile.rating, profile.review_count,
          profile.show_teaching_sessions, profile.show_learning_sessions, profile.show_profile,
          profile.show_social_links, profile.show_contact
        ]);
      }
    } else {
      console.log('No unique profiles found in profiles_backup');
    }

    // Check teacher_profiles_backup
    const teacherProfilesBackupCheck = await pool.query(`
      SELECT ipb.*
      FROM teacher_profiles_backup ipb
      LEFT JOIN user_profiles up ON ipb.user_id = up.user_id
      WHERE up.id IS NULL
    `);

    if (teacherProfilesBackupCheck.rows.length > 0) {
      console.log(`Found ${teacherProfilesBackupCheck.rows.length} profiles in teacher_profiles_backup that are not in user_profiles`);

      // Migrate missing profiles to user_profiles
      for (const profile of teacherProfilesBackupCheck.rows) {
        console.log(`Migrating teacher profile for user ${profile.user_id} to user_profiles`);

        await pool.query(`
          INSERT INTO user_profiles (
            user_id, specializations, skills, certifications, experience, education, website, facebook_url, twitter_url,
            instagram_url, linkedin_url, rating, review_count, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9,
            $10, $11, $12, $13, $14, $15
          )
        `, [
          profile.user_id, profile.specializations, profile.skills, profile.certifications,
          profile.experience, profile.education, profile.website, profile.facebook_url, profile.twitter_url,
          profile.instagram_url, profile.linkedin_url, profile.rating, profile.review_count,
          profile.created_at, profile.updated_at
        ]);
      }
    } else {
      console.log('No unique profiles found in teacher_profiles_backup');
    }

    // Check scheduled_messages_old
    const scheduledMessagesOldCheck = await pool.query(`
      SELECT smo.*
      FROM scheduled_messages_old smo
      LEFT JOIN scheduled_messages sm ON smo.id = sm.id
      WHERE sm.id IS NULL
    `);

    if (scheduledMessagesOldCheck.rows.length > 0) {
      console.log(`Found ${scheduledMessagesOldCheck.rows.length} messages in scheduled_messages_old that are not in scheduled_messages`);

      // Migrate missing messages to scheduled_messages
      for (const message of scheduledMessagesOldCheck.rows) {
        console.log(`Migrating scheduled message ${message.id} to scheduled_messages`);

        // Convert status
        let status = 'pending';
        if (message.sent) {
          status = 'sent';
        } else if (message.error) {
          status = 'error';
        }

        await pool.query(`
          INSERT INTO scheduled_messages (
            id, conversation_id, sender_id, content, scheduled_time, status, created_at, sent_at
          ) VALUES (
            $1, $2, $3, $4, $5, $6, NOW(), $7
          )
        `, [
          message.id, message.conversation_id, message.sender_id, message.content,
          message.send_at, status, message.sent_at
        ]);
      }
    } else {
      console.log('No unique messages found in scheduled_messages_old');
    }

    // 2. Now let's check empty tables that can be safely dropped
    console.log('\nChecking for empty tables that can be safely dropped...');

    const emptyTables = [
      'email_verification_tokens',
      'global_availability',
      'teacher_availability',
      'notification_preferences',
      'payouts',
      'platform_fees',
      'reviews',
      'time_slots',
      'user_devices'
    ];

    for (const tableName of emptyTables) {
      const countResult = await pool.query(`SELECT COUNT(*) FROM "${tableName}"`);
      const count = parseInt(countResult.rows[0].count);

      if (count === 0) {
        console.log(`Table ${tableName} is empty and can be safely dropped`);
        // We won't actually drop these tables yet, just identify them
      } else {
        console.log(`Table ${tableName} has ${count} rows and should not be dropped`);
      }
    }

    // 3. Now let's drop the backup tables that are no longer needed
    console.log('\nDropping backup tables...');

    // We'll only drop tables if explicitly confirmed
    const confirmDrop = true; // Set to true to actually drop tables

    if (confirmDrop) {
      // Drop profiles_backup
      await pool.query('DROP TABLE IF EXISTS profiles_backup');
      console.log('Dropped profiles_backup table');

      // Drop teacher_profiles_backup
      await pool.query('DROP TABLE IF EXISTS teacher_profiles_backup');
      console.log('Dropped teacher_profiles_backup table');

      // Drop scheduled_messages_old
      await pool.query('DROP TABLE IF EXISTS scheduled_messages_old');
      console.log('Dropped scheduled_messages_old table');

      // Check if sessions_complete is a view
      const sessionsCompleteCheck = await pool.query(`
        SELECT table_name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'sessions_complete'
      `);

      if (sessionsCompleteCheck.rows.length > 0) {
        const tableType = sessionsCompleteCheck.rows[0].table_type;

        if (tableType === 'VIEW') {
          await pool.query('DROP VIEW IF EXISTS sessions_complete');
          console.log('Dropped sessions_complete view');
        } else if (tableType === 'BASE TABLE') {
          await pool.query('DROP TABLE IF EXISTS sessions_complete');
          console.log('Dropped sessions_complete table');
        } else {
          console.log(`sessions_complete is a ${tableType}, not dropping`);
        }
      }

      // Check if sessions_view exists
      const sessionsViewCheck = await pool.query(`
        SELECT table_name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'sessions_view'
      `);

      if (sessionsViewCheck.rows.length > 0) {
        const tableType = sessionsViewCheck.rows[0].table_type;

        if (tableType === 'VIEW') {
          await pool.query('DROP VIEW IF EXISTS sessions_view');
          console.log('Dropped sessions_view view');
        } else if (tableType === 'BASE TABLE') {
          await pool.query('DROP TABLE IF EXISTS sessions_view');
          console.log('Dropped sessions_view table');
        } else {
          console.log(`sessions_view is a ${tableType}, not dropping`);
        }
      }
    } else {
      console.log('Skipping table drops (confirmDrop is set to false)');
    }

    // Commit transaction
    await pool.query('COMMIT');

    console.log('\nDatabase cleanup completed successfully!');
  } catch (error) {
    // Rollback transaction in case of error
    await pool.query('ROLLBACK');
    console.error('Error cleaning up database:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
cleanupDatabase()
  .then(() => {
    console.log('Database cleanup script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to clean up database:', err);
    process.exit(1);
  });
