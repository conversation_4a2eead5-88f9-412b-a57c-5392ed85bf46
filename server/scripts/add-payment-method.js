import pg from 'pg';
const { Pool } = pg;

// Create a connection to the database
const pool = new Pool({
  connectionString: 'process.env.DATABASE_URL'
});

async function addPaymentMethod() {
  try {
    // First, check if the table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'teacher_payment_methods'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      console.log('Table teacher_payment_methods does not exist. Creating it...');

      // Create the table if it doesn't exist
      await pool.query(`
        CREATE TABLE teacher_payment_methods (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          payment_type VARCHAR(50) NOT NULL,
          is_default BOOLEAN NOT NULL DEFAULT false,
          details JSONB NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );
      `);

      console.log('Table created successfully.');
    }

    // Add a payment method for the teacher (ID: 6)
    const result = await pool.query(`
      INSERT INTO teacher_payment_methods
      (user_id, payment_type, is_default, details)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [6, 'paypal', true, JSON.stringify({email: '<EMAIL>'})]);

    console.log('Payment method added successfully:', result.rows[0]);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

addPaymentMethod();
