import { Pool } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Create a new pool with the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkUsers() {
  try {
    console.log('Connecting to database...');
    console.log('Using connection string:', process.env.DATABASE_URL ? 'From environment' : 'Not set');
    const client = await pool.connect();

    console.log('Connection successful!');

    // Get all users
    const usersResult = await client.query(`
      SELECT id, username, email, email_verified, created_at, updated_at
      FROM auth.users
      ORDER BY id
    `);

    console.log(`Found ${usersResult.rows.length} users:`);
    usersResult.rows.forEach(user => {
      console.log(`ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);
    });

    // Get user with username 'ynachum'
    const ynachumResult = await client.query(`
      SELECT id, username, email, password, email_verified, created_at, updated_at
      FROM auth.users
      WHERE username = 'ynachum'
    `);

    if (ynachumResult.rows.length > 0) {
      const user = ynachumResult.rows[0];
      console.log('\nFound user ynachum:');
      console.log(`ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);
      console.log(`Password hash: ${user.password.substring(0, 20)}...`);
    } else {
      console.log('\nUser ynachum not found');
    }

    client.release();
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

checkUsers();
