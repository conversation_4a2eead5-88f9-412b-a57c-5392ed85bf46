import { db } from '../db';

async function checkSessions() {
  try {
    console.log('Connecting to database...');
    
    // Check if sessions exist
    const sessionsResult = await db.query(`
      SELECT id, title, is_public, teacher_id
      FROM content.sessions
      ORDER BY id
    `);
    
    console.log(`Found ${sessionsResult.rows.length} sessions in the database`);
    
    // Print the first 10 sessions
    console.log('First 10 sessions:');
    sessionsResult.rows.slice(0, 10).forEach(session => {
      console.log(`ID: ${session.id}, Title: ${session.title}, Public: ${session.is_public}, Teacher: ${session.teacher_id}`);
    });
    
    // Count public vs private sessions
    const publicSessions = sessionsResult.rows.filter(s => s.is_public === true);
    const privateSessions = sessionsResult.rows.filter(s => s.is_public === false);
    
    console.log(`Public sessions: ${publicSessions.length}`);
    console.log(`Private sessions: ${privateSessions.length}`);
    
    // Check if there are any sessions with null is_public
    const nullPublicSessions = sessionsResult.rows.filter(s => s.is_public === null);
    console.log(`Sessions with null is_public: ${nullPublicSessions.length}`);
    
    // Exit the process
    process.exit(0);
  } catch (error) {
    console.error('Error checking sessions:', error);
    process.exit(1);
  }
}

checkSessions();
