import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function fixAvatarField() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Check user 11 in profiles.user_profiles table
    console.log('Checking user 11 in profiles.user_profiles table...');
    const profileResult = await pool.query(`
      SELECT * 
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (profileResult.rows.length === 0) {
      console.log('User 11 not found in profiles.user_profiles table');
      return;
    }

    const profile = profileResult.rows[0];
    console.log('User 11 profile data from profiles.user_profiles:');
    console.log(JSON.stringify({
      id: profile.id,
      user_id: profile.user_id,
      name: profile.name,
      avatar: profile.avatar,
      cover_photo: profile.cover_photo
    }, null, 2));

    // Check if avatar is empty
    if (!profile.avatar) {
      console.log('Avatar is empty. Setting a default avatar...');

      // Generate a default avatar URL
      const defaultAvatarUrl = 'https://sessionhub-images.s3.amazonaws.com/profiles/avatars/default-11.jpg';

      // Update the profile record
      await pool.query(`
        UPDATE profiles.user_profiles
        SET avatar = $1
        WHERE user_id = 11;
      `, [defaultAvatarUrl]);

      console.log(`Updated profile for user 11 with default avatar URL: ${defaultAvatarUrl}`);
    } else {
      console.log('Avatar is already set:', profile.avatar);
    }

    // Verify the changes
    console.log('\nVerifying changes...');

    // Check user 11 in profiles.user_profiles table again
    const updatedProfileResult = await pool.query(`
      SELECT * 
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (updatedProfileResult.rows.length > 0) {
      const updatedProfile = updatedProfileResult.rows[0];
      console.log('Updated user 11 profile data from profiles.user_profiles:');
      console.log(JSON.stringify({
        id: updatedProfile.id,
        user_id: updatedProfile.user_id,
        name: updatedProfile.name,
        avatar: updatedProfile.avatar,
        cover_photo: updatedProfile.cover_photo
      }, null, 2));
    }

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

  } catch (error) {
    console.error('Error fixing avatar field:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

fixAvatarField();
