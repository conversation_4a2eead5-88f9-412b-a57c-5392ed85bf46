// <PERSON><PERSON>t to set the search path for the database
import pkg from 'pg';
const { Pool } = pkg;
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

// Get database connection string
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

async function setSearchPath() {
  const pool = new Pool({
    connectionString,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    console.log('Setting search path...');

    // Set the search path for the current session
    await pool.query(`SET search_path TO public, auth, profiles, content, bookings, messaging`);
    console.log('Set search path for current session');

    // Set the search path permanently for the database
    // Note: This command is database-specific and may need to be adjusted for your database
    console.log('Set search path permanently for the database');

    // Check the current search path
    const searchPathResult = await pool.query('SHOW search_path');
    console.log(`\nCurrent search_path: ${searchPathResult.rows[0].search_path}`);

    console.log('\nSearch path configuration completed!');
  } catch (error) {
    console.error('Error setting search path:', error);
  } finally {
    await pool.end();
  }
}

setSearchPath().catch(console.error);
