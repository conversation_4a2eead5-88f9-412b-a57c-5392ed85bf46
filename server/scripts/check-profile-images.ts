import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkProfileImages() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Get all user profiles with avatar and cover_photo fields
    const profiles = await pool.query(`
      SELECT user_id, avatar, cover_photo FROM profiles.user_profiles;
    `);

    console.log(`Found ${profiles.rows.length} user profiles`);

    // Check avatar and cover_photo fields
    console.log('\nChecking avatar and cover_photo fields:');

    for (const profile of profiles.rows) {
      console.log(`\nProfile for user_id ${profile.user_id}:`);
      console.log(`  Avatar: ${profile.avatar || 'null'}`);
      console.log(`  Cover photo: ${profile.cover_photo || 'null'}`);
    }

    // Close the database connection
    await pool.end();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error checking profile images:', error);
  }
}

checkProfileImages();
