import { Pool } from 'pg';
import dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config();

async function syncProfileImages() {
  // Create a new pool with the DATABASE_URL from environment
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');

    // Sync avatar from profiles.user_profiles to auth.users for user 11
    console.log('Syncing avatar from profiles.user_profiles to auth.users for user 11...');

    // Get the avatar from profiles.user_profiles
    const profileResult = await pool.query(`
      SELECT avatar, cover_photo 
      FROM profiles.user_profiles
      WHERE user_id = 11;
    `);

    if (profileResult.rows.length === 0) {
      console.log('User 11 not found in profiles.user_profiles table');
      return;
    }

    const profile = profileResult.rows[0];
    console.log('User 11 profile data:');
    console.log(JSON.stringify(profile, null, 2));

    // Update the avatar in auth.users
    if (profile.avatar) {
      console.log(`Updating avatar in auth.users to: ${profile.avatar}`);
      await pool.query(`
        UPDATE auth.users
        SET avatar = $1
        WHERE id = 11;
      `, [profile.avatar]);
      console.log('Avatar updated in auth.users');
    }

    // Update the cover_photo in auth.users
    if (profile.cover_photo) {
      console.log(`Updating cover_photo in auth.users to: ${profile.cover_photo}`);
      await pool.query(`
        UPDATE auth.users
        SET cover_photo = $1
        WHERE id = 11;
      `, [profile.cover_photo]);
      console.log('Cover photo updated in auth.users');
    }

    // Verify the changes
    console.log('\nVerifying changes...');

    // Check user 11 in auth.users table
    const userResult = await pool.query(`
      SELECT id, username, email, avatar, cover_photo
      FROM auth.users
      WHERE id = 11;
    `);

    const user = userResult.rows[0];
    console.log('Updated user 11 data in auth.users:');
    console.log(JSON.stringify(user, null, 2));

    // Close the database connection
    await pool.end();
    console.log('\nDatabase connection closed');

  } catch (error) {
    console.error('Error syncing profile images:', error);
    try {
      await pool.end();
    } catch (e) {
      // Ignore
    }
  }
}

syncProfileImages();
