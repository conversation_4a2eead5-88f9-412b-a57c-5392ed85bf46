/**
 * Availability Management Module
 *
 * This module implements a hybrid availability model with:
 * 1. Global Availability - Teachers set their general work hours in their profile
 * 2. Per-Session Overrides - When creating sessions, they can override these defaults
 * 3. Conflict Prevention - The system automatically checks for conflicts
 */

import { db } from './db';
import { TeacherAvailability, TimeSlot } from './types';
import { format, parse, addDays, isSameDay } from 'date-fns';
import serviceManager from './services/ServiceManager';

// Interface for global availability
export interface GlobalAvailability {
  id: number;
  teacher_id: number;
  day_of_week: string;
  start_time: string;
  end_time: string;
  created_at: Date;
  updated_at: Date;
}

export class AvailabilityManager {
  private databaseService;

  constructor() {
    this.databaseService = serviceManager.getDatabaseService();
  }

  /**
   * Initialize the availability manager by creating necessary tables
   */
  async initialize(): Promise<void> {
    try {
      // Create global_availability table if it doesn't exist
      const globalAvailabilityExists = await this.databaseService.executeQuery<{exists: boolean}>(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public' AND table_name = 'global_availability'
        ) as exists`
      );

      if (!globalAvailabilityExists[0].exists) {
        console.log("[AvailabilityManager] Creating global_availability table");
        await this.databaseService.executeQuery(`
          CREATE TABLE bookings.global_availability (
            id SERIAL PRIMARY KEY,
            teacher_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            day_of_week VARCHAR(10) NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            created_at TIMESTAMP DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP DEFAULT NOW() NOT NULL
          )
        `);

        // Create indexes for faster lookups
        await this.databaseService.executeQuery(`
          CREATE INDEX idx_global_availability_teacher_id ON global_availability(teacher_id)
        `);
      }

      // Check if teacher_availability table exists
      const teacherAvailabilityExists = await this.databaseService.executeQuery<{exists: boolean}>(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public' AND table_name = 'teacher_availability'
        ) as exists`
      );

      if (teacherAvailabilityExists[0].exists) {
        // Add is_from_global column if it doesn't exist
        const isFromGlobalColumnExists = await this.databaseService.executeQuery<{exists: boolean}>(
          `SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = 'teacher_availability'
            AND column_name = 'is_from_global'
          ) as exists`
        );

        if (!isFromGlobalColumnExists[0].exists) {
          console.log("[AvailabilityManager] Adding is_from_global column to teacher_availability table");
          await this.databaseService.executeQuery(`
            ALTER TABLE bookings.teacher_availability ADD COLUMN is_from_global BOOLEAN DEFAULT FALSE
          `);

          // Create index for the new column
          await this.databaseService.executeQuery(`
            CREATE INDEX idx_teacher_availability_from_global ON teacher_availability(is_from_global)
          `);
        }
      }

      console.log("[AvailabilityManager] Availability tables initialized successfully");
    } catch (error) {
      console.error("[AvailabilityManager] Error initializing availability tables:", error);
      throw error;
    }
  }

  /**
   * Set global availability for an teacher
   */
  async setGlobalAvailability(
    teacherId: number,
    dayOfWeek: string,
    startTime: string,
    endTime: string
  ): Promise<GlobalAvailability> {
    try {
      // Check if there's an existing entry for this day
      const existingAvailability = await this.databaseService.executeQuery<GlobalAvailability>(
        `SELECT * FROM bookings.global_availability
         WHERE teacher_id = $1 AND day_of_week = $2`,
        [teacherId, dayOfWeek]
      );

      // If exists, update it
      if (existingAvailability.length > 0) {
        const updatedAvailability = await this.databaseService.executeQuery<GlobalAvailability>(
          `UPDATE bookings.global_availability
           SET start_time = $1, end_time = $2, updated_at = NOW()
           WHERE id = $3
           RETURNING *`,
          [startTime, endTime, existingAvailability[0].id]
        );

        return updatedAvailability[0];
      }

      // Otherwise, create a new entry
      const newAvailability = await this.databaseService.executeQuery<GlobalAvailability>(
        `INSERT INTO bookings.global_availability
         (teacher_id, day_of_week, start_time, end_time)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [teacherId, dayOfWeek, startTime, endTime]
      );

      return newAvailability[0];
    } catch (error) {
      console.error("[AvailabilityManager] Error setting global availability:", error);
      throw error;
    }
  }

  /**
   * Get global availability for an teacher
   */
  async getGlobalAvailability(teacherId: number): Promise<GlobalAvailability[]> {
    try {
      return await this.databaseService.executeQuery<GlobalAvailability>(
        `SELECT * FROM bookings.global_availability
         WHERE teacher_id = $1
         ORDER BY
           CASE
             WHEN day_of_week = 'monday' THEN 1
             WHEN day_of_week = 'tuesday' THEN 2
             WHEN day_of_week = 'wednesday' THEN 3
             WHEN day_of_week = 'thursday' THEN 4
             WHEN day_of_week = 'friday' THEN 5
             WHEN day_of_week = 'saturday' THEN 6
             WHEN day_of_week = 'sunday' THEN 7
           END`,
        [teacherId]
      );
    } catch (error) {
      console.error("[AvailabilityManager] Error getting global availability:", error);
      throw error;
    }
  }

  /**
   * Delete global availability for an teacher
   */
  async deleteGlobalAvailability(id: number, teacherId: number): Promise<boolean> {
    try {
      const result = await this.databaseService.executeQuery(
        `DELETE FROM bookings.global_availability
         WHERE id = $1 AND teacher_id = $2
         RETURNING id`,
        [id, teacherId]
      );

      return result.length > 0;
    } catch (error) {
      console.error("[AvailabilityManager] Error deleting global availability:", error);
      throw error;
    }
  }

  /**
   * Apply global availability to a session
   * This creates session-specific availability slots based on the teacher's global settings
   */
  async applyGlobalAvailabilityToSession(
    sessionId: number,
    teacherId: number
  ): Promise<TeacherAvailability[]> {
    try {
      // Get global availability for this teacher
      const globalAvailability = await this.getGlobalAvailability(teacherId);

      if (globalAvailability.length === 0) {
        return [];
      }

      // Delete any existing availability slots derived from global settings
      await this.databaseService.executeQuery(
        `DELETE FROM bookings.teacher_availability
         WHERE session_id = $1 AND teacher_id = $2 AND is_from_global = TRUE`,
        [sessionId, teacherId]
      );

      // Create new availability slots for each global setting
      const created: TeacherAvailability[] = [];

      for (const availability of globalAvailability) {
        const newAvailability = await this.databaseService.executeQuery<TeacherAvailability>(
          `INSERT INTO bookings.teacher_availability
           (teacher_id, session_id, day_of_week, start_time, end_time, recurring, is_from_global, status)
           VALUES ($1, $2, $3, $4, $5, TRUE, TRUE, 'active')
           RETURNING *`,
          [
            teacherId,
            sessionId,
            availability.day_of_week,
            availability.start_time,
            availability.end_time
          ]
        );

        if (newAvailability.length > 0) {
          // Generate time slots for this availability
          await this.generateTimeSlotsForAvailability(newAvailability[0].id);
          created.push(newAvailability[0]);
        }
      }

      return created;
    } catch (error) {
      console.error("[AvailabilityManager] Error applying global availability to session:", error);
      throw error;
    }
  }

  /**
   * Generate time slots for availability
   * This is an implementation similar to PostgresStorage.generateTimeSlotsForAvailability
   * but publicly accessible from this class
   */
  private async generateTimeSlotsForAvailability(availabilityId: number): Promise<void> {
    try {
      // Get the availability record
      const availabilities = await this.databaseService.executeQuery<TeacherAvailability>(
        'SELECT * FROM bookings.teacher_availability WHERE id = $1',
        [availabilityId]
      );

      if (availabilities.length === 0) {
        throw new Error('Availability not found');
      }

      const availability = availabilities[0];

      // Delete existing time slots for this availability
      await this.databaseService.executeQuery('DELETE FROM bookings.time_slots WHERE availability_id = $1', [availabilityId]);

      const insertSlots = async (date: Date, startTime: string, endTime: string) => {
        const dateStr = format(date, 'yyyy-MM-dd');

        // Create the time slot
        await this.databaseService.executeQuery(
          `INSERT INTO bookings.time_slots (availability_id, start_time, end_time, is_booked)
           VALUES ($1, $2, $3, FALSE)`,
          [
            availabilityId,
            `${dateStr} ${startTime}`,
            `${dateStr} ${endTime}`
          ]
        );
      };

      if (availability.recurring) {
        // Generate slots for the next 3 months
        const currentDate = new Date();
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 3);

        // Map day of week string to number (0 = Sunday, 1 = Monday, etc.)
        const dayToNum: Record<string, number> = {
          sunday: 0,
          monday: 1,
          tuesday: 2,
          wednesday: 3,
          thursday: 4,
          friday: 5,
          saturday: 6
        };

        const targetDayNum = dayToNum[availability.day_of_week?.toLowerCase() as string];

        if (targetDayNum === undefined) {
          throw new Error('Invalid day of week');
        }

        // Find the next occurrence of this day of week
        let date = new Date(currentDate);
        while (date.getDay() !== targetDayNum) {
          date.setDate(date.getDate() + 1);
        }

        // Generate slots for each occurrence of this day until the end date
        while (date <= endDate) {
          await insertSlots(date, availability.start_time, availability.end_time);

          // Move to the next week
          date.setDate(date.getDate() + 7);
        }
      } else if (availability.specific_date) {
        // For specific date, just create a single time slot
        const specificDate = new Date(availability.specific_date);
        await insertSlots(specificDate, availability.start_time, availability.end_time);
      }
    } catch (error) {
      console.error('Error generating time slots for availability:', error);
      throw error;
    }
  }

  /**
   * Check for conflicts when creating a new availability
   * Returns true if there's a conflict
   */
  async checkForConflicts(
    teacherId: number,
    startTime: string,
    endTime: string,
    date: Date,
    recurringDayOfWeek?: string,
    existingAvailabilityId?: number
  ): Promise<boolean> {
    try {
      let query: string;
      let params: any[];

      // Convert date to ISO string format
      const dateStr = format(date, 'yyyy-MM-dd');

      if (recurringDayOfWeek) {
        // Check for conflicts with recurring availability
        query = `
          SELECT id FROM bookings.teacher_availability
          WHERE teacher_id = $1
          AND recurring = TRUE
          AND day_of_week = $2
          AND (
            (start_time <= $3 AND end_time > $3) OR
            (start_time < $4 AND end_time >= $4) OR
            (start_time >= $3 AND end_time <= $4)
          )
        `;
        params = [teacherId, recurringDayOfWeek, startTime, endTime];
      } else {
        // Check for conflicts with specific date
        query = `
          SELECT id FROM bookings.teacher_availability
          WHERE teacher_id = $1
          AND recurring = FALSE
          AND specific_date = $2
          AND (
            (start_time <= $3 AND end_time > $3) OR
            (start_time < $4 AND end_time >= $4) OR
            (start_time >= $3 AND end_time <= $4)
          )
        `;
        params = [teacherId, dateStr, startTime, endTime];
      }

      // Exclude the current availability being updated if provided
      if (existingAvailabilityId) {
        query += ' AND id != $' + (params.length + 1);
        params.push(existingAvailabilityId);
      }

      const conflicts = await this.databaseService.executeQuery(query, params);

      return conflicts.length > 0;
    } catch (error) {
      console.error("[AvailabilityManager] Error checking for conflicts:", error);
      throw error;
    }
  }
}

export default AvailabilityManager;