// Direct bookings handler for 404 issues
import { Request, Response } from 'express';
import { storage } from './storage';

export async function handleDirectBookings(req: Request, res: Response): Promise<void> {
  try {
    console.log(`[DIRECT BOOKINGS] Request received for user ${req.params.userId}`);
    res.setHeader('Content-Type', 'application/json');
    
    const userId = parseInt(req.params.userId);
    
    if (isNaN(userId)) {
      res.status(400).json({ message: 'Invalid user ID' });
      return;
    }
    
    try {
      // Get bookings from storage layer but use a modified SQL query that doesn't include the category column
      console.log(`[DIRECT BOOKINGS] Getting bookings for user ${userId} with fixed query`);
      
      if (typeof (storage as any).executeQuery === 'function') {
        const result = await (storage as any).executeQuery(
          `
          SELECT 
            b.*,
            s.id as session_id,
            s.title,
            s.description,
            s.type,
            s.skill_level,
            s.format,
            s.duration,
            s.price,
            s.date,
            s.teacher_id,
            s.is_public,
            s.image_url,
            s.zoom_link,
            s.created_at as session_created_at,
            s.updated_at as session_updated_at,
            u.id as teacher_id,
            u.name as teacher_name,
            u.username as teacher_username,
            u.avatar as teacher_avatar
          FROM 
            bookings b
          JOIN 
            sessions s ON b.session_id = s.id
          JOIN 
            users u ON s.teacher_id = u.id
          WHERE 
            b.user_id = $1
          ORDER BY 
            b.created_at DESC
          `,
          [userId]
        );
        
        console.log(`[DIRECT BOOKINGS] Found ${result.length} bookings for user ${userId}`);
        
        // Transform the results into BookingWithSession objects
        const bookings = result.map(row => {
          // Create the Booking part
          const booking: any = {
            id: row.id,
            userId: row.user_id,
            sessionId: row.session_id,
            status: row.status,
            timeSlotId: row.time_slot_id,
            paymentId: row.payment_id,
            paymentStatus: row.payment_status,
            paymentAmount: row.payment_amount,
            paymentProcessor: row.payment_processor,
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };
          
          // Create the SessionWithTeacher part
          const session: any = {
            id: row.session_id,
            title: row.title,
            description: row.description,
            type: row.type,
            skillLevel: row.skill_level,
            format: row.format,
            duration: row.duration,
            price: row.price,
            date: row.date,
            teacherId: row.teacher_id,
            isPublic: row.is_public,
            imageUrl: row.image_url,
            zoomLink: row.zoom_link,
            createdAt: row.session_created_at,
            updatedAt: row.session_updated_at,
            teacher: {
              id: row.teacher_id,
              name: row.teacher_name,
              username: row.teacher_username,
              avatar: row.teacher_avatar
            }
          };
          
          // Combine them into a BookingWithSession
          const bookingWithSession: any = {
            ...booking,
            session: session
          };
          
          return bookingWithSession;
        });
        
        // If no bookings, try to create a test one
        if (bookings.length === 0) {
          try {
            // Find a session to book
            const sessionQuery = `SELECT id FROM sessions LIMIT 1`;
            const sessions = await (storage as any).executeQuery(sessionQuery, []);
            
            if (sessions.length > 0) {
              const sessionId = sessions[0].id;
              const insertQuery = `
                INSERT INTO bookings (user_id, session_id, status, payment_status, created_at, updated_at)
                VALUES ($1, $2, 'confirmed', 'paid', NOW(), NOW())
                RETURNING id
              `;
              
              const insertResult = await (storage as any).executeQuery(insertQuery, [userId, sessionId]);
              console.log(`[DIRECT BOOKINGS] Created test booking with ID: ${insertResult[0]?.id}`);
              
              // Get bookings again after creating test one
              const updatedResult = await (storage as any).executeQuery(
                `
                SELECT 
                  b.*,
                  s.id as session_id,
                  s.title,
                  s.description,
                  s.type,
                  s.skill_level,
                  s.format,
                  s.duration,
                  s.price,
                  s.date,
                  s.teacher_id,
                  s.is_public,
                  s.image_url,
                  s.zoom_link,
                  s.created_at as session_created_at,
                  s.updated_at as session_updated_at,
                  u.id as teacher_id,
                  u.name as teacher_name,
                  u.username as teacher_username,
                  u.avatar as teacher_avatar
                FROM 
                  bookings b
                JOIN 
                  sessions s ON b.session_id = s.id
                JOIN 
                  users u ON s.teacher_id = u.id
                WHERE 
                  b.user_id = $1
                ORDER BY 
                  b.created_at DESC
                `,
                [userId]
              );
              
              // Transform the results
              const updatedBookings = updatedResult.map(row => {
                // Create the Booking part
                const booking: any = {
                  id: row.id,
                  userId: row.user_id,
                  sessionId: row.session_id,
                  status: row.status,
                  timeSlotId: row.time_slot_id,
                  paymentId: row.payment_id,
                  paymentStatus: row.payment_status,
                  paymentAmount: row.payment_amount,
                  paymentProcessor: row.payment_processor,
                  createdAt: row.created_at,
                  updatedAt: row.updated_at
                };
                
                // Create the SessionWithTeacher part
                const session: any = {
                  id: row.session_id,
                  title: row.title,
                  description: row.description,
                  type: row.type,
                  skillLevel: row.skill_level,
                  format: row.format,
                  duration: row.duration,
                  price: row.price,
                  date: row.date,
                  teacherId: row.teacher_id,
                  isPublic: row.is_public,
                  imageUrl: row.image_url,
                  zoomLink: row.zoom_link,
                  createdAt: row.session_created_at,
                  updatedAt: row.session_updated_at,
                  teacher: {
                    id: row.teacher_id,
                    name: row.teacher_name,
                    username: row.teacher_username,
                    avatar: row.teacher_avatar
                  }
                };
                
                // Combine them into a BookingWithSession
                const bookingWithSession: any = {
                  ...booking,
                  session: session
                };
                
                return bookingWithSession;
              });
              
              res.status(200).json(updatedBookings);
              return;
            }
          } catch (error) {
            console.error('[DIRECT BOOKINGS] Error creating test booking:', error);
            // Continue to return empty bookings array
          }
        }
        
        // Return the bookings
        res.status(200).json(bookings);
        return;
      } else {
        // Fallback to the standard method if executeQuery is not available
        const bookings = await storage.getUserBookings(userId);
        console.log(`[DIRECT BOOKINGS] Found ${bookings.length} bookings for user ${userId} using standard method`);
        res.status(200).json(bookings);
      }
    } catch (error) {
      console.error('[DIRECT BOOKINGS] Error getting bookings:', error);
      res.status(500).json({ 
        message: 'Error getting bookings',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  } catch (error) {
    console.error('[DIRECT BOOKINGS] Unhandled error:', error);
    res.status(500).json({ 
      message: 'Unhandled error in bookings handler',
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Utility function to check if bookings endpoint is working
export async function testBookingsEndpoint(userId: number): Promise<boolean> {
  try {
    console.log(`[TEST BOOKINGS] Testing bookings endpoint for user ${userId}`);
    
    // Try to get bookings from storage directly
    const bookings = await storage.getUserBookings(userId);
    console.log(`[TEST BOOKINGS] Found ${bookings.length} bookings for user ${userId}`);
    
    return true;
  } catch (error) {
    console.error('[TEST BOOKINGS] Error testing bookings endpoint:', error);
    return false;
  }
}

// Exposed API endpoint for testing bookings
export async function createTestBooking(req: Request, res: Response): Promise<void> {
  try {
    console.log(`[CREATE TEST BOOKING] Request received for user ${req.params.userId}`);
    res.setHeader('Content-Type', 'application/json');
    
    const userId = parseInt(req.params.userId);
    
    if (isNaN(userId)) {
      res.status(400).json({ message: 'Invalid user ID' });
      return;
    }
    
    try {
      if (typeof (storage as any).executeQuery === 'function') {
        // Check if the user exists
        const userQuery = `SELECT id FROM users WHERE id = $1`;
        const userResult = await (storage as any).executeQuery(userQuery, [userId]);
        
        if (userResult.length === 0) {
          res.status(404).json({ 
            message: 'User not found',
            error: 'The specified user does not exist'
          });
          return;
        }
        
        // Find a session to book
        const sessionQuery = `SELECT id FROM sessions WHERE is_public = true LIMIT 1`;
        const sessions = await (storage as any).executeQuery(sessionQuery, []);
        
        if (sessions.length === 0) {
          // No sessions yet, create one first
          const teacherQuery = `SELECT id FROM users WHERE is_teacher = true LIMIT 1`;
          const teachers = await (storage as any).executeQuery(teacherQuery, []);
          
          if (teachers.length === 0) {
            // No teachers, make this user an teacher
            const makeTeacherQuery = `UPDATE users SET is_teacher = true WHERE id = $1 RETURNING id`;
            await (storage as any).executeQuery(makeTeacherQuery, [userId]);
            
            // Create a session
            const createSessionQuery = `
              INSERT INTO sessions (
                title, description, teacher_id, type, skill_level, 
                format, price, duration, time_of_day, date, language, 
                is_public, created_at, updated_at
              ) VALUES (
                'Test Session', 'This is a test session', $1, 'Yoga', 'Beginner',
                'One-on-One', 10.0, 60, 'morning', NOW() + INTERVAL '1 day', 'English',
                true, NOW(), NOW()
              ) RETURNING id
            `;
            
            const sessionResult = await (storage as any).executeQuery(createSessionQuery, [userId]);
            const sessionId = sessionResult[0].id;
            
            // Create the booking
            const insertQuery = `
              INSERT INTO bookings (user_id, session_id, status, payment_status, created_at, updated_at)
              VALUES ($1, $2, 'confirmed', 'paid', NOW(), NOW())
              RETURNING id
            `;
            
            const insertResult = await (storage as any).executeQuery(insertQuery, [userId, sessionId]);
            const bookingId = insertResult[0].id;
            console.log(`[CREATE TEST BOOKING] Created booking with ID: ${bookingId}`);
            
            // Get the full booking with session using our fixed query without category
            const bookingQuery = `
              SELECT 
                b.*,
                s.id as session_id,
                s.title,
                s.description,
                s.type,
                s.skill_level,
                s.format,
                s.duration,
                s.price,
                s.date,
                s.teacher_id,
                s.is_public,
                s.image_url,
                s.zoom_link,
                s.created_at as session_created_at,
                s.updated_at as session_updated_at,
                u.id as teacher_id,
                u.name as teacher_name,
                u.username as teacher_username,
                u.avatar as teacher_avatar
              FROM 
                bookings b
              JOIN 
                sessions s ON b.session_id = s.id
              JOIN 
                users u ON s.teacher_id = u.id
              WHERE 
                b.user_id = $1
              ORDER BY 
                b.created_at DESC
            `;
            
            const bookingResult = await (storage as any).executeQuery(bookingQuery, [userId]);
            
            // Transform the results
            const bookings = bookingResult.map(row => {
              // Create the Booking part
              const booking: any = {
                id: row.id,
                userId: row.user_id,
                sessionId: row.session_id,
                status: row.status,
                timeSlotId: row.time_slot_id,
                paymentId: row.payment_id,
                paymentStatus: row.payment_status,
                paymentAmount: row.payment_amount,
                paymentProcessor: row.payment_processor,
                createdAt: row.created_at,
                updatedAt: row.updated_at
              };
              
              // Create the SessionWithTeacher part
              const session: any = {
                id: row.session_id,
                title: row.title,
                description: row.description,
                type: row.type,
                skillLevel: row.skill_level,
                format: row.format,
                duration: row.duration,
                price: row.price,
                date: row.date,
                teacherId: row.teacher_id,
                isPublic: row.is_public,
                imageUrl: row.image_url,
                zoomLink: row.zoom_link,
                createdAt: row.session_created_at,
                updatedAt: row.session_updated_at,
                teacher: {
                  id: row.teacher_id,
                  name: row.teacher_name,
                  username: row.teacher_username,
                  avatar: row.teacher_avatar
                }
              };
              
              // Combine them into a BookingWithSession
              const bookingWithSession: any = {
                ...booking,
                session: session
              };
              
              return bookingWithSession;
            });
            
            res.status(200).json({
              success: true,
              message: `Created test session ${sessionId} and booking ${bookingId}`,
              bookings
            });
            return;
          } else {
            // Use an existing teacher
            const teacherId = teachers[0].id;
            console.log(`[CREATE TEST BOOKING] Using teacher with ID: ${teacherId}`);
            
            // Create a session
            const createSessionQuery = `
              INSERT INTO sessions (
                title, description, teacher_id, type, skill_level, 
                format, price, duration, time_of_day, date, language, 
                is_public, created_at, updated_at
              ) VALUES (
                'Test Session', 'This is a test session', $1, 'Yoga', 'Beginner',
                'One-on-One', 10.0, 60, 'morning', NOW() + INTERVAL '1 day', 'English',
                true, NOW(), NOW()
              ) RETURNING id
            `;
            
            const sessionResult = await (storage as any).executeQuery(createSessionQuery, [teacherId]);
            const sessionId = sessionResult[0].id;
            console.log(`[CREATE TEST BOOKING] Created session with ID: ${sessionId}`);
            
            // Create the booking
            const insertQuery = `
              INSERT INTO bookings (user_id, session_id, status, payment_status, created_at, updated_at)
              VALUES ($1, $2, 'confirmed', 'paid', NOW(), NOW())
              RETURNING id
            `;
            
            const insertResult = await (storage as any).executeQuery(insertQuery, [userId, sessionId]);
            const bookingId = insertResult[0].id;
            console.log(`[CREATE TEST BOOKING] Created booking with ID: ${bookingId}`);
            
            // Get the full booking with session
            const bookings = await storage.getUserBookings(userId);
            
            res.status(200).json({
              success: true,
              message: `Created test session ${sessionId} and booking ${bookingId}`,
              bookings
            });
            return;
          }
        } else {
          // Use an existing session
          const sessionId = sessions[0].id;
          console.log(`[CREATE TEST BOOKING] Found existing session with ID: ${sessionId}`);
          
          // Create the booking
          const insertQuery = `
            INSERT INTO bookings (user_id, session_id, status, payment_status, created_at, updated_at)
            VALUES ($1, $2, 'confirmed', 'paid', NOW(), NOW())
            RETURNING id
          `;
          
          const insertResult = await (storage as any).executeQuery(insertQuery, [userId, sessionId]);
          const bookingId = insertResult[0].id;
          console.log(`[CREATE TEST BOOKING] Created booking with ID: ${bookingId}`);
          
          // Get the full booking with session with our fixed query
          const bookingQuery = `
            SELECT 
              b.*,
              s.id as session_id,
              s.title,
              s.description,
              s.type,
              s.skill_level,
              s.format,
              s.duration,
              s.price,
              s.date,
              s.teacher_id,
              s.is_public,
              s.image_url,
              s.zoom_link,
              s.created_at as session_created_at,
              s.updated_at as session_updated_at,
              u.id as teacher_id,
              u.name as teacher_name,
              u.username as teacher_username,
              u.avatar as teacher_avatar
            FROM 
              bookings b
            JOIN 
              sessions s ON b.session_id = s.id
            JOIN 
              users u ON s.teacher_id = u.id
            WHERE 
              b.user_id = $1
            ORDER BY 
              b.created_at DESC
          `;
          
          const bookingResult = await (storage as any).executeQuery(bookingQuery, [userId]);
          
          // Transform the results
          const bookings = bookingResult.map(row => {
            // Create the Booking part
            const booking: any = {
              id: row.id,
              userId: row.user_id,
              sessionId: row.session_id,
              status: row.status,
              timeSlotId: row.time_slot_id,
              paymentId: row.payment_id,
              paymentStatus: row.payment_status,
              paymentAmount: row.payment_amount,
              paymentProcessor: row.payment_processor,
              createdAt: row.created_at,
              updatedAt: row.updated_at
            };
            
            // Create the SessionWithTeacher part
            const session: any = {
              id: row.session_id,
              title: row.title,
              description: row.description,
              type: row.type,
              skillLevel: row.skill_level,
              format: row.format,
              duration: row.duration,
              price: row.price,
              date: row.date,
              teacherId: row.teacher_id,
              isPublic: row.is_public,
              imageUrl: row.image_url,
              zoomLink: row.zoom_link,
              createdAt: row.session_created_at,
              updatedAt: row.session_updated_at,
              teacher: {
                id: row.teacher_id,
                name: row.teacher_name,
                username: row.teacher_username,
                avatar: row.teacher_avatar
              }
            };
            
            // Combine them into a BookingWithSession
            const bookingWithSession: any = {
              ...booking,
              session: session
            };
            
            return bookingWithSession;
          });
          
          res.status(200).json({
            success: true,
            message: `Created test booking ${bookingId} for session ${sessionId}`,
            bookings
          });
          return;
        }
      } else {
        res.status(500).json({ 
          message: 'Database query method not available',
          error: 'executeQuery not defined'
        });
      }
    } catch (error) {
      console.error('[CREATE TEST BOOKING] Error creating booking:', error);
      res.status(500).json({ 
        message: 'Error creating test booking',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  } catch (error) {
    console.error('[CREATE TEST BOOKING] Unhandled error:', error);
    res.status(500).json({ 
      message: 'Unhandled error in create test booking handler',
      error: error instanceof Error ? error.message : String(error)
    });
  }
} 