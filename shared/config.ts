/**
 * Shared configuration values used across both client and server
 */

// Server port configuration
export const SERVER_PORT = 4005;

// Client port configuration
export const CLIENT_PORT = 3000;

// Environment configuration
export const isDevelopment = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
export const isProduction = process.env.NODE_ENV === 'production';

// API URL configuration
export const getApiUrl = (hostname: string = 'localhost'): string => {
  // In development, use localhost with SERVER_PORT
  if (isDevelopment) {
    return `http://${hostname}:${SERVER_PORT}`;
  }

  // In production, use the hostname without a port (assuming proper DNS and proxy setup)
  return isProduction ? `https://${hostname}` : `http://${hostname}:${SERVER_PORT}`;
};

// Client URL configuration
export const getClientUrl = (hostname: string = 'localhost'): string => {
  // In development, use localhost with CLIENT_PORT
  if (isDevelopment) {
    return `http://${hostname}:${CLIENT_PORT}`;
  }

  // In production, use the hostname without a port (assuming proper DNS and proxy setup)
  return isProduction ? `https://${hostname}` : `http://${hostname}:${CLIENT_PORT}`;
};


