import { z } from "zod";

// ===============================================
// SUPABASE-COMPATIBLE TYPE DEFINITIONS
// Replaces legacy Drizzle schema with Supabase types
// ===============================================

// Enum definitions (matching your database enums)
export const SessionType = {
  Yoga: 'Yoga',
  Pilates: 'Pilates', 
  Language: 'Language',
  Music: 'Music',
  Dance: 'Dance',
  Cooking: 'Cooking',
  Art: 'Art',
  Academic: 'Academic',
  Professional: 'Professional',
  Health: 'Health',
  Fitness: 'Fitness',
  DIY: 'DIY',
  Beauty: 'Beauty',
  Therapy: 'Therapy',
  Relationships: 'Relationships',
  Spirituality: 'Spirituality'
} as const;

export const SkillLevel = {
  Beginner: 'Beginner',
  Intermediate: 'Intermediate', 
  Advanced: 'Advanced',
  'All Levels': 'All Levels'
} as const;

export const SessionFormat = {
  'One-on-One': 'One-on-One',
  Group: 'Group',
  Workshop: 'Workshop'
} as const;

// Base Types (matching Supabase structure)
export type User = {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
};

export type UserProfile = {
  id: string;
  user_id: string;
  bio?: string;
  timezone?: string;
  currency?: string;
  phone?: string;
  location?: string;
  website?: string;
  facebook_url?: string;
  twitter_url?: string;
  instagram_url?: string;
  linkedin_url?: string;
  youtube_url?: string;
  tiktok_url?: string;
  avatar?: string;
  cover_photo?: string;
  cover_photo_position?: string;
  specializations?: string[];
  skills?: string[];
  certifications?: string[];
  education?: string;
  experience?: string;
  availability?: string;
  rating?: number;
  review_count?: number;
  is_teacher?: boolean;
  show_teaching_sessions?: boolean;
  show_learning_sessions?: boolean;
  show_profile?: boolean;
  show_social_links?: boolean;
  show_contact?: boolean;
  created_at?: string;
  updated_at?: string;
};

export type Session = {
  id: string;
  title: string;
  teacher_id: string;
  type: keyof typeof SessionType;
  description: string;
  price: number;
  duration: number;
  date: string;
  language: string;
  skill_level: keyof typeof SkillLevel;
  format: keyof typeof SessionFormat;
  max_participants?: number;
  zoom_link?: string;
  learning_outcomes?: string;
  requirements?: string;
  location_type?: string;
  location_details?: string;
  online_hosting_service?: string;
  is_featured?: boolean;
  rating?: number;
  review_count?: number;
  created_at: string;
  updated_at: string;
  image_url?: string;
  is_public?: boolean;
  cancellation_policy?: string;
  welcome_message?: string;
  reminder_message?: string;
  preparation_notes?: string;
  follow_up_message?: string;
  automated_messages_json?: string;
  recurring_pattern?: string;
  recurring_day?: string;
  num_occurrences?: number;
  cancellation_timeframe?: string;
  custom_cancellation_hours?: number;
  cancellation_fee_percentage?: number;
  no_show_fee_percentage?: number;
  legal_agreement?: string;
  agreement_type?: string;
  custom_agreement?: boolean;
  scheduling_mode?: string;
  media_gallery?: any[];
};

export type Booking = {
  id: string;
  user_id: string;
  session_id: string;
  status?: string;
  time_slot_id?: string;
  payment_id?: string;
  payment_status?: string;
  payment_amount?: number;
  payment_processor?: string;
  created_at: string;
  updated_at: string;
};

export type Review = {
  id: string;
  user_id: string;
  session_id: string;
  rating: number;
  comment?: string;
  created_at: string;
};

export type Conversation = {
  id: string;
  participant_ids: string[];
  name?: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  booking_id?: string;
};

export type Message = {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  read?: boolean;
  metadata?: any;
  attachments?: Array<{ name: string; size: number; url: string; type: string }>;
  links?: Array<{ label: string; url: string }>;
};

export type ScheduledMessage = {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  scheduled_time: string;
  status?: string;
  created_at: string;
  sent_at?: string;
  booking_id?: string;
  session_id?: string;
  metadata?: any;
};

export type SocialAccount = {
  id: string;
  user_id: string;
  provider: string;
  provider_id: string;
  username?: string;
  profile_url?: string;
  access_token?: string;
  refresh_token?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
};

// Insert/Create Types (for forms)
export type InsertUser = Omit<User, 'id' | 'created_at' | 'updated_at'>;
export type InsertUserProfile = Omit<UserProfile, 'id' | 'rating' | 'review_count' | 'created_at' | 'updated_at'>;
export type InsertSession = Omit<Session, 'id' | 'rating' | 'review_count' | 'created_at' | 'updated_at'>;
export type InsertBooking = Omit<Booking, 'id' | 'status' | 'created_at' | 'updated_at'>;
export type InsertReview = Omit<Review, 'id' | 'created_at'>;
export type InsertConversation = Omit<Conversation, 'id' | 'created_at' | 'updated_at' | 'last_message_at'>;
export type InsertMessage = Omit<Message, 'id' | 'created_at' | 'read'>;
export type InsertScheduledMessage = Omit<ScheduledMessage, 'id' | 'created_at' | 'sent_at'>;
export type InsertSocialAccount = Omit<SocialAccount, 'id' | 'created_at' | 'updated_at'>;

// Extended Types with joined data
export type SessionWithTeacher = Session & {
  teacher: User;
};

export type BookingWithSession = Booking & {
  session: SessionWithTeacher;
};

export type UserWithProfile = User & {
  profile?: UserProfile;
  name?: string;
  username?: string;
  avatar?: string;
  bio?: string;
  is_teacher?: boolean;
  specializations?: string[];
};

export type MessageWithSender = Message & {
  sender: User;
};

export type ConversationWithMessages = Conversation & {
  messages: MessageWithSender[];
  participants: User[];
};

export type ReviewWithUser = Review & {
  user: User;
};

// Legacy compatibility aliases
export type Profile = UserProfile;
export type InsertProfile = InsertUserProfile;
export type TeacherProfile = UserProfile;
export type InsertTeacherProfile = InsertUserProfile;
export type TeachingSession = Session;
export type LearningSession = Session & { booking: Booking; };
export type UserWithOldProfile = UserWithProfile;
export type SessionWithInstructor = SessionWithTeacher;

// Zod Schemas for validation
export const insertSessionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  teacher_id: z.string(),
  type: z.enum(['Yoga', 'Pilates', 'Language', 'Music', 'Dance', 'Cooking', 'Art', 'Academic', 'Professional', 'Health', 'Fitness', 'DIY', 'Beauty', 'Therapy', 'Relationships', 'Spirituality']),
  description: z.string().min(1, "Description is required"),
  price: z.number().min(0),
  duration: z.number().min(1),
  date: z.union([z.date(), z.string().transform((str) => new Date(str))]).refine((date) => date instanceof Date && !isNaN(date.getTime()), { message: "Invalid date format" }),
  language: z.string().min(1),
  skill_level: z.enum(['Beginner', 'Intermediate', 'Advanced', 'All Levels']),
  format: z.enum(['One-on-One', 'Group', 'Workshop']),
  max_participants: z.number().optional(),
  zoom_link: z.string().optional(),
  learning_outcomes: z.string().optional(),
  requirements: z.string().optional(),
  location_type: z.enum(['online', 'in_person', 'hybrid']).optional(),
  location_details: z.string().optional(),
  online_hosting_service: z.enum(['zoom', 'google_meet', 'microsoft_teams', 'webex', 'skype', 'other']).optional(),
  is_featured: z.boolean().default(false),
  image_url: z.string().optional(),
  is_public: z.boolean().default(true),
  cancellation_policy: z.string().optional(),
  welcome_message: z.string().optional().default("Thank you for booking my session!"),
  reminder_message: z.string().optional().default("Just a friendly reminder about our upcoming session."),
  preparation_notes: z.string().optional().default("No special preparation needed."),
  follow_up_message: z.string().optional().default("Thank you for attending the session today!"),
  scheduling_mode: z.enum(['fixed', 'recurring', 'availability']).optional(),
  recurring_pattern: z.enum(['daily', 'weekly', 'fortnightly', 'monthly', 'yearly']).optional(),
  recurring_day: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']).optional(),
  num_occurrences: z.number().optional(),
  fixedDates: z.array(z.date()).optional(),
  automatedMessages: z.array(z.object({
    id: z.string(),
    title: z.string().min(1),
    content: z.string().min(1),
    triggerType: z.enum(['before', 'after']).default('before'),
    triggerTime: z.coerce.number().min(0),
    triggerUnit: z.enum(['hours', 'days', 'weeks']).default('hours'),
    enabled: z.boolean().default(true),
    frequency: z.coerce.number().min(1).default(1)
  })).optional().default([])
});

export const insertUserSchema = z.object({
  email: z.string().email()
});

export const insertUserProfileSchema = z.object({
  user_id: z.string(),
  bio: z.string().optional(),
  timezone: z.string().optional(),
  currency: z.string().default('USD').optional(),
  phone: z.string().optional(),
  location: z.string().optional(),
  website: z.string().optional(),
  facebook_url: z.string().optional(),
  twitter_url: z.string().optional(),
  instagram_url: z.string().optional(),
  linkedin_url: z.string().optional(),
  youtube_url: z.string().optional(),
  tiktok_url: z.string().optional(),
  avatar: z.string().optional(),
  cover_photo: z.string().optional(),
  cover_photo_position: z.string().default('50%').optional(),
  specializations: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  certifications: z.array(z.string()).optional(),
  education: z.string().optional(),
  experience: z.string().optional(),
  availability: z.string().optional(),
  is_teacher: z.boolean().default(false).optional(),
  show_teaching_sessions: z.boolean().default(true).optional(),
  show_learning_sessions: z.boolean().default(false).optional(),
  show_profile: z.boolean().default(true).optional(),
  show_social_links: z.boolean().default(true).optional(),
  show_contact: z.boolean().default(false).optional()
});

export const insertBookingSchema = z.object({
  user_id: z.string(),
  session_id: z.string(),
  time_slot_id: z.string().optional(),
  payment_id: z.string().optional(),
  payment_status: z.string().default('pending').optional(),
  payment_amount: z.number().optional(),
  payment_processor: z.string().optional()
});

export const insertReviewSchema = z.object({
  user_id: z.string(),
  session_id: z.string(),
  rating: z.number().min(1).max(5),
  comment: z.string().optional()
});

export const insertConversationSchema = z.object({
  participant_ids: z.array(z.string()),
  name: z.string().optional(),
  booking_id: z.string().optional()
});

export const insertMessageSchema = z.object({
  conversation_id: z.string(),
  sender_id: z.string(),
  content: z.string().min(1),
  metadata: z.any().optional(),
  attachments: z.array(z.object({
    name: z.string(),
    size: z.number(),
    url: z.string().url(),
    type: z.string(),
  })).optional().default([]),
  links: z.array(z.object({
    label: z.string().min(1),
    url: z.string().url(),
  })).optional().default([])
});

export const insertScheduledMessageSchema = z.object({
  conversation_id: z.string(),
  sender_id: z.string(),
  content: z.string().min(1),
  scheduled_time: z.string(),
  status: z.string().default('pending'),
  booking_id: z.string().optional(),
  session_id: z.string().optional(),
  metadata: z.record(z.any()).optional()
});

export const insertSocialAccountSchema = z.object({
  user_id: z.string(),
  provider: z.string(),
  provider_id: z.string(),
  username: z.string().optional(),
  profile_url: z.string().optional(),
  access_token: z.string().optional(),
  refresh_token: z.string().optional(),
  expires_at: z.string().optional()
});

// Export table/schema aliases for compatibility
export const Session = {} as any; // Placeholder for legacy code compatibility
export const users = {} as any;
export const profiles = {} as any;
export const sessions = {} as any;

// Additional types that might be needed
export type Payment = {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created_at: string;
};

export type PaymentMethod = {
  id: string;
  type: string;
  card_last_four?: string;
  created_at: string;
};

export type PaymentIntent = {
  id: string;
  amount: number;
  currency: string;
  status: string;
  client_secret: string;
  created_at: string;
};

export type Notification = {
  id: string;
  user_id: string;
  title: string;
  content: string;
  type: string;
  read: boolean;
  created_at: string;
};

export type InsertNotification = Omit<Notification, 'id' | 'read' | 'created_at'>;

export type NotificationPreference = {
  id: string;
  user_id: string;
  email_enabled: boolean;
  push_enabled: boolean;
  sms_enabled: boolean;
  created_at: string;
};

export type DeviceToken = {
  id: string;
  user_id: string;
  token: string;
  platform: string;
  created_at: string;
};

// For types that reference the non-existent schema
export type InsertScheduledMessageSchema = typeof insertScheduledMessageSchema;