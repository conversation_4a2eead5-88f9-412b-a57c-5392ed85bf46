/**
 * Shared schema definitions using Zod for validation
 * Compatible with Supabase and provides type safety across the application
 */

import { z } from 'zod';

// Enums for better type safety
export const SessionType = {
  'one-on-one': 'One-on-One',
  'group': 'Group Session',
  'workshop': 'Workshop',
  'masterclass': 'Masterclass',
  'consultation': 'Consultation',
  'other': 'Other'
} as const;

export const SkillLevel = {
  'beginner': 'Beginner',
  'intermediate': 'Intermediate',
  'advanced': 'Advanced'
} as const;

export const SessionFormat = {
  'online': 'Online',
  'in-person': 'In-Person',
  'hybrid': 'Hybrid'
} as const;

export const BookingStatus = {
  'pending': 'Pending',
  'confirmed': 'Confirmed',
  'cancelled': 'Cancelled',
  'completed': 'Completed'
} as const;

export const PaymentStatus = {
  'pending': 'Pending',
  'paid': 'Paid',
  'failed': 'Failed',
  'refunded': 'Refunded'
} as const;

// Base schemas
export const userProfileSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  full_name: z.string().min(1, "Full name is required"),
  email: z.string().email(),
  bio: z.string().optional(),
  avatar_url: z.string().url().optional(),
  location: z.string().optional(),
  timezone: z.string().optional(),
  phone: z.string().optional(),
  date_of_birth: z.string().optional(),
  emergency_contact: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export const instructorProfileSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  specializations: z.array(z.string()),
  experience_years: z.number().min(0),
  hourly_rate: z.number().min(0),
  bio: z.string().optional(),
  certifications: z.array(z.string()).optional(),
  languages: z.array(z.string()).optional(),
  availability: z.record(z.any()).optional(),
  rating: z.number().min(0).max(5).optional(),
  total_sessions: z.number().min(0).default(0),
  is_verified: z.boolean().default(false),
  created_at: z.string(),
  updated_at: z.string()
});

export const sessionSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  teacher_id: z.string().uuid(),
  session_type: z.string(),
  type: z.enum(['Yoga', 'Pilates', 'Language', 'Music', 'Dance', 'Cooking', 'Art', 'Academic', 'Professional', 'Health', 'Fitness', 'DIY', 'Beauty', 'Therapy', 'Relationships', 'Spirituality']),
  skill_level: z.enum(['Beginner', 'Intermediate', 'Advanced', 'All Levels']),
  duration: z.number().min(15, "Duration must be at least 15 minutes"),
  duration_minutes: z.number().min(15),
  format: z.string(),
  price: z.number().min(0),
  max_participants: z.number().min(1),
  instructor_id: z.string().uuid(),
  location: z.string().optional(),
  is_online: z.boolean().default(false),
  requirements: z.string().optional(),
  cancellation_policy: z.string().optional(),
  is_active: z.boolean().default(true),
  tags: z.array(z.string()).optional(),
  date: z.string().optional(),
  time_of_day: z.string().optional(),
  zoom_link: z.string().url().optional(),
  learning_outcomes: z.array(z.string()).optional(),
  image_url: z.string().url().optional(),
  language: z.string().optional(),
  is_published: z.boolean().default(true),
  is_public: z.boolean().default(true),
  created_at: z.string(),
  updated_at: z.string()
});

export const bookingSchema = z.object({
  id: z.string().uuid(),
  session_id: z.string().uuid(),
  student_id: z.string().uuid(),
  instructor_id: z.string().uuid(),
  booking_date: z.string(),
  start_time: z.string(),
  end_time: z.string(),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed']),
  payment_status: z.enum(['pending', 'paid', 'failed', 'refunded']),
  total_amount: z.number().min(0),
  notes: z.string().optional(),
  cancellation_reason: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export const reviewSchema = z.object({
  id: z.string().uuid(),
  booking_id: z.string().uuid(),
  reviewer_id: z.string().uuid(),
  reviewee_id: z.string().uuid(),
  rating: z.number().min(1).max(5),
  comment: z.string().optional(),
  is_public: z.boolean().default(true),
  created_at: z.string(),
  updated_at: z.string()
});

export const messageSchema = z.object({
  id: z.string().uuid(),
  sender_id: z.string().uuid(),
  recipient_id: z.string().uuid(),
  booking_id: z.string().uuid().optional(),
  content: z.string().min(1, "Message content is required"),
  message_type: z.enum(['text', 'image', 'file']).default('text'),
  is_read: z.boolean().default(false),
  created_at: z.string(),
  updated_at: z.string()
});

export const conversationSchema = z.object({
  id: z.string().uuid(),
  participant_1_id: z.string().uuid(),
  participant_2_id: z.string().uuid(),
  last_message_id: z.string().uuid().optional(),
  last_message_at: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export const notificationSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  message: z.string().min(1, "Message is required"),
  type: z.enum(['booking', 'message', 'review', 'system']),
  is_read: z.boolean().default(false),
  data: z.record(z.any()).optional(),
  created_at: z.string(),
  updated_at: z.string()
});

export const notificationPreferenceSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  email_notifications: z.boolean().default(true),
  push_notifications: z.boolean().default(true),
  sms_notifications: z.boolean().default(false),
  booking_reminders: z.boolean().default(true),
  message_notifications: z.boolean().default(true),
  review_notifications: z.boolean().default(true),
  created_at: z.string(),
  updated_at: z.string()
});

export const deviceTokenSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  token: z.string().min(1, "Token is required"),
  platform: z.enum(['ios', 'android', 'web']),
  is_active: z.boolean().default(true),
  created_at: z.string(),
  updated_at: z.string()
});

export const socialAccountSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  provider: z.enum(['google', 'facebook', 'apple', 'github']),
  provider_id: z.string().min(1, "Provider ID is required"),
  email: z.string().email().optional(),
  name: z.string().optional(),
  avatar_url: z.string().url().optional(),
  access_token: z.string().optional(),
  refresh_token: z.string().optional(),
  expires_at: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string()
});

// Type exports
export type UserProfile = z.infer<typeof userProfileSchema>;
export type InstructorProfile = z.infer<typeof instructorProfileSchema>;
export type Session = z.infer<typeof sessionSchema>;
export type Booking = z.infer<typeof bookingSchema>;
export type Review = z.infer<typeof reviewSchema>;
export type Message = z.infer<typeof messageSchema>;
export type Conversation = z.infer<typeof conversationSchema>;
export type Notification = z.infer<typeof notificationSchema>;
export type NotificationPreference = z.infer<typeof notificationPreferenceSchema>;
export type DeviceToken = z.infer<typeof deviceTokenSchema>;
export type SocialAccount = z.infer<typeof socialAccountSchema>;

// Insert types (for creating new records)
export type InsertUserProfile = Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>;
export type InsertInstructorProfile = Omit<InstructorProfile, 'id' | 'created_at' | 'updated_at'>;
export type InsertSession = Omit<Session, 'id' | 'created_at' | 'updated_at'>;
export type InsertBooking = Omit<Booking, 'id' | 'created_at' | 'updated_at'>;
export type InsertReview = Omit<Review, 'id' | 'created_at' | 'updated_at'>;
export type InsertMessage = Omit<Message, 'id' | 'created_at' | 'updated_at'>;
export type InsertConversation = Omit<Conversation, 'id' | 'created_at' | 'updated_at'>;
export type InsertNotification = Omit<Notification, 'id' | 'created_at' | 'updated_at'>;
export type InsertSocialAccount = Omit<SocialAccount, 'id' | 'created_at' | 'updated_at'>;

// Extended types with relationships
export type SessionWithTeacher = Session & {
  teacher: UserProfile & {
    isTeacher: true;
    teacherSince: string;
    rating?: number;
    reviewCount?: number;
    sessionCount?: number;
    studentCount?: number;
  };
  instructor: UserProfile;
  instructor_profile: InstructorProfile;
};

export type BookingWithDetails = Booking & {
  session: Session;
  student: UserProfile;
  instructor: UserProfile;
};

export type ReviewWithDetails = Review & {
  reviewer: UserProfile;
  reviewee: UserProfile;
  booking: BookingWithDetails;
};

export type MessageWithSender = Message & {
  sender: UserProfile;
  recipient: UserProfile;
};

export type ConversationWithMessages = Conversation & {
  messages: MessageWithSender[];
  participant_1: UserProfile;
  participant_2: UserProfile;
};

export type ReviewWithUser = Review & {
  reviewer: UserProfile;
  reviewee: UserProfile;
};

// Legacy compatibility types
export type BookingWithSession = BookingWithDetails;
export type User = UserProfile;

// Compatibility exports for existing code
export const User = userProfileSchema;
export const UserWithProfile = userProfileSchema;
export const SessionWithInstructor = sessionSchema;
export const TeacherProfile = userProfileSchema;