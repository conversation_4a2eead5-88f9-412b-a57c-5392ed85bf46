{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["./shared/*"]}, "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "app/**/*"], "exclude": ["node_modules", "scripts/**/*", "server/**/*", "migrations/**/*"]}