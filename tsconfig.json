{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/*": ["./shared/*"]}, "strictNullChecks": false, "noImplicitAny": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "app/**/*"], "exclude": ["node_modules", "scripts/**/*", "server/**/*", "migrations/**/*", "src/features/reviews/**/*", "src/features/search/**/*", "src/features/messaging/**/*", "src/services/supabase/messageService.ts", "src/services/supabase/bookingService.ts", "src/services/supabase/enhancedStorageService.ts", "src/services/supabase/secureStorageService.ts", "src/services/supabase/storageCleanupService.ts", "supabase/functions/**/*"]}