# Table: bookings.payouts

## Columns

    column_name    |          data_type          | is_nullable |           column_default            
-------------------+-----------------------------+-------------+-------------------------------------
 id                | integer                     | NO          | nextval('payouts_id_seq'::regclass)
 user_id           | integer                     | NO          | 
 payment_method_id | integer                     | YES         | 
 amount            | numeric                     | NO          | 
 currency          | character varying           | NO          | 'USD'::character varying
 status            | character varying           | NO          | 'pending'::character varying
 stripe_payout_id  | character varying           | YES         | 
 created_at        | timestamp without time zone | NO          | now()
 processed_at      | timestamp without time zone | YES         | 
 notes             | text                        | YES         | 
(10 rows)



## Primary Keys

 constraint_name | column_name 
-----------------+-------------
 payouts_pkey    | id
(1 row)



## Foreign Keys

        constraint_name         |    column_name    | foreign_table_schema |     foreign_table_name     | foreign_column_name 
--------------------------------+-------------------+----------------------+----------------------------+---------------------
 payouts_payment_method_id_fkey | payment_method_id | bookings             | teacher_payment_methods | id
(1 row)



## Indexes

  indexname   |                               indexdef                                
--------------+-----------------------------------------------------------------------
 payouts_pkey | CREATE UNIQUE INDEX payouts_pkey ON bookings.payouts USING btree (id)
(1 row)



## Sample Data

 id | user_id | payment_method_id | amount | currency | status | stripe_payout_id | created_at | processed_at | notes 
----+---------+-------------------+--------+----------+--------+------------------+------------+--------------+-------
(0 rows)



