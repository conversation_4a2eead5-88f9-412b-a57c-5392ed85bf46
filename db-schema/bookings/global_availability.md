# Table: bookings.global_availability

## Columns

  column_name  |          data_type          | is_nullable | column_default 
---------------+-----------------------------+-------------+----------------
 id            | integer                     | NO          | 
 teacher_id | integer                     | NO          | 
 day_of_week   | integer                     | NO          | 
 start_time    | time without time zone      | NO          | 
 end_time      | time without time zone      | NO          | 
 timezone      | character varying           | NO          | 
 created_at    | timestamp without time zone | NO          | now()
 updated_at    | timestamp without time zone | NO          | now()
(8 rows)



## Primary Keys

     constraint_name      | column_name 
--------------------------+-------------
 global_availability_pkey | id
(1 row)



## Foreign Keys

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



## Indexes

        indexname         |                                           indexdef                                            
--------------------------+-----------------------------------------------------------------------------------------------
 global_availability_pkey | CREATE UNIQUE INDEX global_availability_pkey ON bookings.global_availability USING btree (id)
(1 row)



## Sample Data

 id | teacher_id | day_of_week | start_time | end_time | timezone | created_at | updated_at 
----+---------------+-------------+------------+----------+----------+------------+------------
(0 rows)



