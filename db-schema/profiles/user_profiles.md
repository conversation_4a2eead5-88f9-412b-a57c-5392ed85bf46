# Table: profiles.user_profiles

## Columns

      column_name       |          data_type          | is_nullable |              column_default               
------------------------+-----------------------------+-------------+-------------------------------------------
 id                     | integer                     | NO          | nextval('user_profiles_id_seq'::regclass)
 user_id                | integer                     | NO          | 
 name                   | text                        | YES         | 
 bio                    | text                        | YES         | 
 avatar                 | text                        | YES         | 
 cover_photo            | text                        | YES         | 
 cover_photo_position   | character varying           | YES         | 
 timezone               | text                        | YES         | 
 phone                  | text                        | YES         | 
 location               | text                        | YES         | 
 website                | text                        | YES         | 
 facebook_url           | text                        | YES         | 
 twitter_url            | text                        | YES         | 
 instagram_url          | text                        | YES         | 
 linkedin_url           | text                        | YES         | 
 youtube_url            | text                        | YES         | 
 tiktok_url             | text                        | YES         | 
 specializations        | ARRAY                       | YES         | 
 skills                 | ARRAY                       | YES         | 
 certifications         | ARRAY                       | YES         | 
 education              | text                        | YES         | 
 experience             | text                        | YES         | 
 rating                 | double precision            | YES         | 
 review_count           | integer                     | YES         | 
 teacher_since          | timestamp without time zone | YES         | 
 is_teacher             | boolean                     | YES         | false
 is_teacher          | boolean                     | YES         | false
 show_teaching_sessions | boolean                     | YES         | true
 show_learning_sessions | boolean                     | YES         | false
 show_profile           | boolean                     | YES         | true
 show_social_links      | boolean                     | YES         | true
 show_contact           | boolean                     | YES         | false
 privacy_settings       | jsonb                       | YES         | 
 created_at             | timestamp without time zone | NO          | now()
 updated_at             | timestamp without time zone | NO          | now()
(35 rows)



## Primary Keys

  constraint_name   | column_name 
--------------------+-------------
 user_profiles_pkey | id
(1 row)



## Foreign Keys

      constraint_name       | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
----------------------------+-------------+----------------------+--------------------+---------------------
 user_profiles_user_id_fkey | user_id     | auth                 | users              | id
(1 row)



## Indexes

         indexname         |                                           indexdef                                            
---------------------------+-----------------------------------------------------------------------------------------------
 user_profiles_pkey        | CREATE UNIQUE INDEX user_profiles_pkey ON profiles.user_profiles USING btree (id)
 user_profiles_user_id_key | CREATE UNIQUE INDEX user_profiles_user_id_key ON profiles.user_profiles USING btree (user_id)
(2 rows)



## Sample Data

 id | user_id |           name           |                                                                             bio                                                                              |                                                 avatar                                                 |                                              cover_photo                                              | cover_photo_position |     timezone     | phone | location | website | facebook_url | twitter_url | instagram_url | linkedin_url | youtube_url | tiktok_url |          specializations           |                           skills                            |                     certifications                     |                        education                        |                                     experience                                     | rating | review_count | teacher_since | is_teacher | is_teacher | show_teaching_sessions | show_learning_sessions | show_profile | show_social_links | show_contact |                  privacy_settings                  |         created_at         |         updated_at         
----+---------+--------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------+-------------------------------------------------------------------------------------------------------+----------------------+------------------+-------+----------+---------+--------------+-------------+---------------+--------------+-------------+------------+------------------------------------+-------------------------------------------------------------+--------------------------------------------------------+---------------------------------------------------------+------------------------------------------------------------------------------------+--------+--------------+---------------+------------+---------------+------------------------+------------------------+--------------+-------------------+--------------+----------------------------------------------------+----------------------------+----------------------------
  1 |       1 | Admin User               |                                                                                                                                                              | https://sessionhub-images.s3.amazonaws.com/profiles/avatars/1-ca71ae0d-fa52-4a1d-9e63-9eda87aceaaf.jpg | https://sessionhub-images.s3.amazonaws.com/profiles/covers/1-aea7465d-36b5-421d-b663-beca75e19205.jpg | 50%                  | America/New_York |       |          |         |              |             |               |              |             |            | {Admin,Development}                | {"System Administration",Teaching}                          | {"Technical Certification"}                            |                                                         | System administrator and developer                                                 |    4.5 |            5 |               | t          | t             | t                      | f                      | t            | t                 | f            | {"allowMessages": "all", "showOnlineStatus": true} | 2025-04-23 00:02:40.004834 | 2025-04-23 00:02:40.004834
  2 |       2 | Sarah Johnson            | Certified yoga teacher with 8+ years of experience specializing in Hatha and Vinyasa yoga.                                                                | https://sessionhub-images.s3.amazonaws.com/profiles/avatars/2-88221052-be3a-498c-8975-89b14c312d0c.jpg | https://sessionhub-images.s3.amazonaws.com/profiles/covers/2-bfd6f1ed-43b9-42cc-810d-f7dbb60c545d.jpg | 50%                  |                  |       |          |         |              |             |               |              |             |            | {Yoga,Meditation}                  | {Vinyasa,Hatha,Yin}                                         | {"200hr YTT"}                                          |                                                         | Over 5 years teaching experience                                                   |    4.8 |           42 |               | t          | t             | t                      | f                      | t            | t                 | f            | {"allowMessages": "all", "showOnlineStatus": true} | 2025-04-23 05:39:54.135812 | 2025-04-23 06:01:18.463622
  3 |       3 | [SAMPLE] Emily Rodriguez | Certified yoga teacher with 10+ years of experience specializing in Vinyasa and Yin yoga. This is a sample teacher profile for demonstration purposes. | https://sessionhub-images.s3.amazonaws.com/profiles/avatars/3-27025e3f-05fb-4e10-807e-7c66420c5960.jpg | https://sessionhub-images.s3.amazonaws.com/profiles/covers/3-a590449c-b6bd-4894-9dff-9ff013167ff3.jpg | 50%                  |                  |       |          |         |              |             |               |              |             |            | {Yoga,Mindfulness}                 | {Vinyasa,"Power Yoga",Meditation}                           | {"500hr YTT","Mindfulness Certification"}              | Master's in Physical Therapy, Yoga Alliance Certified   | Over 7 years teaching across Asia and Europe                                       |    4.9 |          118 |               | t          | t             | t                      | f                      | t            | t                 | f            | {"allowMessages": "all", "showOnlineStatus": true} | 2025-04-23 05:39:54.135812 | 2025-04-23 06:01:18.463622
  4 |       4 | [SAMPLE] Michael Chen    | HIIT and strength training specialist with focus on functional fitness. Sample teacher for demonstration purposes.                                        | https://sessionhub-images.s3.amazonaws.com/profiles/avatars/4-73b2f9b5-f8df-4a0d-a777-11d36d7a93c1.jpg | https://sessionhub-images.s3.amazonaws.com/profiles/covers/4-d35d4158-e65c-4b9f-9613-6ab4d720fc35.jpg | 50%                  |                  |       |          |         |              |             |               |              |             |            | {Fitness,HIIT,"Strength Training"} | {"Circuit Training","Weight Lifting","Functional Training"} | {"NASM CPT","TRX Certified"}                           | B.S. in Kinesiology, Sports Science Institute Certified | Fitness coach with 10+ years experience working with athletes                      |    4.7 |           83 |               | t          | t             | t                      | f                      | t            | t                 | f            | {"allowMessages": "all", "showOnlineStatus": true} | 2025-04-23 05:39:54.135812 | 2025-04-23 06:01:18.463622
  5 |       5 | [SAMPLE] Sofia Martinez  | Professional dancer specializing in Latin and contemporary styles. This is a sample teacher profile.                                                      | https://sessionhub-images.s3.amazonaws.com/profiles/avatars/5-450adac9-709b-494f-873a-6a08f303fb3e.jpg | https://sessionhub-images.s3.amazonaws.com/profiles/covers/5-a2d7578e-e618-49c2-a6a6-7ac63430d547.jpg | 50%                  |                  |       |          |         |              |             |               |              |             |            | {Dance,Choreography}               | {Salsa,Bachata,Contemporary}                                | {"Professional Dance Certification","Dance Education"} | BFA in Dance, Conservatory of Performing Arts           | Professional dancer and choreographer with 12 years experience teaching all levels |    4.9 |          156 |               | t          | t             | t                      | f                      | t            | t                 | f            | {"allowMessages": "all", "showOnlineStatus": true} | 2025-04-23 05:39:54.135812 | 2025-04-23 06:01:18.463622
(5 rows)



