# Table: messaging.scheduled_messages

## Columns

   column_name   |          data_type          | is_nullable |                 column_default                  
-----------------+-----------------------------+-------------+-------------------------------------------------
 id              | integer                     | NO          | nextval('scheduled_messages_id_seq1'::regclass)
 conversation_id | integer                     | NO          | 
 sender_id       | integer                     | NO          | 
 content         | text                        | NO          | 
 scheduled_time  | timestamp without time zone | NO          | 
 status          | character varying           | NO          | 'pending'::character varying
 created_at      | timestamp without time zone | NO          | now()
 sent_at         | timestamp without time zone | YES         | 
 booking_id      | integer                     | YES         | 
 session_id      | integer                     | YES         | 
 metadata        | jsonb                       | YES         | 
(11 rows)



## Primary Keys

     constraint_name      | column_name 
--------------------------+-------------
 scheduled_messages_pkey1 | id
(1 row)



## Foreign Keys

             constraint_name              |   column_name   | foreign_table_schema | foreign_table_name | foreign_column_name 
------------------------------------------+-----------------+----------------------+--------------------+---------------------
 scheduled_messages_booking_id_fkey       | booking_id      | bookings             | bookings           | id
 scheduled_messages_conversation_id_fkey1 | conversation_id | messaging            | conversations      | id
 scheduled_messages_session_id_fkey       | session_id      | content              | sessions           | id
(3 rows)



## Indexes

             indexname              |                                                       indexdef                                                       
------------------------------------+----------------------------------------------------------------------------------------------------------------------
 scheduled_messages_pkey1           | CREATE UNIQUE INDEX scheduled_messages_pkey1 ON messaging.scheduled_messages USING btree (id)
 idx_scheduled_messages_status_time | CREATE INDEX idx_scheduled_messages_status_time ON messaging.scheduled_messages USING btree (status, scheduled_time)
(2 rows)



## Sample Data

 id | conversation_id | sender_id |                                                                content                                                                |       scheduled_time       | status |         created_at         |          sent_at           | booking_id | session_id |                              metadata                               
----+-----------------+-----------+---------------------------------------------------------------------------------------------------------------------------------------+----------------------------+--------+----------------------------+----------------------------+------------+------------+---------------------------------------------------------------------
  1 |               1 |         6 | This is a test scheduled message from the test script.                                                                                | 2025-04-17 23:20:01.883768 | sent   | 2025-04-18 07:14:53.470323 | 2025-04-17 23:20:12.885594 |            |            | {"error": null, "links": [], "attachments": [], "recipient_id": 7}
  4 |               2 |         6 | Hello {student}, thank you for booking a session with {teacher}. We look forward to seeing you for your {session} session!         | 2025-04-18 02:00:00        | sent   | 2025-04-18 07:14:53.470323 | 2025-04-18 02:05:39.263886 |            |            | {"error": null, "links": [], "attachments": [], "recipient_id": 11}
  2 |               2 |         6 | Hi {student}, just a reminder that your session with {teacher} is coming up soon. Please be ready 5 minutes before the start time. | 2025-04-19 00:00:00        | sent   | 2025-04-18 07:14:53.470323 | 2025-04-24 14:03:39.243    |            |            | {"error": null, "links": [], "attachments": [], "recipient_id": 11}
  3 |               2 |         6 | Thank you for attending the session, {student}! We hope you enjoyed it. Please let us know if you have any questions or feedback.     | 2025-04-19 04:00:00        | sent   | 2025-04-18 07:14:53.470323 | 2025-04-24 14:03:39.391    |            |            | {"error": null, "links": [], "attachments": [], "recipient_id": 11}
(4 rows)



