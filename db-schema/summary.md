# Database Schema Summary

## Schema: auth

### Table: email_verification_tokens

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: password_reset_tokens

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: session

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: users

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



## Schema: backup

### Table: user_profiles_backup

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: users_backup

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



## Schema: bookings

### Table: bookings

#### Relationships

      constraint_name       | column_name  | foreign_table_schema | foreign_table_name | foreign_column_name 
----------------------------+--------------+----------------------+--------------------+---------------------
 bookings_session_id_fkey   | session_id   | content              | sessions           | id
 bookings_time_slot_id_fkey | time_slot_id | bookings             | time_slots         | id
(2 rows)



### Table: global_availability

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: teacher_availability

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: teacher_payment_methods

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: payouts

#### Relationships

        constraint_name         |    column_name    | foreign_table_schema |     foreign_table_name     | foreign_column_name 
--------------------------------+-------------------+----------------------+----------------------------+---------------------
 payouts_payment_method_id_fkey | payment_method_id | bookings             | teacher_payment_methods | id
(1 row)



### Table: platform_fees

#### Relationships

        constraint_name        | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-------------------------------+-------------+----------------------+--------------------+---------------------
 platform_fees_booking_id_fkey | booking_id  | bookings             | bookings           | id
(1 row)



### Table: time_slots

#### Relationships

      constraint_name       | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
----------------------------+-------------+----------------------+--------------------+---------------------
 time_slots_booking_id_fkey | booking_id  | bookings             | bookings           | id
(1 row)



## Schema: content

### Table: reviews

#### Relationships

     constraint_name     | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-------------------------+-------------+----------------------+--------------------+---------------------
 reviews_session_id_fkey | session_id  | content              | sessions           | id
(1 row)



### Table: sessions

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



## Schema: messaging

### Table: conversations

#### Relationships

        constraint_name        | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-------------------------------+-------------+----------------------+--------------------+---------------------
 conversations_booking_id_fkey | booking_id  | bookings             | bookings           | id
(1 row)



### Table: messages

#### Relationships

        constraint_name        |   column_name   | foreign_table_schema | foreign_table_name | foreign_column_name 
-------------------------------+-----------------+----------------------+--------------------+---------------------
 messages_conversation_id_fkey | conversation_id | messaging            | conversations      | id
(1 row)



### Table: notification_preferences

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: scheduled_messages

#### Relationships

             constraint_name              |   column_name   | foreign_table_schema | foreign_table_name | foreign_column_name 
------------------------------------------+-----------------+----------------------+--------------------+---------------------
 scheduled_messages_booking_id_fkey       | booking_id      | bookings             | bookings           | id
 scheduled_messages_conversation_id_fkey1 | conversation_id | messaging            | conversations      | id
 scheduled_messages_session_id_fkey       | session_id      | content              | sessions           | id
(3 rows)




## Schema: pooling

## Schema: profiles

### Table: social_accounts

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: user_devices

#### Relationships

 constraint_name | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
-----------------+-------------+----------------------+--------------------+---------------------
(0 rows)



### Table: user_payment_info

#### Relationships

        constraint_name         | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
--------------------------------+-------------+----------------------+--------------------+---------------------
 user_payment_info_user_id_fkey | user_id     | auth                 | users              | id
(1 row)



### Table: user_profiles

#### Relationships

      constraint_name       | column_name | foreign_table_schema | foreign_table_name | foreign_column_name 
----------------------------+-------------+----------------------+--------------------+---------------------
 user_profiles_user_id_fkey | user_id     | auth                 | users              | id
(1 row)



## Schema: public

