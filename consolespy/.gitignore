# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
out/
.next/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Operating System Files
.DS_Store
Thumbs.db
.Spotlight-V100
.Trashes
ehthumbs.db
Desktop.ini

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.settings/
.classpath
.factorypath

# Debug files
.nyc_output/
coverage/

# Temporary files
tmp/
temp/
.cache/

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Puppeteer Chromium
**/node_modules/puppeteer/.local-chromium