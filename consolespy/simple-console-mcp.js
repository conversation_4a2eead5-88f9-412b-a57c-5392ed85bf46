#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
    CallToolRequestSchema,
    ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import http from 'http';
import fs from 'fs';
import path from 'path';

// Store for console logs
let consoleLogs = [];
let logServer = null;

// Define the Console Spy tools
const CONSOLE_TOOLS = [
    {
        name: 'getBrowserConsoleLogs',
        description: 'Get recent browser console logs from the monitoring server',
        inputSchema: {
            type: 'object',
            properties: {
                limit: {
                    type: 'number',
                    description: 'Maximum number of logs to return (default: 50)',
                    default: 50
                }
            },
            required: [],
        },
    },
    {
        name: 'clearConsoleLogs',
        description: 'Clear all stored console logs',
        inputSchema: {
            type: 'object',
            properties: {},
            required: [],
        },
    },
    {
        name: 'startConsoleMonitor',
        description: 'Start the browser console monitoring server',
        inputSchema: {
            type: 'object',
            properties: {
                port: {
                    type: 'number',
                    description: 'Port to run the monitor on (default: 4006)',
                    default: 4006
                }
            },
            required: [],
        },
    }
];

// Create the server
const server = new Server(
    {
        name: 'browser-console-spy',
        version: '1.0.0',
    },
    {
        capabilities: {
            tools: {},
        },
    }
);

// Handle tool listing requests
server.setRequestHandler(ListToolsRequestSchema, async () => ({
    tools: CONSOLE_TOOLS,
}));

// Handle tool call requests
server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    switch (name) {
        case 'getBrowserConsoleLogs':
            const limit = args?.limit || 50;
            const recentLogs = consoleLogs.slice(-limit);
            return {
                content: [
                    {
                        type: 'text',
                        text: recentLogs.length > 0
                            ? `Recent Browser Console Logs (${recentLogs.length} entries):\n\n${recentLogs.join('\n')}`
                            : 'No console logs available. Make sure the browser console monitor is running and the page is loaded.',
                    },
                ],
            };

        case 'clearConsoleLogs':
            consoleLogs = [];
            return {
                content: [
                    {
                        type: 'text',
                        text: 'Console logs cleared successfully.',
                    },
                ],
            };

        case 'startConsoleMonitor':
            const port = args?.port || 4006;
            try {
                if (logServer) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `Console monitor is already running on port ${port}`,
                            },
                        ],
                    };
                }

                await startLogServer(port);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Console monitor started successfully on port ${port}. Make sure your browser has the console-logger.js script loaded.`,
                        },
                    ],
                };
            } catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Failed to start console monitor: ${error.message}`,
                        },
                    ],
                    isError: true,
                };
            }

        default:
            return {
                content: [
                    {
                        type: 'text',
                        text: `Unknown tool: ${name}`,
                    },
                ],
                isError: true,
            };
    }
});

// Function to start the log server
function startLogServer(port) {
    return new Promise((resolve, reject) => {
        logServer = http.createServer((req, res) => {
            // Enable CORS
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            if (req.method === 'POST' && req.url === '/console-logs') {
                let body = '';
                req.on('data', chunk => {
                    body += chunk.toString();
                });

                req.on('end', () => {
                    try {
                        const logData = JSON.parse(body);
                        const timestamp = new Date().toLocaleTimeString();
                        const logEntry = `[${timestamp}] [${logData.level.toUpperCase()}] ${logData.url} - ${logData.message}`;

                        consoleLogs.push(logEntry);

                        // Keep only last 1000 logs to prevent memory issues
                        if (consoleLogs.length > 1000) {
                            consoleLogs = consoleLogs.slice(-1000);
                        }

                        console.error(logEntry); // Log to stderr so it appears in MCP logs

                        res.writeHead(200, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ success: true }));
                    } catch (error) {
                        res.writeHead(400, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Invalid JSON' }));
                    }
                });
            } else if (req.method === 'GET' && req.url === '/logs') {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ logs: consoleLogs }));
            } else {
                res.writeHead(404);
                res.end('Not Found');
            }
        });

        logServer.listen(port, (err) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });

        logServer.on('error', reject);
    });
}

// Start the server
async function runServer() {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('Browser Console Spy MCP Server running on stdio');
}

runServer().catch((error) => {
    console.error('Fatal error running server:', error);
    process.exit(1);
}); 