<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Session Placeholder</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F5F5F5" offset="0%"></stop>
            <stop stop-color="#E0E0E0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="800" height="600"></rect>
        <g id="Icon" transform="translate(300.000000, 200.000000)" fill="#BDBDBD">
            <path d="M200,0 C200,110.457 110.457,200 0,200 C-110.457,200 -200,110.457 -200,0 C-200,-110.457 -110.457,-200 0,-200 C110.457,-200 200,-110.457 200,0 Z M-40,-40 L-40,40 L40,40 L40,-40 L-40,-40 Z M60,-60 L60,60 L-60,60 L-60,-60 L60,-60 Z" id="Combined-Shape"></path>
        </g>
        <text id="Session-Hub" font-family="Arial-BoldMT, Arial" font-size="48" font-weight="bold" fill="#757575">
            <tspan x="280" y="450">Session Hub</tspan>
        </text>
    </g>
</svg>
