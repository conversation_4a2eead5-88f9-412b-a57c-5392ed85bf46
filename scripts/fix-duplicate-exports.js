#!/usr/bin/env node

/**
 * <PERSON>ript to fix duplicate exports issue
 * 
 * This script helps fix the duplicate exports issue that was created by the convert-named-exports.js script.
 * 
 * Usage:
 *   node scripts/fix-duplicate-exports.js [path]
 * 
 * Example:
 *   node scripts/fix-duplicate-exports.js src/components
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File extensions to process
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude
const EXCLUDED_DIRS = ['node_modules', 'dist', 'build', '.git'];

// Files to exclude
const EXCLUDED_FILES = [
  'main.tsx',
  'index.tsx',
  'vite.config.ts',
  'App.tsx',
];

// Get the target path from command line arguments
const targetPath = process.argv[2] || 'src';
const fullTargetPath = path.resolve(process.cwd(), targetPath);

// Counter for tracking changes
let filesProcessed = 0;
let filesModified = 0;
let filesWithErrors = 0;
let filesSkipped = 0;

// Find all source files in the target directory
function findSourceFiles(targetPath) {
  const results = [];
  
  if (EXCLUDED_DIRS.some(dir => targetPath.includes(`/${dir}/`))) {
    return results;
  }
  
  const stat = fs.statSync(targetPath);
  
  if (stat.isFile()) {
    // If it's a file, check if it has a valid extension
    if (EXTENSIONS.includes(path.extname(targetPath))) {
      results.push(targetPath);
    }
  } else if (stat.isDirectory()) {
    // If it's a directory, recursively find all files
    const files = fs.readdirSync(targetPath);
    for (const file of files) {
      const filePath = path.join(targetPath, file);
      const fileStat = fs.statSync(filePath);
      
      if (fileStat.isDirectory() && !EXCLUDED_DIRS.includes(file)) {
        results.push(...findSourceFiles(filePath));
      } else if (EXTENSIONS.includes(path.extname(file)) && !EXCLUDED_FILES.includes(file)) {
        results.push(filePath);
      }
    }
  }
  
  return results;
}

// Process a file to fix duplicate exports
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  filesProcessed++;
  
  try {
    // Skip excluded files
    if (EXCLUDED_FILES.includes(path.basename(filePath))) {
      console.log(`  Skipping excluded file: ${filePath}`);
      filesSkipped++;
      return false;
    }
    
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check for duplicate export patterns
    const duplicateExportPatterns = [
      // Pattern 1: export { ComponentName }; export default ComponentName; // For backward compatibility
      {
        regex: /export\s+{\s+([A-Za-z0-9_]+)\s+};\s*\n\s*export\s+default\s+\1;/g,
        replacement: (match, componentName) => {
          console.log(`  Found duplicate export: ${componentName}`);
          return `export { ${componentName} };\nexport default ${componentName}; // For backward compatibility`;
        }
      },
      // Pattern 2: export const ComponentName = ...; export { ComponentName };
      {
        regex: /export\s+const\s+([A-Za-z0-9_]+)\s*=.*\n.*export\s+{\s+\1\s+};/gs,
        replacement: (match, componentName) => {
          console.log(`  Found duplicate export with export const: ${componentName}`);
          return match.replace(`export { ${componentName} };`, '');
        }
      },
      // Pattern 3: export function ComponentName(...) {...} export { ComponentName };
      {
        regex: /export\s+function\s+([A-Za-z0-9_]+).*\n.*export\s+{\s+\1\s+};/gs,
        replacement: (match, componentName) => {
          console.log(`  Found duplicate export with export function: ${componentName}`);
          return match.replace(`export { ${componentName} };`, '');
        }
      },
      // Pattern 4: export class ComponentName {...} export { ComponentName };
      {
        regex: /export\s+class\s+([A-Za-z0-9_]+).*\n.*export\s+{\s+\1\s+};/gs,
        replacement: (match, componentName) => {
          console.log(`  Found duplicate export with export class: ${componentName}`);
          return match.replace(`export { ${componentName} };`, '');
        }
      }
    ];
    
    // Apply each pattern
    for (const pattern of duplicateExportPatterns) {
      const newContent = content.replace(pattern.regex, pattern.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }
    
    // Write the modified content back to the file
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  Successfully fixed duplicate exports`);
      filesModified++;
      return true;
    } else {
      console.log(`  No changes needed`);
      return false;
    }
  } catch (error) {
    console.error(`  Error processing ${filePath}:`, error);
    filesWithErrors++;
    return false;
  }
}

// Main function
function main() {
  console.log(`Fixing duplicate exports in ${fullTargetPath}`);
  
  // Find all source files
  const sourceFiles = findSourceFiles(fullTargetPath);
  console.log(`Found ${sourceFiles.length} source files to process`);
  
  // Process each file
  for (const filePath of sourceFiles) {
    processFile(filePath);
  }
  
  // Print summary
  console.log('\nSummary:');
  console.log(`  Files processed: ${filesProcessed}`);
  console.log(`  Files modified: ${filesModified}`);
  console.log(`  Files skipped: ${filesSkipped}`);
  console.log(`  Files with errors: ${filesWithErrors}`);
}

main();
