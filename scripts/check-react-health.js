#!/usr/bin/env node

import http from 'http';

function checkReactHealth() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3000', (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                // Check for common React error indicators
                const hasReactRefresh = data.includes('@react-refresh');
                const hasViteClient = data.includes('@vite/client');
                const hasReactRoot = data.includes('id="root"');

                console.log('🔍 React Health Check Results:');
                console.log(`✅ React Refresh: ${hasReactRefresh ? 'Found' : 'Missing'}`);
                console.log(`✅ Vite Client: ${hasViteClient ? 'Found' : 'Missing'}`);
                console.log(`✅ React Root: ${hasReactRoot ? 'Found' : 'Missing'}`);

                if (hasReactRefresh && hasViteClient) {
                    console.log('🎉 Client appears to be running correctly!');
                    console.log('📝 Check browser console for any remaining React hook errors.');
                    resolve(true);
                } else {
                    console.log('❌ Client may have issues. Check the logs.');
                    resolve(false);
                }
            });
        });

        req.on('error', (err) => {
            console.log('❌ Client is not responding:', err.message);
            console.log('💡 Try running: pnpm run start:client');
            reject(err);
        });

        req.setTimeout(5000, () => {
            console.log('⏰ Request timed out. Client may be starting up.');
            req.destroy();
            reject(new Error('Timeout'));
        });
    });
}

checkReactHealth().catch(() => process.exit(1)); 