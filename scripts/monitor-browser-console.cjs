#!/usr/bin/env node

const http = require('http');
const url = require('url');

console.log('🔍 Browser Console Monitor Started');
console.log('📡 Listening for browser console logs...');
console.log('💡 Make sure your browser has the console-logger.js script loaded');
console.log('🌐 Open your app at http://localhost:3002');
console.log('---');

// Simple HTTP server to capture logs
const server = http.createServer((req, res) => {
    if (req.method === 'POST' && req.url === '/console-logs') {
        let body = '';

        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const logData = JSON.parse(body);
                const timestamp = new Date(logData.timestamp).toLocaleTimeString();
                const urlPath = new URL(logData.url).pathname;

                // Format and display the log
                const prefix = `[${timestamp}] [BROWSER-${logData.level.toUpperCase()}] ${urlPath}`;
                console.log(`${prefix} - ${logData.message}`);

                // Send response
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type'
                });
                res.end(JSON.stringify({ success: true }));
            } catch (error) {
                console.error('Error parsing log data:', error);
                res.writeHead(400);
                res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
        });
    } else if (req.method === 'OPTIONS') {
        // Handle CORS preflight
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': 'Content-Type'
        });
        res.end();
    } else {
        res.writeHead(404);
        res.end('Not Found');
    }
});

const PORT = 4006;
server.listen(PORT, () => {
    console.log(`🚀 Console monitor running on http://localhost:${PORT}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down console monitor...');
    server.close(() => {
        process.exit(0);
    });
}); 