#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update import statements to use named imports
 *
 * This script helps automate the conversion of default imports to named imports
 * throughout the codebase.
 *
 * Usage:
 *   node scripts/update-imports.js [path]
 *
 * Example:
 *   node scripts/update-imports.js src/components
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File extensions to process
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude
const EXCLUDED_DIRS = ['node_modules', 'dist', 'build', '.git'];

// Files to exclude
const EXCLUDED_FILES = [
  'main.tsx',
  'vite.config.ts',
];

// Components that have been converted to named exports
const COMPONENTS_WITH_NAMED_EXPORTS = [
  // UI Components
  'BackdropCleaner',
  'SearchBar',
  'FilterBar',
  'MobileFilterBar',
  'SessionCard',
  'InstructorCard',
  'InstructorList',
  'InstructorGrid',
  'InstructorListItem',
  'CategoryCard',
  'SessionListItem',
  'FilterSelect',
  'StarRating',
  'LoadingSpinner',
  'FormWrapper',
  'CardGrid',
  'ErrorDisplay',
  'ReviewsContainer',
  'PaginationWithResults',
  'DataTable',
  'Button',
  'Card',
  'Dialog',
  'Tabs',
  'Avatar',
  'Skeleton',
  'Accordion',
  'AccordionItem',
  'AccordionTrigger',
  'AccordionContent',
  'Popover',
  'PopoverTrigger',
  'PopoverContent',
  'Toaster',
  'Toast',
  'ToastProvider',
  'ToastViewport',

  // Feature Components
  'NotificationPreferencesComponent',
  'ChatPopupComponent',
  'MessageNotificationComponent',
  'ChatPopupManager',
  'SocialLoginButtons',
  'PaymentDialog',
  'StripeCheckoutRedirect',
  'PaymentForm',
  'SessionCardComponent',
  'SessionCardSkeleton',
  'XLogo',
  'ReviewList',
  'ReviewSection',
  'ReviewForm',

  // Context Providers
  'ToastContextProvider',
  'AuthProvider',
  'ProfileProvider',
  'BookingProvider',
  'MessagingProvider',
  'FilterProvider',
  'SessionProvider',
  'NextAuthProvider',
  'StripeProvider',

  // Pages
  'HomePage',
  'ProfilePage',
  'ResetPassword',
  'InstructorDashboardPage',
  'SessionDetailPage',
  'TeacherProfilePage',
  'MessagesPage',
  'BookingsPage',
  'SettingsPage',
  'AdminPage',

  // Hooks
  'useAuth',
  'useProfile',
  'useBooking',
  'useMessaging',
  'useSession',
  'useFilter',
  'useForm',
  'useFetch',
  'useApi',
  'usePagination',
  'useInfiniteScroll',
  'useLocalStorage',
  'useMediaQuery',
  'usePushNotifications',
  'useNextAuth',
  'useEventEmitter',
  'useMobile',
];

// Get the target path from command line arguments
const targetPath = process.argv[2] || 'src';
const fullTargetPath = path.resolve(process.cwd(), targetPath);

// Counter for tracking changes
let filesProcessed = 0;
let filesModified = 0;
let filesWithErrors = 0;
let filesSkipped = 0;

// Find all source files in the target directory
function findSourceFiles(targetPath) {
  const results = [];

  if (EXCLUDED_DIRS.some(dir => targetPath.includes(`/${dir}/`))) {
    return results;
  }

  const stat = fs.statSync(targetPath);

  if (stat.isFile()) {
    // If it's a file, check if it has a valid extension
    if (EXTENSIONS.includes(path.extname(targetPath))) {
      results.push(targetPath);
    }
  } else if (stat.isDirectory()) {
    // If it's a directory, recursively find all files
    const files = fs.readdirSync(targetPath);
    for (const file of files) {
      const filePath = path.join(targetPath, file);
      const fileStat = fs.statSync(filePath);

      if (fileStat.isDirectory() && !EXCLUDED_DIRS.includes(file)) {
        results.push(...findSourceFiles(filePath));
      } else if (EXTENSIONS.includes(path.extname(file)) && !EXCLUDED_FILES.includes(file)) {
        results.push(filePath);
      }
    }
  }

  return results;
}

// Process a file to update import statements and remove backward compatibility exports
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  filesProcessed++;

  try {
    // Skip excluded files
    if (EXCLUDED_FILES.includes(path.basename(filePath))) {
      console.log(`  Skipping excluded file: ${filePath}`);
      filesSkipped++;
      return false;
    }

    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Check for default import patterns for each component
    for (const componentName of COMPONENTS_WITH_NAMED_EXPORTS) {
      const importRegex = new RegExp(`import\\s+${componentName}\\s+from\\s+['"]([^'"]+)['"]`, 'g');

      // Replace default imports with named imports
      const newContent = content.replace(importRegex, (match, importPath) => {
        console.log(`  Found default import: ${componentName} from ${importPath}`);

        // Special case for InstructorCard which has a memoized version
        if (componentName === 'InstructorCard') {
          return `import { MemoizedInstructorCard as ${componentName} } from '${importPath}'`;
        }

        return `import { ${componentName} } from '${importPath}'`;
      });

      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // Remove backward compatibility default exports
    const backwardCompatRegex = /export\s+default\s+\w+;\s*\/\/\s*For\s+backward\s+compatibility.*/g;
    const contentWithoutBackwardCompat = content.replace(backwardCompatRegex, (match) => {
      console.log(`  Removing backward compatibility export: ${match.trim()}`);
      return ''; // Remove the line entirely
    });

    if (contentWithoutBackwardCompat !== content) {
      content = contentWithoutBackwardCompat;
      modified = true;
    }

    // Write the modified content back to the file
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  Successfully updated file`);
      filesModified++;
      return true;
    } else {
      console.log(`  No changes needed`);
      return false;
    }
  } catch (error) {
    console.error(`  Error processing ${filePath}:`, error);
    filesWithErrors++;
    return false;
  }
}

// Main function
function main() {
  console.log(`Updating import statements in ${fullTargetPath}`);

  // Find all source files
  const sourceFiles = findSourceFiles(fullTargetPath);
  console.log(`Found ${sourceFiles.length} source files to process`);

  // Process each file
  for (const filePath of sourceFiles) {
    processFile(filePath);
  }

  // Print summary
  console.log('\nSummary:');
  console.log(`  Files processed: ${filesProcessed}`);
  console.log(`  Files modified: ${filesModified}`);
  console.log(`  Files skipped: ${filesSkipped}`);
  console.log(`  Files with errors: ${filesWithErrors}`);
}

main();
