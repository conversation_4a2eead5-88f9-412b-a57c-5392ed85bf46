#!/usr/bin/env node

/**
 * Direct database update to fix the session teacher_id
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function directDbUpdate() {
    console.log('🚀 Direct database update...\n');

    const sessionId = '3c0c0f52-d6f5-4407-bcac-cb0a2ef81829';
    const teacherId = '028e0933-2730-4689-8564-3f519426037e';

    try {
        // Update the session directly in the database
        const { data, error } = await supabase
            .from('sessions')
            .update({
                teacher_id: teacherId,
                title: 'Yoga for Beginners - Multiple Dates Available',
                image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&auto=format&fit=crop'
            })
            .eq('id', sessionId)
            .select();

        if (error) {
            console.error('❌ Error updating session:', error);
            return;
        }

        console.log('✅ Successfully updated session:', data);

        // Verify the update
        const { data: verifyData, error: verifyError } = await supabase
            .from('sessions')
            .select('id, title, teacher_id, image_url')
            .eq('id', sessionId)
            .single();

        if (verifyError) {
            console.error('❌ Error verifying update:', verifyError);
            return;
        }

        console.log('✅ Verification - Session data:', verifyData);

    } catch (error) {
        console.error('❌ Unexpected error:', error);
    }
}

directDbUpdate(); 