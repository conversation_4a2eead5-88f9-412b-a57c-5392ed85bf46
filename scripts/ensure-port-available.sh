#!/bin/bash

# Script to ensure port 4005 is available
PORT=${1:-4005}

echo "Checking if port $PORT is available..."

# Check if port is in use
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "Port $PORT is in use. Attempting to free it..."
    
    # Kill processes using the port
    lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
    
    # Wait a moment for the port to be freed
    sleep 2
    
    # Check again
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
        echo "Warning: Port $PORT is still in use after cleanup attempt"
        exit 1
    else
        echo "Port $PORT is now available"
    fi
else
    echo "Port $PORT is available"
fi

exit 0 