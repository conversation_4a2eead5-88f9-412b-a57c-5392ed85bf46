/**
 * Feature Export Checker Script
 * 
 * This script examines feature index.ts files to identify potential export issues.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const FEATURES_DIR = path.resolve(__dirname, '../src/features');

// Find all index.ts files in features directory
function findIndexFiles(dir) {
  const results = [];
  const list = fs.readdirSync(dir);
  
  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results.push(...findIndexFiles(filePath));
    } else if (file === 'index.ts') {
      results.push(filePath);
    }
  }
  
  return results;
}

// Check for potential issues in index files
function checkIndexFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // Check for wildcard exports (export * from)
  const wildcardExports = content.match(/export\s+\*\s+from\s+['"][^'"]+['"]/g) || [];
  if (wildcardExports.length > 0) {
    issues.push({
      type: 'wildcard',
      count: wildcardExports.length,
      examples: wildcardExports.slice(0, 3)
    });
  }
  
  // Check for type exports without 'type' keyword
  const typeExportMatches = content.match(/export\s+{[^}]*}/g) || [];
  let potentialTypeIssues = 0;
  
  for (const match of typeExportMatches) {
    // Look for common type names in exports without the 'type' keyword
    if (/\b(Type|Interface|Props|State|Config|Options|Context)\b/i.test(match) && 
        !match.includes('export type')) {
      potentialTypeIssues++;
    }
  }
  
  if (potentialTypeIssues > 0) {
    issues.push({
      type: 'missing-type-keyword',
      count: potentialTypeIssues
    });
  }
  
  // Check for duplicate exports
  const exportedSymbols = new Set();
  const duplicateExports = [];
  
  const exportMatches = content.match(/export\s+{[^}]+}/g) || [];
  for (const match of exportMatches) {
    const symbols = match.replace(/export\s+{|}|type/g, '')
      .split(',')
      .map(s => s.trim().split(' as ')[0].trim())
      .filter(s => s);
    
    for (const symbol of symbols) {
      if (exportedSymbols.has(symbol)) {
        duplicateExports.push(symbol);
      } else {
        exportedSymbols.add(symbol);
      }
    }
  }
  
  if (duplicateExports.length > 0) {
    issues.push({
      type: 'duplicate',
      symbols: duplicateExports
    });
  }
  
  return issues;
}

// Main function
async function main() {
  console.log('Scanning for feature index files...');
  const indexFiles = findIndexFiles(FEATURES_DIR);
  console.log(`Found ${indexFiles.length} index.ts files.`);
  
  let filesWithIssues = 0;
  
  for (const file of indexFiles) {
    const relativePath = path.relative(process.cwd(), file);
    const issues = checkIndexFile(file);
    
    if (issues.length > 0) {
      console.log(`\n${relativePath}:`);
      filesWithIssues++;
      
      for (const issue of issues) {
        if (issue.type === 'wildcard') {
          console.log(`  ⚠️ Contains ${issue.count} wildcard exports (export * from)`);
          console.log(`    Examples: ${issue.examples.join(', ')}`);
          console.log('    Consider using explicit named exports instead');
        } else if (issue.type === 'missing-type-keyword') {
          console.log(`  ⚠️ Contains ${issue.count} potential type exports without 'type' keyword`);
          console.log('    Use "export type { TypeName }" for TypeScript interfaces and types');
        } else if (issue.type === 'duplicate') {
          console.log(`  ⚠️ Contains duplicate exports: ${issue.symbols.join(', ')}`);
          console.log('    Remove duplicate exports to avoid conflicts');
        }
      }
      
      // Show the file content for reference
      console.log('\n  File content:');
      const content = fs.readFileSync(file, 'utf8');
      console.log('  ' + content.replace(/\n/g, '\n  '));
    }
  }
  
  if (filesWithIssues === 0) {
    console.log('\n✅ No obvious issues found in feature index files!');
  } else {
    console.log(`\n⚠️ Found issues in ${filesWithIssues} files.`);
    console.log('\nRecommendations:');
    console.log('1. Replace wildcard exports with explicit named exports');
    console.log('2. Use "export type { TypeName }" for TypeScript interfaces and types');
    console.log('3. Remove duplicate exports');
  }
}

main().catch(console.error);
