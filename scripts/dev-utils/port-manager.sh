#!/bin/bash

# Port Manager Script for Session Hub V.2
# Usage: ./scripts/dev-utils/port-manager.sh [command] [port]

set -e

# Default ports
CLIENT_PORT=3000
SERVER_PORT=4005
SUPABASE_PORT=54321

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[PORT-MANAGER]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -i :$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to get process info for a port
get_port_info() {
    local port=$1
    lsof -i :$port 2>/dev/null || echo "Port $port is free"
}

# Function to kill process on a specific port
kill_port() {
    local port=$1
    local force=${2:-false}
    
    if check_port $port; then
        local pid=$(lsof -ti :$port)
        if [ ! -z "$pid" ]; then
            print_status "Found process $pid using port $port"
            
            if [ "$force" = "true" ]; then
                kill -9 $pid
                print_success "Forcefully killed process $pid on port $port"
            else
                kill $pid
                print_success "Gracefully killed process $pid on port $port"
                sleep 2
                
                # Check if process is still running
                if check_port $port; then
                    print_warning "Process still running, force killing..."
                    kill -9 $pid
                    print_success "Force killed process on port $port"
                fi
            fi
        fi
    else
        print_status "Port $port is already free"
    fi
}

# Function to check all development ports
check_all_ports() {
    print_status "Checking development ports..."
    echo
    
    echo -e "${BLUE}Client Port ($CLIENT_PORT):${NC}"
    get_port_info $CLIENT_PORT
    echo
    
    echo -e "${BLUE}Server Port ($SERVER_PORT):${NC}"
    get_port_info $SERVER_PORT
    echo
    
    echo -e "${BLUE}Supabase Port ($SUPABASE_PORT):${NC}"
    get_port_info $SUPABASE_PORT
    echo
}

# Function to clean all development ports
clean_all_ports() {
    print_status "Cleaning all development ports..."
    
    kill_port $CLIENT_PORT
    kill_port $SERVER_PORT
    kill_port $SUPABASE_PORT
    
    print_success "All development ports cleaned"
}

# Function to find next available port
find_next_port() {
    local start_port=$1
    local port=$start_port
    
    while check_port $port; do
        port=$((port + 1))
        if [ $port -gt $((start_port + 100)) ]; then
            print_error "Could not find available port after checking 100 ports from $start_port"
            exit 1
        fi
    done
    
    echo $port
}

# Function to start development servers with proper port management
start_dev() {
    print_status "Starting development environment..."
    
    # Clean ports first
    clean_all_ports
    
    # Wait a moment for ports to be freed
    sleep 2
    
    # Check if ports are available
    if check_port $CLIENT_PORT; then
        print_warning "Client port $CLIENT_PORT still in use, finding alternative..."
        CLIENT_PORT=$(find_next_port $CLIENT_PORT)
        print_status "Using client port: $CLIENT_PORT"
    fi
    
    if check_port $SERVER_PORT; then
        print_warning "Server port $SERVER_PORT still in use, finding alternative..."
        SERVER_PORT=$(find_next_port $SERVER_PORT)
        print_status "Using server port: $SERVER_PORT"
    fi
    
    print_success "Ports ready - Client: $CLIENT_PORT, Server: $SERVER_PORT"
    
    # Export ports for use by other scripts
    export CLIENT_PORT
    export SERVER_PORT
    export SUPABASE_PORT
    
    # Start servers
    print_status "Starting servers..."
    echo "Use these commands in separate terminals:"
    echo "  Client:  cd client && VITE_PORT=$CLIENT_PORT npm run dev"
    echo "  Server:  PORT=$SERVER_PORT npm run dev"
}

# Main script logic
case "${1:-help}" in
    "check")
        if [ -n "$2" ]; then
            get_port_info $2
        else
            check_all_ports
        fi
        ;;
    "kill")
        if [ -n "$2" ]; then
            kill_port $2 ${3:-false}
        else
            print_error "Please specify a port number"
            exit 1
        fi
        ;;
    "clean")
        clean_all_ports
        ;;
    "start")
        start_dev
        ;;
    "find")
        if [ -n "$2" ]; then
            next_port=$(find_next_port $2)
            print_success "Next available port after $2: $next_port"
        else
            print_error "Please specify a starting port number"
            exit 1
        fi
        ;;
    "help"|*)
        echo "Port Manager for Session Hub V.2"
        echo
        echo "Usage: $0 [command] [options]"
        echo
        echo "Commands:"
        echo "  check [port]     - Check if port is in use (or check all dev ports)"
        echo "  kill <port>      - Kill process using specified port"
        echo "  clean            - Clean all development ports"
        echo "  start            - Start development environment with clean ports"
        echo "  find <port>      - Find next available port starting from given port"
        echo "  help             - Show this help message"
        echo
        echo "Examples:"
        echo "  $0 check         - Check all development ports"
        echo "  $0 check 3000    - Check if port 3000 is in use"
        echo "  $0 kill 3000     - Kill process on port 3000"
        echo "  $0 clean         - Clean all development ports"
        echo "  $0 start         - Start clean development environment"
        ;;
esac 