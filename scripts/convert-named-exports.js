#!/usr/bin/env node

/**
 * <PERSON>ript to convert default exports to named exports
 * 
 * This script helps automate the conversion of default exports to named exports
 * while maintaining backward compatibility during the migration period.
 * 
 * Usage:
 *   node scripts/convert-named-exports.js [path]
 * 
 * Example:
 *   node scripts/convert-named-exports.js src/components
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File extensions to process
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude
const EXCLUDED_DIRS = ['node_modules', 'dist', 'build', '.git'];

// Files to exclude (these are allowed to have default exports)
const EXCLUDED_FILES = [
  'main.tsx',
  'index.tsx',
  'vite.config.ts',
  'App.tsx',
];

// Get the target path from command line arguments
const targetPath = process.argv[2] || 'src';
const fullTargetPath = path.resolve(process.cwd(), targetPath);

// Counter for tracking changes
let filesProcessed = 0;
let filesModified = 0;
let filesWithErrors = 0;
let filesSkipped = 0;

// Find all source files in the target directory
function findSourceFiles(targetPath) {
  const results = [];

  if (EXCLUDED_DIRS.some(dir => targetPath.includes(`/${dir}/`))) {
    return results;
  }

  const stat = fs.statSync(targetPath);

  if (stat.isFile()) {
    // If it's a file, check if it has a valid extension
    if (EXTENSIONS.includes(path.extname(targetPath))) {
      results.push(targetPath);
    }
  } else if (stat.isDirectory()) {
    // If it's a directory, recursively find all files
    const files = fs.readdirSync(targetPath);
    for (const file of files) {
      const filePath = path.join(targetPath, file);
      const fileStat = fs.statSync(filePath);

      if (fileStat.isDirectory() && !EXCLUDED_DIRS.includes(file)) {
        results.push(...findSourceFiles(filePath));
      } else if (EXTENSIONS.includes(path.extname(file)) && !EXCLUDED_FILES.includes(file)) {
        results.push(filePath);
      }
    }
  }

  return results;
}

// Process a file to convert default exports to named exports
function processFile(filePath) {
  console.log(`Processing ${filePath}...`);
  filesProcessed++;

  try {
    // Skip excluded files
    if (EXCLUDED_FILES.includes(path.basename(filePath))) {
      console.log(`  Skipping excluded file: ${filePath}`);
      filesSkipped++;
      return false;
    }

    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Check for default export patterns
    const defaultExportPatterns = [
      // Pattern 1: export default ComponentName;
      {
        regex: /export\s+default\s+([A-Za-z0-9_]+)\s*;/g,
        replacement: (match, componentName) => {
          console.log(`  Found default export: ${componentName}`);
          return `export { ${componentName} };\nexport default ${componentName}; // For backward compatibility`;
        }
      },
      // Pattern 2: export default function ComponentName() { ... }
      {
        regex: /export\s+default\s+function\s+([A-Za-z0-9_]+)/g,
        replacement: (match, componentName) => {
          console.log(`  Found default export function: ${componentName}`);
          return `export function ${componentName}`;
        }
      },
      // Pattern 3: export default class ComponentName { ... }
      {
        regex: /export\s+default\s+class\s+([A-Za-z0-9_]+)/g,
        replacement: (match, componentName) => {
          console.log(`  Found default export class: ${componentName}`);
          return `export class ${componentName}`;
        }
      },
      // Pattern 4: const ComponentName = () => { ... }; export default ComponentName;
      {
        regex: /const\s+([A-Za-z0-9_]+)\s*=\s*(?:React\.memo\()?(?:function|\()/,
        replacement: (match, componentName) => {
          // Only replace if there's a default export of this component
          if (content.includes(`export default ${componentName}`)) {
            console.log(`  Found component declaration with separate default export: ${componentName}`);
            // We'll handle the export default separately
            return `export const ${componentName} = ${match.substring(6)}`;
          }
          return match;
        }
      },
      // Pattern 5: const ComponentName = memo(() => { ... }); export default ComponentName;
      {
        regex: /const\s+([A-Za-z0-9_]+)\s*=\s*memo\(/,
        replacement: (match, componentName) => {
          // Only replace if there's a default export of this component
          if (content.includes(`export default ${componentName}`)) {
            console.log(`  Found memoized component with separate default export: ${componentName}`);
            // We'll handle the export default separately
            return `export const ${componentName} = memo(`;
          }
          return match;
        }
      }
    ];

    // Apply each pattern
    for (const pattern of defaultExportPatterns) {
      const newContent = content.replace(pattern.regex, pattern.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    }

    // Handle separate export default statements
    if (modified) {
      // Find separate export default statements
      const exportDefaultRegex = /export\s+default\s+([A-Za-z0-9_]+)\s*;/g;
      let match;
      const componentNames = [];

      while ((match = exportDefaultRegex.exec(content)) !== null) {
        componentNames.push(match[1]);
      }

      // Replace separate export default statements with backward compatibility comments
      for (const componentName of componentNames) {
        const exportDefaultPattern = new RegExp(`export\\s+default\\s+${componentName}\\s*;`, 'g');
        content = content.replace(exportDefaultPattern, `export default ${componentName}; // For backward compatibility`);
      }
    }

    // Special case for anonymous default exports
    if (content.includes('export default') && !modified) {
      console.log(`  Warning: Found complex default export pattern that couldn't be automatically converted`);
      console.log(`  Please manually convert: ${filePath}`);
      return false;
    }

    // Write the modified content back to the file
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  Successfully converted to named export`);
      filesModified++;
      return true;
    } else {
      console.log(`  No changes needed`);
      return false;
    }
  } catch (error) {
    console.error(`  Error processing ${filePath}:`, error);
    filesWithErrors++;
    return false;
  }
}

// Main function
function main() {
  console.log(`Converting default exports to named exports in ${fullTargetPath}`);

  // Find all source files
  const sourceFiles = findSourceFiles(fullTargetPath);
  console.log(`Found ${sourceFiles.length} source files to process`);

  // Process each file
  for (const filePath of sourceFiles) {
    processFile(filePath);
  }

  // Print summary
  console.log('\nSummary:');
  console.log(`  Files processed: ${filesProcessed}`);
  console.log(`  Files modified: ${filesModified}`);
  console.log(`  Files skipped: ${filesSkipped}`);
  console.log(`  Files with errors: ${filesWithErrors}`);
}

main();
