#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 Validating Architecture Separation...\n');

// Read package.json files
const rootPkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const clientPkg = JSON.parse(fs.readFileSync('client/package.json', 'utf8'));

// Define what should be where
const CLIENT_ONLY_PATTERNS = [
    /^@radix-ui\/react-/,
    /^react-/,
    /^@vitejs\//,
    /^tailwind/,
    /^@replit\/vite-plugin/,
    /^@emotion\//,
    /^framer-motion$/,
    /^lucide-react$/,
    /^@tanstack\/react-query$/,
    /^clsx$/,
    /^class-variance-authority$/,
    /^cmdk$/,
    /^vaul$/,
    /^wouter$/,
    /^@emoji-mart\//,
    /^emoji-mart$/,
    /^canvas-confetti$/,
    /^embla-carousel-react$/,
    /^recharts$/,
    /^input-otp$/,
];

const SERVER_ONLY_PATTERNS = [
    /^express/,
    /^@aws-sdk\//,
    /^postgres$/,
    /^pg$/,
    /^redis$/,
    /^ioredis$/,
    /^socket\.io/,
    /^passport/,
    /^bcrypt/,
    /^stripe$/,
    /^nodemailer$/,
    /^multer$/,
    /^archiver$/,
    /^compression$/,
    /^cors$/,
    /^@prisma\//,
    /^drizzle-/,
    /^firebase-admin$/,
    /^@socket\.io\//,
];

let errors = [];
let warnings = [];

function checkPackage(pkg, location, shouldHavePatterns, shouldNotHavePatterns) {
    const deps = { ...pkg.dependencies, ...pkg.devDependencies };

    for (const [depName] of Object.entries(deps)) {
        // Check if package should NOT be here
        for (const pattern of shouldNotHavePatterns) {
            if (pattern.test(depName)) {
                errors.push(`❌ ${depName} should NOT be in ${location} (move to ${location === 'root' ? 'client' : 'root'})`);
            }
        }
    }
}

function validateSharedDependencies() {
    const rootDeps = { ...rootPkg.dependencies, ...rootPkg.devDependencies };
    const clientDeps = { ...clientPkg.dependencies, ...clientPkg.devDependencies };

    // Check for duplicates that might cause conflicts
    const CONFLICT_PRONE = ['react', 'react-dom', '@types/react', '@types/react-dom', 'typescript', 'zod'];

    for (const dep of CONFLICT_PRONE) {
        if (rootDeps[dep] && clientDeps[dep]) {
            if (rootDeps[dep] !== clientDeps[dep]) {
                errors.push(`❌ Version mismatch for ${dep}: root(${rootDeps[dep]}) vs client(${clientDeps[dep]})`);
            } else {
                warnings.push(`⚠️ ${dep} exists in both root and client with same version - consider if needed`);
            }
        }
    }
}

function validateConfigurations() {
    // Check for conflicting config files
    if (fs.existsSync('vite.config.ts')) {
        errors.push('❌ Root vite.config.ts should be removed (conflicts with client/vite.config.ts)');
    }

    if (!fs.existsSync('client/vite.config.ts')) {
        errors.push('❌ Client vite.config.ts is missing');
    }

    // Check tailwind configs
    if (fs.existsSync('tailwind.config.ts') && fs.existsSync('client/tailwind.config.ts')) {
        warnings.push('⚠️ Both root and client have tailwind configs - ensure no conflicts');
    }
}

// Run validations
console.log('📋 Checking dependency separation...');
checkPackage(rootPkg, 'root', SERVER_ONLY_PATTERNS, CLIENT_ONLY_PATTERNS);
checkPackage(clientPkg, 'client', CLIENT_ONLY_PATTERNS, SERVER_ONLY_PATTERNS);

console.log('🔄 Checking for shared dependency conflicts...');
validateSharedDependencies();

console.log('⚙️ Checking configuration files...');
validateConfigurations();

// Report results
console.log('\n📊 Validation Results:');
console.log('='.repeat(50));

if (errors.length === 0) {
    console.log('✅ No architecture violations found!');
} else {
    console.log(`❌ Found ${errors.length} error(s):`);
    errors.forEach(error => console.log(`  ${error}`));
}

if (warnings.length > 0) {
    console.log(`\n⚠️ Found ${warnings.length} warning(s):`);
    warnings.forEach(warning => console.log(`  ${warning}`));
}

// Additional checks
console.log('\n📈 Architecture Health:');
const rootDepCount = Object.keys(rootPkg.dependencies || {}).length;
const clientDepCount = Object.keys(clientPkg.dependencies || {}).length;
const rootDevDepCount = Object.keys(rootPkg.devDependencies || {}).length;
const clientDevDepCount = Object.keys(clientPkg.devDependencies || {}).length;

console.log(`📦 Root dependencies: ${rootDepCount} prod + ${rootDevDepCount} dev = ${rootDepCount + rootDevDepCount} total`);
console.log(`📦 Client dependencies: ${clientDepCount} prod + ${clientDevDepCount} dev = ${clientDepCount + clientDevDepCount} total`);

// Success criteria
const isHealthy = errors.length === 0 && warnings.length < 3;
console.log(`\n🏥 Architecture Health: ${isHealthy ? '✅ HEALTHY' : '❌ NEEDS ATTENTION'}`);

if (!isHealthy) {
    console.log('\n💡 Recommendations:');
    console.log('  1. Move client-specific dependencies to client/package.json');
    console.log('  2. Move server-specific dependencies to root package.json');
    console.log('  3. Remove duplicate dependencies where possible');
    console.log('  4. Align versions for shared dependencies');
    console.log('  5. Review configuration file placement');
}

process.exit(errors.length > 0 ? 1 : 0); 