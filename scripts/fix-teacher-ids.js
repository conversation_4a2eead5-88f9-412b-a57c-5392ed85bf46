#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix teacher_ids in sessions for testing multiple dates functionality
 * This directly updates the database to assign teacher_ids to existing sessions
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixTeacherIds() {
    console.log('🚀 Fixing teacher_ids for multiple dates functionality...\n');

    try {
        // Step 1: Get existing sessions
        console.log('📋 Getting existing sessions...');
        const { data: sessions, error: sessionsError } = await supabase
            .from('sessions')
            .select('id, title, teacher_id')
            .limit(10);

        if (sessionsError) {
            console.error('❌ Error fetching sessions:', sessionsError);
            return;
        }

        console.log(`Found ${sessions.length} sessions`);

        // Step 2: Create a test teacher user in auth.users
        console.log('\n👤 Creating test teacher user...');

        const testTeacherEmail = '<EMAIL>';
        const testTeacherPassword = 'testpassword123';

        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
            email: testTeacherEmail,
            password: testTeacherPassword,
            email_confirm: true,
            user_metadata: {
                name: 'Sarah Johnson',
                username: 'yoga_teacher_test'
            }
        });

        if (authError) {
            console.error('❌ Error creating auth user:', authError);
            return;
        }

        const teacherId = authUser.user.id;
        console.log(`✅ Created teacher user: ${teacherId}`);

        // Step 3: Create a profile for the teacher
        console.log('\n📝 Creating teacher profile...');
        const { error: profileError } = await supabase
            .from('profiles')
            .insert({
                id: teacherId,
                display_name: 'Sarah Johnson',
                username: 'yoga_teacher_test',
                bio: 'Experienced yoga instructor with 5+ years of teaching',
                is_teacher: true,
                show_profile: true
            });

        if (profileError) {
            console.error('❌ Error creating profile:', profileError);
            // Continue anyway, the auth user is created
        } else {
            console.log('✅ Created teacher profile');
        }

        // Step 4: Update sessions to have the same teacher and similar titles
        if (sessions.length > 0) {
            console.log('\n🔄 Updating sessions with teacher_id and similar titles...');

            const baseTitle = 'Yoga for Beginners - Multiple Dates Available';
            const updatedSessions = [];

            // Update first 4 sessions to have the same teacher and similar titles
            for (let i = 0; i < Math.min(4, sessions.length); i++) {
                const session = sessions[i];
                const { error: updateError } = await supabase
                    .from('sessions')
                    .update({
                        teacher_id: teacherId,
                        title: baseTitle,
                        description: 'A beginner-friendly yoga session with multiple available dates. Perfect for those new to yoga who want flexibility in scheduling.'
                    })
                    .eq('id', session.id);

                if (updateError) {
                    console.error(`❌ Error updating session ${session.id}:`, updateError);
                } else {
                    console.log(`✅ Updated session ${session.id} with teacher_id and title`);
                    updatedSessions.push(session.id);
                }
            }

            if (updatedSessions.length > 0) {
                console.log('\n🎉 Setup complete!');
                console.log(`\n📊 Summary:`);
                console.log(`   - Created teacher: Sarah Johnson (ID: ${teacherId})`);
                console.log(`   - Updated ${updatedSessions.length} sessions with title: "${baseTitle}"`);
                console.log(`   - Teacher email: ${testTeacherEmail}`);
                console.log(`   - Teacher password: ${testTeacherPassword}`);
                console.log(`\n🔗 Test the multiple dates functionality at:`);
                console.log(`   http://localhost:3000/session/${updatedSessions[0]}`);
                console.log(`\n💡 The session detail page should now show all ${updatedSessions.length} available dates for this session.`);
            }
        } else {
            console.log('❌ No sessions found to update');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the script
fixTeacherIds().catch(console.error); 