/**
 * Advanced script to fix exports in feature modules using TypeScript Compiler API
 * 
 * This script uses the TypeScript Compiler API to accurately analyze exports and imports
 * across the codebase and ensure that all feature index.ts files correctly re-export
 * all necessary items.
 */

const ts = require('typescript');
const fs = require('fs');
const path = require('path');

// Path to the features directory
const featuresDir = path.resolve(__dirname, '../src/features');
const srcDir = path.resolve(__dirname, '../src');

// Create a TypeScript program to analyze the codebase
console.log('Creating TypeScript program...');
const configPath = path.resolve(__dirname, '../tsconfig.json');
const configFile = ts.readConfigFile(configPath, ts.sys.readFile);
const parsedConfig = ts.parseJsonConfigFileContent(configFile.config, ts.sys, path.dirname(configPath));
const program = ts.createProgram(parsedConfig.fileNames, parsedConfig.options);
const typeChecker = program.getTypeChecker();

console.log('Analyzing imports and exports...');

// Get all feature directories
const featureDirectories = fs.readdirSync(featuresDir)
  .filter(dir => fs.statSync(path.join(featuresDir, dir)).isDirectory());

console.log(`Found ${featureDirectories.length} feature directories: ${featureDirectories.join(', ')}`);

// Track imports from features
const featureImports = new Map();

// First pass: collect all imports from feature directories
program.getSourceFiles().forEach(sourceFile => {
  // Skip declaration files and node_modules
  if (sourceFile.isDeclarationFile || sourceFile.fileName.includes('node_modules')) {
    return;
  }
  
  // Process imports
  ts.forEachChild(sourceFile, node => {
    if (ts.isImportDeclaration(node)) {
      const importPath = node.moduleSpecifier.text;
      
      // Check if this is importing from a feature
      if (importPath.startsWith('@/features/')) {
        const featurePath = importPath.split('/');
        const featureName = featurePath[2];
        
        // Extract imported symbols
        let importedSymbols = [];
        
        if (node.importClause) {
          // Named imports
          if (node.importClause.namedBindings) {
            if (ts.isNamedImports(node.importClause.namedBindings)) {
              importedSymbols = node.importClause.namedBindings.elements.map(element => 
                element.propertyName ? element.propertyName.text : element.name.text
              );
            }
          }
          
          // Default import
          if (node.importClause.name) {
            importedSymbols.push('default');
          }
        }
        
        // Add to the map
        if (!featureImports.has(featureName)) {
          featureImports.set(featureName, new Set());
        }
        
        importedSymbols.forEach(symbol => {
          featureImports.get(featureName).add(symbol);
        });
      }
    }
  });
});

// Process each feature directory
featureDirectories.forEach(featureDir => {
  const featurePath = path.join(featuresDir, featureDir);
  console.log(`\nProcessing feature: ${featureDir}`);
  
  // Get all TypeScript files in this feature
  const featureFiles = program.getSourceFiles()
    .filter(sourceFile => 
      !sourceFile.isDeclarationFile && 
      sourceFile.fileName.startsWith(featurePath) &&
      path.basename(sourceFile.fileName) !== 'index.ts'
    );
  
  console.log(`Found ${featureFiles.length} TypeScript files`);
  
  // Collect all exports from these files
  const exportedSymbols = new Map();
  
  featureFiles.forEach(sourceFile => {
    const relativePath = './' + path.relative(featurePath, sourceFile.fileName).replace(/\.tsx?$/, '');
    
    // Skip node_modules
    if (relativePath.includes('node_modules')) {
      return;
    }
    
    const symbols = [];
    
    // Check for exports
    ts.forEachChild(sourceFile, node => {
      // Export declarations
      if (ts.isExportDeclaration(node)) {
        if (node.exportClause && ts.isNamedExports(node.exportClause)) {
          node.exportClause.elements.forEach(element => {
            symbols.push(element.name.text);
          });
        }
      }
      
      // Exported variables, functions, classes, etc.
      if ((ts.isFunctionDeclaration(node) || 
           ts.isClassDeclaration(node) || 
           ts.isVariableStatement(node) ||
           ts.isInterfaceDeclaration(node) ||
           ts.isTypeAliasDeclaration(node) ||
           ts.isEnumDeclaration(node)) && 
          node.modifiers && 
          node.modifiers.some(modifier => modifier.kind === ts.SyntaxKind.ExportKeyword)) {
        
        if (ts.isFunctionDeclaration(node) || 
            ts.isClassDeclaration(node) || 
            ts.isInterfaceDeclaration(node) ||
            ts.isTypeAliasDeclaration(node) ||
            ts.isEnumDeclaration(node)) {
          if (node.name) {
            symbols.push(node.name.text);
          }
        } else if (ts.isVariableStatement(node)) {
          node.declarationList.declarations.forEach(declaration => {
            if (ts.isIdentifier(declaration.name)) {
              symbols.push(declaration.name.text);
            }
          });
        }
      }
      
      // Default exports
      if ((ts.isFunctionDeclaration(node) || 
           ts.isClassDeclaration(node)) && 
          node.modifiers && 
          node.modifiers.some(modifier => modifier.kind === ts.SyntaxKind.ExportKeyword) &&
          node.modifiers.some(modifier => modifier.kind === ts.SyntaxKind.DefaultKeyword)) {
        symbols.push('default');
      }
    });
    
    if (symbols.length > 0) {
      exportedSymbols.set(relativePath, symbols);
    }
  });
  
  // Get the imported symbols for this feature
  const requiredSymbols = featureImports.get(featureDir) || new Set();
  
  console.log(`Feature ${featureDir} needs to export: ${Array.from(requiredSymbols).join(', ')}`);
  
  // Update the index.ts file
  const indexPath = path.join(featurePath, 'index.ts');
  let indexContent = '';
  let existingExports = new Set();
  
  if (fs.existsSync(indexPath)) {
    indexContent = fs.readFileSync(indexPath, 'utf8');
    
    // Parse existing exports
    const indexSourceFile = ts.createSourceFile(
      indexPath,
      indexContent,
      ts.ScriptTarget.Latest,
      true
    );
    
    ts.forEachChild(indexSourceFile, node => {
      if (ts.isExportDeclaration(node)) {
        if (node.exportClause && ts.isNamedExports(node.exportClause)) {
          node.exportClause.elements.forEach(element => {
            existingExports.add(element.name.text);
          });
        }
      }
    });
  }
  
  // Generate new export statements
  let newExports = '';
  let missingExports = new Set();
  
  // Find symbols that need to be exported but aren't yet
  requiredSymbols.forEach(symbol => {
    if (!existingExports.has(symbol)) {
      missingExports.add(symbol);
    }
  });
  
  // Find which file exports each missing symbol
  const symbolToFile = new Map();
  
  exportedSymbols.forEach((symbols, filePath) => {
    symbols.forEach(symbol => {
      if (missingExports.has(symbol)) {
        if (!symbolToFile.has(symbol)) {
          symbolToFile.set(symbol, []);
        }
        symbolToFile.get(symbol).push(filePath);
      }
    });
  });
  
  // Generate export statements
  symbolToFile.forEach((files, symbol) => {
    if (files.length > 0) {
      const filePath = files[0]; // Use the first file that exports this symbol
      newExports += `export { ${symbol} } from '${filePath}';\n`;
    }
  });
  
  // Add new exports to the index file
  if (newExports) {
    console.log(`Adding new exports to ${indexPath}`);
    
    // If the file exists, append to it
    if (fs.existsSync(indexPath)) {
      fs.appendFileSync(indexPath, '\n// Auto-generated exports\n' + newExports);
    } else {
      // Create a new file
      fs.writeFileSync(indexPath, '// Auto-generated exports\n' + newExports);
    }
  } else {
    console.log(`No new exports needed for ${indexPath}`);
  }
});

console.log('\nExport fixing completed!');
