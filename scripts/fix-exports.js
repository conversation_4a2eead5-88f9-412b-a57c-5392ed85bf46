/**
 * <PERSON><PERSON><PERSON> to fix exports in feature modules
 *
 * This script scans all feature directories and ensures that their index.ts files
 * correctly re-export all named exports from the modules within that feature.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the features directory
const featuresDir = path.resolve(__dirname, '../src/features');

// Get all feature directories
const featureDirectories = fs.readdirSync(featuresDir)
  .filter(dir => fs.statSync(path.join(featuresDir, dir)).isDirectory());

console.log(`Found ${featureDirectories.length} feature directories: ${featureDirectories.join(', ')}`);

// Process each feature directory
featureDirectories.forEach(featureDir => {
  const featurePath = path.join(featuresDir, featureDir);
  console.log(`\nProcessing feature: ${featureDir}`);

  // Find all TypeScript files in the feature directory (recursively)
  const tsFiles = findTsFiles(featurePath);
  console.log(`Found ${tsFiles.length} TypeScript files`);

  // Extract exportable items from each file
  const exportableItems = [];
  tsFiles.forEach(file => {
    // Skip index.ts files
    if (path.basename(file) === 'index.ts') return;

    const relativePath = path.relative(featurePath, file);
    const importPath = './' + relativePath.replace(/\.tsx?$/, '');

    // Read the file content
    const content = fs.readFileSync(file, 'utf8');

    // Extract named exports using regex
    const exportMatches = content.match(/export\s+(const|function|class|type|interface|enum)\s+([a-zA-Z0-9_]+)/g) || [];
    const namedExports = exportMatches.map(match => {
      const parts = match.split(/\s+/);
      return parts[parts.length - 1];
    });

    // Extract default exports
    const hasDefaultExport = content.includes('export default');

    if (namedExports.length > 0 || hasDefaultExport) {
      exportableItems.push({
        path: importPath,
        namedExports,
        hasDefaultExport
      });
    }
  });

  // Update or create the index.ts file
  const indexPath = path.join(featurePath, 'index.ts');
  let indexContent = '';

  if (fs.existsSync(indexPath)) {
    indexContent = fs.readFileSync(indexPath, 'utf8');
  }

  // Generate new export statements
  let newExports = '';
  exportableItems.forEach(item => {
    if (item.namedExports.length > 0) {
      // Check if all of these exports are already in the index file
      const allExportsExist = item.namedExports.every(exportName => {
        // Check for export patterns like:
        // export { X } from './path'
        // export { X as Y } from './path'
        // export { type X } from './path'
        const regex = new RegExp(`export\\s+\\{[^}]*\\b${exportName}\\b[^}]*\\}\\s+from\\s+['"]${item.path.replace(/\./g, '\\.')}['"]`);
        return regex.test(indexContent);
      });

      if (!allExportsExist) {
        const exportStatement = `export { ${item.namedExports.join(', ')} } from '${item.path}';`;
        newExports += exportStatement + '\n';
      }
    }

    if (item.hasDefaultExport) {
      const moduleName = path.basename(item.path);
      const exportRegex = new RegExp(`export\\s+\\{[^}]*\\bdefault\\s+as\\s+${moduleName}\\b[^}]*\\}\\s+from\\s+['"]${item.path.replace(/\./g, '\\.')}['"]`);

      if (!exportRegex.test(indexContent)) {
        const exportStatement = `export { default as ${moduleName} } from '${item.path}';`;
        newExports += exportStatement + '\n';
      }
    }
  });

  // Add new exports to the index file
  if (newExports) {
    console.log(`Adding new exports to ${indexPath}`);

    // If the file exists, append to it
    if (fs.existsSync(indexPath)) {
      fs.appendFileSync(indexPath, '\n// Auto-generated exports\n' + newExports);
    } else {
      // Create a new file
      fs.writeFileSync(indexPath, '// Auto-generated exports\n' + newExports);
    }
  } else {
    console.log(`No new exports needed for ${indexPath}`);
  }
});

console.log('\nExport fixing completed!');

// Helper function to find all TypeScript files in a directory (recursively)
function findTsFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Recursively search subdirectories
      results = results.concat(findTsFiles(filePath));
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      results.push(filePath);
    }
  });

  return results;
}
