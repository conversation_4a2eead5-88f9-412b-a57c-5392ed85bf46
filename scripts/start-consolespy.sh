#!/bin/bash

# Start Console Spy servers
echo "🔍 Starting Console Spy servers..."

# Check if already running
if lsof -i :3333 >/dev/null 2>&1; then
    echo "⚠️  Console Spy server already running on port 3333"
else
    echo "🚀 Starting Console Spy servers..."
    cd consolespy && ./start-servers.sh &
    
    # Wait a moment for servers to start
    sleep 3
    
    if lsof -i :3333 >/dev/null 2>&1 && lsof -i :8766 >/dev/null 2>&1; then
        echo "✅ Console Spy servers started successfully!"
        echo "📊 Console log server: http://localhost:3333"
        echo "🔌 MCP server: http://localhost:8766/sse"
        echo ""
        echo "📝 To configure in Cursor:"
        echo "   1. Go to Settings > Features > MCP"
        echo "   2. Add server: ConsoleSpy, Type: sse, URL: http://localhost:8766/sse"
        echo ""
        echo "🌐 Don't forget to enable the browser extension on localhost:3000!"
    else
        echo "❌ Failed to start Console Spy servers"
        exit 1
    fi
fi 