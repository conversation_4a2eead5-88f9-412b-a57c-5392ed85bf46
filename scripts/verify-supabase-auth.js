#!/usr/bin/env node
/**
 * Supabase Auth Verification Script
 * 
 * This script verifies that Supabase authentication is properly configured
 * and connected throughout the application.
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 Verifying Supabase Auth Integration...\n');

const checks = {
    passed: 0,
    failed: 0,
    warnings: 0
};

function checkPass(message) {
    console.log(`✅ ${message}`);
    checks.passed++;
}

function checkFail(message) {
    console.log(`❌ ${message}`);
    checks.failed++;
}

function checkWarn(message) {
    console.log(`⚠️  ${message}`);
    checks.warnings++;
}

// 1. Check environment variables
console.log('📋 Environment Configuration:');
try {
    const envExample = fs.readFileSync('.env.example', 'utf8');

    if (envExample.includes('SUPABASE_URL') && envExample.includes('SUPABASE_ANON_KEY')) {
        checkPass('Environment variables configured for Supabase');
    } else {
        checkFail('Missing Supabase environment variables');
    }

    if (!envExample.includes('NEXTAUTH_')) {
        checkPass('NextAuth references removed from environment');
    } else {
        checkWarn('NextAuth references still found in environment');
    }

    if (!envExample.includes('AWS_')) {
        checkPass('AWS references removed from environment');
    } else {
        checkWarn('AWS references still found in environment');
    }
} catch (error) {
    checkFail('Could not read .env.example file');
}

// 2. Check Supabase client setup
console.log('\n🔧 Supabase Client Configuration:');
try {
    const supabaseSingleton = fs.readFileSync('client/src/lib/supabase-singleton.ts', 'utf8');

    if (supabaseSingleton.includes('createClient') && supabaseSingleton.includes('Database')) {
        checkPass('Supabase client properly configured with TypeScript types');
    } else {
        checkFail('Supabase client configuration incomplete');
    }

    if (supabaseSingleton.includes('onAuthStateChange')) {
        checkPass('Auth state change listener configured');
    } else {
        checkWarn('Auth state change listener not found');
    }
} catch (error) {
    checkFail('Could not read Supabase singleton file');
}

// 3. Check Auth Context
console.log('\n🔐 Auth Context Integration:');
try {
    const authContext = fs.readFileSync('client/src/features/auth/AuthContext.tsx', 'utf8');

    if (authContext.includes('supabase.auth.') && authContext.includes('AuthProvider')) {
        checkPass('Supabase Auth Context properly implemented');
    } else {
        checkFail('Auth Context not properly integrated with Supabase');
    }

    if (authContext.includes('signInWithOAuth')) {
        checkPass('OAuth login methods available');
    } else {
        checkWarn('OAuth login methods not found');
    }

    if (authContext.includes('onAuthStateChange')) {
        checkPass('Auth state change handling implemented');
    } else {
        checkWarn('Auth state change handling not found');
    }
} catch (error) {
    checkFail('Could not read Auth Context file');
}

// 4. Check compatibility layer
console.log('\n🔄 NextAuth Compatibility Layer:');
try {
    const authCompat = fs.readFileSync('client/src/hooks/use-auth-compat.tsx', 'utf8');

    if (authCompat.includes('useAuth') && authCompat.includes('useSession')) {
        checkPass('NextAuth compatibility layer implemented');
    } else {
        checkFail('NextAuth compatibility layer incomplete');
    }
} catch (error) {
    checkFail('Could not read auth compatibility file');
}

// 5. Check for remaining NextAuth imports
console.log('\n🚫 NextAuth Import Cleanup:');
const checkNextAuthImports = (dir) => {
    const files = fs.readdirSync(dir, { withFileTypes: true });
    let foundImports = false;

    files.forEach(file => {
        const fullPath = path.join(dir, file.name);

        if (file.isDirectory() && file.name !== 'node_modules' && file.name !== '.git') {
            const result = checkNextAuthImports(fullPath);
            if (result) foundImports = true;
        } else if (file.name.endsWith('.tsx') || file.name.endsWith('.ts')) {
            try {
                const content = fs.readFileSync(fullPath, 'utf8');
                if (content.includes('next-auth/react') && !fullPath.includes('use-auth-compat')) {
                    console.log(`   Found NextAuth import in: ${fullPath}`);
                    foundImports = true;
                }
            } catch (error) {
                // Skip files that can't be read
            }
        }
    });

    return foundImports;
};

const hasNextAuthImports = checkNextAuthImports('client/src');
if (!hasNextAuthImports) {
    checkPass('No remaining NextAuth imports found');
} else {
    checkFail('NextAuth imports still found (see above)');
}

// 6. Check auth callback page
console.log('\n📞 Auth Callback Configuration:');
try {
    const authCallback = fs.readFileSync('client/src/pages/auth/callback.tsx', 'utf8');

    if (authCallback.includes('supabase.auth.') && authCallback.includes('exchangeCodeForSession')) {
        checkPass('Auth callback properly configured for Supabase OAuth');
    } else {
        checkWarn('Auth callback may need Supabase OAuth configuration');
    }
} catch (error) {
    checkWarn('Auth callback file not found or not readable');
}

// 7. Check main App component
console.log('\n🏠 Main App Integration:');
try {
    const appFile = fs.readFileSync('client/src/App.tsx', 'utf8');

    if (appFile.includes('AuthProvider') && appFile.includes('features/auth')) {
        checkPass('Supabase AuthProvider properly integrated in App component');
    } else {
        checkFail('AuthProvider not found in App component');
    }

    if (appFile.includes('initSupabaseAuth')) {
        checkPass('Supabase auth initialization called');
    } else {
        checkWarn('Supabase auth initialization not found');
    }
} catch (error) {
    checkFail('Could not read App.tsx file');
}

// Summary
console.log('\n📊 Verification Summary:');
console.log(`✅ Passed: ${checks.passed}`);
console.log(`❌ Failed: ${checks.failed}`);
console.log(`⚠️  Warnings: ${checks.warnings}`);

if (checks.failed === 0) {
    console.log('\n🎉 Supabase Auth Integration: VERIFIED ✅');
    console.log('Your application is properly configured for Supabase authentication!');
} else {
    console.log('\n🚨 Issues Found - Please address the failed checks above');
}

if (checks.warnings > 0) {
    console.log('💡 Consider addressing the warnings for optimal configuration');
} 