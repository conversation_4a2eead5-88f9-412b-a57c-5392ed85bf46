#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix the test sessions to have different dates for multiple dates functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixDifferentDates() {
    console.log('🚀 Fixing test sessions to have different dates...\n');

    const teacherId = '028e0933-2730-4689-8564-3f519426037e';
    const sessionIds = [
        '7fe3382a-8644-43ec-84e4-f55fc1c5cd8a',
        '20498152-a753-4930-9e27-e2daf8c7f920',
        '8fee4b6b-532a-445c-b159-4e39d6be2618',
        'b23a8237-9690-4d80-a4dd-83beea0d4e93'
    ];

    // Create different dates (future dates)
    const today = new Date();
    const dates = [
        new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000), // 10 days from now  
        new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
        new Date(today.getTime() + 21 * 24 * 60 * 60 * 1000)  // 3 weeks from now
    ];

    try {
        for (let i = 0; i < sessionIds.length; i++) {
            const sessionId = sessionIds[i];
            const newDate = dates[i].toISOString();

            console.log(`📅 Updating session ${sessionId} to date: ${newDate}`);

            const { error } = await supabase
                .from('sessions')
                .update({
                    date: newDate,
                    // Also ensure they all have the same title and teacher
                    title: 'Yoga for Beginners - Multiple Dates Available',
                    teacher_id: teacherId
                })
                .eq('id', sessionId);

            if (error) {
                console.error(`❌ Error updating session ${sessionId}:`, error);
            } else {
                console.log(`✅ Updated session ${sessionId} with new date`);
            }
        }

        console.log('\n🎉 All sessions updated with different dates!');
        console.log(`\n🔗 Test the multiple dates functionality at:`);
        console.log(`   http://localhost:3000/session/${sessionIds[0]}`);
        console.log(`\n💡 The session detail page should now show all 4 available dates.`);

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Run the script
fixDifferentDates().catch(console.error); 