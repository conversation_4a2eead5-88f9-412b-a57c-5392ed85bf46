#!/usr/bin/env node

/**
 * Context7 Documentation Update Runner
 * Systematically checks and updates documentation for all project dependencies
 */

import { readFileSync } from 'fs';
import { join } from 'path';

// Color codes for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

class Context7UpdateRunner {
    constructor() {
        this.dependencies = this.loadDependencies();
        this.updateLog = [];
    }

    loadDependencies() {
        try {
            const serverPackage = JSON.parse(readFileSync('package.json', 'utf8'));
            const clientPackage = JSON.parse(readFileSync('client/package.json', 'utf8'));

            return {
                server: {
                    dependencies: serverPackage.dependencies || {},
                    devDependencies: serverPackage.devDependencies || {}
                },
                client: {
                    dependencies: clientPackage.dependencies || {},
                    devDependencies: clientPackage.devDependencies || {}
                }
            };
        } catch (error) {
            console.error(`${colors.red}Error loading package.json files:${colors.reset}`, error.message);
            return { server: {}, client: {} };
        }
    }

    log(message, color = 'reset') {
        console.log(`${colors[color]}${message}${colors.reset}`);
    }

    async runSecurityAudit() {
        this.log('\n🔒 SECURITY AUDIT - Context7 Queries', 'cyan');
        this.log('='.repeat(50), 'cyan');

        const securityQueries = [
            {
                library: 'Express.js',
                query: 'Get latest Express.js security middleware and CORS configuration best practices',
                priority: 'HIGH',
                focus: 'Security headers, rate limiting, input validation'
            },
            {
                library: 'Supabase',
                query: 'Show Supabase RLS security patterns and authentication best practices',
                priority: 'HIGH',
                focus: 'Row Level Security, JWT handling, OAuth security'
            },
            {
                library: 'Stripe',
                query: 'Find Stripe webhook security and payment processing best practices',
                priority: 'HIGH',
                focus: 'Webhook verification, PCI compliance, secure payment flows'
            },
            {
                library: 'Passport.js',
                query: 'Get Passport.js OAuth security patterns and session management',
                priority: 'MEDIUM',
                focus: 'Session security, OAuth flows, CSRF protection'
            }
        ];

        securityQueries.forEach((item, index) => {
            this.log(`\n${index + 1}. ${item.library} (${item.priority})`, 'yellow');
            this.log(`   Query: "${item.query}"`, 'bright');
            this.log(`   Focus: ${item.focus}`, 'blue');
            this.log(`   📋 Action: Ask Context7 this exact query`, 'green');
        });
    }

    async runPerformanceAudit() {
        this.log('\n⚡ PERFORMANCE AUDIT - Context7 Queries', 'magenta');
        this.log('='.repeat(50), 'magenta');

        const performanceQueries = [
            {
                library: 'React 18',
                query: 'Get React 18 performance optimization and concurrent features documentation',
                priority: 'HIGH',
                focus: 'Concurrent rendering, Suspense, useMemo optimization'
            },
            {
                library: 'Drizzle ORM',
                query: 'Show Drizzle ORM query optimization and connection pooling patterns',
                priority: 'HIGH',
                focus: 'Query batching, connection pooling, prepared statements'
            },
            {
                library: 'Vite',
                query: 'Get Vite build optimization and code splitting best practices',
                priority: 'MEDIUM',
                focus: 'Bundle optimization, lazy loading, tree shaking'
            },
            {
                library: 'Redis',
                query: 'Find Redis caching strategies and memory optimization techniques',
                priority: 'MEDIUM',
                focus: 'Cache invalidation, memory usage, clustering'
            }
        ];

        performanceQueries.forEach((item, index) => {
            this.log(`\n${index + 1}. ${item.library} (${item.priority})`, 'yellow');
            this.log(`   Query: "${item.query}"`, 'bright');
            this.log(`   Focus: ${item.focus}`, 'blue');
            this.log(`   📋 Action: Ask Context7 this exact query`, 'green');
        });
    }

    async runUIUXAudit() {
        this.log('\n🎨 UI/UX AUDIT - Context7 Queries', 'blue');
        this.log('='.repeat(50), 'blue');

        const uiQueries = [
            {
                library: 'Radix UI',
                query: 'Get Radix UI latest component patterns and accessibility features',
                priority: 'HIGH',
                focus: 'ARIA compliance, keyboard navigation, screen reader support'
            },
            {
                library: 'TailwindCSS',
                query: 'Show TailwindCSS utility updates and responsive design patterns',
                priority: 'MEDIUM',
                focus: 'Mobile-first design, dark mode, performance optimization'
            },
            {
                library: 'React Hook Form',
                query: 'Find React Hook Form validation patterns and performance tips',
                priority: 'MEDIUM',
                focus: 'Form validation, error handling, performance optimization'
            },
            {
                library: 'Framer Motion',
                query: 'Get Framer Motion animation best practices and performance',
                priority: 'LOW',
                focus: 'Smooth animations, reduced motion, performance impact'
            }
        ];

        uiQueries.forEach((item, index) => {
            this.log(`\n${index + 1}. ${item.library} (${item.priority})`, 'yellow');
            this.log(`   Query: "${item.query}"`, 'bright');
            this.log(`   Focus: ${item.focus}`, 'blue');
            this.log(`   📋 Action: Ask Context7 this exact query`, 'green');
        });
    }

    async runArchitectureAudit() {
        this.log('\n🏗️  ARCHITECTURE AUDIT - Context7 Queries', 'green');
        this.log('='.repeat(50), 'green');

        const architectureQueries = [
            {
                library: 'Socket.io',
                query: 'Get Socket.io scaling patterns and cluster management documentation',
                priority: 'HIGH',
                focus: 'Horizontal scaling, load balancing, connection management'
            },
            {
                library: 'BullMQ',
                query: 'Show BullMQ job queue scaling and Redis cluster configuration',
                priority: 'MEDIUM',
                focus: 'Job processing, queue management, error handling'
            },
            {
                library: 'TypeScript',
                query: 'Find TypeScript latest configuration and strict mode best practices',
                priority: 'MEDIUM',
                focus: 'Type safety, build performance, error prevention'
            },
            {
                library: 'Vercel',
                query: 'Get Vercel deployment optimization and edge function patterns',
                priority: 'LOW',
                focus: 'Edge computing, serverless functions, deployment strategies'
            }
        ];

        architectureQueries.forEach((item, index) => {
            this.log(`\n${index + 1}. ${item.library} (${item.priority})`, 'yellow');
            this.log(`   Query: "${item.query}"`, 'bright');
            this.log(`   Focus: ${item.focus}`, 'blue');
            this.log(`   📋 Action: Ask Context7 this exact query`, 'green');
        });
    }

    displayDependencyOverview() {
        this.log('\n📦 DEPENDENCY OVERVIEW', 'cyan');
        this.log('='.repeat(50), 'cyan');

        const serverDeps = Object.keys(this.dependencies.server.dependencies || {}).length;
        const serverDevDeps = Object.keys(this.dependencies.server.devDependencies || {}).length;
        const clientDeps = Object.keys(this.dependencies.client.dependencies || {}).length;
        const clientDevDeps = Object.keys(this.dependencies.client.devDependencies || {}).length;

        this.log(`Server Dependencies: ${serverDeps}`, 'bright');
        this.log(`Server Dev Dependencies: ${serverDevDeps}`, 'bright');
        this.log(`Client Dependencies: ${clientDeps}`, 'bright');
        this.log(`Client Dev Dependencies: ${clientDevDeps}`, 'bright');
        this.log(`Total Dependencies: ${serverDeps + serverDevDeps + clientDeps + clientDevDeps}`, 'yellow');
    }

    displayInstructions() {
        this.log('\n📋 HOW TO USE CONTEXT7 FOR UPDATES', 'bright');
        this.log('='.repeat(50), 'bright');
        this.log('1. Copy each query exactly as shown above', 'green');
        this.log('2. Ask Context7 in Cursor using the exact query text', 'green');
        this.log('3. Review the documentation provided by Context7', 'green');
        this.log('4. Implement the recommended changes in your codebase', 'green');
        this.log('5. Test the changes thoroughly', 'green');
        this.log('6. Update this log with what was changed', 'green');

        this.log('\n💡 PRO TIPS:', 'yellow');
        this.log('• Start with HIGH priority items first', 'blue');
        this.log('• Focus on one library at a time', 'blue');
        this.log('• Document all changes made', 'blue');
        this.log('• Test in development before production', 'blue');
    }

    async run() {
        this.log('🚀 Context7 Documentation Update Runner', 'bright');
        this.log('Session Hub V.2 - Production Readiness Audit\n', 'bright');

        this.displayDependencyOverview();
        await this.runSecurityAudit();
        await this.runPerformanceAudit();
        await this.runUIUXAudit();
        await this.runArchitectureAudit();
        this.displayInstructions();

        this.log('\n✅ Audit complete! Use the queries above with Context7 in Cursor.', 'green');
        this.log('📄 See docs/context7-update-strategy.md for the full strategy.', 'cyan');
    }
}

// Run the audit
const runner = new Context7UpdateRunner();
runner.run().catch(console.error); 