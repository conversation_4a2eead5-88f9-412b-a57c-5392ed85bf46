/**
 * Export Checker Script
 *
 * This script scans the codebase for import statements and checks if the
 * imported symbols are actually exported by the target modules.
 *
 * Usage:
 *   node scripts/check-exports.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const SRC_DIR = path.resolve(__dirname, '../src');
const EXCLUDED_DIRS = ['node_modules', 'dist', 'build', '.git'];
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Track issues
const issues = [];

// Find all source files
function findSourceFiles(dir) {
  const results = [];

  if (EXCLUDED_DIRS.some(excluded => dir.includes(`/${excluded}/`))) {
    return results;
  }

  const list = fs.readdirSync(dir);

  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      results.push(...findSourceFiles(filePath));
    } else if (EXTENSIONS.includes(path.extname(file))) {
      results.push(filePath);
    }
  }

  return results;
}

// Extract imports from a file
function extractImports(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const imports = [];

  // Match import statements
  const importRegex = /import\s+{([^}]+)}\s+from\s+['"]([^'"]+)['"]/g;
  let match;

  while ((match = importRegex.exec(content)) !== null) {
    const symbols = match[1].split(',').map(s => s.trim().split(' as ')[0].trim());
    const source = match[2];

    // Only check local imports
    if (source.startsWith('.') || source.startsWith('@/')) {
      imports.push({
        symbols,
        source,
        sourceFile: resolveImportPath(filePath, source)
      });
    }
  }

  return imports;
}

// Resolve import path to actual file path
function resolveImportPath(importerPath, importPath) {
  // Handle @ alias
  if (importPath.startsWith('@/')) {
    importPath = importPath.replace('@/', `${SRC_DIR}/`);
  } else if (importPath.startsWith('.')) {
    // Handle relative imports
    importPath = path.resolve(path.dirname(importerPath), importPath);
  }

  // Try to resolve the actual file
  for (const ext of EXTENSIONS) {
    if (fs.existsSync(`${importPath}${ext}`)) {
      return `${importPath}${ext}`;
    }
  }

  // Check for index files
  for (const ext of EXTENSIONS) {
    if (fs.existsSync(path.join(importPath, `index${ext}`))) {
      return path.join(importPath, `index${ext}`);
    }
  }

  return importPath;
}

// Extract exports from a file
function extractExports(filePath) {
  if (!fs.existsSync(filePath)) {
    return { namedExports: [], typeExports: [] };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const namedExports = [];
  const typeExports = [];

  // Match direct exports
  const exportRegex = /export\s+(const|function|class|enum|let|var)\s+([a-zA-Z0-9_]+)/g;
  let match;

  while ((match = exportRegex.exec(content)) !== null) {
    namedExports.push(match[2]);
  }

  // Match type exports
  const typeExportRegex = /export\s+(type|interface)\s+([a-zA-Z0-9_]+)/g;
  while ((match = typeExportRegex.exec(content)) !== null) {
    typeExports.push(match[2]);
  }

  // Match named exports
  const namedExportRegex = /export\s+{([^}]+)}/g;
  while ((match = namedExportRegex.exec(content)) !== null) {
    const exportedSymbols = match[1].split(',').map(s => {
      const parts = s.trim().split(' as ');
      return parts[parts.length - 1].trim();
    });
    namedExports.push(...exportedSymbols);
  }

  // Match type re-exports
  const typeReExportRegex = /export\s+type\s+{([^}]+)}/g;
  while ((match = typeReExportRegex.exec(content)) !== null) {
    const exportedTypes = match[1].split(',').map(s => {
      const parts = s.trim().split(' as ');
      return parts[parts.length - 1].trim();
    });
    typeExports.push(...exportedTypes);
  }

  return { namedExports, typeExports };
}

// Check if imports are properly exported
function checkImports(filePath, imports) {
  for (const imp of imports) {
    const { symbols, source, sourceFile } = imp;

    // Skip if we couldn't resolve the source file
    if (!sourceFile || !fs.existsSync(sourceFile)) {
      continue;
    }

    const { namedExports, typeExports } = extractExports(sourceFile);
    const allExports = [...namedExports, ...typeExports];

    for (const symbol of symbols) {
      if (!allExports.includes(symbol)) {
        issues.push({
          file: path.relative(process.cwd(), filePath),
          symbol,
          source,
          sourceFile: path.relative(process.cwd(), sourceFile)
        });
      }
    }
  }
}

// Main function
function main() {
  console.log('Scanning for source files...');
  const sourceFiles = findSourceFiles(SRC_DIR);
  console.log(`Found ${sourceFiles.length} source files.`);

  console.log('Checking imports...');
  for (const file of sourceFiles) {
    const imports = extractImports(file);
    checkImports(file, imports);
  }

  // Report issues
  if (issues.length > 0) {
    console.log(`\nFound ${issues.length} potential export issues:`);

    for (const issue of issues) {
      console.log(`\n${issue.file}:`);
      console.log(`  Symbol '${issue.symbol}' is imported from '${issue.source}'`);
      console.log(`  but not exported by '${issue.sourceFile}'`);
    }

    console.log('\nSuggestions:');
    console.log('1. Add the missing exports to the source files');
    console.log('2. For TypeScript interfaces/types, use "export type { Symbol }" syntax');
    console.log('3. Check for typos in import statements');

    process.exit(1);
  } else {
    console.log('\nNo export issues found!');
  }
}

main();
