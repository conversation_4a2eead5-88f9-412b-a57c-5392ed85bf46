#!/usr/bin/env node

/**
 * This script checks for default exports in the codebase
 * and reports files that are still using them.
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directories to check
const DIRS_TO_CHECK = [
  'src/components',
  'src/pages',
  'src/features',
  'src/contexts',
  'src/hooks',
  'src/lib',
  'src/utils',
];

// Files to exclude (these are allowed to have default exports)
const EXCLUDED_FILES = [
  'main.tsx',
  'index.tsx',
  'vite.config.ts',
  'App.tsx',
];

// Function to check if a file has default exports
function hasDefaultExport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for "export default" pattern
    const hasExportDefault = /export\s+default\s+/.test(content);

    // Check for "export { default }" pattern
    const hasExportDefaultNamed = /export\s+{\s*default\s*}/.test(content);

    return hasExportDefault || hasExportDefaultNamed;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('Checking for default exports in the codebase...');

  let filesWithDefaultExports = [];

  // Check each directory
  for (const dir of DIRS_TO_CHECK) {
    const fullDir = path.join(process.cwd(), dir);

    if (!fs.existsSync(fullDir)) {
      console.log(`Directory ${fullDir} does not exist, skipping...`);
      continue;
    }

    try {
      // Find all TypeScript and TypeScript React files
      const cmd = `find ${fullDir} -type f -name "*.ts" -o -name "*.tsx"`;
      const files = execSync(cmd, { encoding: 'utf8' }).split('\n').filter(Boolean);

      // Check each file
      for (const file of files) {
        const fileName = path.basename(file);

        // Skip excluded files
        if (EXCLUDED_FILES.includes(fileName)) {
          continue;
        }

        if (hasDefaultExport(file)) {
          filesWithDefaultExports.push(file);
        }
      }
    } catch (error) {
      console.error(`Error processing directory ${dir}:`, error);
    }
  }

  // Report results
  if (filesWithDefaultExports.length === 0) {
    console.log('✅ No default exports found in the codebase!');
  } else {
    console.log(`❌ Found ${filesWithDefaultExports.length} files with default exports:`);
    filesWithDefaultExports.forEach(file => {
      console.log(`  - ${file.replace(process.cwd() + '/', '')}`);
    });
    console.log('\nConsider converting these to named exports for consistency.');
    process.exit(1);
  }
}

main();
