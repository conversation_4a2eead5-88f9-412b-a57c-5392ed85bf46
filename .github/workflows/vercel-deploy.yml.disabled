name: Deploy to Vercel

on:
  push:
    branches:
      - main
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      
      - name: Set environment variables
        id: env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "VERCEL_ENV=production" >> $GITHUB_ENV
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "VERCEL_ENV=preview" >> $GITHUB_ENV
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi
      
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=${{ env.VERCEL_ENV }} --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
      
      - name: Deploy Project Artifacts to Vercel
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
          else
            vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
          fi
